<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="model"
            type="com.zizhiguanjia.model_home.viewmodel.HomeFlutterViewModel" />

        <variable
            name="studyModel"
            type="com.zizhiguanjia.model_home.viewmodel.StudyViewModel" />

        <import type="android.view.View" />
    </data>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <!-- 引入顶部标题栏 -->
                <include
                    android:id="@+id/home_head"
                    layout="@layout/home_head_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="30dp"
                    app:model="@{studyModel}" />

                <!-- 顶部通知栏 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cd_notify"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="12dp"
                    app:cardBackgroundColor="#E4EDFF"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_speaker"
                            android:tint="#3163F6" />

                        <TextView
                            android:id="@+id/tv_notify_des"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:text="--题库为--住建委公布的官方题库，请放心学习"
                            android:textColor="#333333"
                            android:textSize="12sp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#F5F6FA"
                android:fillViewport="true">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <View
                        android:id="@+id/vChapter"
                        android:layout_width="200dp"
                        android:layout_height="100dp"
                        android:layout_centerHorizontal="true"
                        android:layout_marginLeft="12dp"
                        android:layout_marginTop="237dp"
                        android:layout_marginRight="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_exame"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="测试智能模考"
                            android:visibility="gone" />
                        <!-- Banner轮播 -->
                        <include
                            android:id="@+id/home_banner_layout"
                            layout="@layout/home_banner_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="12dp"
                            app:model="@{studyModel}" />

                        <!-- 重大更新 Banner -->
                        <!--                <ImageView-->
                        <!--                    android:layout_width="match_parent"-->
                        <!--                    android:layout_height="130dp"-->
                        <!--                    android:layout_marginHorizontal="12dp"-->
                        <!--                    android:layout_marginTop="12dp"-->
                        <!--                    android:adjustViewBounds="true"-->
                        <!--                    android:scaleType="fitCenter"-->
                        <!--                    android:src="@drawable/test_date_top_bg" />-->

                        <!-- 章节练习卡片 -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="12dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="0dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/title"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="章节练习"
                                        android:textColor="#333333"
                                        android:textSize="18sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/tv_study_time"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="8dp"
                                        android:text="学习时间:3分钟"
                                        android:textColor="#666666"
                                        android:textSize="12sp" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="16dp"
                                    android:gravity="bottom"
                                    android:orientation="horizontal">

                                    <!-- 左侧进度信息 -->
                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="bottom"
                                            android:orientation="horizontal">

                                            <TextView
                                                android:id="@+id/tv_completed_count"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="0"
                                                android:textColor="#222222"
                                                android:textSize="40sp"
                                                android:textStyle="bold" />

                                            <TextView
                                                android:id="@+id/tv_total_count"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_marginStart="2dp"
                                                android:paddingBottom="4dp"
                                                android:text="/0题"
                                                android:textColor="#666666"
                                                android:textSize="14sp" />

                                            <TextView
                                                android:layout_width="0dp"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1" />

                                            <TextView
                                                android:id="@+id/tv_progress_percent"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_gravity="bottom|end"
                                                android:layout_marginStart="8dp"
                                                android:text="0%"
                                                android:textColor="#666666"
                                                android:textSize="12sp" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="8dp"
                                            android:gravity="center_vertical">

                                            <ProgressBar
                                                android:id="@+id/progress_bar"
                                                style="?android:attr/progressBarStyleHorizontal"
                                                android:layout_width="0dp"
                                                android:layout_height="6dp"
                                                android:layout_weight="1"
                                                android:max="100"
                                                android:progress="0"
                                                android:progressDrawable="@drawable/blue_rounded_button_bg" />
                                        </LinearLayout>

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="4dp"
                                            android:text="学习进度"
                                            android:textColor="#999999"
                                            android:textSize="12sp" />
                                    </LinearLayout>

                                    <!-- 右侧正确率 -->
                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_marginStart="16dp"
                                        android:background="@drawable/accuracy_box_background"
                                        android:gravity="center"
                                        android:minWidth="80dp"
                                        android:orientation="vertical"
                                        android:padding="8dp">

                                        <LinearLayout
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="bottom">

                                            <TextView
                                                android:id="@+id/tv_accuracy"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="0%"
                                                android:textColor="#222222"
                                                android:textSize="25sp"
                                                android:textStyle="bold" />
                                        </LinearLayout>

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="2dp"
                                            android:text="正确率"
                                            android:textColor="#999999"
                                            android:textSize="12sp" />
                                    </LinearLayout>
                                </LinearLayout>

                                <!-- 继续练习按钮 -->
                                <Button
                                    android:id="@+id/btn_start_practice"
                                    android:layout_width="260dp"
                                    android:layout_height="60dp"
                                    android:layout_gravity="center"
                                    android:layout_marginTop="16dp"
                                    android:background="@drawable/main_start_bg"
                                    android:text="继续练习"
                                    android:textColor="#FFFFFF"
                                    android:textSize="16sp" />
                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- 功能按钮区域 -->
                        <LinearLayout
                            android:id="@+id/menu_nav_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="12dp"
                            android:layout_marginLeft="10dp"
                            android:layout_marginRight="10dp"
                            android:baselineAligned="false"
                            android:orientation="horizontal">

                            <!-- 这里将由代码动态生成菜单按钮 -->

                        </LinearLayout>

                        <!-- 试用权限卡片 -->
                        <LinearLayout
                            android:id="@+id/layout_ad"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="12dp"
                            android:layout_marginTop="10dp"
                            android:background="@drawable/rounded_white_bg"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:padding="12dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center_vertical"
                                android:src="@drawable/main_mess" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="试用权益"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tv_ad_content"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="2dp"
                                    android:text="--"
                                    android:textColor="#666666"
                                    android:textSize="12sp" />
                            </LinearLayout>

                            <Button
                                android:id="@+id/btn_upgrade_database"
                                android:layout_width="wrap_content"
                                android:layout_height="32dp"
                                android:background="@drawable/outlined_button_background"
                                android:paddingHorizontal="12dp"
                                android:text="升级题库"
                                android:textColor="#3163F6"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <!-- 学习计划模块 -->
                        <LinearLayout
                            android:id="@+id/layout_study_plan"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="12dp"
                            android:layout_marginTop="16dp"
                            android:background="@drawable/rounded_white_bg"
                            android:orientation="vertical">

                            <!-- 分割线 -->
<!--                            <View-->
<!--                                android:layout_width="match_parent"-->
<!--                                android:layout_height="0.5dp"-->
<!--                                android:background="#E5E5E5" />-->

                            <!-- 未设置考试日期的视图 -->
                            <LinearLayout
                                android:id="@+id/layout_unset_exam_date"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:visibility="gone">

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="vertical"
                                    android:paddingStart="24dp"
                                    android:paddingTop="20dp"
                                    android:paddingBottom="20dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="自动生成每日学习计划"
                                        android:textColor="#424242"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="4dp"
                                        android:text="日拱一卒 稳步通关"
                                        android:textColor="#A9A9A9"
                                        android:textSize="12sp" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:background="@drawable/outlined_button_background"
                                    android:gravity="center"
                                    android:orientation="horizontal"
                                    android:paddingLeft="15dp"
                                    android:paddingRight="15dp">

                                    <ImageView
                                        android:layout_width="15dp"
                                        android:layout_height="15dp"
                                        android:layout_marginRight="5dp"
                                        android:src="@drawable/ic_calendar_study" />

                                    <TextView
                                        android:id="@+id/btn_set_exam_date"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:paddingVertical="10dp"
                                        android:text="点击设置考试倒计时"
                                        android:textColor="#3265F6"
                                        android:textSize="12sp" />
                                </LinearLayout>
                            </LinearLayout>
                            <!-- 已设置考试日期的视图 -->
                            <FrameLayout
                                android:id="@+id/layout_set_exam_date"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:visibility="visible">

                                <!-- 背景图 -->
                                <ImageView
                                    android:layout_width="100dp"
                                    android:layout_height="60dp"
                                    android:layout_gravity="bottom|end"
                                    android:layout_marginBottom="1dp"
                                    android:src="@drawable/bg_main_study_plan" />

                                <!-- 立即学习按钮 -->
                                <Button
                                    android:id="@+id/btn_start_study"
                                    android:layout_width="wrap_content"
                                    android:layout_height="32dp"
                                    android:layout_gravity="bottom|end"
                                    android:layout_marginTop="20dp"
                                    android:layout_marginEnd="15dp"
                                    android:layout_marginBottom="20dp"
                                    android:background="@drawable/outlined_button_background"
                                    android:text="立即学习"
                                    android:textColor="#3265F6"
                                    android:textSize="12sp" />

                                <!-- 学习信息 -->
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:paddingStart="24dp"
                                    android:paddingTop="10dp">

                                    <TextView
                                        android:id="@+id/tv_remaining_questions"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="剩余0道题未练习，0道错题未清空"
                                        android:textColor="#A9A9A9"
                                        android:textSize="12sp" />

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:id="@+id/tv_exam_day_prefix"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="距考试剩"
                                            android:textColor="#3C3C43"
                                            android:textSize="13sp"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:id="@+id/tv_remaining_days"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:background="#F0F0F0"
                                            android:paddingHorizontal="12dp"
                                            android:text="0"
                                            android:textColor="#007AFF"
                                            android:textSize="21sp"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:id="@+id/tv_exam_day_suffix"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="天"
                                            android:textColor="#3C3C43"
                                            android:textSize="13sp"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:id="@+id/tv_exam_day_status"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:paddingHorizontal="10dp"
                                            android:paddingVertical="10dp"
                                            android:text="考试日"
                                            android:textColor="#007AFF"
                                            android:textSize="16sp"
                                            android:textStyle="bold"
                                            android:visibility="gone" />
                                    </LinearLayout>
                                </LinearLayout>
                            </FrameLayout>
                        </LinearLayout>

                        <!-- 考试日期提示 -->
                        <LinearLayout
                            android:id="@+id/layout_exam_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="12dp"
                            android:layout_marginTop="16dp"
                            android:background="@drawable/rounded_blue_bg"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:padding="12dp"
                            android:visibility="gone">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/ic_calendar_study" />

                            <TextView
                                android:id="@+id/tv_exam_date_tip"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:text="距离考试还有30天"
                                android:textColor="#FFFFFF"
                                android:textSize="14sp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <!-- 添加通关精讲课容器 -->
                        <LinearLayout
                            android:id="@+id/ll_live"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:visibility="visible">
                            <!-- 通关精讲课 -->
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="16dp"
                                android:layout_marginTop="24dp"
                                android:layout_marginEnd="16dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="通关精讲课"
                                    android:textColor="#333333"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tv_more"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerVertical="true"
                                    android:text="查看更多 >"
                                    android:textColor="#999999"
                                    android:textSize="12sp" />
                            </RelativeLayout>

                            <!-- 视频课程列表 -->
                            <LinearLayout
                                android:id="@+id/live_course_container"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="12dp"
                                android:layout_marginTop="12dp"
                                android:orientation="horizontal">

                                <!-- 视频课程2 -->
                                <LinearLayout
                                    android:id="@+id/layout_video_2"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="8dp"
                                    android:layout_weight="1"
                                    android:orientation="vertical">

                                    <androidx.cardview.widget.CardView
                                        android:layout_width="match_parent"
                                        android:layout_height="100dp"
                                        app:cardCornerRadius="8dp"
                                        app:cardElevation="0dp">

                                        <ImageView
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:background="@drawable/video_placeholder_background" />
                                    </androidx.cardview.widget.CardView>

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="8dp"
                                        android:ellipsize="end"
                                        android:maxLines="2"
                                        android:text="===="
                                        android:textColor="#333333"
                                        android:textSize="13sp" />
                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>
                        <!-- 底部信息 -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:gravity="center"
                            android:text="考安管人员ABC证，用安全员考试宝典"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:gravity="center"
                            android:text="其他功能"
                            android:textColor="#999999"
                            android:textSize="12sp" />

                        <!-- 底部功能按钮 -->
                        <LinearLayout
                            android:id="@+id/bottom_functions_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginEnd="16dp"
                            android:layout_marginBottom="16dp"
                            android:orientation="horizontal">

                            <!-- 按钮将在代码中动态添加 -->

                        </LinearLayout>
                        <!-- 底部空白 -->
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="30dp" />
                    </LinearLayout>
                </RelativeLayout>
            </androidx.core.widget.NestedScrollView>
        </LinearLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</layout> 