<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.zizhiguanjia.model_home.viewmodel.HomeFlutterViewModel" />

        <variable
            name="studyModel"
            type="com.zizhiguanjia.model_home.viewmodel.StudyViewModel" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#F5F6FA"
        android:orientation="vertical">

        <com.wb.lib_weiget.titlebar.TitleBar
            android:id="@+id/tbflutter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:tb_centerText="北京 A证"
            app:tb_centerTextColor="@color/black"
            app:tb_fillStatusBar="true"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            app:tb_leftType="imageButton"
            app:tb_statusBarColor="#F5F6FA"
            app:tb_titleBarColor="#F5F6FA" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!--            <View-->
            <!--                android:id="@+id/vChapter"-->
            <!--                android:layout_width="200dp"-->
            <!--                android:layout_height="100dp"-->
            <!--                android:layout_centerHorizontal="true"-->
            <!--                android:layout_marginLeft="12dp"-->
            <!--                android:layout_marginTop="237dp"-->
            <!--                android:layout_marginRight="12dp" />-->

            <!-- 原生学习页面布局 -->
            <!--            <LinearLayout-->
            <!--                android:id="@+id/ll_native_study_layout"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="match_parent"-->
            <!--                android:background="#F5F6FA"-->
            <!--                android:orientation="vertical"-->
            <!--                android:visibility="visible">-->

            <!-- 引入顶部标题栏 -->
            <include
                android:id="@+id/home_head"
                layout="@layout/home_head_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp" />
            <!-- 通知条 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#EDF0FF"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_speaker"
                        android:tint="#3163F6" />

                    <TextView
                        android:id="@+id/tv_notify_des"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="--题库为--住建委公布的官方题库，请放心学习"
                        android:textColor="#333333"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipe_refresh_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.core.widget.NestedScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <!-- 顶部信息区域 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="#F5F6FA"
                        android:orientation="vertical">

                        <ScrollView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tv_exame"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="测试智能练习"
                                    android:visibility="gone" />
                                <!-- AI学习助手区域 -->
                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="258dp"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginEnd="16dp"
                                    android:layout_marginBottom="10dp"
                                    android:background="@drawable/ai_bg"
                                    android:paddingHorizontal="10dp"
                                    android:paddingTop="16dp"
                                    android:paddingBottom="10dp">

                                    <TextView
                                        android:id="@+id/tv_ai_helper_title"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="0dp"
                                        android:layout_marginTop="20dp"
                                        android:text="AI学习助手"
                                        android:textColor="#333333"
                                        android:textSize="20sp"
                                        android:textStyle="bold" />


                                    <!-- 学习进度卡片 - 内嵌到AI学习助手区域 -->
                                    <LinearLayout
                                        android:id="@+id/ll_study_time"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_below="@id/tv_ai_helper_title"
                                        android:layout_alignTop="@id/tv_ai_helper_title"
                                        android:layout_marginStart="40dp"
                                        android:layout_toEndOf="@+id/tv_ai_helper_title"
                                        android:background="@drawable/rounded_white_bg_1"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal"
                                        android:paddingStart="10dp"
                                        android:paddingTop="3dp"
                                        android:paddingEnd="10dp"
                                        android:paddingBottom="3dp">

                                        <TextView
                                            android:id="@+id/tv_study_time"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="学习时长: 0分钟"
                                            android:textColor="#3163F6"
                                            android:textSize="12sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/ll_study_progress"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_below="@id/ll_study_time"
                                        android:layout_marginTop="15dp"
                                        android:orientation="horizontal">

                                        <!-- 进度环 -->
                                        <RelativeLayout
                                            android:layout_width="0dp"
                                            android:layout_height="80dp"
                                            android:layout_weight="1.3"
                                            android:gravity="center"
                                            android:orientation="vertical">

                                            <com.zizhiguanjia.model_home.view.CustomProgressView
                                                android:id="@+id/progress_circular"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                app:layout_constraintEnd_toEndOf="parent"
                                                app:layout_constraintStart_toStartOf="parent"
                                                app:layout_constraintTop_toTopOf="parent"
                                                tools:ignore="MissingClass,TooDeepLayout" />

                                            <TextView
                                                android:id="@+id/tv_num"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_alignParentBottom="true"
                                                android:layout_centerInParent="true"
                                                android:layout_marginBottom="10dp"
                                                android:text="0/0"
                                                android:textColor="#777777"
                                                android:textSize="12sp"
                                                android:textStyle="bold" />

                                        </RelativeLayout>

                                        <!-- 模考分数 -->
                                        <LinearLayout
                                            android:layout_width="0dp"
                                            android:layout_height="match_parent"
                                            android:layout_weight="1"
                                            android:gravity="center"
                                            android:orientation="vertical">

                                            <TextView
                                                android:id="@+id/tv_exam_score"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="0"
                                                android:textColor="#333333"
                                                android:textSize="24sp"
                                                android:textStyle="bold" />

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="最新模考"
                                                android:textColor="#666666"
                                                android:textSize="12sp" />
                                        </LinearLayout>

                                        <!-- 知识掌握度 -->
                                        <LinearLayout
                                            android:layout_width="0dp"
                                            android:layout_height="match_parent"
                                            android:layout_weight="1"
                                            android:gravity="center"
                                            android:orientation="vertical">

                                            <LinearLayout
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:gravity="center"
                                                android:orientation="horizontal">

                                                <TextView
                                                    android:id="@+id/tv_knowledge_mastery"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:text="0"
                                                    android:textColor="#333333"
                                                    android:textSize="24sp"
                                                    android:textStyle="bold" />

                                                <TextView
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:text="%"
                                                    android:textColor="#333333"
                                                    android:textSize="16sp"
                                                    android:textStyle="bold" />
                                            </LinearLayout>

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="知识掌握度"
                                                android:textColor="#666666"
                                                android:textSize="12sp" />
                                        </LinearLayout>

                                        <!-- 坚持学习 -->
                                        <LinearLayout
                                            android:layout_width="0dp"
                                            android:layout_height="match_parent"
                                            android:layout_weight="1"
                                            android:gravity="center"
                                            android:orientation="vertical">

                                            <LinearLayout
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:gravity="center"
                                                android:orientation="horizontal">

                                                <TextView
                                                    android:id="@+id/tv_study_days"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:text="0天"
                                                    android:textColor="#333333"
                                                    android:textSize="24sp"
                                                    android:textStyle="bold" />

                                                <!-- 移除重复的"天"字 -->
                                            </LinearLayout>

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="坚持学习"
                                                android:textColor="#666666"
                                                android:textSize="12sp" />

                                        </LinearLayout>
                                    </LinearLayout>

                                    <!-- 摸底测评卡片 - 内嵌到AI学习助手区域 -->
                                    <RelativeLayout
                                        android:id="@+id/ll_test_card"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_below="@id/ll_study_progress"
                                        android:layout_marginTop="3dp"
                                        android:background="@drawable/md_bg"
                                        android:padding="10dp">

                                        <ImageView
                                            android:id="@+id/iv_close_test"
                                            android:layout_width="25dp"
                                            android:layout_height="25dp"
                                            android:layout_alignParentTop="true"
                                            android:layout_alignParentEnd="true"
                                            android:background="?attr/selectableItemBackgroundBorderless"
                                            android:clickable="true"
                                            android:focusable="true"
                                            android:src="@drawable/ic_close"
                                            android:tint="#999999" />

                                        <LinearLayout
                                            android:id="@+id/ll_test_content"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:orientation="vertical">

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="摸底测评"
                                                android:textColor="#333333"
                                                android:textSize="16sp"
                                                android:textStyle="bold" />

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_marginTop="8dp"
                                                android:text="参与摸底测评 找到知识薄弱点AI\n定制学习计划 更高效更省力"
                                                android:textColor="#666666"
                                                android:textSize="12sp" />
                                        </LinearLayout>

                                        <Button
                                            android:id="@+id/btn_start_test"
                                            android:layout_width="wrap_content"
                                            android:layout_height="29dp"
                                            android:layout_below="@id/ll_test_content"
                                            android:layout_alignTop="@id/ll_test_content"
                                            android:layout_alignParentEnd="true"
                                            android:layout_marginStart="25dp"
                                            android:layout_marginTop="30dp"
                                            android:layout_marginEnd="10dp"
                                            android:layout_toEndOf="@+id/ll_test_content"
                                            android:background="@drawable/rounded_blue_button_bg"
                                            android:text="立即测评"
                                            android:textColor="#FFFFFF"
                                            android:textSize="14sp" />
                                    </RelativeLayout>

                                    <Button
                                        android:id="@+id/btn_start_practice"
                                        android:layout_width="200dp"
                                        android:layout_height="60dp"
                                        android:layout_below="@id/ll_study_progress"
                                        android:layout_centerInParent="true"
                                        android:layout_gravity="center"
                                        android:layout_marginTop="16dp"
                                        android:background="@drawable/main_start_bg"
                                        android:text="继续练习"
                                        android:textColor="#FFFFFF"
                                        android:textSize="14sp"
                                        android:visibility="gone" />
                                </RelativeLayout>

                                <!-- VIP权益模块 -->
                                <!--                        <include-->
                                <!--                            android:id="@+id/vip_rights_layout"-->
                                <!--                            layout="@layout/vip_rights_layout"-->
                                <!--                            android:layout_width="match_parent"-->
                                <!--                            android:layout_height="wrap_content"-->
                                <!--                            android:layout_marginTop="16dp" />-->

                                <!-- 功能按钮区域（两行） -->
                                <!-- 第一行功能按钮 -->
                                <HorizontalScrollView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:fillViewport="true"
                                    android:overScrollMode="never"
                                    android:scrollbars="none">

                                    <LinearLayout
                                        android:id="@+id/function_button_row1"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="8dp"
                                        android:layout_marginEnd="8dp"
                                        android:orientation="horizontal" />
                                </HorizontalScrollView>

                                <!-- 第二行功能按钮 -->
                                <LinearLayout
                                    android:id="@+id/function_button_row2"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="8dp"
                                    android:layout_marginTop="8dp"
                                    android:layout_marginEnd="8dp"
                                    android:orientation="horizontal" />

                                <!-- 功能按钮区域 -->
                                <!--                        <LinearLayout-->
                                <!--                            android:layout_width="match_parent"-->
                                <!--                            android:layout_height="wrap_content"-->
                                <!--                            android:layout_marginTop="16dp"-->
                                <!--                            android:orientation="horizontal">-->

                                <!--                            &lt;!&ndash; 高频考题 &ndash;&gt;-->
                                <!--                            <androidx.cardview.widget.CardView-->
                                <!--                                android:id="@+id/card_high_freq"-->
                                <!--                                android:layout_width="0dp"-->
                                <!--                                android:layout_height="wrap_content"-->
                                <!--                                android:layout_marginStart="16dp"-->
                                <!--                                android:layout_marginEnd="0dp"-->
                                <!--                                android:layout_weight="1"-->
                                <!--                                app:cardCornerRadius="8dp"-->
                                <!--                                app:cardElevation="2dp">-->

                                <!--                                <LinearLayout-->
                                <!--                                    android:layout_width="match_parent"-->
                                <!--                                    android:layout_height="wrap_content"-->
                                <!--                                    android:background="#6A91FF"-->
                                <!--                                    android:orientation="vertical"-->
                                <!--                                    android:padding="14dp">-->

                                <!--                                    <LinearLayout-->
                                <!--                                        android:layout_width="match_parent"-->
                                <!--                                        android:layout_height="wrap_content"-->
                                <!--                                        android:gravity="center_vertical"-->
                                <!--                                        android:orientation="horizontal">-->

                                <!--                                        <TextView-->
                                <!--                                            android:layout_width="wrap_content"-->
                                <!--                                            android:layout_height="wrap_content"-->
                                <!--                                            android:background="@drawable/rounded_light_blue_bg"-->
                                <!--                                            android:padding="4dp"-->
                                <!--                                            android:text="100"-->
                                <!--                                            android:textColor="#3163F6"-->
                                <!--                                            android:textSize="18sp"-->
                                <!--                                            android:textStyle="bold" />-->

                                <!--                                        <LinearLayout-->
                                <!--                                            android:layout_width="match_parent"-->
                                <!--                                            android:layout_height="wrap_content"-->
                                <!--                                            android:layout_marginLeft="10dp"-->
                                <!--                                            android:orientation="vertical">-->

                                <!--                                            <TextView-->
                                <!--                                                android:layout_width="wrap_content"-->
                                <!--                                                android:layout_height="wrap_content"-->
                                <!--                                                android:text="高频考题"-->
                                <!--                                                android:textColor="@color/white"-->
                                <!--                                                android:textSize="14sp"-->
                                <!--                                                android:textStyle="bold" />-->

                                <!--                                            <TextView-->
                                <!--                                                android:layout_width="match_parent"-->
                                <!--                                                android:layout_height="wrap_content"-->
                                <!--                                                android:layout_marginTop="8dp"-->
                                <!--                                                android:text="事半功倍快速提分"-->
                                <!--                                                android:textColor="@color/white"-->
                                <!--                                                android:textSize="12sp" />-->
                                <!--                                        </LinearLayout>-->

                                <!--                                    </LinearLayout>-->


                                <!--                                </LinearLayout>-->
                                <!--                            </androidx.cardview.widget.CardView>-->

                                <!--                            &lt;!&ndash; 智能模考 &ndash;&gt;-->
                                <!--                            <androidx.cardview.widget.CardView-->
                                <!--                                android:id="@+id/card_smart_exam"-->
                                <!--                                android:layout_width="0dp"-->
                                <!--                                android:layout_height="wrap_content"-->
                                <!--                                android:layout_marginStart="16dp"-->
                                <!--                                android:layout_marginEnd="16dp"-->
                                <!--                                android:layout_weight="1"-->
                                <!--                                app:cardCornerRadius="8dp"-->
                                <!--                                app:cardElevation="2dp">-->

                                <!--                                <LinearLayout-->
                                <!--                                    android:layout_width="match_parent"-->
                                <!--                                    android:layout_height="wrap_content"-->
                                <!--                                    android:background="#6A91FF"-->
                                <!--                                    android:orientation="vertical"-->
                                <!--                                    android:padding="14dp">-->

                                <!--                                    <LinearLayout-->
                                <!--                                        android:layout_width="match_parent"-->
                                <!--                                        android:layout_height="wrap_content"-->
                                <!--                                        android:gravity="center_vertical"-->
                                <!--                                        android:orientation="horizontal">-->

                                <!--                                        <TextView-->
                                <!--                                            android:layout_width="wrap_content"-->
                                <!--                                            android:layout_height="wrap_content"-->
                                <!--                                            android:background="@drawable/rounded_light_blue_bg"-->
                                <!--                                            android:padding="4dp"-->
                                <!--                                            android:text="100"-->
                                <!--                                            android:textColor="#3163F6"-->
                                <!--                                            android:textSize="18sp"-->
                                <!--                                            android:textStyle="bold" />-->

                                <!--                                        <LinearLayout-->
                                <!--                                            android:layout_width="match_parent"-->
                                <!--                                            android:layout_height="wrap_content"-->
                                <!--                                            android:layout_marginLeft="10dp"-->
                                <!--                                            android:orientation="vertical">-->

                                <!--                                            <TextView-->
                                <!--                                                android:layout_width="wrap_content"-->
                                <!--                                                android:layout_height="wrap_content"-->
                                <!--                                                android:text="智能模考"-->
                                <!--                                                android:textColor="@color/white"-->
                                <!--                                                android:textSize="14sp"-->
                                <!--                                                android:textStyle="bold" />-->

                                <!--                                            <TextView-->
                                <!--                                                android:layout_width="match_parent"-->
                                <!--                                                android:layout_height="wrap_content"-->
                                <!--                                                android:layout_marginTop="8dp"-->
                                <!--                                                android:text="按重难点智能组卷"-->
                                <!--                                                android:textColor="@color/white"-->
                                <!--                                                android:textSize="12sp" />-->
                                <!--                                        </LinearLayout>-->

                                <!--                                    </LinearLayout>-->


                                <!--                                </LinearLayout>-->
                                <!--                            </androidx.cardview.widget.CardView>-->
                                <!--                        </LinearLayout>-->

                                <!--                        &lt;!&ndash; 学习功能按钮 &ndash;&gt;-->
                                <!--                        <LinearLayout-->
                                <!--                            android:layout_width="match_parent"-->
                                <!--                            android:layout_height="wrap_content"-->
                                <!--                            android:layout_marginStart="16dp"-->
                                <!--                            android:layout_marginTop="16dp"-->
                                <!--                            android:layout_marginEnd="16dp"-->
                                <!--                            android:orientation="horizontal">-->

                                <!--                            &lt;!&ndash; 知识点练习 &ndash;&gt;-->
                                <!--                            <LinearLayout-->
                                <!--                                android:id="@+id/layout_knowledge"-->
                                <!--                                android:layout_width="0dp"-->
                                <!--                                android:layout_height="match_parent"-->
                                <!--                                android:layout_marginRight="3dp"-->
                                <!--                                android:layout_weight="1"-->
                                <!--                                android:background="@drawable/shape_white"-->
                                <!--                                android:gravity="center"-->
                                <!--                                android:orientation="vertical"-->
                                <!--                                android:padding="10dp">-->

                                <!--                                <ImageView-->
                                <!--                                    android:layout_width="25dp"-->
                                <!--                                    android:layout_height="25dp"-->
                                <!--                                    android:background="@drawable/circle_yellow_bg"-->
                                <!--                                    android:padding="12dp"-->
                                <!--                                    android:src="@drawable/ic_knowledge" />-->

                                <!--                                <TextView-->
                                <!--                                    android:layout_width="wrap_content"-->
                                <!--                                    android:layout_height="wrap_content"-->
                                <!--                                    android:layout_marginTop="4dp"-->
                                <!--                                    android:text="知识点练习"-->
                                <!--                                    android:textColor="#333333"-->
                                <!--                                    android:textSize="12sp" />-->
                                <!--                            </LinearLayout>-->

                                <!--                            &lt;!&ndash; 题型练习 &ndash;&gt;-->
                                <!--                            <LinearLayout-->
                                <!--                                android:id="@+id/layout_question_type"-->
                                <!--                                android:layout_width="0dp"-->
                                <!--                                android:layout_height="match_parent"-->
                                <!--                                android:layout_margin="3dp"-->
                                <!--                                android:layout_weight="1"-->
                                <!--                                android:background="@drawable/shape_white"-->
                                <!--                                android:gravity="center"-->
                                <!--                                android:orientation="vertical"-->
                                <!--                                android:padding="5dp">-->

                                <!--                                <ImageView-->
                                <!--                                    android:layout_width="22dp"-->
                                <!--                                    android:layout_height="22dp"-->
                                <!--                                    android:background="@drawable/circle_yellow_bg"-->
                                <!--                                    android:padding="12dp"-->
                                <!--                                    android:src="@drawable/ic_knowledge" />-->

                                <!--                                <TextView-->
                                <!--                                    android:layout_width="wrap_content"-->
                                <!--                                    android:layout_height="wrap_content"-->
                                <!--                                    android:layout_marginTop="4dp"-->
                                <!--                                    android:text="题型练习"-->
                                <!--                                    android:textColor="#333333"-->
                                <!--                                    android:textSize="12sp" />-->
                                <!--                            </LinearLayout>-->
                                <!--                            &lt;!&ndash; 易错100题 &ndash;&gt;-->
                                <!--                            <LinearLayout-->
                                <!--                                android:id="@+id/layout_error_prone"-->
                                <!--                                android:layout_width="0dp"-->
                                <!--                                android:layout_height="match_parent"-->
                                <!--                                android:layout_margin="3dp"-->
                                <!--                                android:layout_weight="1"-->
                                <!--                                android:background="@drawable/shape_white"-->
                                <!--                                android:gravity="center"-->
                                <!--                                android:orientation="vertical">-->

                                <!--                                <ImageView-->
                                <!--                                    android:layout_width="22dp"-->
                                <!--                                    android:layout_height="22dp"-->
                                <!--                                    android:background="@drawable/circle_yellow_bg"-->
                                <!--                                    android:padding="12dp"-->
                                <!--                                    android:src="@drawable/ic_knowledge" />-->

                                <!--                                <TextView-->
                                <!--                                    android:layout_width="wrap_content"-->
                                <!--                                    android:layout_height="wrap_content"-->
                                <!--                                    android:layout_marginTop="4dp"-->
                                <!--                                    android:text="易错100题"-->
                                <!--                                    android:textColor="#333333"-->
                                <!--                                    android:textSize="12sp" />-->
                                <!--                            </LinearLayout>-->

                                <!--                            &lt;!&ndash; 通关精讲课 &ndash;&gt;-->
                                <!--                            <LinearLayout-->
                                <!--                                android:id="@+id/layout_lecture"-->
                                <!--                                android:layout_width="0dp"-->
                                <!--                                android:layout_height="match_parent"-->
                                <!--                                android:layout_marginLeft="3dp"-->
                                <!--                                android:layout_weight="1"-->
                                <!--                                android:background="@drawable/shape_white"-->
                                <!--                                android:gravity="center"-->
                                <!--                                android:orientation="vertical">-->

                                <!--                                <ImageView-->
                                <!--                                    android:layout_width="22dp"-->
                                <!--                                    android:layout_height="22dp"-->
                                <!--                                    android:background="@drawable/circle_yellow_bg"-->
                                <!--                                    android:padding="12dp"-->
                                <!--                                    android:src="@drawable/ic_knowledge" />-->

                                <!--                                <TextView-->
                                <!--                                    android:layout_width="wrap_content"-->
                                <!--                                    android:layout_height="wrap_content"-->
                                <!--                                    android:layout_marginTop="4dp"-->
                                <!--                                    android:text="通关精讲课"-->
                                <!--                                    android:textColor="#333333"-->
                                <!--                                    android:textSize="12sp" />-->
                                <!--                            </LinearLayout>-->
                                <!--                        </LinearLayout>-->

                                <!-- VIP权益区域 -->
                                <androidx.cardview.widget.CardView
                                    android:id="@+id/vip_card_view"
                                    android:layout_width="match_parent"
                                    android:layout_height="120dp"
                                    android:layout_marginStart="16dp"
                                    android:layout_marginTop="16dp"
                                    android:layout_marginEnd="16dp"
                                    app:cardCornerRadius="8dp"
                                    app:cardElevation="2dp">

                                    <LinearLayout
                                        android:background="@drawable/vip_bg"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="vertical"
                                        android:padding="10dp">

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="top"
                                            android:orientation="horizontal">

                                            <ImageView
                                                android:visibility="gone"
                                                android:layout_width="24dp"
                                                android:layout_height="24dp"
                                                android:src="@drawable/ic_hg"
                                                android:tint="#FFD700" />

                                            <TextView
                                                android:layout_width="0dp"
                                                android:layout_height="wrap_content"
                                                android:layout_marginStart="8dp"
                                                android:layout_weight="1"
                                                android:text=""
                                                android:textColor="@android:color/white"
                                                android:textSize="15sp"
                                                android:textStyle="bold" />

                                            <Button
                                                android:id="@+id/btn_become_vip"
                                                android:layout_width="wrap_content"
                                                android:layout_height="28dp"
                                                android:background="@drawable/thumbnail_3"
                                                android:text="成为VIP"
                                                android:textColor="#6F4E18"
                                                android:textSize="12sp"
                                                android:textStyle="bold" />
                                        </LinearLayout>

                                        <!--                                <TextView-->
                                        <!--                                    android:layout_width="match_parent"-->
                                        <!--                                    android:layout_height="wrap_content"-->
                                        <!--                                    android:layout_marginTop="12dp"-->
                                        <!--                                    android:text="@{`试用权益：您可试用 ` + String.valueOf(studyModel.trialQuestions) + ` 道题 ` + String.valueOf(studyModel.trialPapers) + ` 套试卷`}"-->
                                        <!--                                    android:textColor="#E0E0E0"-->
                                        <!--                                    android:textSize="14sp" />-->

                                        <!-- 进度条区域 -->
                                        <RelativeLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="8dp">

                                            <ProgressBar
                                                android:id="@+id/progress_vip_usage"
                                                style="@style/CustomProgressBar"
                                                android:layout_width="match_parent"
                                                android:layout_height="35dp"
                                                android:layout_centerVertical="true"
                                                android:max="100"
                                                android:progress="@{studyModel.vipUsagePercent}"
                                                android:progressDrawable="@drawable/vip_progress_bar" />

                                            <TextView
                                                android:id="@+id/tv_des"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_centerVertical="true"
                                                android:layout_marginLeft="10dp"
                                                android:layout_marginTop="4dp"
                                                android:textColor="#E0E0E0"
                                                android:textSize="12sp" />
                                        </RelativeLayout>

                                        <LinearLayout
                                            android:visibility="gone"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="12dp"
                                            android:layout_marginBottom="4dp"
                                            android:orientation="horizontal">

                                            <TextView
                                                android:layout_width="0dp"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1.1"
                                                android:gravity="center"
                                                android:text="全部题库都能刷"
                                                android:textColor="@android:color/white"
                                                android:textSize="12sp" />

                                            <TextView
                                                android:layout_width="0dp"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:gravity="center"
                                                android:text="AI学习助手"
                                                android:textColor="@android:color/white"
                                                android:textSize="12sp" />

                                            <TextView
                                                android:layout_width="0dp"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:gravity="center"
                                                android:text="高频考题"
                                                android:textColor="@android:color/white"
                                                android:textSize="12sp" />

                                            <TextView
                                                android:layout_width="0dp"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:gravity="center"
                                                android:text="智能模考"
                                                android:textColor="@android:color/white"
                                                android:textSize="12sp" />
                                        </LinearLayout>
                                    </LinearLayout>
                                </androidx.cardview.widget.CardView>

                                <!-- 通关精讲课模块 -->
                                <LinearLayout
                                    android:id="@+id/live_course_container"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="24dp"
                                    android:orientation="vertical"
                                    android:visibility="gone">

                                    <RelativeLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="16dp"
                                        android:layout_marginEnd="16dp">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="通关精讲课"
                                            android:textColor="#333333"
                                            android:textSize="16sp"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:id="@+id/tv_more"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_alignParentEnd="true"
                                            android:layout_centerVertical="true"
                                            android:text="查看更多 >"
                                            android:textColor="#999999"
                                            android:textSize="12sp" />
                                    </RelativeLayout>

                                    <!-- 视频课程列表 -->
                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycler_live_courses"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="12dp"
                                        android:clipToPadding="false"
                                        android:orientation="horizontal"
                                        android:paddingStart="12dp"
                                        android:paddingEnd="12dp" />
                                </LinearLayout>

                                <!-- 底部信息 -->
                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="24dp"
                                    android:gravity="center"
                                    android:text="考安管人员ABC证，用安全员考试宝典"
                                    android:textColor="#333333"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <LinearLayout
                                    android:layout_margin="10dp"
                                    android:gravity="center_vertical"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <View
                                        android:background="#50808080"
                                        android:layout_width="0dp"
                                        android:layout_height="0.5dp"
                                        android:layout_weight="1" />

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="0.5"
                                        android:gravity="center"
                                        android:text="其他功能"
                                        android:textColor="#999999"
                                        android:textSize="12sp" />

                                    <View
                                        android:background="#50808080"
                                        android:layout_width="0dp"
                                        android:layout_height="0.5dp"
                                        android:layout_weight="1" />


                                </LinearLayout>
                                <!-- 底部功能按钮 -->
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="16dp"
                                    android:layout_marginTop="16dp"
                                    android:layout_marginEnd="16dp"
                                    android:layout_marginBottom="16dp"
                                    android:orientation="horizontal">

                                    <!-- 微信好友 -->
                                    <LinearLayout
                                        android:id="@+id/layout_wechat"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:layout_width="35dp"
                                            android:layout_height="35dp"
                                            android:src="@drawable/wx" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="4dp"
                                            android:text="微信好友"
                                            android:textColor="#333333"
                                            android:textSize="12sp" />
                                    </LinearLayout>

                                    <!-- 朋友圈 -->
                                    <LinearLayout
                                        android:id="@+id/layout_moments"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:layout_width="35dp"
                                            android:layout_height="35dp"
                                            android:src="@drawable/pyq" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="4dp"
                                            android:text="朋友圈"
                                            android:textColor="#333333"
                                            android:textSize="12sp" />
                                    </LinearLayout>

                                    <!-- 拨打电话 -->
                                    <LinearLayout
                                        android:id="@+id/layout_phone"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:layout_width="35dp"
                                            android:layout_height="35dp"
                                            android:src="@drawable/phone" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="4dp"
                                            android:text="拨打电话"
                                            android:textColor="#333333"
                                            android:textSize="12sp" />
                                    </LinearLayout>

                                    <!-- 在线客服 -->
                                    <LinearLayout
                                        android:id="@+id/layout_service"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:layout_width="35dp"
                                            android:layout_height="35dp"
                                            android:src="@drawable/call" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="4dp"
                                            android:text="在线客服"
                                            android:textColor="#333333"
                                            android:textSize="12sp" />
                                    </LinearLayout>
                                </LinearLayout>
                            </LinearLayout>
                        </ScrollView>
                    </LinearLayout>
                </androidx.core.widget.NestedScrollView>
            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
        </LinearLayout>

    </LinearLayout>
</layout>