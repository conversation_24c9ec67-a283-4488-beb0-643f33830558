<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_button_1"
        android:orientation="horizontal"
        android:padding="10dp"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/icon_image_view"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:padding="6dp"
            android:contentDescription="@string/app_name" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@android:color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/subtitle_text_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="事半功倍快速提分"
                android:textColor="@android:color/white"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 