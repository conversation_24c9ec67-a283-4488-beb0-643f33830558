package com.zizhiguanjia.model_home.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.webkit.WebSettings;
import android.webkit.WebView;

import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.model_home.R;

/**
 * 功能作用：课程详情-商品信息fragment
 * 初始注释时间： 2023/11/23 10:47
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class CourseDetailGoodsFragment extends BaseFragment {

    public void setUrl(String url) {
        ((WebView) this.findViewById(R.id.web_view)).loadUrl(url);

    }

    @Override
    public int initLayoutResId() {
        return R.layout.home_couse_detail_goods_layout;
    }


    @SuppressLint("SetJavaScriptEnabled")
    @Override
    public void initView(Bundle savedInstanceState) {
        WebView webView = (WebView) this.findViewById(R.id.web_view);
        webView.getSettings().setBuiltInZoomControls(false);
        webView.getSettings().setJavaScriptEnabled(true);  //支持js
        webView.getSettings().setUseWideViewPort(true);  //将图片调整到适合webview的大小
        webView.getSettings().setLoadWithOverviewMode(true); // 缩放至屏幕的大小
        webView.getSettings().setSupportZoom(false);  //支持缩放，默认为true。是下面那个的前提。
        webView.getSettings().setLayoutAlgorithm(WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING); //支持内容重新布局
        webView.getSettings().setAllowFileAccess(true);  //设置可以访问文件
        webView.getSettings().setLoadsImagesAutomatically(true);  //支持自动加载图片
        webView.getSettings().setDefaultTextEncodingName("utf-8");//设置编码格式
        webView.getSettings().setDomStorageEnabled(true);
        webView.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);
        webView.getSettings().setAppCacheEnabled(true);
        webView.getSettings().setJavaScriptCanOpenWindowsAutomatically(true);
        webView.getSettings().setSupportMultipleWindows(true);
    }

    @Override
    public void initViewData() {
        super.initViewData();
    }

    @Override
    public void initObservable() {
        super.initObservable();
    }
}
