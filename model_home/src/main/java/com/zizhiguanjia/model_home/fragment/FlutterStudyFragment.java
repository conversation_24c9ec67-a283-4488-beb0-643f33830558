package com.zizhiguanjia.model_home.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.lib_common.arouter.ARouterUtils;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.lib_base.helper.AddressHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.ListHelper;
import com.zizhiguanjia.lib_base.helper.VideoHelper;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.model_home.bean.MenuNavsBean;
import com.zizhiguanjia.model_home.config.HomeFlutterChannerTpis;
import com.zizhiguanjia.model_home.listener.IStudyButtonClickListener;
import com.zizhiguanjia.model_home.viewmodel.HomeFlutterViewModel;
import com.wb.lib_arch.base.IFragment;

import java.util.HashMap;
import java.util.Map;

public class FlutterStudyFragment extends Fragment {

    private HomeFlutterViewModel viewModel;
    private IStudyButtonClickListener buttonClickListener;
    private Bundle fragmentArguments = new Bundle();
    private HomeFragment parentHomeFragment; // 父Fragment引用

    public FlutterStudyFragment() {
        // 必需的空构造函数
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // 布局inflate由外部完成，这里不需要实际实现
        return null;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 初始化可能会在这里完成，但具体实现由外部控制
    }

    /**
     * 设置ViewModel引用
     * @param viewModel 来自HomeFragment的ViewModel
     */
    public void setViewModel(HomeFlutterViewModel viewModel) {
        this.viewModel = viewModel;
    }

    /**
     * 设置按钮点击监听器
     * @param listener 按钮点击回调接口
     */
    public void setButtonClickListener(IStudyButtonClickListener listener) {
        this.buttonClickListener = listener;
    }
    
    /**
     * 设置父HomeFragment引用
     * @param homeFragment 父HomeFragment实例
     */
    public void setParentHomeFragment(HomeFragment homeFragment) {
        this.parentHomeFragment = homeFragment;
    }

    /**
     * 处理功能区按钮点击事件
     * @param bean 按钮相关数据
     */
    public void handleFunctionClick(MenuNavsBean bean) {
        if (bean == null || viewModel == null) return;
        
        // 通知回调接口（如果存在）
        if (buttonClickListener != null) {
            buttonClickListener.onButtonClick(bean);
        }

        // 处理不同类型按钮的跳转逻辑
        switch (bean.getNavType()) {
            case 1: // 错题集
                handleErrorListPage();
                break;
            case 2: // 收藏夹
                handleSaveListPage();
                break;
            case 3: // 题型练习
                handleTxlxListPage();
                break;
            case 4: // 模拟考试
            case 10:
            case 11:
                handleMnksListPage(bean);
                break;
            case 5: // 历年真题
                viewModel.goToLnzt();
                break;
            case 6: // 二级模拟考试
                viewModel.goToEjMnks();
                break;
            case 16: // 易错100题
                handleErrorHundredPage();
                break;
            case 17: // 学习资料
                handleStudyMaterialsPage();
                break;
            case 20: // 直播
                if (bean.getUrl() != null && !bean.getUrl().isEmpty()) {
                    handleLivePage();
                }
                break;
        }
    }

    private void handleErrorListPage() {
        if (checkLoginAndAddress()) {
            fragmentArguments.putString("flutterRoute", "error");
            Fragment fragment = new HomeFragment();
            fragment.setArguments(fragmentArguments);
            startFragmentDirectly(fragment);
        }
    }

    private void handleSaveListPage() {
        if (checkLoginAndAddress()) {
            fragmentArguments.putString("flutterRoute", "save");
            Fragment fragment = new HomeFragment();
            fragment.setArguments(fragmentArguments);
            startFragmentDirectly(fragment);
        }
    }

    private void handleTxlxListPage() {
        if (checkLoginAndAddress()) {
            fragmentArguments.putInt("index", 2);
            fragmentArguments.putBoolean("chapter", false);
            if (parentHomeFragment != null) {
                // 使用父HomeFragment的参数和方法
                updateParentArguments();
                parentHomeFragment.startFragment(ListHelper.toPageExamList());
            }
        }
    }

    private void handleMnksListPage(MenuNavsBean bean) {
        if (checkLoginAndAddress()) {
            boolean isDialog = bean.getDateBoxStatus() != 1;
            viewModel.goToAutoExam(true, isDialog, String.valueOf(bean.getNavType()));
        }
    }

    private void handleErrorHundredPage() {
        if (!NoDoubleClickUtils.isDoubleClick() && checkLoginAndAddress()) {
            fragmentArguments.putString("title", "易错100题");
            fragmentArguments.putBoolean("restart", false);
            fragmentArguments.putString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG));
            if (parentHomeFragment != null) {
                updateParentArguments();
                parentHomeFragment.startFragment(ListHelper.toPageExamDes());
            }
        }
    }

    private void handleStudyMaterialsPage() {
        if (!NoDoubleClickUtils.isDoubleClick() && checkLoginAndAddress()) {
            if (parentHomeFragment != null) {
                updateParentArguments();
                parentHomeFragment.startFragment(ListHelper.toPageDocMajors());
            }
        }
    }

    private void handleLivePage() {
        if (checkLoginAndAddress()) {
            // 使用正确的常量
            PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_START, true);
            viewModel.goToMore(false);
        }
    }

    private boolean checkLoginAndAddress() {
        if (viewModel != null) {
            // 直接调用viewModel的方法检查登录和地址状态
            return viewModel.checkLoginOrAddress(true, BaseConfig.ORDER_BIND_TYPE);
        }
        return false;
    }

    /**
     * 将当前fragment参数同步到父HomeFragment
     */
    private void updateParentArguments() {
        if (parentHomeFragment != null && fragmentArguments != null) {
            Bundle parentArgs = parentHomeFragment.initArguments();
            if (parentArgs != null) {
                for (String key : fragmentArguments.keySet()) {
                    parentArgs.putAll(fragmentArguments);
                }
            }
        }
    }

    private void startFragmentDirectly(Fragment fragment) {
        if (getActivity() != null) {
            getActivity().getSupportFragmentManager()
                .beginTransaction()
                .replace(android.R.id.content, fragment)
                .addToBackStack(null)
                .commit();
        }
    }
    
    /**
     * 获取当前Fragment的参数
     */
    public Bundle getFragmentArguments() {
        return fragmentArguments;
    }
    
    /**
     * 设置Fragment参数
     */
    public void setFragmentArguments(Bundle args) {
        if (args != null) {
            fragmentArguments.putAll(args);
        }
    }
} 