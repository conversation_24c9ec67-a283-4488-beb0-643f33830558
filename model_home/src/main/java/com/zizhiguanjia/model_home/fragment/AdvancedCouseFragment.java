package com.zizhiguanjia.model_home.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.viewpager.widget.ViewPager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kevin.slidingtab.SlidingTabLayout;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.adapter.ViewPageAdapter;
@Route(path = HomeRoutherPath.AVD_FRAGMENT)
public class AdvancedCouseFragment extends BaseFragment {
    private SlidingTabLayout slidingTabLayout;
    private ViewPager mViewPager;
    private ImageView img1,img2;
    @Override
    public int initLayoutResId() {
        return R.layout.home_advance_couse_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        slidingTabLayout=this.findViewById(R.id.slidTl);
        mViewPager=this.findViewById(R.id.advVp);
        img1=this.findViewById(R.id.image1);
        img2=this.findViewById(R.id.image2);
        ViewPageAdapter viewPageAdapter=new ViewPageAdapter(getActivity().getSupportFragmentManager());
        mViewPager.setAdapter(viewPageAdapter);
        slidingTabLayout.setupWithViewPager(mViewPager);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if(position==0){
                    img1.setVisibility(View.VISIBLE);
                    img2.setVisibility(View.GONE);
                }else {
                    img1.setVisibility(View.GONE);
                    img2.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }
    private String[] getTitle(){
        String[] str=new String[2];
        str[0]="你猜1";
        str[1]="你猜2";
        return str;
    }
    @Override
    public void initViewData() {
        super.initViewData();
    }

    @Override
    public void initObservable() {
        super.initObservable();
    }
}
