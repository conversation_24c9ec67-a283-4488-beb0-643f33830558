package com.zizhiguanjia.model_home.fragment;

import android.annotation.SuppressLint;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.kevin.slidingtab.SlidingTabLayout;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.PayHelper;
import com.zizhiguanjia.lib_base.view.ScrollSetViewPager;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.event.CourseDetailNextEvent;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.CourseDetailCatalogueBean;
import com.zizhiguanjia.model_home.bean.CourseDetailGoodsBean;
import com.zizhiguanjia.model_home.dialog.CourseDetailPlayViewSpeedDialog;
import com.zizhiguanjia.model_home.navigator.CourseDetailNavigator;
import com.zizhiguanjia.model_home.view.CourseDetailPlayView;
import com.zizhiguanjia.model_home.viewmodel.CourseDetailViewModel;

import java.math.BigDecimal;
import java.util.Objects;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;

/**
 * 功能作用：课程详情页面
 * 初始注释时间： 2023/11/22 15:18
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
@Route(path = HomeRoutherPath.COURSE_DETAIL)
public class CourseDetailFragment extends BaseFragment implements CourseDetailNavigator {
    private SlidingTabLayout slidingTabLayout;
    private ScrollSetViewPager mViewPager;
    private CourseDetailPlayView mJzVideo;
    private ConstraintLayout mClBuy;
    private AppCompatTextView mTvPrice;
    @BindViewModel
    CourseDetailViewModel mViewModel;
    private final CourseDetailCatalogueFragment detailCatalogueFragment = new CourseDetailCatalogueFragment(this);
    private final CourseDetailGoodsFragment mCourseDetailGoodsFragment = new CourseDetailGoodsFragment();

    /**
     * 图片上传进度
     */
    private BasePopupView mLoadingView;

    /**
     * 标题栏
     */
    private TitleBar mTitleBar;

    /**
     * 适配器
     */
    private FragmentPagerAdapter mPagerAdapter;

    @Override
    public int initLayoutResId() {
        return R.layout.home_couse_detail_layout;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void initView(Bundle savedInstanceState) {
        //加载中
        mLoadingView = new PopupManager.Builder(getContext()).asLoading("", R.layout.popup_center_impl_loading);
        //数据处理
        mViewModel.initParams(this, this);
        slidingTabLayout = this.findViewById(R.id.slidTl);
        mViewPager = this.findViewById(R.id.advVp);
        mJzVideo = this.findViewById(R.id.jz_video);
        mClBuy = this.findViewById(R.id.cl_buy);
        mTvPrice = this.findViewById(R.id.tv_price);
        mTitleBar = this.findViewById(R.id.tbflutter);
        slidingTabLayout.setupWithViewPager(mViewPager);

        //轮播列表
        mPagerAdapter = new FragmentPagerAdapter(getActivity().getSupportFragmentManager()) {
            @NonNull
            @Override
            public Fragment getItem(int position) {
                if (position == 0) {
                    return mCourseDetailGoodsFragment;
                } else {
                    return detailCatalogueFragment;
                }
            }

            @Override
            public CharSequence getPageTitle(int position) {
                if (position == 0) {
                    return "简介";
                } else {
                    return "目录";
                }
            }

            @Override
            public int getCount() {
                return 2;
            }
        };
        mViewPager.setAdapter(mPagerAdapter);
        mViewPager.setOffscreenPageLimit(2);
        mViewPager.setCurrentItem(1);
        //标题栏左侧按钮
        mTitleBar.getLeftImageButton().setImageTintList(ColorStateList.valueOf(Color.BLACK));
        mTitleBar.getLeftImageButton().setOnClickListener(v -> finish());

        mJzVideo.setOnStateChangeInterface(new CourseDetailPlayView.OnStateChangeInterface() {
            private final CourseDetailPlayViewSpeedDialog courseDetailPlayViewSpeedDialog = new CourseDetailPlayViewSpeedDialog(mJzVideo.getSpeeds(),
                    mJzVideo.getCurrentSpeedIndex(), this);

            /**
             * 修改播放速度
             *
             * @param j     下标
             * @param speed 速度
             */
            @Override
            public void changeSpeedToValue(int j, float speed) {
                mJzVideo.setSpeed(j);
            }

            /**
             * 上传播放进度
             *
             * @param item     当前播放实例
             * @param millTime 时间戳
             */
            @Override
            public void uploadPlayProgress(CourseDetailCatalogueBean.Item item, long millTime) {
                mViewModel.submitVideoDuration(item, millTime);
            }

            /**
             * 视频是自动播放完成
             */
            @Override
            public void onAutoComplete() {
                //上传播放记录
                CourseDetailFragment.this.uploadPlayProgress();
            }

            /**
             * 倍速点击
             */
            @Override
            public void onSpeedClick() {
                courseDetailPlayViewSpeedDialog.setSpeeds(mJzVideo.getCurrentSpeedIndex());
                courseDetailPlayViewSpeedDialog.show(requireFragmentManager());
            }

            /**
             * 打开声音
             */
            @Override
            public void onVolumeOpen() {
            }

            /**
             * 声音关闭
             */
            @Override
            public void onVolumeClose() {
            }
        });
    }

    @Override
    public void initViewData() {
        super.initViewData();
    }

    @Override
    public void initObservable() {
        super.initObservable();
        //购买按钮点击
        mClBuy.setOnClickListener(v -> PayHelper.payOrder(mViewModel.getmCourseDetailGoodsBean().getGoods().getGoodsId(), getActivity(), "23"));
        //监听下一节
        LiveEventBus.get(CourseDetailNextEvent.class).observe(this, courseDetailNextEvent -> {
            CourseDetailCatalogueBean.Item bean = mViewModel.getNextShowData(courseDetailNextEvent.getCode());
            setCurrentShowData(bean, true);
        });
    }

    /**
     * 获取当前视频播放进度
     *
     * @return 播放进度，毫秒值
     */
    @Override
    public Long getCurrentPlayProgress() {
        return mJzVideo.getCurrentPositionWhenPlaying();
    }

    @Override
    public void setShowData(CourseDetailCatalogueBean bean) {
        if (bean.getVip()) {
            slidingTabLayout.setVisibility(View.GONE);
        } else {
            slidingTabLayout.setVisibility(View.VISIBLE);
        }
        mCourseDetailGoodsFragment.setUrl(bean.getDesc());
        detailCatalogueFragment.setInfo(bean);
        if (bean.getVip()) {
            mViewPager.setCurrentItem(1);
        }
        mViewPager.setNoScroll(bean.getVip());
        //设置需要播放的数据
        if (mJzVideo.getCurrentBean() == null) {
            CourseDetailCatalogueBean.Item needPlayChild = mViewModel.getNeedPlayChild();
            setCurrentShowData(needPlayChild, false);
        }
        //购买按钮处理
        if (bean.getVip()) {
            mClBuy.setVisibility(View.GONE);
        } else {
            mClBuy.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 设置商品信息
     *
     * @param data 商品信息
     */
    @SuppressLint("SetTextI18n")
    @Override
    public void setShowData(CourseDetailGoodsBean data) {
        if (data != null && data.getGoods() != null && data.getGoods().getPrice() != null) {
            mTvPrice.setText(BigDecimal.valueOf(Double.parseDouble(data.getGoods().getPrice())).divide(BigDecimal.valueOf(100F)).setScale(2)
                    .toString());
        } else {
            mTvPrice.setText("");
        }
    }

    /**
     * 设置当前要播放的数据
     *
     * @param bean           当前要播放的数据
     * @param reloadBaseData 是否重新加载数据
     */
    public void setCurrentShowData(CourseDetailCatalogueBean.Item bean, boolean reloadBaseData) {
        //先提交上一次的
        if (mJzVideo.getCurrentBean() != null && !Objects.equals(bean.getVIdeoId(), mJzVideo.getCurrentBean().getVIdeoId())) {
            //上传播放记录
            uploadPlayProgress();
        }
        if (bean != null) {
            //设置给当前播放的
            mJzVideo.setCurrentShowData(mViewModel.getDetailCatalogueBean(), bean, v -> openQuestionPage(mJzVideo.getCurrentBean()), v -> {
                mViewModel.submitVideoDuration(mJzVideo.getCurrentBean(), mJzVideo.getCurrentPositionWhenPlaying());
                setCurrentShowData(mViewModel.getNextShowData(bean), true);
            });
            //设置给子控件
            detailCatalogueFragment.setCurrent(bean);
        }
        if (reloadBaseData) {
            mViewModel.loadCurrentShowData(bean);
        }
    }

    /**
     * 打开答题页面
     */
    @Override
    public void openQuestionPage(CourseDetailCatalogueBean.Item childBean) {
        initArguments().putString("title", "本节习题");
        initArguments().putBoolean("restart", false);
        initArguments().putString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_COURSE_DETAIL_QUESTION));
        initArguments().putString("paperId", childBean.getCode());
        //已完成直接跳转结果页
        if (childBean.getComplete() && 0 == childBean.getUnDoQuestionCount()) {
            startFragment(CoreExamHelper.courseDetailChildFinish(getActivity()));
        } else {
            startFragment(CoreExamHelper.mainPage(getActivity()));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        mJzVideo.resumeVideo();
        mViewModel.getHttpData();
    }

    public void onPause() {
        super.onPause();
        mJzVideo.pauseVideo();
        //上传播放记录
        uploadPlayProgress();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mJzVideo.releaseAllVideos();
    }

    @Override
    public void showLoading(boolean isVision) {
        if (isVision) {
            if (!mLoadingView.isShow()) {
                mLoadingView.show();
            }
        } else {
            if (mLoadingView.isShow()) {
                mLoadingView.dismiss();
            }
        }
    }

    /**
     * 上传播放进度
     */
    @Override
    public void uploadPlayProgress() {
        mJzVideo.uploadPlayProgress();
    }
}
