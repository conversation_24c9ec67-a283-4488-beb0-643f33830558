package com.zizhiguanjia.model_home.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.ListHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.tb.TableUserInfo;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.zizhiguanjia.model_core.fragment.BaselineAssessmentFragment;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.api.HomeSeriveApi;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.bean.HomeShaperBean;
import com.zizhiguanjia.model_home.bean.MenuNavsBean;
import com.zizhiguanjia.model_home.bean.MiddleAdsBean;
import com.zizhiguanjia.model_home.databinding.HomeNativeLayoutBinding;
import com.zizhiguanjia.model_home.listener.INativeStudyFragment;
import com.zizhiguanjia.model_home.ui.HomeNativeActivity;
import com.zizhiguanjia.model_home.viewmodel.HomeViewModel;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import io.reactivex.Observable;

/**
 * 原生学习页面Fragment
 * 负责显示原生Android学习界面
 */
public class NativeStudyFragment extends BaseFragment {
    private HomeNativeLayoutBinding binding;
    private INativeStudyFragment listener;
    private SwipeRefreshLayout swipeRefreshLayout;
    private HomeSeriveApi mApi;

    // 摸底测评模块
    private RelativeLayout evaluationLayout;
    private ImageView ivCloseTest;

    // 功能按钮区域
    private LinearLayout firstRowContainer; // 第一排功能按钮容器
    private LinearLayout secondRowContainer; // 第二排功能按钮容器

    // VIP权益模块相关控件
    private View vipRightsLayout;
    private TextView tvVipRightsTitle;
    private TextView tvVipRightsSubtitle;
    private Button btnVipRightsAction;

    // 底部分享功能按钮相关控件
    private LinearLayout bottomFunctionsContainer; // 底部分享功能按钮容器

    // 存储首页数据
    private HomeBean homeData;

    @BindViewModel
    StudyViewModel studyViewModel;

    // 添加HomeViewModel用于获取真实首页数据
    @BindViewModel
    HomeViewModel homeViewModel;

    public static NativeStudyFragment newInstance() {
        NativeStudyFragment fragment = new NativeStudyFragment();
        return fragment;
    }

    @Override
    public int initLayoutResId() {
        return R.layout.home_native_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();

        // 初始化API
        mApi = new Http().create(HomeSeriveApi.class);

        // 初始化StudyViewModel
        if (studyViewModel == null) {
            studyViewModel = new StudyViewModel();
        }

        // 初始化ViewModel数据，确保AI学习助手模块有默认值显示
        studyViewModel.initData();

        binding.setStudyModel(studyViewModel);

        // 初始化HomeViewModel
        if (homeViewModel == null) {
            homeViewModel = new HomeViewModel();
            homeViewModel.init((HomeNativeActivity) getActivity(), (HomeNativeActivity) getActivity());
        }

        // 设置Activity引用，用于处理页面跳转
        if (studyViewModel != null) {
            studyViewModel.setActivity(getActivity());
            // 仅设置Activity引用，不进行其他初始化，避免可能的循环依赖
            LogUtils.e("NativeStudyFragment - initView - StudyViewModel活动引用设置完成");
        }

        // 初始化下拉刷新
        setupSwipeRefresh();

        // 为include布局中的head_layout设置model绑定
        View homeHead = binding.getRoot().findViewById(R.id.home_head);
        if (homeHead != null) {
            Object headBinding = androidx.databinding.DataBindingUtil.getBinding(homeHead);
            if (headBinding != null && headBinding instanceof com.zizhiguanjia.model_home.databinding.HomeHeadLayoutBinding) {
                com.zizhiguanjia.model_home.databinding.HomeHeadLayoutBinding headLayoutBinding =
                        (com.zizhiguanjia.model_home.databinding.HomeHeadLayoutBinding) headBinding;
                headLayoutBinding.setModel(studyViewModel);
                LogUtils.e("为顶部布局设置ViewModel绑定成功");
            } else {
                LogUtils.e("获取顶部布局的绑定对象失败");
            }
        }

        // 初始化顶部通知条
        initNotifyBar();

        // 初始化底部分享功能按钮
        initBottomFunctionButtons();

        // 初始化VIP权益模块
//        initVipRightsModule();

        // 初始化摸底测评和开始学习相关控件
        initEvaluationAndLearningViews();

        // 初始化功能按钮区域
        initFunctionButtonsArea();

        // 设置点击事件
        setupClickListeners();

        // 更新顶部标题
        updateTopTitle();

        // 立即请求首页数据，确保AI学习助手模块能正确显示数据
        fetchRealHomeData();

        // 初始化AI学习助手模块的显示
        initAILearningAssistantUI();

        LogUtils.e("NativeStudyFragment初始化完成");
        binding.tvExame.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                testToExam();
            }
        });
    }

    /**
     * 初始化顶部通知条
     */
    private void initNotifyBar() {
        LogUtils.e("NativeStudyFragment - initNotifyBar - 开始初始化通知条");

        try {
            // 查找通知条布局
            // 先尝试查看布局文件中的通知条
            LinearLayout notifyLayout = null;
            TextView tvNotify = null;

            // 直接查找通知文本
            View tvView = binding.tvNotifyDes;
            if (tvView instanceof TextView) {
                tvNotify = (TextView) tvView;
                // 获取父布局
                if (tvNotify.getParent() instanceof LinearLayout) {
                    notifyLayout = (LinearLayout) tvNotify.getParent();
                    LogUtils.e("NativeStudyFragment - initNotifyBar - 通过ID直接找到通知文本");
                }
            } else {
                LogUtils.e("NativeStudyFragment - initNotifyBar - 找不到通知条布局，尝试查找父视图");
                // 尝试获取LinearLayout容器
                ViewGroup parentLayout = binding.getRoot().findViewById(android.R.id.content);
                if (parentLayout == null) {
                    parentLayout = (ViewGroup) binding.getRoot();
                }

                if (parentLayout != null) {
                    // 尝试查找通知文本视图
                    for (int i = 0; i < parentLayout.getChildCount(); i++) {
                        View child = parentLayout.getChildAt(i);
                        if (child instanceof LinearLayout) {
                            LinearLayout linearLayout = (LinearLayout) child;
                            for (int j = 0; j < linearLayout.getChildCount(); j++) {
                                View subChild = linearLayout.getChildAt(j);
                                if (subChild instanceof TextView) {
                                    String text = ((TextView) subChild).getText().toString();
                                    if (text.contains("题库") || text.contains("官方")) {
                                        tvNotify = (TextView) subChild;
                                        notifyLayout = linearLayout;
                                        LogUtils.e("NativeStudyFragment - initNotifyBar - 通过内容查找到通知文本");
                                        break;
                                    }
                                }
                            }
                            if (tvNotify != null) break;
                        }
                    }
                }
            }

            // 如果找不到通知文本视图，尝试直接获取带文本的视图
            if (tvNotify == null) {
                LinearLayout linearLayout = binding.getRoot().findViewWithTag("notify_layout");
                if (linearLayout != null) {
                    for (int i = 0; i < linearLayout.getChildCount(); i++) {
                        View child = linearLayout.getChildAt(i);
                        if (child instanceof TextView) {
                            tvNotify = (TextView) child;
                            notifyLayout = linearLayout;
                            LogUtils.e("NativeStudyFragment - initNotifyBar - 通过Tag查找到通知文本");
                            break;
                        }
                    }
                }

                // 最后尝试
                if (tvNotify == null) {
                    // 直接查找通知条中的文本视图
                    View view = binding.getRoot().findViewById(R.id.tv_notify_des);
                    if (view instanceof TextView) {
                        tvNotify = (TextView) view;
                        notifyLayout = (LinearLayout) tvNotify.getParent();
                        LogUtils.e("NativeStudyFragment - initNotifyBar - 通过ID找到通知文本");
                    }
                }
            }

            // 如果找到了通知文本和布局
            if (notifyLayout != null && tvNotify != null) {
                LogUtils.e("NativeStudyFragment - initNotifyBar - 成功找到通知条和文本视图");
                final TextView finalTvNotify = tvNotify;
                final LinearLayout finalNotifyLayout = notifyLayout;

                // 默认隐藏通知条
                finalNotifyLayout.setVisibility(View.GONE);

                // 设置初始状态
                if (studyViewModel != null) {
                    // 初始通知文本
                    String initialText = studyViewModel.getOfficialText().getValue();
                    if (initialText != null && !initialText.isEmpty()) {
                        finalTvNotify.setText(initialText);
                        LogUtils.e("NativeStudyFragment - initNotifyBar - 设置初始通知文本: " + initialText);
                    }

                    // 初始显示状态
                    Boolean isOfficial = studyViewModel.getIsOfficial().getValue();
                    if (isOfficial != null && isOfficial) {
                        finalNotifyLayout.setVisibility(View.VISIBLE);
                        LogUtils.e("NativeStudyFragment - initNotifyBar - 初始显示通知条");
                    }

                    // 观察通知文本变化
                    studyViewModel.getOfficialText().observe(getViewLifecycleOwner(), text -> {
                        if (finalTvNotify != null && text != null && !text.isEmpty()) {
                            finalTvNotify.setText(text);
                            LogUtils.e("NativeStudyFragment - initNotifyBar - 更新通知文本: " + text);
                        }
                    });

                    // 观察是否显示通知条
                    studyViewModel.getIsOfficial().observe(getViewLifecycleOwner(), isOfficialValue -> {
                        if (finalNotifyLayout != null) {
                            boolean isVisible = isOfficialValue != null && isOfficialValue;
                            finalNotifyLayout.setVisibility(isVisible ? View.VISIBLE : View.GONE);
                            LogUtils.e("NativeStudyFragment - initNotifyBar - 更新通知条显示状态: " + (isVisible ? "显示" : "隐藏"));
                        }
                    });
                }
            } else {
                LogUtils.e("NativeStudyFragment - initNotifyBar - 无法找到通知条或文本视图");
            }
        } catch (Exception e) {
            LogUtils.e("NativeStudyFragment - initNotifyBar - 发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 初始化VIP权益模块
     */
//    private void initVipRightsModule() {
//        // 获取VIP权益模块布局
//        vipRightsLayout = binding.getRoot().findViewById(R.id.vip_rights_layout);
//
//        if (vipRightsLayout != null) {
//            // 获取控件引用
//            tvVipRightsTitle = vipRightsLayout.findViewById(R.id.tv_vip_rights_title);
//            tvVipRightsSubtitle = vipRightsLayout.findViewById(R.id.tv_des);
//            btnVipRightsAction = vipRightsLayout.findViewById(R.id.btn_vip_rights_action);
//
//            // 默认隐藏VIP权益模块，等待数据加载后再显示
//            vipRightsLayout.setVisibility(View.GONE);
//
//            // 设置按钮点击事件
//            if (btnVipRightsAction != null) {
//                btnVipRightsAction.setOnClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        handleVipRightsButtonClick();
//                    }
//                });
//            }
//        }
//    }

    /**
     * 处理VIP权益按钮点击事件
     */
//    private void handleVipRightsButtonClick() {
//        if (homeData != null && homeData.getMiddleAds() != null && !TextUtils.isEmpty(homeData.getMiddleAds().getUrl())) {
//            // 检查是否已登录
//            if (!AccountHelper.isUserLogin()) {
//                ToastUtils.normal("请先登录");
//                startFragment(AccountHelper.showAccountLogin());
//                return;
//            }
//
//            // 确保Arguments不为null
//            if (getArguments() == null) {
//                setArguments(new Bundle());
//            }
//
//            // 跳转到VIP购买页面
//            String url = homeData.getMiddleAds().getUrl();
//            getArguments().putString("routh", "home");
//            getArguments().putString("url", url);
//            getArguments().putInt("payType", 1);
//            getArguments().putString("payRouthParams", PayRouthConfig.PAY_UPDATE);
//            startFragment(CommonHelper.showCommonWeb());
//        } else {
//            ToastUtils.normal("获取VIP信息失败");
//        }
//    }

    /**
     * 初始化摸底测评和开始学习相关控件
     */
    private void initEvaluationAndLearningViews() {
        // 直接通过ID获取摸底测评布局
        evaluationLayout = binding.llTestCard;

        if (evaluationLayout != null) {
            LogUtils.e("成功找到摸底测评布局 by ID ll_test_card");
            // 在摸底测评布局中查找关闭按钮
            ivCloseTest = evaluationLayout.findViewById(R.id.iv_close_test);

            if (ivCloseTest != null) {
                LogUtils.e("成功找到关闭按钮 iv_close_test");
                ivCloseTest.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        LogUtils.e("关闭按钮被点击");
                        closeEvaluation();
                    }
                });
            } else {
                LogUtils.e("在摸底测评布局中未找到关闭按钮");
            }

            // 默认隐藏摸底测评布局，等待数据加载完成后再决定是否显示
            evaluationLayout.setVisibility(View.GONE);
            LogUtils.e("默认隐藏摸底测评布局，等待数据加载");
        } else {
            LogUtils.e("未找到摸底测评布局 by ID ll_test_card");
        }

        // 初始化"继续练习"按钮点击事件
        Button btnStartPractice = binding.getRoot().findViewById(R.id.btn_start_practice);
        if (btnStartPractice != null) {
            LogUtils.e("成功找到继续练习按钮 btn_start_practice");
            btnStartPractice.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!AccountHelper.checkLoginState()) {
                        return;
                    }
                    // 点击继续练习按钮，先检查登录状态
//                    if (!NoDoubleClickUtils.isDoubleClick()) {
                    // 检查是否已登录
                    if (AccountHelper.isUserLogin()) {
                        // 已登录，跳转到题型练习页面
                        if (getArguments() == null) {
                            setArguments(new Bundle());
                        }
//                        getArguments().putInt("index", 0);
//                        startFragment(ListHelper.toPageExamList());
                        MenuNavsBean menuNavsBean = new MenuNavsBean();
                        menuNavsBean.setNavType(MenuNavsBean.TYPE_AI_TEST);
                        studyViewModel.onMainNavFunctionClick(menuNavsBean);
                    } else {
                        // 未登录，跳转到登录页面
                        ToastUtils.normal("请先登录");
                        startFragment(AccountHelper.showAccountLogin());
//                            AccountHelper.checkLoginState();
                    }
//                    }
                }
            });

            // 默认隐藏继续练习按钮，等待首页数据加载后根据摸底测评状态决定是否显示
            btnStartPractice.setVisibility(View.GONE);
            LogUtils.e("默认隐藏继续练习按钮，等待数据加载");
        } else {
            LogUtils.e("未找到继续练习按钮 btn_start_practice");
        }

        // 查找开始测评按钮并设置点击事件
        Button btnStartTest = evaluationLayout != null ? evaluationLayout.findViewById(R.id.btn_start_test) : null;
        if (btnStartTest != null) {
            btnStartTest.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!AccountHelper.checkLoginState()) {
                        return;
                    }
                    // 检查是否已登录
                    if (AccountHelper.isUserLogin()) {
                        // 已登录，跳转到摸底测评页面
                        if (getArguments() == null) {
                            setArguments(new Bundle());
                        }
                        // 跳转到摸底测评页面
                        // 根据实际路由设置参数 - 使用常量值1表示摸底测评
//                        getArguments().putInt("examType", 1); // 1对应摸底测评类型
//                        startFragment(ListHelper.toPageExamList());
                        testToExam();
                    } else {
                        // 未登录，跳转到登录页面
                        ToastUtils.normal("请先登录");
                        startFragment(AccountHelper.showAccountLogin());
//                            AccountHelper.checkLoginState();
                    }
                }
            });
            LogUtils.e("为开始测评按钮设置点击事件");
        }
    }

    /**
     * 初始化底部分享功能按钮
     */
    private void initBottomFunctionButtons() {
        LogUtils.e("NativeStudyFragment - initBottomFunctionButtons - 初始化底部分享功能按钮");

        // 首先直接创建并显示模拟数据，确保UI不为空
        List<HomeShaperBean> mockShareNavs = createMockShareNavs();
        updateBottomFunctionButtons(mockShareNavs);
        LogUtils.e("NativeStudyFragment - initBottomFunctionButtons - 先使用模拟数据初始化UI");

        // 不再在这里调用fetchRealHomeData()，因为已经在initView()中调用了
        // fetchRealHomeData();
    }

    /**
     * 创建模拟的分享按钮数据
     */
    private List<HomeShaperBean> createMockShareNavs() {
        List<HomeShaperBean> mockShareNavs = new ArrayList<>();

        // 微信好友
        HomeShaperBean wechatShareNav = new HomeShaperBean();
        wechatShareNav.setNavType(1);
        wechatShareNav.setTitle("微信好友");
        wechatShareNav.setShareData("");
        wechatShareNav.setAtWork(true);
        mockShareNavs.add(wechatShareNav);

        // 朋友圈
        HomeShaperBean momentsShareNav = new HomeShaperBean();
        momentsShareNav.setNavType(2);
        momentsShareNav.setTitle("朋友圈");
        momentsShareNav.setShareData("");
        momentsShareNav.setAtWork(true);
        mockShareNavs.add(momentsShareNav);

        // 拨打电话
        HomeShaperBean phoneShareNav = new HomeShaperBean();
        phoneShareNav.setNavType(3);
        phoneShareNav.setTitle("拨打电话");
        phoneShareNav.setShareData("************");
        phoneShareNav.setAtWork(true);
        mockShareNavs.add(phoneShareNav);

        // 在线客服
        HomeShaperBean serviceShareNav = new HomeShaperBean();
        serviceShareNav.setNavType(4);
        serviceShareNav.setTitle("在线客服");
        serviceShareNav.setShareData("https://wpa.qq.com/msgrd?v=3&uin=1234567890");
        serviceShareNav.setAtWork(true);
        mockShareNavs.add(serviceShareNav);

        return mockShareNavs;
    }

    /**
     * 更新底部分享功能按钮
     *
     * @param shareNavs 底部分享功能按钮数据
     */
    private void updateBottomFunctionButtons(List<HomeShaperBean> shareNavs) {
        if (shareNavs == null || shareNavs.isEmpty()) {
            LogUtils.e("NativeStudyFragment - updateBottomFunctionButtons - 数据为空");
            return;
        }

        LogUtils.e("NativeStudyFragment - updateBottomFunctionButtons - 开始更新，按钮数量: " + shareNavs.size());

        try {
            // 获取底部功能按钮容器 - 这是之前静态布局里的那一行容器
            LinearLayout staticButtonContainer = null;
            View layoutWechat = binding.layoutWechat;
            if (layoutWechat != null && layoutWechat.getParent() instanceof LinearLayout) {
                staticButtonContainer = (LinearLayout) layoutWechat.getParent();
                // 隐藏静态布局中的底部按钮容器
                staticButtonContainer.setVisibility(View.GONE);
                LogUtils.e("NativeStudyFragment - updateBottomFunctionButtons - 已隐藏静态按钮容器");
            }

            // 查找父视图
            ViewGroup parentView = null;
            if (staticButtonContainer != null && staticButtonContainer.getParent() instanceof ViewGroup) {
                parentView = (ViewGroup) staticButtonContainer.getParent();
            } else {
                // 如果找不到静态按钮容器，尝试使用binding的root
                parentView = (ViewGroup) binding.getRoot().findViewById(android.R.id.content);
                if (parentView == null) {
                    // 再尝试直接获取binding的根视图
                    parentView = (ViewGroup) binding.getRoot();
                }
            }

            if (parentView == null) {
                LogUtils.e("NativeStudyFragment - updateBottomFunctionButtons - 找不到合适的父视图");
                return;
            }

            // 尝试移除之前可能添加的动态按钮容器
            View existingContainer = parentView.findViewWithTag("dynamic_bottom_functions_container");
            if (existingContainer != null) {
                parentView.removeView(existingContainer);
                LogUtils.e("NativeStudyFragment - updateBottomFunctionButtons - 移除之前的动态按钮容器");
            }

            // 创建新的动态按钮容器替代原有的静态容器
            LinearLayout bottomFunctionsContainer = new LinearLayout(getActivity());
            bottomFunctionsContainer.setTag("dynamic_bottom_functions_container"); // 添加标记以便后续查找
            LinearLayout.LayoutParams containerParams = new LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT);
            containerParams.setMargins(
                    DpUtils.dp2px(getActivity(), 16),
                    DpUtils.dp2px(getActivity(), 16),
                    DpUtils.dp2px(getActivity(), 16),
                    DpUtils.dp2px(getActivity(), 16));
            bottomFunctionsContainer.setLayoutParams(containerParams);
            bottomFunctionsContainer.setOrientation(LinearLayout.HORIZONTAL);
            bottomFunctionsContainer.setId(View.generateViewId()); // 生成唯一ID

            // 计算每个按钮的权重
            float buttonWeight = 1.0f / shareNavs.size();
            LogUtils.e("NativeStudyFragment - updateBottomFunctionButtons - 按钮数量: " + shareNavs.size() +
                    ", 每个按钮的权重: " + buttonWeight);

            // 添加新的按钮
            int addedButtonCount = 0;
            for (HomeShaperBean shareNav : shareNavs) {
                LogUtils.e("NativeStudyFragment - updateBottomFunctionButtons - 添加按钮: " + shareNav.getTitle() +
                        ", 类型: " + shareNav.getNavType() +
                        ", 是否启用: " + shareNav.isAtWork());

                // 创建按钮布局
                LinearLayout buttonLayout = new LinearLayout(getActivity());
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                        0, LinearLayout.LayoutParams.WRAP_CONTENT, buttonWeight);
                buttonLayout.setLayoutParams(layoutParams);
                buttonLayout.setGravity(android.view.Gravity.CENTER);
                buttonLayout.setOrientation(LinearLayout.VERTICAL);

                // 创建图标
                ImageView iconView = new ImageView(getActivity());
                int iconSize = DpUtils.dp2px(getActivity(), 40); // 统一为40dp，和静态布局保持一致
                iconView.setLayoutParams(new LinearLayout.LayoutParams(iconSize, iconSize));
                iconView.setScaleType(ImageView.ScaleType.FIT_CENTER);

                // 设置图标
                int iconResId = getShareNavIconResId(shareNav.getNavType());
                iconView.setImageResource(iconResId);

                // 创建文本
                TextView textView = new TextView(getActivity());
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT);
                textParams.topMargin = DpUtils.dp2px(getActivity(), 4);
                textView.setLayoutParams(textParams);
                textView.setText(shareNav.getTitle());
                textView.setTextColor(android.graphics.Color.parseColor("#333333"));
                textView.setTextSize(12);
                textView.setGravity(android.view.Gravity.CENTER);

                // 添加到布局
                buttonLayout.addView(iconView);
                buttonLayout.addView(textView);

                // 设置点击事件
                final HomeShaperBean finalShareNav = shareNav;
                buttonLayout.setOnClickListener(v -> {
                    if (!NoDoubleClickUtils.isDoubleClick()) {
                        handleShareButtonClick(finalShareNav);
                    }
                });

                // 添加到容器
                bottomFunctionsContainer.addView(buttonLayout);
                addedButtonCount++;
            }

            // 直接将新容器添加到父视图末尾
            parentView.addView(bottomFunctionsContainer);
            LogUtils.e("NativeStudyFragment - updateBottomFunctionButtons - 添加新的动态按钮容器");

            LogUtils.e("NativeStudyFragment - updateBottomFunctionButtons - 完成，添加了 " + addedButtonCount + " 个按钮");

        } catch (Exception e) {
            LogUtils.e("NativeStudyFragment - updateBottomFunctionButtons - 异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理分享按钮点击事件
     */
    private void handleShareButtonClick(HomeShaperBean shareNav) {
        LogUtils.e("NativeStudyFragment - handleShareButtonClick - 点击底部分享按钮: " + shareNav.getTitle());

        // 使用StudyViewModel处理分享按钮点击
        if (studyViewModel != null) {
            studyViewModel.handleShareNavClick(shareNav);
        } else {
            LogUtils.e("NativeStudyFragment - handleShareButtonClick - studyViewModel为null，无法处理点击事件");
            ToastUtils.normal("系统繁忙，请稍后再试");
        }
    }

    /**
     * 获取分享导航图标资源ID
     */
    private int getShareNavIconResId(int navType) {
        int iconResId;

        switch (navType) {
            case 1: // 微信好友
                iconResId = R.drawable.wx;
                break;
            case 2: // 朋友圈
                iconResId = R.drawable.pyq;
                break;
            case 3: // 拨打电话
                iconResId = R.drawable.phone;
                break;
            case 4: // 在线客服
                iconResId = R.drawable.call;
                break;
            default:
                // 默认图标
                iconResId = R.drawable.phone;
                break;
        }

        return iconResId;
    }

    /**
     * 初始化功能按钮区域
     */
    private void initFunctionButtonsArea() {
        // 查找功能按钮容器
        firstRowContainer = binding.getRoot().findViewById(R.id.function_button_row1);
        secondRowContainer = binding.getRoot().findViewById(R.id.function_button_row2);

        if (firstRowContainer == null || secondRowContainer == null) {
            LogUtils.e("找不到功能按钮容器，初始化功能按钮区域失败");
            return;
        }

        LogUtils.e("功能按钮区域初始化完成");
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        swipeRefreshLayout = binding.getRoot().findViewById(R.id.swipe_refresh_layout);
        if (swipeRefreshLayout != null) {
            // 设置下拉刷新的颜色
            swipeRefreshLayout.setColorSchemeResources(
                    android.R.color.holo_blue_light,
                    android.R.color.holo_green_light,
                    android.R.color.holo_orange_light,
                    android.R.color.holo_red_light
            );

            // 设置下拉刷新的监听器
            swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
                @Override
                public void onRefresh() {
                    // 刷新数据
                    refreshData();
                }
            });

            // 设置下拉刷新的距离和大小
            swipeRefreshLayout.setDistanceToTriggerSync(300);
            swipeRefreshLayout.setSize(SwipeRefreshLayout.DEFAULT);

            LogUtils.e("下拉刷新初始化完成");
        } else {
            LogUtils.e("找不到SwipeRefreshLayout，下拉刷新初始化失败");
        }
    }

    @Override
    public void initViewData() {
        // 获取真实的首页数据
        fetchRealHomeData();

        // 确保进度环显示有默认值
        updateProgressCircleText();
    }

    /**
     * 更新进度环显示文本
     */
    private void updateProgressCircleText() {
        LogUtils.e("NativeStudyFragment - updateProgressCircleText - 更新进度环文本");

        try {
            // 获取进度环中的文本视图
            TextView tvNum = binding.getRoot().findViewById(R.id.tv_num);
            if (tvNum != null) {
                // 获取currentCount和questionCount的值，如果为null则使用默认值
                String currentCount = "0";
                String questionCount = "0";

                if (studyViewModel != null) {
                    if (studyViewModel.getCurrentCount().getValue() != null) {
                        currentCount = studyViewModel.getCurrentCount().getValue();
                    }

                    if (studyViewModel.getQuestionCount().getValue() != null) {
                        questionCount = studyViewModel.getQuestionCount().getValue();
                    }
                }

                // 直接设置文本，避免数据绑定中的null值问题
                String displayText = currentCount + "/" + questionCount;
                tvNum.setText(displayText);
                LogUtils.e("NativeStudyFragment - updateProgressCircleText - 设置进度环文本: " + displayText);

                // 更新进度环的进度值
                com.zizhiguanjia.model_home.view.CustomProgressView progressCircular =
                        binding.getRoot().findViewById(R.id.progress_circular);
                if (progressCircular != null) {
                    try {
                        int current = Integer.parseInt(currentCount);
                        int total = Integer.parseInt(questionCount);

                        if (total > 0) {
                            // CustomProgressView 需要设置最大值为100，进度为百分比
                            int progressPercent = (int) (((float) current / total) * 100);
                            progressCircular.setMax(100);
                            progressCircular.setProgress(progressPercent);
                            LogUtils.e("NativeStudyFragment - updateProgressCircleText - 设置进度环进度: " +
                                    progressPercent + "% (" + current + "/" + total + ")");
                        } else {
                            // 防止除零错误，设置默认值
                            progressCircular.setMax(100);
                            progressCircular.setProgress(0);
                            LogUtils.e("NativeStudyFragment - updateProgressCircleText - 设置进度环默认进度: 0%");
                        }

                        // 刷新进度环视图
                        progressCircular.invalidate();
                    } catch (NumberFormatException e) {
                        LogUtils.e("NativeStudyFragment - updateProgressCircleText - 解析数值异常: " + e.getMessage());
                        // 异常时设置默认值
                        progressCircular.setMax(100);
                        progressCircular.setProgress(0);
                    }
                } else {
                    LogUtils.e("NativeStudyFragment - updateProgressCircleText - 找不到进度环视图");
                }
            } else {
                LogUtils.e("NativeStudyFragment - updateProgressCircleText - 找不到进度环文本视图");
            }
        } catch (Exception e) {
            LogUtils.e("NativeStudyFragment - updateProgressCircleText - 异常: " + e.getMessage());
        }
    }

    /**
     * 获取真实的首页数据
     */
    private void fetchRealHomeData() {
        LogUtils.e("NativeStudyFragment - fetchRealHomeData - 开始获取首页数据");

        // 显示加载提示
//        if (listener != null) {
//            listener.showLoading(true, "加载中...");
//        }

        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        if (BaseConfig.channelUID != null && !BaseConfig.channelUID.isEmpty()) {
            params.put("fromWhichPerson", BaseConfig.channelUID);
        }

        // 使用原始方法直接调用API
        Observable<BaseData<HomeBean>> observable = mApi.getMainInfo(params);
        if (getActivity() != null) {
            // 使用标准的RxJava订阅方式，避免使用StudyViewModel可能导致的循环依赖
            observable.subscribeOn(io.reactivex.schedulers.Schedulers.io())
                    .observeOn(io.reactivex.android.schedulers.AndroidSchedulers.mainThread())
                    .subscribe(new io.reactivex.functions.Consumer<BaseData<HomeBean>>() {
                        @Override
                        public void accept(BaseData<HomeBean> data) throws Exception {
                            // 关闭加载提示
                            if (listener != null) {
                                listener.showLoading(false, null);
                            }

                            if (data != null && data.Data != null) {
                                // 保存首页数据
                                homeData = data.Data;

                                // 更新UI
                                updateHomeData(homeData);

                                // 如果StudyViewModel不为空，更新它的数据
                                if (studyViewModel != null) {
                                    // 只更新数据，不触发新的网络请求
                                    studyViewModel.updateFromHomeBean(data.Data);

                                    // 延迟强制刷新UI，确保数据绑定生效
                                    new android.os.Handler().postDelayed(() -> {
                                        // 强制刷新数据绑定
                                        if (binding != null) {
                                            binding.notifyChange();
                                            LogUtils.e("NativeStudyFragment - fetchRealHomeData - 强制刷新数据绑定");
                                        }
                                    }, 100);
                                }

                                // 直接更新底部分享按钮
                                if (data.Data.getShareNavs() != null && !data.Data.getShareNavs().isEmpty()) {
                                    updateBottomFunctionButtons(data.Data.getShareNavs());
                                    LogUtils.e("NativeStudyFragment - fetchRealHomeData - 数据加载成功，直接更新底部按钮");
                                }

                                // 直接更新AI学习助手模块
                                updateAILearningAssistantFromHomeBean(data.Data);

                                LogUtils.e("NativeStudyFragment - fetchRealHomeData - 获取首页数据成功");
                            } else {
                                LogUtils.e("NativeStudyFragment - fetchRealHomeData - 获取首页数据成功，但数据为空");
                                ToastUtils.normal("获取数据失败，请稍后重试");
                            }
                        }
                    }, new io.reactivex.functions.Consumer<Throwable>() {
                        @Override
                        public void accept(Throwable throwable) throws Exception {
                            // 关闭加载提示
                            if (listener != null) {
                                listener.showLoading(false, null);
                            }

                            LogUtils.e("NativeStudyFragment - fetchRealHomeData - 获取首页数据失败: " + throwable.getMessage());
                            ToastUtils.normal("获取数据失败: " + throwable.getMessage());
                        }
                    });
        }
    }

    /**
     * 设置点击事件
     */
    private void setupClickListeners() {
        // 注意：关闭按钮的点击事件已在initEvaluationAndLearningViews中设置
        // 设置顶部区域头像点击事件
        View imgMainUser = binding.getRoot().findViewById(R.id.imgMainUser);
        if (imgMainUser != null) {
            imgMainUser.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!NoDoubleClickUtils.isDoubleClick()) {
                        // 跳转到用户信息页面
                        startFragment(AccountHelper.showAccountLogin());
                    }
                }
            });
        }

        // 设置顶部区域地区/科目点击事件
        View llHomeSwitch = binding.getRoot().findViewById(R.id.llHomeSwitch);
        if (llHomeSwitch != null) {
            llHomeSwitch.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!NoDoubleClickUtils.isDoubleClick()) {
                        // 跳转到证书选择页面
                        startFragment(CertificateHelper.startChoseCertificateZzByAddress());
                    }
                }
            });
        }
    }

    /**
     * 关闭摸底测评
     */
    private void closeEvaluation() {
        if (!AccountHelper.isUserLogin()) {
            ToastUtils.normal("请先登录");
//            startFragment(AccountHelper.showAccountLogin());
            AccountHelper.checkLoginState();
            return;
        }
        LogUtils.e("开始执行关闭摸底测评");

        // 首先隐藏摸底测评布局
        if (evaluationLayout != null) {
            evaluationLayout.setVisibility(View.GONE);
            LogUtils.e("摸底测评布局已隐藏");
        } else {
            LogUtils.e("摸底测评布局为null，无法隐藏");
        }

        // 显示"继续练习"按钮
        Button btnStartPractice = binding.getRoot().findViewById(R.id.btn_start_practice);
        if (btnStartPractice != null) {
            btnStartPractice.setVisibility(View.VISIBLE);
            LogUtils.e("成功显示继续练习按钮");
        } else {
            LogUtils.e("未找到继续练习按钮(btn_start_practice)");
        }

        // 调用后端API通知服务器关闭摸底测评
        if (studyViewModel != null) {
            studyViewModel.closeEvaluation(new StudyViewModel.OnHandleException<BaseData>() {
                @Override
                public void success(BaseData data) {
                    LogUtils.e("关闭摸底测评API调用成功");

                    // API调用成功后，如果homeData不为空，更新其isShowEvaluation状态
                    if (homeData != null) {
                        homeData.setShowEvaluation(false);
                        LogUtils.e("更新homeData中的isShowEvaluation为false");
                    }

                    // 在UI线程上显示提示
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                ToastUtils.normal("已关闭摸底测评");
                            }
                        });
                    }
                }

                @Override
                public void error(String msg) {
                    LogUtils.e("关闭摸底测评API调用失败: " + msg);

                    // 即使API调用失败，也应该保持UI一致性（隐藏摸底测评，显示继续练习按钮）
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                ToastUtils.normal("关闭摸底测评失败: " + msg);
                            }
                        });
                    }
                }
            });
        } else {
            LogUtils.e("studyViewModel为空，无法调用关闭摸底测评API");
            ToastUtils.normal("已关闭摸底测评");
        }
    }

    /**
     * 更新顶部标题
     */
    private void updateTopTitle() {
        // 优先使用HomeBean中的DefaultNav字段作为标题
        if (homeData != null && homeData.getDefaultNav() != null && !homeData.getDefaultNav().isEmpty()) {
            String defaultNav = homeData.getDefaultNav();
            // 获取顶部标题TextView
            TextView titleTopic = binding.getRoot().findViewById(R.id.titleTopic);
            if (titleTopic != null) {
                titleTopic.setText(defaultNav);
                LogUtils.e("NativeStudyFragment - updateTopTitle - 使用DefaultNav更新顶部标题: " + defaultNav);
            }
            return;
        }

        // 如果HomeBean为空或DefaultNav为空，则使用证书信息作为备选方案
        String title = CertificateHelper.initCertificate(AccountHelper.isUserLogin());
        if (!StringUtils.isEmpty(title)) {
            // 获取顶部标题TextView
            TextView titleTopic = binding.getRoot().findViewById(R.id.titleTopic);
            if (titleTopic != null) {
                titleTopic.setText(title);
                LogUtils.e("NativeStudyFragment - updateTopTitle - 使用证书信息更新顶部标题: " + title);
            }
        }
    }

    @Override
    public void initObservable() {
        LogUtils.e("NativeStudyFragment - initObservable - 初始化Observable");

        // 我们不再监听StudyViewModel中的LiveData，而是直接使用更新方法
        // 避免可能导致的循环引用和死锁问题
    }

    /**
     * 设置监听器
     */
    public void setListener(INativeStudyFragment listener) {
        this.listener = listener;
    }

    /**
     * 刷新数据
     */
    public void refreshData() {
        LogUtils.e("NativeStudyFragment - refreshData - 开始刷新数据");

        // 更新顶部标题（确保在刷新时保持最新状态）
        updateTopTitle();

        // 更新进度环文本，确保在刷新时也能正确显示
        updateProgressCircleText();

        // 直接获取首页数据，避免调用StudyViewModel可能导致的循环依赖
        fetchRealHomeData();
    }

    /**
     * 更新首页数据
     */
    public void updateHomeData(HomeBean homeBean) {
        if (homeBean == null) return;

        LogUtils.e("NativeStudyFragment - updateHomeData - 开始更新首页数据");

        // 保存首页数据
        this.homeData = homeBean;

        // 更新顶部信息（如用户名、头像等）
        // 直接使用homeBean提供的数据更新StudyViewModel，无需getUsers()方法
        if (studyViewModel != null) {
            LogUtils.e("NativeStudyFragment - updateHomeData - 调用StudyViewModel.updateFromHomeBean");
            studyViewModel.updateFromHomeBean(homeBean);

            // 手动更新进度环文本，避免出现null值
            updateProgressCircleText();

            // 手动更新顶部标题，确保显示正确
            updateTopTitle();

            // 直接更新AI学习助手模块UI
            updateAILearningAssistantFromHomeBean(homeBean);

            // 手动更新用户头像
            ImageView imgMainUser = binding.getRoot().findViewById(R.id.imgMainUser);
            if (imgMainUser != null) {
                if (AccountHelper.isUserLogin()) {
                    // 从UserHelper获取用户信息
                    TableUserInfo userInfo = UserHelper.getUserInfo();
                    if (userInfo != null && !TextUtils.isEmpty(userInfo.getHeadimgurl())) {
                        // 使用Glide加载头像
                        Glide.with(requireActivity())
                                .load(Objects.requireNonNull(studyViewModel.getHomeBean().getValue()).getHeadimg())
                                .apply(new RequestOptions().error(R.drawable.home_login_state_no))
                                .into(imgMainUser);
                        LogUtils.e("NativeStudyFragment - updateHomeData - 更新用户头像成功");
                    } else {
                        imgMainUser.setImageResource(R.drawable.home_login_state_no);
                        LogUtils.e("NativeStudyFragment - updateHomeData - 用户头像为空，使用默认图片");
                    }
                } else {
                    imgMainUser.setImageResource(R.drawable.home_login_state_no);
                    LogUtils.e("NativeStudyFragment - updateHomeData - 用户未登录，使用默认图片");
                }
            }

            // 手动更新通知条
            try {
                LinearLayout notifyLayout = null;
                TextView tvNotify = null;

                // 尝试直接通过ID查找
                View notifyParent = binding.getRoot().findViewById(android.R.id.content);
                if (notifyParent == null) {
                    notifyParent = binding.getRoot();
                }

                if (notifyParent instanceof ViewGroup) {
                    // 遍历查找符合条件的TextView
                    ViewGroup parent = (ViewGroup) notifyParent;
                    for (int i = 0; i < parent.getChildCount(); i++) {
                        View child = parent.getChildAt(i);
                        if (child instanceof LinearLayout) {
                            LinearLayout linearLayout = (LinearLayout) child;
                            for (int j = 0; j < linearLayout.getChildCount(); j++) {
                                View subChild = linearLayout.getChildAt(j);
                                if (subChild instanceof LinearLayout) {
                                    LinearLayout innerLayout = (LinearLayout) subChild;
                                    for (int k = 0; k < innerLayout.getChildCount(); k++) {
                                        View innerChild = innerLayout.getChildAt(k);
                                        if (innerChild instanceof TextView) {
                                            String text = ((TextView) innerChild).getText().toString();
                                            if (text.contains("题库") || text.contains("官方")) {
                                                tvNotify = (TextView) innerChild;
                                                notifyLayout = (LinearLayout) subChild;
                                                break;
                                            }
                                        }
                                    }
                                }
                                if (tvNotify != null) break;
                            }
                        }
                        if (tvNotify != null) break;
                    }
                }

                // 如果还是没找到，直接尝试所有layout下的TextView
                if (tvNotify == null) {
                    View view = binding.getRoot().findViewById(R.id.tv_notify_des);
                    if (view instanceof TextView) {
                        tvNotify = (TextView) view;
                        if (tvNotify.getParent() instanceof LinearLayout) {
                            notifyLayout = (LinearLayout) tvNotify.getParent();
                            while (!(notifyLayout.getParent() instanceof LinearLayout) && notifyLayout.getParent() instanceof ViewGroup) {
                                notifyLayout = (LinearLayout) notifyLayout.getParent();
                            }
                        }
                    }
                }

                // 如果找到通知文本和布局
                if (notifyLayout != null && tvNotify != null) {
                    // 更新通知文本
                    if (homeBean.getOfficialText() != null && !homeBean.getOfficialText().isEmpty()) {
                        tvNotify.setText(homeBean.getOfficialText());
                        LogUtils.e("NativeStudyFragment - updateHomeData - 手动更新通知文本: " + homeBean.getOfficialText());
                    }

                    // 更新通知栏显示状态
                    boolean isOfficial = homeBean.isOfficial();
                    notifyLayout.setVisibility(isOfficial ? View.VISIBLE : View.GONE);
                    LogUtils.e("NativeStudyFragment - updateHomeData - 手动更新通知条显示状态: " + (isOfficial ? "显示" : "隐藏"));
                }
            } catch (Exception e) {
                LogUtils.e("NativeStudyFragment - updateHomeData - 更新通知条异常: " + e.getMessage());
            }
        }

        // 处理摸底测评显示状态
        if (evaluationLayout != null) {
            // 使用isShowEvaluation()方法判断是否显示摸底测评
            boolean shouldShowEvaluation = homeBean.isShowEvaluation();
            LogUtils.e("NativeStudyFragment - updateHomeData - HomeBean中的IsShowEvaluation值: " + shouldShowEvaluation);

            evaluationLayout.setVisibility(shouldShowEvaluation ? View.VISIBLE : View.GONE);
            LogUtils.e("NativeStudyFragment - updateHomeData - 设置摸底测评显示状态: " + (shouldShowEvaluation ? "显示" : "隐藏"));

            // 同时处理"继续练习"按钮的显示状态
            Button btnStartPractice = binding.getRoot().findViewById(R.id.btn_start_practice);
            if (btnStartPractice != null) {
                btnStartPractice.setVisibility(shouldShowEvaluation ? View.GONE : View.VISIBLE);
                LogUtils.e("NativeStudyFragment - updateHomeData - 设置继续练习按钮: " + (shouldShowEvaluation ? "隐藏" : "显示"));
            }

            // 确保开始测评按钮状态正确
            Button btnStartTest = evaluationLayout.findViewById(R.id.btn_start_test);
            if (btnStartTest != null) {
                btnStartTest.setEnabled(true);
                LogUtils.e("NativeStudyFragment - updateHomeData - 启用开始测评按钮");
            }
        }

        // 更新VIP权益模块
        updateVipRightsModule(homeBean);

        // 更新功能按钮区域
        if (homeBean.getMenuNavs() != null && !homeBean.getMenuNavs().isEmpty()) {
            updateFunctionButtons(homeBean.getMenuNavs());
            LogUtils.e("NativeStudyFragment - updateHomeData - 更新功能按钮区域，数量: " + homeBean.getMenuNavs().size());
        } else {
            LogUtils.e("NativeStudyFragment - updateHomeData - 菜单导航数据为空");
        }

        // 更新底部分享功能按钮 - 动态更新按钮
        if (homeBean.getShareNavs() != null && !homeBean.getShareNavs().isEmpty()) {
            LogUtils.e("NativeStudyFragment - updateHomeData - 获取到底部分享功能按钮数据，数量: " + homeBean.getShareNavs().size());
            // 更新按钮UI
            updateBottomFunctionButtons(homeBean.getShareNavs());
            // 同时更新StudyViewModel中的数据
            if (studyViewModel != null) {
                studyViewModel.updateShareNavs(homeBean.getShareNavs());
            }
        } else {
            LogUtils.e("NativeStudyFragment - updateHomeData - 底部分享功能按钮数据为空，尝试使用模拟数据");
            // 使用模拟数据
            List<HomeShaperBean> mockShareNavs = createMockShareNavs();
            updateBottomFunctionButtons(mockShareNavs);
        }

        // 如果下拉刷新正在进行中，关闭它
        if (swipeRefreshLayout != null && swipeRefreshLayout.isRefreshing()) {
            swipeRefreshLayout.setRefreshing(false);
            LogUtils.e("NativeStudyFragment - updateHomeData - 关闭下拉刷新");
        }

        // 通知监听器刷新完成
        if (listener != null) {
            listener.onRefreshComplete();
        }

        LogUtils.e("NativeStudyFragment - updateHomeData - 首页数据更新完成");
    }

    /**
     * 更新功能按钮区域
     *
     * @param menuNavs 功能按钮数据
     */
    private void updateFunctionButtons(List<MenuNavsBean> menuNavs) {
        if (firstRowContainer == null || secondRowContainer == null) return;

        // 清空之前的按钮
        firstRowContainer.removeAllViews();
        secondRowContainer.removeAllViews();

        // 分类存储第一排和第二排的按钮数据
        List<MenuNavsBean> firstRowButtons = new ArrayList<>();
        List<MenuNavsBean> secondRowButtons = new ArrayList<>();

        // 根据RowNum参数分类
        for (MenuNavsBean menuNav : menuNavs) {
            if (menuNav.getRowNum() == 1) {
                firstRowButtons.add(menuNav);
            } else {
                secondRowButtons.add(menuNav);
            }
        }

        // 设置第一排按钮（使用卡片样式）
        if (firstRowButtons.size() > 0) {
            firstRowContainer.setVisibility(View.VISIBLE);

            // 确定布局方式：超过2个使用固定宽度并水平滚动
            boolean useFixedWidth = firstRowButtons.size() > 2;
            LogUtils.e("NativeStudyFragment - updateFunctionButtons - 第一排按钮数量: " + firstRowButtons.size() + 
                      ", 使用" + (useFixedWidth ? "固定宽度" : "等分宽度"));

            for (MenuNavsBean menuNav : firstRowButtons) {
                // 使用卡片样式布局
                View buttonView = getLayoutInflater().inflate(R.layout.item_menu_nav_button_card, firstRowContainer, false);

                // 设置布局参数
                LinearLayout.LayoutParams params;
                
                if (useFixedWidth) {
                    // 使用固定宽度
                    int buttonWidth = getResources().getDimensionPixelSize(R.dimen.menu_nav_long_button_width);
                    params = new LinearLayout.LayoutParams(
                            buttonWidth,
                            LinearLayout.LayoutParams.WRAP_CONTENT);
                } else {
                    // 使用等分宽度
                    params = new LinearLayout.LayoutParams(
                            0,
                            LinearLayout.LayoutParams.WRAP_CONTENT,
                            1.0f); // 等分宽度
                }

                // 设置边距
                int margin = getResources().getDimensionPixelSize(R.dimen.menu_nav_button_margin);
                params.setMargins(margin, margin, margin, margin);
                buttonView.setLayoutParams(params);

                // 设置图标和文本
                ImageView iconImageView = buttonView.findViewById(R.id.icon_image_view);
                TextView textView = buttonView.findViewById(R.id.text_view);
                TextView subtitleView = buttonView.findViewById(R.id.subtitle_text_view);

                // 设置图标
                if (menuNav.getImgSrc() != null && !menuNav.getImgSrc().isEmpty()) {
                    Glide.with(requireActivity())
                            .load(menuNav.getImgSrc())
                            .into(iconImageView);
                } else {
                    // 使用默认图标并设置为白色
                    iconImageView.setImageResource(R.drawable.ic_speaker);
                    iconImageView.setColorFilter(android.graphics.Color.WHITE);
                }

                // 设置文本
                textView.setText(menuNav.getTitle());
                if (menuNav.getOfficialText()!=null) {
                    subtitleView.setText(menuNav.getOfficialText());
                }

                // 设置点击事件
                final MenuNavsBean finalMenuNav = menuNav;
                buttonView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
//                        if (NoDoubleClickUtils.isDoubleClick()) return;

//                        ToastUtils.normal(finalMenuNav.getTitle());

                        // 确保Arguments不为null
                        if (getArguments() == null) {
                            setArguments(new Bundle());
                        }

                        // 处理不同类型的按钮点击
                        handleButtonClick(finalMenuNav);
                    }
                });

                firstRowContainer.addView(buttonView);
            }
        } else {
            firstRowContainer.setVisibility(View.GONE);
        }

        // 设置第二排按钮
        if (secondRowButtons.size() > 0) {
            secondRowContainer.setVisibility(View.VISIBLE);

            // 判断按钮数量决定布局方式
            if (secondRowButtons.size() <= 5) {
                // 按钮数量小于等于5个，均匀分配宽度
                LogUtils.e("NativeStudyFragment - updateFunctionButtons - 第二排按钮小于等于5个，均匀分配宽度");

                for (MenuNavsBean menuNav : secondRowButtons) {
                    // 使用原始样式布局
                    View buttonView = getLayoutInflater().inflate(R.layout.item_menu_nav_button, secondRowContainer, false);

                    // 设置布局参数，均匀分配宽度
                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                            0,
                            LinearLayout.LayoutParams.WRAP_CONTENT,
                            1.0f); // 等分宽度

                    // 设置边距
                    int margin = getActivity().getResources().getDimensionPixelSize(R.dimen.menu_nav_button_margin);
                    params.setMargins(margin, 0, margin, margin);
                    buttonView.setLayoutParams(params);

                    // 设置图标和文本
                    ImageView iconImageView = buttonView.findViewById(R.id.icon_image_view);
                    TextView textView = buttonView.findViewById(R.id.text_view);

                    // 设置图标
                    if (menuNav.getImgSrc() != null && !menuNav.getImgSrc().isEmpty()) {
                        Glide.with(requireActivity())
                                .load(menuNav.getImgSrc())
                                .into(iconImageView);
                    } else {
                        iconImageView.setImageResource(R.drawable.ic_speaker);
                    }

                    // 设置文本
                    textView.setText(menuNav.getTitle());

                    // 设置点击事件
                    final MenuNavsBean finalMenuNav = menuNav;
                    buttonView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
//                            if (NoDoubleClickUtils.isDoubleClick()) return;

                            //                        ToastUtils.normal(finalMenuNav.getTitle());

                            // 确保Arguments不为null
                            if (getArguments() == null) {
                                setArguments(new Bundle());
                            }

                            // 处理不同类型的按钮点击
                            handleButtonClick(finalMenuNav);
                        }
                    });

                    secondRowContainer.addView(buttonView);
                }
            } else {
                // 按钮数量大于5个，使用固定宽度并支持水平滑动
                LogUtils.e("NativeStudyFragment - updateFunctionButtons - 第二排按钮大于5个，使用固定宽度和水平滑动");

                // 创建水平滚动视图
                HorizontalScrollView horizontalScrollView = new HorizontalScrollView(getActivity());
                horizontalScrollView.setLayoutParams(new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT));
                horizontalScrollView.setHorizontalScrollBarEnabled(false); // 隐藏滚动条

                // 创建内部容器
                LinearLayout innerContainer = new LinearLayout(getActivity());
                innerContainer.setLayoutParams(new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT));
                innerContainer.setOrientation(LinearLayout.HORIZONTAL);

                // 设置固定宽度的按钮
                int buttonWidth = getResources().getDimensionPixelSize(R.dimen.menu_nav_button_width);
                if (buttonWidth <= 0) {
                    // 如果没有定义宽度，默认使用屏幕宽度的1/5
                    buttonWidth = getResources().getDisplayMetrics().widthPixels / 5;
                }

                for (MenuNavsBean menuNav : secondRowButtons) {
                    // 使用原始样式布局
                    View buttonView = getLayoutInflater().inflate(R.layout.item_menu_nav_button, innerContainer, false);

                    // 设置固定宽度布局参数
                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                            buttonWidth,
                            LinearLayout.LayoutParams.WRAP_CONTENT);

                    // 设置边距
                    int margin = getResources().getDimensionPixelSize(R.dimen.menu_nav_button_margin);
                    params.setMargins(margin, margin, margin, margin);
                    buttonView.setLayoutParams(params);

                    // 设置图标和文本
                    ImageView iconImageView = buttonView.findViewById(R.id.icon_image_view);
                    TextView textView = buttonView.findViewById(R.id.text_view);

                    // 设置图标
                    if (menuNav.getImgSrc() != null && !menuNav.getImgSrc().isEmpty()) {
                        Glide.with(requireActivity())
                                .load(menuNav.getImgSrc())
                                .into(iconImageView);
                    } else {
                        iconImageView.setImageResource(R.drawable.ic_speaker);
                    }

                    // 设置文本
                    textView.setText(menuNav.getTitle());

                    // 设置点击事件
                    final MenuNavsBean finalMenuNav = menuNav;
                    buttonView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
//                            if (NoDoubleClickUtils.isDoubleClick()) return;

                            //                        ToastUtils.normal(finalMenuNav.getTitle());

                            // 确保Arguments不为null
                            if (getArguments() == null) {
                                setArguments(new Bundle());
                            }

                            // 处理不同类型的按钮点击
                            handleButtonClick(finalMenuNav);
                        }
                    });

                    // 添加到内部容器
                    innerContainer.addView(buttonView);
                }

                // 将内部容器添加到滚动视图
                horizontalScrollView.addView(innerContainer);

                // 将滚动视图添加到第二行容器
                secondRowContainer.addView(horizontalScrollView);
            }
        } else {
            secondRowContainer.setVisibility(View.GONE);
        }
    }

    /**
     * 处理按钮点击事件
     *
     * @param menuNav 按钮数据
     */
    private void handleButtonClick(MenuNavsBean menuNav) {
        // 检查是否已登录
//        if (!AccountHelper.isUserLogin()) {
//            ToastUtils.normal("请先登录");
//            startFragment(AccountHelper.showAccountLogin());
////            AccountHelper.checkLoginState();
//            return;
//        }
        if (!AccountHelper.checkLoginState()) {
            return;
        }
        // 使用StudyViewModel处理按钮点击
        if (studyViewModel != null) {
            LogUtils.e("NativeStudyFragment - 使用StudyViewModel处理按钮点击: " + menuNav.getTitle());
            studyViewModel.onMainNavFunctionClick(menuNav);
        } else {
            LogUtils.e("NativeStudyFragment - studyViewModel为空，无法处理按钮点击");
            ToastUtils.normal("系统繁忙，请稍后再试");
        }
    }

    /**
     * 更新VIP权益模块
     */
    @SuppressLint("SetTextI18n")
    private void updateVipRightsModule(HomeBean homeBean) {
        if (binding == null) {
            return;
        }

        // 获取VIP权益卡片视图
        androidx.cardview.widget.CardView vipCardView = binding.getRoot().findViewById(R.id.vip_card_view);
        if (vipCardView == null) {
            LogUtils.e("NativeStudyFragment - updateVipRightsModule - 找不到VIP权益卡片");
            return;
        }

        // 检查是否应该显示VIP权益卡片
        boolean shouldShowAds = homeBean.isShowAds();
        LogUtils.e("NativeStudyFragment - updateVipRightsModule - HomeBean中isShowAds值: " + shouldShowAds);

        // 如果isShowAds为false，隐藏VIP权益卡片并返回
        if (!shouldShowAds) {
            vipCardView.setVisibility(View.GONE);
            LogUtils.e("NativeStudyFragment - updateVipRightsModule - 根据isShowAds隐藏VIP权益卡片");
            return;
        } else {
            vipCardView.setVisibility(View.VISIBLE);
            LogUtils.e("NativeStudyFragment - updateVipRightsModule - 根据isShowAds显示VIP权益卡片");
        }

        // 获取MiddleAds数据
        MiddleAdsBean middleAds = homeBean.getMiddleAds();

        if (middleAds != null) {
            // 设置副标题(您可使用0道)
            if (!TextUtils.isEmpty(middleAds.getSubTitle())) {
                String displayText = middleAds.getTitle() + ":" + middleAds.getSubTitle();

                // 检查是否包含HTML标签
                if (displayText.contains("<") && displayText.contains(">")) {
                    // 使用Html.fromHtml处理HTML格式文本
                    binding.tvDes.setText(android.text.Html.fromHtml(displayText));
                    LogUtils.e("NativeStudyFragment - updateVipRightsModule - 更新HTML格式内容: " + displayText);
                } else {
                    // 普通文本直接设置
                    binding.tvDes.setText(displayText);
                    LogUtils.e("NativeStudyFragment - updateVipRightsModule - 更新普通文本内容: " + displayText);
                }
            }
            binding.btnBecomeVip.setOnClickListener(view ->
                    {
                        if (!AccountHelper.checkLoginState()) {
                            return;
                        }
                        // 检查用户登录状态
                        if (AccountHelper.isUserLogin()) {
                            if (middleAds.getUrl() != null && !middleAds.getUrl().isEmpty()) {
                                // 确保Arguments不为null
                                if (getArguments() == null) {
                                    setArguments(new Bundle());
                                }

                                // 跳转到URL链接
                                getArguments().putString("routh", "home");
                                getArguments().putString("url", middleAds.getUrl());
                                getArguments().putInt("payType", 1);
                                getArguments().putString("payRouthParams", com.zizhiguanjia.lib_base.config.PayRouthConfig.PAY_UPDATE);
                                startFragment(com.zizhiguanjia.lib_base.helper.CommonHelper.showCommonWeb());
                            } else {
                                // 如果URL为空，使用默认的购买对话框
                                MessageHelper.openNoPremissBuyDialog(getActivity(), true, "PAY_UPDATE");
                            }
                        } else {
                            // 直接跳转到登录页面
                            ToastUtils.normal("请先登录");
                            startFragment(AccountHelper.showAccountLogin());
                            AccountHelper.checkLoginState();
                        }
                    }
            );

            LogUtils.e("VIP权益模块 - 标题: " + middleAds.getTitle() + ", 副标题: " + middleAds.getSubTitle());
        } else {
            LogUtils.e("VIP权益模块 - 无MiddleAds数据");
        }
    }
    /**
     * 初始化AI学习助手模块UI
     */
    private void initAILearningAssistantUI() {
        LogUtils.e("NativeStudyFragment - initAILearningAssistantUI - 初始化AI学习助手UI");

        // 设置默认值
        updateAILearningAssistantUI(0, 0, 0, 0, 0, 0);
    }

    /**
     * 更新AI学习助手模块UI
     */
    private void updateAILearningAssistantUI(int studyMinutes, int examScore, int knowledgeMastery,
                                           int studyDays, int currentCount, int totalCount) {
        updateAILearningAssistantUI(studyMinutes, examScore, knowledgeMastery, studyDays, currentCount, totalCount, 0);
    }

    /**
     * 更新AI学习助手模块UI（带进度百分比）
     */
    private void updateAILearningAssistantUI(int studyMinutes, int examScore, int knowledgeMastery,
                                           int studyDays, int currentCount, int totalCount, int progressPercent) {
        try {
            // 学习时长
            TextView tvStudyTime = binding.getRoot().findViewById(R.id.tv_study_time);
            if (tvStudyTime != null) {
                tvStudyTime.setText("学习时长: " + studyMinutes + "分钟");
                LogUtils.e("NativeStudyFragment - 更新学习时长: " + studyMinutes + "分钟");
            }

            // 最新模考分数
            TextView tvExamScore = binding.getRoot().findViewById(R.id.tv_exam_score);
            if (tvExamScore != null) {
                tvExamScore.setText(String.valueOf(examScore));
                LogUtils.e("NativeStudyFragment - 更新模考分数: " + examScore + "分");
            }

            // 知识掌握度
            TextView tvKnowledgeMastery = binding.getRoot().findViewById(R.id.tv_knowledge_mastery);
            if (tvKnowledgeMastery != null) {
                tvKnowledgeMastery.setText(String.valueOf(knowledgeMastery));
                LogUtils.e("NativeStudyFragment - 更新知识掌握度: " + knowledgeMastery + "%");
            }

            // 坚持学习天数
            TextView tvStudyDays = binding.getRoot().findViewById(R.id.tv_study_days);
            if (tvStudyDays != null) {
                tvStudyDays.setText(studyDays + "天");
                LogUtils.e("NativeStudyFragment - 更新坚持学习天数: " + studyDays + "天");
            }

            // 题目进度
            TextView tvNum = binding.getRoot().findViewById(R.id.tv_num);
            if (tvNum != null) {
                tvNum.setText(currentCount + "/" + totalCount+"题");
                LogUtils.e("NativeStudyFragment - 更新题目进度: " + currentCount + "/" + totalCount);
            }

            // 更新进度环 - 优先使用 QuestionMakingProgress，否则计算
            int finalProgress = progressPercent;
            if (finalProgress <= 0 && totalCount > 0) {
                finalProgress = (int) (((float) currentCount / totalCount) * 100);
            }
            updateProgressCircle(finalProgress);

        } catch (Exception e) {
            LogUtils.e("NativeStudyFragment - updateAILearningAssistantUI - 更新UI异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 更新进度环
     */
    private void updateProgressCircle(int progressPercent) {
        try {
            com.zizhiguanjia.model_home.view.CustomProgressView progressCircular =
                binding.getRoot().findViewById(R.id.progress_circular);
            if (progressCircular != null) {
                // 确保进度值在0-100范围内
                int safeProgress = Math.max(0, Math.min(100, progressPercent));

                // CustomProgressView 需要设置最大值为100，进度为百分比
                progressCircular.setMax(100);
                progressCircular.setProgress(safeProgress);

                // 刷新视图
                progressCircular.invalidate();

                LogUtils.e("NativeStudyFragment - 更新进度环: " + safeProgress + "%");
            }
        } catch (Exception e) {
            LogUtils.e("NativeStudyFragment - updateProgressCircle - 更新进度环异常: " + e.getMessage());
        }
    }

    /**
     * 从HomeBean数据更新AI学习助手模块
     */
    private void updateAILearningAssistantFromHomeBean(HomeBean homeBean) {
        if (homeBean == null) {
            LogUtils.e("NativeStudyFragment - updateAILearningAssistantFromHomeBean - homeBean为空");
            return;
        }

        LogUtils.e("NativeStudyFragment - updateAILearningAssistantFromHomeBean - 开始更新AI学习助手数据");

        try {
            // 获取基础数据
            int studyMinutes = homeBean.getStudyMinutes();  // 学习时间（分钟）- int类型
            int studyDays = homeBean.getStudyDays();        // 坚持学习天数 - int类型
            int currentCount = homeBean.getAnswerNumber();  // 已完成题目数
            int totalCount = homeBean.getTotalNumber();     // 总题目数

            // 解析最新模考分数 (ExamScores - string)
            int examScore = 0;
            if (homeBean.getExamScores() != null && !homeBean.getExamScores().isEmpty()) {
                try {
                    String scoreStr = homeBean.getExamScores().trim();
                    examScore = Integer.parseInt(scoreStr);
                    LogUtils.e("NativeStudyFragment - 解析模考分数成功: " + examScore);
                } catch (NumberFormatException e) {
                    LogUtils.e("NativeStudyFragment - 解析模考分数失败: " + homeBean.getExamScores());
                    examScore = 0;
                }
            }

            // 解析知识掌握度 (MasteryPercent - string)
            int knowledgeMastery = 0;
            if (homeBean.getMasteryPercent() != null && !homeBean.getMasteryPercent().isEmpty()) {
                try {
                    // 移除可能的百分号和其他非数字字符
                    String masteryStr = homeBean.getMasteryPercent().trim()
                            .replace("%", "")
                            .replace("％", "")  // 全角百分号
                            .replaceAll("[^0-9.]", "");  // 只保留数字和小数点

                    if (!masteryStr.isEmpty()) {
                        // 如果包含小数点，先转为double再转int
                        if (masteryStr.contains(".")) {
                            knowledgeMastery = (int) Double.parseDouble(masteryStr);
                        } else {
                            knowledgeMastery = Integer.parseInt(masteryStr);
                        }
                    }
                    LogUtils.e("NativeStudyFragment - 解析知识掌握度成功: " + knowledgeMastery + "% (原始值: " + homeBean.getMasteryPercent() + ")");
                } catch (NumberFormatException e) {
                    LogUtils.e("NativeStudyFragment - 解析知识掌握度失败: " + homeBean.getMasteryPercent());
                    knowledgeMastery = 0;
                }
            }

            // 获取进度条百分比 (QuestionMakingProgress - string)
            int progressPercent = 0;
            if (homeBean.getQuestionMakingProgress() != null && !homeBean.getQuestionMakingProgress().isEmpty()) {
                try {
                    // 移除百分号和其他非数字字符
                    String progressStr = homeBean.getQuestionMakingProgress().trim()
                            .replace("%", "")
                            .replace("％", "")  // 全角百分号
                            .replaceAll("[^0-9.]", "");  // 只保留数字和小数点

                    if (!progressStr.isEmpty()) {
                        // 如果包含小数点，先转为double再转int
                        if (progressStr.contains(".")) {
                            progressPercent = (int) Double.parseDouble(progressStr);
                        } else {
                            progressPercent = Integer.parseInt(progressStr);
                        }
                    }
                    LogUtils.e("NativeStudyFragment - 解析进度百分比成功: " + progressPercent + "% (原始值: " + homeBean.getQuestionMakingProgress() + ")");
                } catch (NumberFormatException e) {
                    LogUtils.e("NativeStudyFragment - 解析进度百分比失败: " + homeBean.getQuestionMakingProgress());
                    // 如果解析失败，根据题目数量计算
                    if (totalCount > 0) {
                        progressPercent = (int) (((float) currentCount / totalCount) * 100);
                    }
                }
            } else if (totalCount > 0) {
                // 如果没有进度百分比数据，根据题目数量计算
                progressPercent = (int) (((float) currentCount / totalCount) * 100);
            }

            LogUtils.e("NativeStudyFragment - AI学习助手完整数据: " +
                    "学习时间=" + studyMinutes + "分钟, " +
                    "模考分数=" + examScore + "分, " +
                    "知识掌握度=" + knowledgeMastery + "%, " +
                    "学习天数=" + studyDays + "天, " +
                    "题目进度=" + currentCount + "/" + totalCount + ", " +
                    "进度百分比=" + progressPercent + "%");

            // 更新UI
            updateAILearningAssistantUI(studyMinutes, examScore, knowledgeMastery,
                                      studyDays, currentCount, totalCount, progressPercent);

        } catch (Exception e) {
            LogUtils.e("NativeStudyFragment - updateAILearningAssistantFromHomeBean - 异常: " + e.getMessage());
            e.printStackTrace();

            // 异常时显示默认值
            updateAILearningAssistantUI(0, 0, 0, 0, 0, 0);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        refreshData();
    }

    /**
     * 刷新首页数据
     */
    private void refreshHomeData() {
        try {
            LogUtils.e("NativeStudyFragment - refreshHomeData - 开始刷新首页数据");

            // 重新获取首页数据
            fetchRealHomeData();

            // 如果StudyViewModel存在，调用其刷新方法
            if (studyViewModel != null) {
                studyViewModel.refreshData();
                LogUtils.e("NativeStudyFragment - refreshHomeData - 调用StudyViewModel.refreshData()");
            }

        } catch (Exception e) {
            LogUtils.e("NativeStudyFragment - refreshHomeData - 刷新数据异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void testToExam(){
//        initArguments().putString("title","智能练习");
//        initArguments().putBoolean("restart",false);
//        initArguments().putString("paperType", "21");
////        initArguments().putString("paperId","21");
//        startFragment(CoreExamHelper.mainPage(getContext()));
//
        startFragment(new BaselineAssessmentFragment());
    }
}