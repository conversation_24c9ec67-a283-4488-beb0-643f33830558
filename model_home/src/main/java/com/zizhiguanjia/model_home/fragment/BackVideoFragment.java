package com.zizhiguanjia.model_home.fragment;

import android.os.Bundle;

import androidx.fragment.app.FragmentTransaction;

import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.model_home.R;

public class BackVideoFragment extends BaseFragment {
    private FragmentTransaction fragmentManager;
    @Override
    public int initLayoutResId() {
        return R.layout.home_frame_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {


    }

    @Override
    public void onVisible() {
        super.onVisible();
        if(fragmentManager==null){
            fragmentManager = getActivity().getSupportFragmentManager().beginTransaction();
            fragmentManager.add(R.id.main_framelayout, HomeFragment.getInstance("/avd"));
            fragmentManager.commit();
        }
    }
}
