package com.zizhiguanjia.model_home.fragment;

import android.os.Bundle;
import android.os.Handler;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;

import com.binioter.guideview.Component;
import com.binioter.guideview.Guide;
import com.binioter.guideview.GuideBuilder;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CarseHelper;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.TaskHelper;
import com.zizhiguanjia.lib_base.listeners.CoupoinValidationListenter;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.CertificateMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.SdkMsgTypeConfig;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.bean.HomeShaperBean;
import com.zizhiguanjia.model_home.bean.MenuNavsBean;
import com.zizhiguanjia.model_home.databinding.HomeFlutterStyleLayoutBinding;
import com.zizhiguanjia.model_home.listener.IHomeFragment;
import com.zizhiguanjia.model_home.listener.INativeStudyFragment;
import com.zizhiguanjia.model_home.navigator.HomeFlutterNavigator;
import com.zizhiguanjia.model_home.report.HomeMsgReport;
import com.zizhiguanjia.model_home.ui.HomeNativeActivity;
import com.zizhiguanjia.model_home.view.CustomGuideView;
import com.zizhiguanjia.model_home.view.NativeStudyAIBannerHelper;
import com.zizhiguanjia.model_home.view.NativeStudyAILiveCoursesManager;
import com.zizhiguanjia.model_home.view.NativeStudyAIMenuManager;
import com.zizhiguanjia.model_home.view.NativeStudyAIShareNavManager;
import com.zizhiguanjia.model_home.view.NativeStudyAIStudyPlanManager;
import com.zizhiguanjia.model_home.view.NativeStudyAITrialRightsManager;
import com.zizhiguanjia.model_home.view.NativeStudyAIViewManager;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;

import java.util.ArrayList;
import java.util.List;

import io.flutter.embedding.android.FlutterView;
import io.flutter.plugin.common.MethodChannel;

/**
 * Flutter风格的原生学习页面Fragment
 * 负责显示与Flutter页面相同风格的原生Android学习界面
 */
public class NativeStudyAIFragment extends BaseFragment implements HomeFlutterNavigator {
    private HomeFlutterStyleLayoutBinding binding;
    private INativeStudyFragment listener;
    private IHomeFragment iHomeFragment;
    // 视图管理器
    private NativeStudyAIViewManager viewManager;
    private NativeStudyAIBannerHelper bannerHelper;
    private NativeStudyAIMenuManager menuManager;
    private NativeStudyAILiveCoursesManager liveCoursesManager;
    private NativeStudyAIStudyPlanManager studyPlanManager;
    private NativeStudyAITrialRightsManager trialRightsManager;
    private NativeStudyAIShareNavManager shareNavManager;

    @BindViewModel
    StudyViewModel studyViewModel;

    public static NativeStudyAIFragment newInstance() {
        return new NativeStudyAIFragment();
    }

    @Override
    public int initLayoutResId() {
        return R.layout.home_flutter_style_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();

        // 初始化StudyViewModel
        if (studyViewModel == null) {
            studyViewModel = new StudyViewModel();
        }

        // 设置Activity引用，用于处理页面跳转
        studyViewModel.setActivity(getActivity());

        // 设置导航器
        if (getActivity() instanceof HomeNativeActivity) {
            studyViewModel.setNavigator((HomeNativeActivity) getActivity());
            studyViewModel.init((HomeNativeActivity) getActivity());
        }

        binding.setStudyModel(studyViewModel);

        // 初始化视图管理器
        initViewManagers();

        // 在这里立即初始化观察者，确保在数据加载前就设置好了
        initObservers();

        // 设置点击事件
        setupClickListeners();

        // 更新顶部标题
        viewManager.updateTopTitle();

        // 初始化章节练习UI
        viewManager.initChapterPracticeUI();

        // 请求首页数据，包括Banner数据
        studyViewModel.getIndexDatas(null, false);

        // 如果已经有菜单导航数据，立即更新UI
        List<MenuNavsBean> existingMenuNavs = studyViewModel.getMenuNavs().getValue();
        if (existingMenuNavs != null && !existingMenuNavs.isEmpty()) {
            LogUtils.e("NativeStudyAIFragment - initView - 使用现有菜单导航数据更新UI，数量: " + existingMenuNavs.size());
            menuManager.updateMenuNavsView(existingMenuNavs);
        } else {
            LogUtils.e("NativeStudyAIFragment - initView - 暂无菜单导航数据");
        }

        // 如果已经有底部分享按钮数据，立即更新UI
        List<HomeShaperBean> existingShareNavs = studyViewModel.getShareNavs().getValue();
        if (existingShareNavs != null && !existingShareNavs.isEmpty()) {
            LogUtils.e("NativeStudyAIFragment - initView - 使用现有底部分享按钮数据更新UI，数量: " + existingShareNavs.size());
            // ShareNavManager会自动通过观察者更新，但为了确保，我们再次手动触发ViewModel更新
            studyViewModel.updateShareNavs(existingShareNavs);
        } else {
            LogUtils.e("NativeStudyAIFragment - initView - 暂无底部分享按钮数据");
        }

        LogUtils.e("NativeStudyAIFragment初始化完成");
        checkPremiss();

        // 检查是否已显示过引导页，只在首次使用时显示
        boolean hasShownGuide = KvUtils.get("main_guilder", false);
        LogUtils.e("NativeStudyAIFragment - initView - 是否已显示过引导页: " + hasShownGuide);
        if (!hasShownGuide) {
            showGuide();
        } else {
            LogUtils.e("NativeStudyAIFragment - initView - 已显示过引导页，不再显示");
        }
        binding.tvExame.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                testToExam();
            }
        });
    }

    /**
     * 初始化各个视图管理器
     */
    private void initViewManagers() {
        // 通用视图管理器
        viewManager = new NativeStudyAIViewManager(binding, studyViewModel, getActivity(), this);

        // Banner管理器
        bannerHelper = new NativeStudyAIBannerHelper(binding, studyViewModel, getActivity(), this);
        bannerHelper.initBanner();

        // 菜单导航管理器
        menuManager = new NativeStudyAIMenuManager(binding, studyViewModel, getActivity(), this);

        // 通关精讲课管理器
        liveCoursesManager = new NativeStudyAILiveCoursesManager(binding, studyViewModel, getActivity(), this);

        // 学习计划管理器
        studyPlanManager = new NativeStudyAIStudyPlanManager(binding, studyViewModel, getActivity(), this);

        // 试用权益管理器
        trialRightsManager = new NativeStudyAITrialRightsManager(binding, studyViewModel, getActivity(), this);

        // 底部分享导航管理器
        shareNavManager = new NativeStudyAIShareNavManager(binding, studyViewModel, getActivity(), this);

        // 初始化通知栏
        viewManager.initNotify();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 添加额外的日志输出
        LogUtils.e("NativeStudyAIFragment - onViewCreated - 视图创建完成");

        // 确保studyViewModel已初始化
        if (studyViewModel == null) {
            LogUtils.e("NativeStudyAIFragment - onViewCreated - 初始化studyViewModel");
            studyViewModel = new StudyViewModel();
            studyViewModel.setActivity(getActivity());
        }

        // 添加明确的日志，表示要初始化观察者
        LogUtils.e("NativeStudyAIFragment - onViewCreated - 准备初始化观察者");

        // 再次初始化观察者，确保在onViewCreated中也调用了
        initObservers();
    }

    public void setiHomeFragment(IHomeFragment iHomeFragment) {
        this.iHomeFragment = iHomeFragment;
    }

    @Override
    public void initViewData() {
        // 初始化StudyViewModel数据
        if (studyViewModel != null) {
            studyViewModel.initData();
            LogUtils.e("StudyViewModel初始化完成");
        } else {
            LogUtils.e("StudyViewModel为空，无法初始化数据");
        }
    }

    /**
     * 设置点击事件
     */
    private void setupClickListeners() {
        if (binding == null) {
            LogUtils.e("setupClickListeners - binding为null，无法设置点击事件");
            return;
        }

        LogUtils.e("开始设置Flutter风格原生页面点击事件");

        try {
            // 开始练习按钮点击事件
            binding.btnStartPractice.setOnClickListener(v -> {
//                if (!com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils.isDoubleClick()) {
                // 检查用户登录状态
                if (AccountHelper.checkLoginState()) {
//                        com.wb.lib_utils.utils.ToastUtils.normal(studyViewModel.hasStartedChapterPractice() ? "继续章节练习" : "开始章节练习");
                    com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamList();
                    android.os.Bundle args = new android.os.Bundle();
                    LogUtils.i("跳转类型：" + studyViewModel.getPaperType().getValue());
                    int index = 0;
                    if (java.util.Objects.equals(studyViewModel.getHomeBean().getValue().getPaperType(), "5")) {
                        index = 1;
                    }
                    args.putInt("index", index);
                    setArguments(args);
                    startFragment(fragment);
                } else {
                    // 直接跳转到登录页面
//                        startFragment(AccountHelper.showAccountLogin());
                }
//                }
            });
        } catch (Exception e) {
            LogUtils.e("设置点击事件出错: " + e.getMessage() + "\n" + e.toString());
        }
    }

    @Override
    public void initObservable() {
        // 添加消息总线监听
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if (msgEvent.getCode() == CertificateMsgTypeConfig.CERTIFICATE_MSG_BIND_SUCCESS ||
                        msgEvent.getCode() == 10106) { // 10106 对应 CertificateTypeConfig.MSG_TYPE_FINSH_ADDRESS_MAJOR 常量，表示地区和科目选择完成
                    // 证书绑定成功，更新顶部标题和刷新数据
                    LogUtils.e("NativeStudyAIFragment - 收到证书绑定成功消息，更新UI");
                    viewManager.updateTopTitle();
//                    refreshData();
                } else if (msgEvent.getCode() == SdkMsgTypeConfig.SDK_MSG_RESHLOGIN) {
                    startFragment(AccountHelper.showAccountLogin());
                } else if (msgEvent.getCode() == AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS) {
                    refreshData();
                } else if (msgEvent.getCode() == AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT) {
                    refreshData();
                } else {
                    HomeMsgReport.getInstance().getMsgChanner().busMsgChanner(msgEvent.getCode(), msgEvent.getMsg(), studyViewModel.IMsgListener);
                }
            }
        });
    }

    /**
     * 设置监听器
     */
    public void setListener(INativeStudyFragment listener) {
        this.listener = listener;
    }

    public void updateHomeData(HomeBean homeBean) {
        if (homeBean == null) return;

        LogUtils.e("NativeStudyFragment - updateHomeData - 开始更新首页数据");
    }

    /**
     * 刷新数据
     */
    public void refreshData() {
        LogUtils.e("NativeStudyAIFragment - refreshData - 开始刷新数据");

        // 显示刷新动画
        viewManager.showRefreshing();

        // 请求最新数据
        if (studyViewModel != null) {
            // 先重置数据，确保UI会刷新
            studyViewModel.resetChapterPractice();

            // 请求首页数据
            studyViewModel.getIndexDatas(null, false);

            // 强制更新UI
            new Handler().postDelayed(() -> {
                // 更新顶部标题
                viewManager.updateTopTitle();

                // 更新章节练习UI
                viewManager.initChapterPracticeUI();

                // 直接从HomeBean获取最新数据进行更新
                HomeBean homeBean = studyViewModel.getHomeBean().getValue();
                if (homeBean != null) {
                    // 强制更新学习模块数据
                    LogUtils.e("NativeStudyAIFragment - refreshData - 强制更新学习模块数据");
                    viewManager.updateChapterPracticeData(homeBean);

                    // 强制更新试用权益
                    if (homeBean.getMiddleAds() != null) {
                        LogUtils.e("NativeStudyAIFragment - refreshData - 强制更新试用权益");
                        trialRightsManager.updateTrialRightsView(homeBean.getMiddleAds());
                    }

                    // 强制更新学习计划
                    LogUtils.e("NativeStudyAIFragment - refreshData - 强制更新学习计划");
                    studyPlanManager.updateStudyPlanView(homeBean);

                    // 强制更新通关精讲课
                    if (homeBean.getLives() != null && homeBean.getLives().getItems() != null) {
                        LogUtils.e("NativeStudyAIFragment - refreshData - 强制更新通关精讲课");
                        liveCoursesManager.updateLivesView(homeBean.getLives().getItems());
                    }
                }

                // 更新用户头像
                viewManager.updateUserLoginAvatar();

                // 手动更新菜单导航 - 强制刷新UI时显式调用
                List<MenuNavsBean> currentMenuNavs = studyViewModel.getMenuNavs().getValue();
                if (currentMenuNavs != null && !currentMenuNavs.isEmpty()) {
                    LogUtils.e("NativeStudyAIFragment - refreshData - 强制更新菜单导航，数量: " + currentMenuNavs.size());
                    menuManager.updateMenuNavsView(currentMenuNavs);
                } else {
                    LogUtils.e("NativeStudyAIFragment - refreshData - 无法强制更新菜单导航，数据为空");
                }

                // 手动更新底部分享按钮
                List<HomeShaperBean> currentShareNavs = studyViewModel.getShareNavs().getValue();
                if (currentShareNavs != null && !currentShareNavs.isEmpty()) {
                    LogUtils.e("NativeStudyAIFragment - refreshData - 强制更新底部分享按钮，数量: " + currentShareNavs.size());
                    shareNavManager.updateShareNavs(currentShareNavs);
                }

                // 隐藏刷新动画
                viewManager.hideRefreshing();

                LogUtils.e("NativeStudyAIFragment - refreshData - 数据刷新完成，UI已更新");
            }, 800); // 延迟800ms确保数据已加载
        } else {
            // 隐藏刷新动画
            viewManager.hideRefreshing();

            LogUtils.e("NativeStudyAIFragment - refreshData - studyViewModel为空，无法刷新数据");
        }
    }

    /**
     * 模拟完成一道题目（用于测试）
     *
     * @param correct 是否回答正确
     */
    public void completeQuestion(boolean correct) {
        if (studyViewModel == null) return;

        // 更新ViewModel中的数据
        studyViewModel.completeOneQuestion(correct);

        // 更新UI显示
        viewManager.initChapterPracticeUI();
    }

    /**
     * 初始化观察者
     */
    private void initObservers() {
        if (studyViewModel == null) {
            LogUtils.e("NativeStudyAIFragment - initObservers - studyViewModel为空，无法初始化观察者");
            return;
        }

        LogUtils.e("NativeStudyAIFragment - initObservers - 开始初始化观察者 - " + System.currentTimeMillis());

        // 观察HomeBean数据
        studyViewModel.getHomeBean().observe(getViewLifecycleOwner(), homeBean -> {
            LogUtils.e("NativeStudyAIFragment - HomeBean观察者触发 - " + (homeBean != null ? "数据不为空" : "数据为空"));
            if (homeBean != null) {
                LogUtils.e("NativeStudyAIFragment - 收到HomeBean数据");

                // 更新章节练习数据
                viewManager.updateChapterPracticeData(homeBean);

                // 不再需要在这里更新试用权益，因为TrialRightsManager已经设置了自己的观察者
                /*
                // 更新试用权益
                if (homeBean.getMiddleAds() != null) {
                    trialRightsManager.updateTrialRightsView(homeBean.getMiddleAds());
                }
                */

                // 更新菜单导航 - 从HomeBean中获取并直接更新
                if (homeBean.getMenuNavs() != null) {
                    LogUtils.e("NativeStudyAIFragment - 直接从HomeBean更新菜单导航，数量: " + homeBean.getMenuNavs().size());
                    // 注意：这里不再需要调用menuManager.updateMenuNavsView，
                    // 因为我们在StudyViewModel.updateFromHomeBean中已经调用了updateMenuNavs，
                    // 并且MenuManager已经设置了观察者，会自动更新UI

                    // 为了确保更新成功，触发一次ViewModel中的菜单导航更新
                    studyViewModel.updateMenuNavs(homeBean.getMenuNavs());
                }

                // 直接从HomeBean更新底部分享按钮
                if (homeBean.getShareNavs() != null) {
                    LogUtils.e("NativeStudyAIFragment - 直接从HomeBean更新底部分享按钮，数量: " + homeBean.getShareNavs().size());
                    // 为了确保更新成功，直接从HomeBean获取数据并触发更新
                    studyViewModel.updateShareNavs(homeBean.getShareNavs());

                    // 为了防止某些情况下观察者未触发，延迟1秒后再次尝试触发
                    new Handler().postDelayed(() -> {
                        LogUtils.e("NativeStudyAIFragment - 延迟触发底部分享按钮更新");
                        studyViewModel.updateShareNavs(homeBean.getShareNavs());
                    }, 1000);
                }

                // 控制通关精讲课模块的显示与隐藏
                View liveCourseContainer = binding.llLive;
                Integer majorPid = homeBean.getMajorPid();
                boolean shouldShowLiveCourse = (majorPid != null && majorPid == 1100);
                liveCourseContainer.setVisibility(shouldShowLiveCourse ? View.VISIBLE : View.GONE);
                LogUtils.e("NativeStudyFragment - updateHomeData - MajorPid: " + majorPid +
                        ", 设置通关精讲课显示状态: " + (!shouldShowLiveCourse ? "显示" : "隐藏"));
            }
        });

        // 不再需要这个观察者，因为MenuManager已经设置了自己的观察者
        // 这样避免重复更新UI
        /*
        studyViewModel.getMenuNavs().observe(getViewLifecycleOwner(), menuNavs -> {
            LogUtils.e("NativeStudyAIFragment - MenuNavs观察者触发 - " + (menuNavs != null ? "数据不为空" : "数据为空"));
            // 处理菜单导航数据
            if (menuNavs != null) {
                LogUtils.e("NativeStudyAIFragment - 观察到菜单导航数据变化，数量: " + menuNavs.size());
                menuManager.updateMenuNavsView(menuNavs);
            } else {
                LogUtils.e("NativeStudyAIFragment - 观察到菜单导航数据变化，但数据为null");
            }
        });
        */

        // 观察paperTitle
        studyViewModel.getPaperTitle().observe(getViewLifecycleOwner(), title -> {
            LogUtils.e("NativeStudyAIFragment - PaperTitle观察者触发 - " + (title != null ? "数据不为空" : "数据为空"));
            LogUtils.e("NativeStudyAIFragment - 观察到paperTitle变化: " + title);
            if (title != null && !title.isEmpty()) {
                // 更新UI显示
                if (binding != null && binding.title != null) {
                    binding.title.setText(title);
                }
            }
        });

        // 手动设置值来测试观察者是否正常工作
        new Handler().postDelayed(() -> {
            LogUtils.e("NativeStudyAIFragment - 尝试手动触发观察者 - " + System.currentTimeMillis());
            if (studyViewModel != null) {
                // 获取当前值
                String currentTitle = studyViewModel.getPaperTitle().getValue();
                // 不直接调用setValue，而是使用ViewModel中的方法更新值
                studyViewModel.updatePaperTitle(currentTitle);
                LogUtils.e("NativeStudyAIFragment - 手动设置paperTitle值: " + currentTitle);
            }
        }, 1000); // 延迟1秒
    }

    public void checkPremiss() {
        CarseHelper.uploadCarse();
    }

    @Override
    public void onPause() {
        super.onPause();
        LogUtils.e("NativeStudyAIFragment - onPause");

        // 暂停Banner轮播
        if (bannerHelper != null) {
            bannerHelper.onPause();
        }

        // 检查是否需要刷新数据
        checkAndRefreshData();

        // 检查本地优惠券
        checkLocalCoupon();

        // 检查底部分享按钮是否为空，如果为空则生成模拟数据
        new Handler().postDelayed(() -> {
            List<HomeShaperBean> existingShareNavs = studyViewModel.getShareNavs().getValue();
            if (existingShareNavs == null || existingShareNavs.isEmpty()) {
                LogUtils.e("NativeStudyAIFragment - onResume - 底部分享按钮数据为空，生成模拟数据");
                createMockShareNavData();
            }
        }, 1500); // 延迟1.5秒，确保初始化已经完成
    }

    @Override
    public void onResume() {
        super.onResume();
        LogUtils.e("NativeStudyAIFragment - onResume");

        // 恢复Banner轮播
        if (bannerHelper != null) {
            bannerHelper.onResume();
        }

        // 检查是否需要刷新数据
        checkAndRefreshData();

        // 检查本地优惠券
        checkLocalCoupon();

        // 检查底部分享按钮是否为空，如果为空则生成模拟数据
        new Handler().postDelayed(() -> {
            List<HomeShaperBean> existingShareNavs = studyViewModel.getShareNavs().getValue();
            if (existingShareNavs == null || existingShareNavs.isEmpty()) {
                LogUtils.e("NativeStudyAIFragment - onResume - 底部分享按钮数据为空，生成模拟数据");
                createMockShareNavData();
            }
        }, 1500); // 延迟1.5秒，确保初始化已经完成
    }

    @Override
    public void onDestroy() {
        // 清理Banner轮播资源
        if (bannerHelper != null) {
            bannerHelper.onDestroy();
        }
        
        super.onDestroy();
        LogUtils.e("NativeStudyAIFragment - onDestroy");
    }

    /**
     * 生成模拟的分享按钮数据
     */
    private void createMockShareNavData() {
        LogUtils.e("NativeStudyAIFragment - createMockShareNavData - 开始生成模拟数据");
        List<HomeShaperBean> mockShareNavs = new ArrayList<>();

        // 添加微信好友按钮
        HomeShaperBean wechatShareNav = new HomeShaperBean();
        wechatShareNav.setTitle("微信好友");
        wechatShareNav.setNavType(1);
        wechatShareNav.setShareData("mock_wechat_data");
        wechatShareNav.setAtWork(true);
        mockShareNavs.add(wechatShareNav);

        // 添加朋友圈按钮
        HomeShaperBean momentShareNav = new HomeShaperBean();
        momentShareNav.setTitle("朋友圈");
        momentShareNav.setNavType(2);
        momentShareNav.setShareData("mock_moment_data");
        momentShareNav.setAtWork(true);
        mockShareNavs.add(momentShareNav);

        // 添加拨打电话按钮
        HomeShaperBean phoneShareNav = new HomeShaperBean();
        phoneShareNav.setTitle("拨打电话");
        phoneShareNav.setNavType(3);
        phoneShareNav.setShareData("https://www.example.com/service"); // 使用有效的URL作为ShareData
        phoneShareNav.setAtWork(false); // 设置为false，模拟非工作时间
        mockShareNavs.add(phoneShareNav);

        // 添加在线客服按钮
        HomeShaperBean serviceShareNav = new HomeShaperBean();
        serviceShareNav.setTitle("在线客服");
        serviceShareNav.setNavType(4);
        serviceShareNav.setShareData("https://www.example.com/service");
        serviceShareNav.setAtWork(true);
        mockShareNavs.add(serviceShareNav);

        // 更新底部分享按钮数据
        LogUtils.e("NativeStudyAIFragment - createMockShareNavData - 更新底部分享按钮数据，数量: " + mockShareNavs.size());
        studyViewModel.updateShareNavs(mockShareNavs);
    }

    /**
     * 检查是否需要刷新数据
     * 在区域切换后或其他需要刷新的场景调用
     */
    private void checkAndRefreshData() {
        // 获取当前区域和科目信息
        String currentArea = com.zizhiguanjia.lib_base.helper.CertificateHelper.getCurrentCertificateAddressName();
        // 由于getCurrentCertificateName不存在，使用initCertificate方法获取科目名称
        String currentSubject = com.zizhiguanjia.lib_base.helper.CertificateHelper.initCertificate(AccountHelper.isUserLogin());

        // 获取上次记录的区域和科目信息
        String lastArea = KvUtils.get("last_selected_area", "");
        String lastSubject = KvUtils.get("last_selected_subject", "");

        LogUtils.e("NativeStudyAIFragment - checkAndRefreshData - 当前区域: " + currentArea +
                ", 当前科目: " + currentSubject +
                ", 上次区域: " + lastArea +
                ", 上次科目: " + lastSubject);

        // 如果区域或科目发生变化，刷新数据
        if (!currentArea.equals(lastArea) || !currentSubject.equals(lastSubject)) {
            LogUtils.e("NativeStudyAIFragment - checkAndRefreshData - 区域或科目已变化，刷新数据");

            // 保存当前区域和科目信息
            KvUtils.save("last_selected_area", currentArea);
            KvUtils.save("last_selected_subject", currentSubject);

            // 刷新数据
            refreshData();

            // 更新顶部标题
            viewManager.updateTopTitle();
        }
    }

    public void checkLocalCoupon() {
        MainThreadUtils.postDelayed(new Runnable() {
            @Override
            public void run() {
                TaskHelper.detectionCoupon(new CoupoinValidationListenter() {
                    @Override
                    public void onCoupoinShow() {
                        LogUtils.e("优惠卷----->>>>2" + (iHomeFragment == null ? "yes" : "no"));
                        if (iHomeFragment == null) {
                            Bus.post(new MsgEvent(666666));
                        } else {
//                            iHomeFragment.clearAllActivity();
                        }
                    }
                });
            }
        }, 500L);
    }

    @Override
    public void showDyVipUpdateHintDialog(DyVipUpdateHintBean data) {
        // 实现方法
    }

    @Override
    public void successMethodChannel(MethodChannel methodChannel) {
        // 实现方法
    }

    @Override
    public void successFlutterView(FlutterView flutterView) {
        // 实现方法
    }

    @Override
    public void showToast(String msg) {
        // 实现方法
    }

    @Override
    public void reshHomeData(boolean passCertificate) {
        if (studyViewModel != null) {
            studyViewModel.reshHomeData(passCertificate);
        }
    }

    @Override
    public void setTopTitleInfo(boolean show, String title) {
        // 实现方法
    }

    @Override
    public String initRoute() {
        return "";
    }

    @Override
    public void showLoading(boolean visition, String msg) {
        // 实现方法
    }

    @Override
    public void showTsDialog(int type) {
        // 实现方法
    }

    @Override
    public void showGuide() {
        // 立即保存引导状态，确保不会再次显示
        KvUtils.save("main_guilder", true);
        LogUtils.e("NativeStudyAIFragment - showGuide - 已保存引导状态，下次不再显示");

        binding.vChapter.post(new Runnable() {
            @Override
            public void run() {
                try {
                    final GuideBuilder builder1 = new GuideBuilder();
                    builder1.setTargetView(binding.vChapter)
                            .setAlpha(150)
                            .setHighTargetCorner(DpUtils.dp2px(getContext(), 0))
                            .setHighTargetPaddingBottom(DpUtils.dp2px(getContext(), 0))
                            .setHighTargetGraphStyle(Component.ROUNDRECT);
                    builder1.setOnVisibilityChangedListener(new GuideBuilder.OnVisibilityChangedListener() {
                        @Override
                        public void onShown() {
                            // 引导页显示时的操作
                            LogUtils.e("NativeStudyAIFragment - showGuide - 引导页显示");
                        }

                        @Override
                        public void onDismiss() {
                            // 保存第一个引导页已显示的状态
                            LogUtils.e("NativeStudyAIFragment - showGuide - 引导页关闭，显示第二个引导页");

                            // 再次保存状态，确保无论如何都会保存
                            KvUtils.save("main_guilder", true);

                            if (iHomeFragment != null) {
                                iHomeFragment.showGuide2();
                            } else {
                                LogUtils.e("iHomeFragment is null, cannot show guide2");
                                // 如果无法显示第二个引导页，也要保存状态
                                KvUtils.save("main_guilder", true);
                            }
                        }
                    });

                    builder1.addComponent(new CustomGuideView());
                    Guide guide = builder1.createGuide();
                    guide.show(getActivity());
                } catch (Exception e) {
                    LogUtils.e("NativeStudyAIFragment - showGuide - 显示引导页异常: " + e.getMessage());
                    // 发生异常时也要保存状态，避免无限循环
                    KvUtils.save("main_guilder", true);
                }
            }
        });
    }

    @Override
    public void closeView() {
        // 实现方法
    }

    @Override
    public void toMainPage(String pid) {
        // 实现方法
    }
    private void testToExam(){
        initArguments().putString("title","智能练习");
        initArguments().putBoolean("restart",false);
        initArguments().putString("paperType", "21");
//        initArguments().putString("paperId","21");
        startFragment(CoreExamHelper.mainPage(getContext()));

    }
}