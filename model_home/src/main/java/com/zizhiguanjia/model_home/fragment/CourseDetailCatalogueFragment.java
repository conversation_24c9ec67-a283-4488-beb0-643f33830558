package com.zizhiguanjia.model_home.fragment;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseExpandableListAdapter;
import android.widget.EditText;
import android.widget.ExpandableListView;
import android.widget.TextView;

import com.caimuhao.rxpicker.RxPicker;
import com.caimuhao.rxpicker.bean.ImageItem;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_utils.permission.PermissionListener;
import com.wb.lib_utils.permission.PermissionUtils;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.model_common.imageLoader.GlideSelectImageLoader;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.adapter.CourseDetailCommentImageAdapter;
import com.zizhiguanjia.model_home.bean.CourseDetailCatalogueBean;
import com.zizhiguanjia.model_home.listener.CourseDetailCommentImageListener;
import com.zizhiguanjia.model_home.navigator.CourseDetailCatalogueNavigator;
import com.zizhiguanjia.model_home.navigator.CourseDetailNavigator;
import com.zizhiguanjia.model_home.viewmodel.CourseDetailCatalogueViewModel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.cardview.widget.CardView;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 功能作用：课程详情-商品信息fragment
 * 初始注释时间： 2023/11/23 10:47
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class CourseDetailCatalogueFragment extends BaseFragment implements CourseDetailCatalogueNavigator {
    @BindViewModel
    CourseDetailCatalogueViewModel mViewModel;
    private CourseDetailNavigator mCourseDetailNavigator;
    /**
     * 总的播放的
     */
    private CourseDetailCatalogueBean mUseBean;
    /**
     * 当前播放的
     */
    private CourseDetailCatalogueBean.Item mCurrentBean;
    /**
     * 标题区域
     */
    private CardView mCardView;
    /**
     * 标题描述
     */
    private AppCompatTextView mTitleDesc;
    /**
     * 列表适配器
     */
    private ExpandableListView mListView;

    /**
     * 评论按钮
     */
    private AppCompatButton mBtnComment;

    /**
     * 关闭按钮
     */
    private AppCompatImageButton mIvClose;

    /**
     * 评论滑动框
     */
    private NestedScrollView mNlComment;

    /**
     * 图片选择适配器
     */
    private CourseDetailCommentImageAdapter imageLookAdapter;

    /**
     * 评论图片列表
     */
    private RecyclerView mCommentImageList;

    /**
     * 评论提交按钮
     */
    private AppCompatButton mBtnCommentSubmit;

    /**
     * 评论输入空间
     */
    private EditText mEtCommentInput;

    public CourseDetailCatalogueFragment() {
    }

    public CourseDetailCatalogueFragment(CourseDetailNavigator courseDetailNavigator) {
        this.mCourseDetailNavigator = courseDetailNavigator;
    }

    @Override
    public int initLayoutResId() {
        return R.layout.home_couse_detail_catalogue_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        mViewModel.initParams(this, this);
        mCardView = this.findViewById(R.id.cd_title);
        mTitleDesc = this.findViewById(R.id.tv_title_sum);
        mListView = this.findViewById(R.id.list_view);
        mBtnComment = this.findViewById(R.id.btn_comment);
        mIvClose = this.findViewById(R.id.iv_close);
        mNlComment = this.findViewById(R.id.nl_comment);
        mCommentImageList = this.findViewById(R.id.gride_view);
        mBtnCommentSubmit = this.findViewById(R.id.btn_submit);
        mEtCommentInput = this.findViewById(R.id.common_faceback_msg_ed);
        mBtnComment.setOnClickListener(v -> {
            mNlComment.setVisibility(View.VISIBLE);
            mIvClose.setVisibility(View.VISIBLE);
            mListView.setVisibility(View.GONE);
            mBtnComment.setVisibility(View.GONE);
            resetCommentLevelSelect();
            resetInputAndImageSelect();
        });
        this.findViewById(R.id.btn_select_current).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                resetCommentLevelSelect();
                resetInputAndImageSelect();
            }
        });
        this.findViewById(R.id.btn_select_all).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                resetCommentLevelSelect();
                resetInputAndImageSelect();
            }
        });
        mIvClose.setOnClickListener(v -> {
            mNlComment.setVisibility(View.GONE);
            mIvClose.setVisibility(View.GONE);
            mListView.setVisibility(View.VISIBLE);
            mBtnComment.setVisibility(View.VISIBLE);
            //重置评论相关
            resetInputAndImageSelect();
            resetCommentLevelSelectOnClicks();
            resetCommentTypeSelectOnClicks();
        });
        //评论相关
        RxPicker.init(new GlideSelectImageLoader());
        resetInputAndImageSelect();
        resetCommentLevelSelectOnClicks();
        resetCommentTypeSelectOnClicks();
        mBtnCommentSubmit.setOnClickListener(v -> {
            //上传进度
            mCourseDetailNavigator.uploadPlayProgress();
            //提交数据
            mViewModel.submitCommentInfo();
        });
    }

    @Override
    public void initViewData() {
        super.initViewData();
        if(mUseBean == null){
            return;
        }
        mViewModel.setUseBean(mUseBean);
        mViewModel.setCurrentPlayBean(this.mCurrentBean);
        if (mUseBean.getVip()) {
            mCardView.setVisibility(View.VISIBLE);
            mTitleDesc.setText(mUseBean.getDesc());
        } else {
            mCardView.setVisibility(View.GONE);
        }
        //设置列表
        mListView.setAdapter(new BaseExpandableListAdapter() {
            @Override
            public int getGroupCount() {
                return mUseBean.getItems().size();
            }

            @Override
            public int getChildrenCount(int groupPosition) {
                return mUseBean.getItems().get(groupPosition).getChilds().size();
            }

            @Override
            public Object getGroup(int groupPosition) {
                return mUseBean.getItems().get(groupPosition);
            }

            @Override
            public Object getChild(int groupPosition, int childPosition) {
                return mUseBean.getItems().get(groupPosition).getChilds().get(childPosition);
            }

            @Override
            public long getGroupId(int groupPosition) {
                return groupPosition;
            }

            @Override
            public long getChildId(int groupPosition, int childPosition) {
                return childPosition;
            }

            @Override
            public boolean hasStableIds() {
                return false;
            }

            @SuppressLint("SetTextI18n")
            @Override
            public View getGroupView(int groupPosition, boolean isExpanded, View convertView, ViewGroup parent) {
                ViewHolderGroup viewHolderGroup;
                if (convertView == null) {
                    convertView = View.inflate(getContext(), R.layout.item_list_couse_detail_catalogue_group, null);
                    viewHolderGroup = new ViewHolderGroup(convertView);
                    convertView.setTag(viewHolderGroup);
                } else {
                    viewHolderGroup = (ViewHolderGroup) convertView.getTag();
                }
                CourseDetailCatalogueBean.Item group = (CourseDetailCatalogueBean.Item) getGroup(groupPosition);
                StringBuilder titleAppend = new StringBuilder();
                int index = groupPosition + 1;
                do {
                    switch (index % 10) {
                        case 0:
                            titleAppend.append("零", 0, 1);
                            break;
                        case 1:
                            titleAppend.append("一", 0, 1);
                            break;
                        case 2:
                            titleAppend.append("二", 0, 1);
                            break;
                        case 3:
                            titleAppend.append("三", 0, 1);
                            break;
                        case 4:
                            titleAppend.append("四", 0, 1);
                            break;
                        case 5:
                            titleAppend.append("五", 0, 1);
                            break;
                        case 6:
                            titleAppend.append("六", 0, 1);
                            break;
                        case 7:
                            titleAppend.append("七", 0, 1);
                            break;
                        case 8:
                            titleAppend.append("八", 0, 1);
                            break;
                        case 9:
                            titleAppend.append("九", 0, 1);
                            break;
                    }
                    index = index / 10;
                    if (index > 0) {
                        switch (titleAppend.length()) {
                            case 1:
                                titleAppend.append("十", 0, 1);
                                break;
                            case 2:
                                titleAppend.append("百", 0, 1);
                                break;
                            case 3:
                                titleAppend.append("千", 0, 1);
                                break;
                            case 4:
                                titleAppend.append("万", 0, 1);
                                break;
                        }
                    }
                } while (index > 0);
                viewHolderGroup.mTvTitle.setText("第" + titleAppend + "章 " + group.getTitle());
                titleAppend.setLength(0);
                // 根据展开状态显示箭头方向
                if (isExpanded) {
                    viewHolderGroup.mIvArrow.setRotation(180);
                } else {
                    viewHolderGroup.mIvArrow.setRotation(0);
                }
                return convertView;
            }

            @SuppressLint("SetTextI18n")
            @Override
            public View getChildView(int groupPosition, int childPosition, boolean isLastChild, View convertView, ViewGroup parent) {
                ViewHolderChild viewHolderChild;
                if (convertView == null) {
                    convertView = View.inflate(getContext(), R.layout.item_list_couse_detail_catalogue_child, null);
                    viewHolderChild = new ViewHolderChild(convertView);
                    convertView.setTag(viewHolderChild);
                } else {
                    viewHolderChild = (ViewHolderChild) convertView.getTag();
                }
                CourseDetailCatalogueBean.Item childBean = (CourseDetailCatalogueBean.Item) getChild(groupPosition, childPosition);
                //标题
                viewHolderChild.mTvTitle.setText("第" + childBean.getSort() + "节 " + childBean.getTitle());
                if (mViewModel.isPlayIng(childBean)) {
                    viewHolderChild.mTvTitle.setTextColor(Color.parseColor("#007AFF"));
                } else {
                    viewHolderChild.mTvTitle.setTextColor(Color.BLACK);
                }
                //时间
                viewHolderChild.mTvTime.setText(childBean.getTotalDuration());
                //是否学完
                if (mViewModel.checkFinish(childBean)) {
                    viewHolderChild.mIvState.setImageResource(R.drawable.course_detail_child_finish_y);
                } else {
                    viewHolderChild.mIvState.setImageResource(R.drawable.course_detail_child_finish_n);
                }
                //剩余问题
                String unDoQuestion = mViewModel.getUnDoQuestion(childBean);
                if (unDoQuestion == null) {
                    viewHolderChild.mIvQuestionArrow.setVisibility(View.GONE);
                    viewHolderChild.mTvQuestion.setVisibility(View.GONE);
                } else {
                    viewHolderChild.mIvQuestionArrow.setVisibility(View.VISIBLE);
                    viewHolderChild.mTvQuestion.setVisibility(View.VISIBLE);
                    viewHolderChild.mTvQuestion.setText(unDoQuestion);
                }
                //上次看到时间
                String lastTime = mViewModel.getLastPlayTime(childBean);
                if (lastTime == null) {
                    viewHolderChild.mTvProgress.setVisibility(View.GONE);
                } else {
                    viewHolderChild.mTvProgress.setVisibility(View.VISIBLE);
                    viewHolderChild.mTvProgress.setText(lastTime);
                }
                //是否可播放
                if (mViewModel.isPlayIng(childBean)) {
                    viewHolderChild.mIvOptions.setImageResource(R.drawable.course_detail_child_play);
                } else if (childBean.getCanGo()) {
                    viewHolderChild.mIvOptions.setImageResource(R.drawable.course_detail_child_unlock);
                } else {
                    viewHolderChild.mIvOptions.setImageResource(R.drawable.course_detail_child_lock);
                }
                //点击处理
                viewHolderChild.mIvOptions.setOnClickListener(v -> {
                    if (!mViewModel.isPlayIng(childBean) && mUseBean.getVip()) {
                        mCurrentBean.setLastDuration(mCourseDetailNavigator.getCurrentPlayProgress() / 1000);
                        notifyDataSetChanged();
                        mCourseDetailNavigator.setCurrentShowData(childBean,false);
                    }
                });
                viewHolderChild.mTvQuestion.setOnClickListener(v -> mCourseDetailNavigator.openQuestionPage(childBean));
                viewHolderChild.mIvQuestionArrow.setOnClickListener(v -> viewHolderChild.mTvQuestion.performClick());
                viewHolderChild.mTvTitle.setOnClickListener(v -> viewHolderChild.mIvOptions.performClick());
                return convertView;
            }

            @Override
            public boolean isChildSelectable(int groupPosition, int childPosition) {
                return true;
            }
        });
        //展开所有
        for (int i = 0; i < mUseBean.getItems().size(); i++) {
            mListView.expandGroup(i);
        }
        //滑动到指定位置
        if (mCurrentBean != null && mCurrentBean.getVIdeoId() != null) {
            for (int i = 0; i < mUseBean.getItems().size(); i++) {
                for (int j = 0; j < mUseBean.getItems().get(i).getChilds().size(); j++) {
                    if (mCurrentBean.getVIdeoId().equals(mUseBean.getItems().get(i).getChilds().get(j).getVIdeoId())) {
                        scroll(i, j);
                    }
                }
            }
        }

    }

    @Override
    public void initObservable() {
        super.initObservable();
        //评价是否提交成功监听
        mViewModel.mCommentSubmitSuccess.observe(this, aBoolean -> {
            if (aBoolean) {
                //重置评论相关
                resetInputAndImageSelect();
                resetCommentLevelSelectOnClicks();
                resetCommentTypeSelectOnClicks();
            }
        });
        //是否允许提交评价
        mViewModel.mCommentAllowSubmit.observe(this, allow -> {
            mBtnCommentSubmit.setEnabled(allow);
            if (allow) {
                mBtnCommentSubmit.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor("#ff5081F7")));
            } else {
                mBtnCommentSubmit.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor("#885081F7")));
            }
        });
    }

    /**
     * 设置当前正在播放的
     *
     * @param needPlayChild 当前播放的
     */
    public void setCurrent(CourseDetailCatalogueBean.Item needPlayChild) {
        this.mCurrentBean = needPlayChild;
        if (mViewModel != null) {
            mViewModel.setCurrentPlayBean(needPlayChild);
            initViewData();
        }
    }

    /**
     * 设置信息
     *
     * @param bean 信息
     */
    public void setInfo(CourseDetailCatalogueBean bean) {
        this.mUseBean = bean;
        if (mViewModel != null) {
            mViewModel.setUseBean(bean);
            initViewData();
        }
    }

    /**
     * 滑动到指定位置
     *
     * @param groupIndex 组坐标
     * @param childIndex 子坐标
     */

    private void scroll(int groupIndex, int childIndex) {
        int position = 0;
        for (int i = 0; i < groupIndex; i++) {
            position++;
            if (mListView.isGroupExpanded(i)) {
                position = position + mListView.getExpandableListAdapter().getChildrenCount(i);
            }
        }
        position++;
        position = position + childIndex;
        mListView.smoothScrollToPosition(position);
    }

    private static class ViewHolderGroup {
        AppCompatTextView mTvTitle;
        AppCompatImageView mIvArrow;

        public ViewHolderGroup(View convertView) {
            mTvTitle = convertView.findViewById(R.id.tv_title);
            mIvArrow = convertView.findViewById(R.id.iv_arrow);
        }
    }

    private static class ViewHolderChild {
        AppCompatImageView mIvState;
        AppCompatTextView mTvTitle;
        AppCompatImageButton mIvOptions;
        AppCompatTextView mTvTime;
        AppCompatTextView mTvProgress;
        AppCompatTextView mTvQuestion;
        AppCompatImageView mIvQuestionArrow;

        public ViewHolderChild(View convertView) {
            mIvState = convertView.findViewById(R.id.iv_state);
            mTvTitle = convertView.findViewById(R.id.tv_title);
            mIvOptions = convertView.findViewById(R.id.iv_options);
            mTvTime = convertView.findViewById(R.id.tv_time);
            mTvProgress = convertView.findViewById(R.id.tv_progress);
            mTvQuestion = convertView.findViewById(R.id.tv_question);
            mIvQuestionArrow = convertView.findViewById(R.id.iv_question_arrow);
        }
    }

    /*-----------------------------------------评价相关-----------------------------------------------*/

    /**
     * 重置输入和图片选择
     */
    private void resetInputAndImageSelect() {
        mEtCommentInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                ((TextView) findViewById(R.id.tv_input_length)).setText(s == null ? "0" : String.valueOf(s.length()));
                mViewModel.setCommentInputContent(s == null ? null : s.toString());
            }
        });
        mEtCommentInput.setText("");
        mViewModel.clearSelectCommonImages();
        //图片选择适配器
        if (imageLookAdapter == null) {
            imageLookAdapter = new CourseDetailCommentImageAdapter(this,new CourseDetailCommentImageListener() {
                @Override
                public void showImage() {
                    PermissionUtils.with(getActivity()).addPermissions(Manifest.permission.WRITE_EXTERNAL_STORAGE).addPermissions(
                                    Manifest.permission.READ_EXTERNAL_STORAGE).setPermissionsCheckListener(new PermissionListener() {
                                @Override
                                public void permissionRequestSuccess() {
                                    mViewModel.openCommentImageSelect(imageLookAdapter.getImageMaxSize());
                                }

                                @Override
                                public void permissionRequestFail(String[] grantedPermissions, String[] deniedPermissions, String[] forceDeniedPermissions) {
                                    MessageHelper.permissionsTips(getActivity(), "温馨提示",
                                            "请前往设置->应用->【" + PermissionUtils.getAppName(getActivity()) +
                                                    "】->权限中打开文件读写权限，否则功能无法正常运行！", "确定");
                                }
                            }).createConfig().setForceAllPermissionsGranted(false).setForceDeniedPermissionTips(
                                    "请前往设置->应用->【" + PermissionUtils.getAppName(getActivity()) + "】->权限中打开相关权限，否则功能无法正常运行！")
                            .buildConfig().startCheckPermission();
                }

                @Override
                public void delectImage(int ids) {
                    List<ImageItem> imageItemLists = imageLookAdapter.getData();
                    Iterator<ImageItem> iterator = imageItemLists.iterator();
                    while (iterator.hasNext()) {
                        ImageItem imageItem = iterator.next();
                        if (imageItem.getId() == ids) {
                            iterator.remove();
                        }
                    }
                    imageLookAdapter.notifyItemRangeChanged(0, imageLookAdapter.getItemCount());
                    mViewModel.removeCommentImage(ids);
                }
            });
            mCommentImageList.setLayoutManager(new GridLayoutManager(getContext(), 4));
            mCommentImageList.addItemDecoration(new GridSpaceItemDecoration(4, DpUtils.dp2px(getContext(), 5), DpUtils.dp2px(getContext(), 5),false));
            mCommentImageList.setAdapter(imageLookAdapter);
        }else {
            List<ImageItem> strings = new ArrayList<>();
            ImageItem imageItem = new ImageItem();
            imageItem.setId(0);
            strings.add(imageItem);
            imageLookAdapter.setDataItems(strings);
        }
    }

    /**
     * 重置等级选择
     */
    private void resetCommentLevelSelect() {
        mViewModel.setCommentSelectLevel(null);
        int color = Color.parseColor("#666666");
        ((AppCompatButton) findViewById(R.id.btn_level_1)).setBackgroundResource(R.drawable.course_detail_comment_level_n);
        ((AppCompatButton) findViewById(R.id.btn_level_1)).setTextColor(color);
        ((AppCompatButton) findViewById(R.id.btn_level_2)).setBackgroundResource(R.drawable.course_detail_comment_level_n);
        ((AppCompatButton) findViewById(R.id.btn_level_2)).setTextColor(color);
        ((AppCompatButton) findViewById(R.id.btn_level_3)).setBackgroundResource(R.drawable.course_detail_comment_level_n);
        ((AppCompatButton) findViewById(R.id.btn_level_3)).setTextColor(color);
        ((AppCompatButton) findViewById(R.id.btn_level_4)).setBackgroundResource(R.drawable.course_detail_comment_level_n);
        ((AppCompatButton) findViewById(R.id.btn_level_4)).setTextColor(color);
        ((AppCompatButton) findViewById(R.id.btn_level_5)).setBackgroundResource(R.drawable.course_detail_comment_level_n);
        ((AppCompatButton) findViewById(R.id.btn_level_5)).setTextColor(color);
        ((AppCompatButton) findViewById(R.id.btn_level_6)).setBackgroundResource(R.drawable.course_detail_comment_level_n);
        ((AppCompatButton) findViewById(R.id.btn_level_6)).setTextColor(color);
        ((AppCompatButton) findViewById(R.id.btn_level_7)).setBackgroundResource(R.drawable.course_detail_comment_level_n);
        ((AppCompatButton) findViewById(R.id.btn_level_7)).setTextColor(color);
        ((AppCompatButton) findViewById(R.id.btn_level_8)).setBackgroundResource(R.drawable.course_detail_comment_level_n);
        ((AppCompatButton) findViewById(R.id.btn_level_8)).setTextColor(color);
        ((AppCompatButton) findViewById(R.id.btn_level_9)).setBackgroundResource(R.drawable.course_detail_comment_level_n);
        ((AppCompatButton) findViewById(R.id.btn_level_9)).setTextColor(color);
        ((AppCompatButton) findViewById(R.id.btn_level_10)).setBackgroundResource(R.drawable.course_detail_comment_level_n);
        ((AppCompatButton) findViewById(R.id.btn_level_10)).setTextColor(color);
    }

    /**
     * 等级选择点击
     */
    private void resetCommentLevelSelectOnClicks() {
        resetCommentLevelSelect();
        mViewModel.checkCommentAllowSubmit();
        findViewById(R.id.btn_level_1).setOnClickListener(getCommentLevelSelectOnClick(1));
        findViewById(R.id.btn_level_2).setOnClickListener(getCommentLevelSelectOnClick(2));
        findViewById(R.id.btn_level_3).setOnClickListener(getCommentLevelSelectOnClick(3));
        findViewById(R.id.btn_level_4).setOnClickListener(getCommentLevelSelectOnClick(4));
        findViewById(R.id.btn_level_5).setOnClickListener(getCommentLevelSelectOnClick(5));
        findViewById(R.id.btn_level_6).setOnClickListener(getCommentLevelSelectOnClick(6));
        findViewById(R.id.btn_level_7).setOnClickListener(getCommentLevelSelectOnClick(7));
        findViewById(R.id.btn_level_8).setOnClickListener(getCommentLevelSelectOnClick(8));
        findViewById(R.id.btn_level_9).setOnClickListener(getCommentLevelSelectOnClick(9));
        findViewById(R.id.btn_level_10).setOnClickListener(getCommentLevelSelectOnClick(10));
    }

    /**
     * 评论类型选择点击
     */
    private void resetCommentTypeSelectOnClicks() {
        AppCompatButton btnCurrent = (AppCompatButton) findViewById(R.id.btn_select_current);
        AppCompatButton btnAll = (AppCompatButton) findViewById(R.id.btn_select_all);
        getCommentTypeOnClick(btnCurrent, btnAll, 21).onClick(btnCurrent);
        btnCurrent.setOnClickListener(getCommentTypeOnClick(btnCurrent, btnAll, 21));
        btnAll.setOnClickListener(getCommentTypeOnClick(btnAll, btnCurrent, 22));

    }

    /**
     * 获取评论选择等级的点击事件
     *
     * @param level 等级
     * @return 点击事件
     */
    private View.OnClickListener getCommentLevelSelectOnClick(int level) {
        return v -> {
            resetCommentLevelSelect();
            ((AppCompatButton) v).setBackgroundResource(R.drawable.course_detail_comment_level_y);
            ((AppCompatButton) v).setTextColor(Color.WHITE);
            mViewModel.setCommentSelectLevel(level);
        };
    }

    /**
     * 获取评论类型选择点击事件
     *
     * @param selectBtn   选择的按钮
     * @param unSelectBtn 未选择的按钮
     * @param type        类型
     * @return 点击事件
     */
    private View.OnClickListener getCommentTypeOnClick(AppCompatButton selectBtn, AppCompatButton unSelectBtn, int type) {
        return v -> {
            selectBtn.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor("#5081F7")));
            selectBtn.setTextColor(Color.WHITE);
            unSelectBtn.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor("#F6F7F8")));
            unSelectBtn.setTextColor(Color.parseColor("#333333"));
            mViewModel.setCommentSelectType(type);
        };
    }

    /**
     * 更新选择的图片
     *
     * @param imageItems 选择的图片
     */
    @Override
    public void updateCommentImage(List<ImageItem> imageItems) {
        if (imageLookAdapter.getData().size() >= 9) {
            for (ImageItem imageItem : imageLookAdapter.getData()) {
                if (imageItem.getId() == 0) {
                    imageLookAdapter.getData().remove(imageItem);
                }
            }
            imageLookAdapter.notifyItemRangeChanged(0, imageLookAdapter.getItemCount());
        } else {
            imageLookAdapter.addDataItem(imageLookAdapter.getData().size() - 1, imageItems);
        }
    }

    @Override
    public void showLoading(boolean isVision) {
        mCourseDetailNavigator.showLoading(isVision);
    }

}
