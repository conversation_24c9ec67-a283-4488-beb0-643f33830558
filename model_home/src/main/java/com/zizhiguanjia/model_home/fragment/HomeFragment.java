package com.zizhiguanjia.model_home.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.binioter.guideview.Component;
import com.binioter.guideview.Guide;
import com.binioter.guideview.GuideBuilder;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.DebugApplication;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;
import com.zizhiguanjia.model_home.config.RouteConfig;
import com.zizhiguanjia.model_home.databinding.HomeLayoutBinding;
import com.zizhiguanjia.model_home.dialog.DyVipUpdateHintDialog;
import com.zizhiguanjia.model_home.listener.IHomeFragment;
import com.zizhiguanjia.model_home.navigator.HomeFlutterNavigator;
import com.zizhiguanjia.model_home.report.HomeMsgReport;
import com.zizhiguanjia.model_home.ui.HomeActivity;
import com.zizhiguanjia.model_home.ui.HomeNativeActivity;
import com.zizhiguanjia.model_home.view.CustomGuideView;
import com.zizhiguanjia.model_home.viewmodel.HomeFlutterViewModel;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import io.flutter.embedding.android.FlutterView;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

@Route(path = HomeRoutherPath.MAIN_FRAGMENT)
public class HomeFragment extends BaseFragment implements HomeFlutterNavigator,MethodChannel.MethodCallHandler, TitleBar.OnTitleBarListener {
    private HomeLayoutBinding binding;
    private IHomeFragment iHomeFragment;
    private boolean pageState=false;
    private boolean isHomePageCreated = false;
    @BindViewModel
    HomeFlutterViewModel model;
    public static  HomeFragment getInstance(String route){
        HomeFragment homeFragment=new HomeFragment();
        Bundle bundle=new Bundle();
        bundle.putString("flutterRoute",route);
        homeFragment.setArguments(bundle);
        return homeFragment;
    }
    @Override
    public int initLayoutResId() {
        return R.layout.home_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();
        binding.tbflutter.setClickListener(this);
        boolean istoast=initArguments().getBoolean("autoDialog",false);
        LogUtils.e("看看传递的----->>>>"+istoast);
        loadingPopupView=new PopupManager.Builder(getContext()).asLoading("",R.layout.popup_center_impl_loading);
        PointHelper.joinPointData(DebugApplication.isColdLaunch?  PointerMsgType.POINTER_A_CLODSTART_APP:PointerMsgType.POINTER_A_WARMSTART_APP,false);
    }
    @Override
    public void initViewData() {
        String route=initRoute();
        binding.setModel(model);
        model.initParams(this, this,route);
        model.setiHomeFragment(iHomeFragment);
    }

    @Override
    public String initRoute() {
        String route=getArguments().getString("flutterRoute",null);
        if(route==null||route.isEmpty()){
            route=initArguments().getString("flutterRoute",null);
            if(StringUtils.isEmpty(route)){
                route= RouteConfig.ROUTE_TESTDATE;
            }
        }else {
            SdkHelper.countJpushMessage(BaseConfig.messageId);
        }
        return route;
    }
    private LoadingPopupView loadingPopupView;
    @Override
    public void showLoading(boolean visition, String msg) {
        if(visition){
            if(loadingPopupView==null)return;
            loadingPopupView.setTitle(msg);
            if(!loadingPopupView.isShow())loadingPopupView.show();
        }else {
            if(loadingPopupView==null)return;
            if(loadingPopupView.isShow())loadingPopupView.dismiss();
        }
    }

    @Override
    public void showTsDialog(int type) {
        MessageHelper.openGeneralCentDialog(getActivity(),
                type == 5 ? "清空错题集全部错题" : "清空收藏夹全部收藏", "确定清空吗？", "取消", "确定", false, true,
                new GeneralDialogListener() {
                    @Override
                    public void onCancel() {
                    }
                    @Override
                    public void onConfim() {
                        model.cleanExamData();
                    }

                    /**
                     *
                     */
                    @Override
                    public void onDismiss() {

                    }
                });
    }

    @Override
    public void showGuide() {
//        binding.vChapter.post(new Runnable() {
//            @Override
//            public void run() {
//                final GuideBuilder builder1 = new GuideBuilder();
////                builder1.setTargetView(binding.vChapter)
////                        .setAlpha(150)
////                        .setHighTargetCorner(DpUtils.dp2px(getContext(),0))
////                        .setHighTargetPaddingBottom(DpUtils.dp2px(getContext(),0))
////                        .setHighTargetGraphStyle(Component.ROUNDRECT);
//                builder1.setOnVisibilityChangedListener(new GuideBuilder.OnVisibilityChangedListener() {
//                    @Override
//                    public void onShown() {
//                    }
//
//                    @Override
//                    public void onDismiss() {
//                        if (iHomeFragment != null) {
//                            iHomeFragment.showGuide2();
//                        } else {
//                            LogUtils.e("iHomeFragment is null, cannot show guide2");
//                        }
//                    }
//                });
//
//                builder1.addComponent(new CustomGuideView());
//                Guide guide = builder1.createGuide();
//                guide.show(getActivity());
//            }
//        });
    }

    @Override
    public void closeView() {
        LogUtils.e("开始关闭--------》》》》》》");
//        finish();
        getActivity().finish();
    }

    @Override
    public void toMainPage(String pid) {
        BaseConfig.MAJOR_PID=Integer.parseInt(pid);
        KvUtils.save("newUserCertificateGuide",true);

        LogUtils.e("跳转了");
        Intent intent = new Intent(getActivity(), HomeNativeActivity.class);
        intent.putExtra("showNativePage", true);
        startActivity(intent);
//        iHomeFragment.closeActivity();
//        finish();
//        HomeHelper.start(getActivity());
//        startFragment(new HomeFragment());
    }

    public void setiHomeFragment(IHomeFragment iHomeFragment) {
        this.iHomeFragment = iHomeFragment;
    }
    @Override
    public void initObservable() {
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                HomeMsgReport.getInstance().getMsgChanner().busMsgChanner(msgEvent.getCode(),msgEvent.getMsg(),model.IMsgListener);
            }
        });
    }

    private DyVipUpdateHintDialog mDyVipUpdateHintDialog;
    /**
     * 显示抖音vip更新提醒弹窗
     *
     * @param data 数据
     */
    @Override
    public void showDyVipUpdateHintDialog(DyVipUpdateHintBean data) {
        if(mDyVipUpdateHintDialog == null){
            mDyVipUpdateHintDialog = new DyVipUpdateHintDialog();
        }
        if(mDyVipUpdateHintDialog.isVisible()){
            mDyVipUpdateHintDialog.dismiss();
        }
        data.setMajorId(model.mHomeBean.getMajorId() != null ? String.valueOf(model.mHomeBean.getMajorId()) : BaseConfig.majId);
        data.setMajorPid(BaseConfig.MAJOR_PID+"");
        data.setAreaPid(model.mHomeBean.getAreaId() != null ? String.valueOf(model.mHomeBean.getAreaId()) : StringUtils.isEmpty(BaseConfig.cityCode) ? "" : BaseConfig.cityCode);
        mDyVipUpdateHintDialog.setShowData(data);
        mDyVipUpdateHintDialog.show(getChildFragmentManager());
    }

    @Override
    public void successMethodChannel(MethodChannel methodChannel) {
        methodChannel.setMethodCallHandler(this);
    }

    @Override
    public void successFlutterView(FlutterView flutterView) {
        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT);
        binding.homeFrameIcb.mainFramelayout.addView(flutterView, lp);
    }

    @Override
    public void showToast(String msg) {
        if(StringUtils.isEmpty(msg))return;
        ToastUtils.normal(msg, Gravity.CENTER);
    }

    @Override
    public void reshHomeData(boolean passCertificate) {
        model.getHttpsData(null,passCertificate);
    }

    @Override
    public void setTopTitleInfo(boolean show, String title) {
        binding.tbflutter.setVisibility(show?View.VISIBLE:View.GONE);
        binding.tbflutter.getCenterTextView().setText(title==null?"":title);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                model.onMethodCall(call, result);
            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        LogUtils.e("看下路由---->>>"+model.mRoute);
        if(model.mRoute==null)return;
        if(!model.mRoute.contains(RouteConfig.ROUTE_HOME))return;
        if (!isHomePageCreated) {
            DebugApplication.isColdLaunch = DebugApplication.tempCL;
            DebugApplication.tempCL = false;
            isHomePageCreated = true;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        SdkHelper.onResumeDou(getActivity());
        if(model.flutterEngine!=null){
            model.flutterEngine.getLifecycleChannel().appIsResumed();
        }
    }
    @Override
    public void onPause() {
        super.onPause();
        SdkHelper.onPauseDou(getActivity());
        pageState=true;
        if(model.flutterEngine!=null){
            model.flutterEngine.getLifecycleChannel().appIsInactive();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if(model.flutterEngine!=null){
            model.flutterEngine.getLifecycleChannel().appIsPaused();
        }

    }

    @Override
    public void onVisible() {
        super.onVisible();
        if(model.mRoute.contains(RouteConfig.ROUTE_HOME))return;
        LogUtils.e("开始刷新-----》》》》3333"+pageState);
        if(pageState){
            LogUtils.e("开始刷新-----》》》》44444"+pageState);
            model.getHttpsData(null,false);
        }
    }

    @Override
    public void onClicked(View v, int action) {
        if(action==TitleBar.ACTION_LEFT_BUTTON){
            finish();
        }
    }

    @Override
    public boolean isMain() {
        if(model.mRoute.contains(RouteConfig.ROUTE_HOME)){
            return true;
        }
        return  false;
    }
}
