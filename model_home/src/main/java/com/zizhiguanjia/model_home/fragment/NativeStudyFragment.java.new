package com.zizhiguanjia.model_home.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.Fragment;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bumptech.glide.Glide;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_network.BaseViewModel;
import com.wb.lib_network.utils.GsonUtils;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.ListHelper;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.api.HomeSeriveApi;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.bean.MenuNavsBean;
import com.zizhiguanjia.model_home.config.HomeFlutterChannerTpis;
import com.zizhiguanjia.model_home.databinding.HomeNativeLayoutBinding;
import com.zizhiguanjia.model_home.listener.INativeStudyFragment;
import com.zizhiguanjia.model_home.ui.HomeNativeActivity;
import com.zizhiguanjia.model_home.utils.HomeUtils;
import com.zizhiguanjia.model_home.viewmodel.HomeViewModel;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;
import com.zizhiguanjia.lib_base.config.Http;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import io.reactivex.Observable;

/**
 * 原生学习页面Fragment
 * 负责显示原生Android学习界面
 */
public class NativeStudyFragment extends BaseFragment {
    private HomeNativeLayoutBinding binding;
    private INativeStudyFragment listener;
    private SwipeRefreshLayout swipeRefreshLayout;
    private HomeSeriveApi mApi;
    
    // 摸底测评模块
    private RelativeLayout evaluationLayout;
    private ImageView ivCloseTest;
    
    // 开始学习按钮
    private Button btnStartLearning;
    
    // 功能按钮区域
    private LinearLayout firstRowContainer; // 第一排功能按钮容器
    private LinearLayout secondRowContainer; // 第二排功能按钮容器
    
    @BindViewModel
    StudyViewModel studyViewModel;
    
    // 添加HomeViewModel用于获取真实首页数据
    @BindViewModel
    HomeViewModel homeViewModel;
    
    // 存储首页数据
    private HomeBean homeData;
    
    public static NativeStudyFragment newInstance() {
        NativeStudyFragment fragment = new NativeStudyFragment();
        return fragment;
    }
    
    @Override
    public int initLayoutResId() {
        return R.layout.home_native_layout;
    }
    
    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();
        
        // 初始化API
        mApi = new Http().create(HomeSeriveApi.class);
        
        // 初始化StudyViewModel
        if (studyViewModel == null) {
            studyViewModel = new StudyViewModel();
        }
        binding.setStudyModel(studyViewModel);
        
        // 初始化HomeViewModel
        if (homeViewModel == null) {
            homeViewModel = new HomeViewModel();
            homeViewModel.init((HomeNativeActivity)getActivity(), (HomeNativeActivity)getActivity());
        }
        
        // 设置Activity引用，用于处理页面跳转
        if (studyViewModel != null) {
            studyViewModel.setActivity(getActivity());
        }
        
        // 初始化下拉刷新
        setupSwipeRefresh();
        
        // 为include布局中的head_layout设置model绑定
        View homeHead = binding.getRoot().findViewById(R.id.home_head);
        if (homeHead != null) {
            Object headBinding = androidx.databinding.DataBindingUtil.getBinding(homeHead);
            if (headBinding != null && headBinding instanceof com.zizhiguanjia.model_home.databinding.HomeHeadLayoutBinding) {
                com.zizhiguanjia.model_home.databinding.HomeHeadLayoutBinding headLayoutBinding = 
                    (com.zizhiguanjia.model_home.databinding.HomeHeadLayoutBinding) headBinding;
                headLayoutBinding.setModel(studyViewModel);
                LogUtils.e("为顶部布局设置ViewModel绑定成功");
            } else {
                LogUtils.e("获取顶部布局的绑定对象失败");
            }
        }
        
        // 初始化摸底测评和开始学习相关控件
        initEvaluationAndLearningViews();
        
        // 初始化功能按钮区域
        initFunctionButtonsArea();
        
        // 设置点击事件
        setupClickListeners();
        
        // 更新顶部标题
        updateTopTitle();
        
        LogUtils.e("NativeStudyFragment初始化完成");
    }
    
    /**
     * 初始化摸底测评和开始学习相关控件
     */
    private void initEvaluationAndLearningViews() {
        // 获取摸底测评布局和关闭按钮
        View aiHelperView = binding.getRoot().findViewById(R.id.tv_ai_helper_title).getRootView();
        if (aiHelperView instanceof RelativeLayout) {
            // 查找摸底测评模块
            for (int i = 0; i < ((RelativeLayout) aiHelperView).getChildCount(); i++) {
                View child = ((RelativeLayout) aiHelperView).getChildAt(i);
                if (child instanceof RelativeLayout && 
                    child.findViewById(R.id.iv_close_test) != null) {
                    evaluationLayout = (RelativeLayout) child;
                    ivCloseTest = child.findViewById(R.id.iv_close_test);
                    break;
                }
            }
        }
        
        // 创建开始学习按钮（类似NativeStudyAIFragment中的开始学习按钮）
        btnStartLearning = new Button(getActivity());
        btnStartLearning.setId(View.generateViewId());
        btnStartLearning.setLayoutParams(new RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.MATCH_PARENT,
                getResources().getDimensionPixelSize(R.dimen.start_button_height)));
        btnStartLearning.setText("开始学习");
        btnStartLearning.setTextColor(getResources().getColor(R.color.white));
        btnStartLearning.setTextSize(16);
        btnStartLearning.setBackgroundResource(R.drawable.main_start_bg);
        
        // 默认隐藏开始学习按钮
        btnStartLearning.setVisibility(View.GONE);
        
        // 如果找到了摸底测评布局，则添加开始学习按钮到摸底测评布局的父布局
        if (evaluationLayout != null && evaluationLayout.getParent() instanceof RelativeLayout) {
            RelativeLayout parentLayout = (RelativeLayout) evaluationLayout.getParent();
            
            // 设置布局参数，让开始学习按钮显示在摸底测评模块的位置
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                    RelativeLayout.LayoutParams.MATCH_PARENT,
                    getResources().getDimensionPixelSize(R.dimen.start_button_height));
            params.addRule(RelativeLayout.BELOW, R.id.ll_study_progress);
            params.setMargins(
                    getResources().getDimensionPixelSize(R.dimen.start_button_margin_horizontal),
                    getResources().getDimensionPixelSize(R.dimen.start_button_margin_top),
                    getResources().getDimensionPixelSize(R.dimen.start_button_margin_horizontal),
                    0);
            btnStartLearning.setLayoutParams(params);
            
            // 添加开始学习按钮到布局
            parentLayout.addView(btnStartLearning);
        }
    }
    
    /**
     * 初始化功能按钮区域
     */
    private void initFunctionButtonsArea() {
        // 查找功能按钮容器
        firstRowContainer = binding.getRoot().findViewById(R.id.function_button_row1);
        secondRowContainer = binding.getRoot().findViewById(R.id.function_button_row2);
        
        if (firstRowContainer == null || secondRowContainer == null) {
            LogUtils.e("找不到功能按钮容器，初始化功能按钮区域失败");
            return;
        }
        
        LogUtils.e("功能按钮区域初始化完成");
    }
    
    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        swipeRefreshLayout = binding.getRoot().findViewById(R.id.swipe_refresh_layout);
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
                @Override
                public void onRefresh() {
                    // 刷新数据
                    refreshData();
                }
            });
            LogUtils.e("下拉刷新初始化完成");
        } else {
            LogUtils.e("找不到SwipeRefreshLayout，下拉刷新初始化失败");
        }
    }
    
    @Override
    public void initViewData() {
        // 获取真实的首页数据
        fetchRealHomeData();
    }
    
    /**
     * 获取真实的首页数据
     */
    private void fetchRealHomeData() {
        // 显示加载提示
        if (listener != null) {
            listener.showLoading(true, "加载中...");
        }
        
        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        if (BaseConfig.channelUID != null && !BaseConfig.channelUID.isEmpty()) {
            params.put("fromWhichPerson", BaseConfig.channelUID);
        }
        
        // 调用API获取首页数据
        Observable<BaseData<HomeBean>> observable = mApi.getMainInfo(params);
        if (studyViewModel != null) {
            studyViewModel.launchOnlyResult(observable, new BaseViewModel.OnHandleException<BaseData<HomeBean>>() {
                @Override
                public void success(BaseData<HomeBean> data) {
                    // 关闭加载提示
                    if (listener != null) {
                        listener.showLoading(false, null);
                    }
                    
                    if (data != null && data.Data != null) {
                        // 保存首页数据
                        homeData = data.Data;
                        
                        // 更新UI
                        updateHomeData(homeData);
                        
                        LogUtils.e("获取首页数据成功");
                    } else {
                        LogUtils.e("获取首页数据成功，但数据为空");
                        ToastUtils.normal("获取数据失败，请稍后重试");
                    }
                }
                
                @Override
                public void error(String msg) {
                    // 关闭加载提示
                    if (listener != null) {
                        listener.showLoading(false, null);
                    }
                    
                    LogUtils.e("获取首页数据失败: " + msg);
                    ToastUtils.normal("获取数据失败: " + msg);
                }
            });
        }
    }
    
    /**
     * 设置点击事件
     */
    private void setupClickListeners() {
        // 设置摸底测评关闭按钮点击事件
        if (ivCloseTest != null) {
            ivCloseTest.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    closeEvaluation();
                }
            });
        }
        
        // 设置开始学习按钮点击事件
        if (btnStartLearning != null) {
            btnStartLearning.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // 点击开始学习按钮，跳转到题型练习页面
                    if (!NoDoubleClickUtils.isDoubleClick()) {
                        if (getArguments() == null) {
                            setArguments(new Bundle());
                        }
                        getArguments().putInt("index", 0);
                        startFragment(ListHelper.toPageExamList());
                    }
                }
            });
        }
        
        // 设置顶部区域头像点击事件
        View imgMainUser = binding.getRoot().findViewById(R.id.imgMainUser);
        if (imgMainUser != null) {
            imgMainUser.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!NoDoubleClickUtils.isDoubleClick()) {
                        // 跳转到用户信息页面
                        startFragment(AccountHelper.showUserPage());
                    }
                }
            });
        }
        
        // 设置顶部区域地区/科目点击事件
        View llHomeSwitch = binding.getRoot().findViewById(R.id.llHomeSwitch);
        if (llHomeSwitch != null) {
            llHomeSwitch.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!NoDoubleClickUtils.isDoubleClick()) {
                        // 跳转到证书选择页面
                        startFragment(AccountHelper.showCertificateChosePage());
                    }
                }
            });
        }
    }
    
    /**
     * 关闭摸底测评
     */
    private void closeEvaluation() {
        if (listener != null) {
            listener.showLoading(true, "关闭中...");
        }
        
        // 调用API关闭摸底测评
        Map<String, String> params = new HashMap<>();
        Observable<BaseData> observable = mApi.closeTip(params);
        if (studyViewModel != null) {
            studyViewModel.launchOnlyResult(observable, new BaseViewModel.OnHandleException<BaseData>() {
                @Override
                public void success(BaseData data) {
                    if (listener != null) {
                        listener.showLoading(false, null);
                    }
                    
                    // 隐藏摸底测评，显示开始学习按钮
                    if (evaluationLayout != null) {
                        evaluationLayout.setVisibility(View.GONE);
                    }
                    
                    if (btnStartLearning != null) {
                        btnStartLearning.setVisibility(View.VISIBLE);
                    }
                    
                    // 显示提示消息
                    if (data != null && !StringUtils.isEmpty(data.Message)) {
                        ToastUtils.normal(data.Message);
                    }
                }
                
                @Override
                public void error(String msg) {
                    if (listener != null) {
                        listener.showLoading(false, null);
                    }
                    
                    // 显示错误消息
                    ToastUtils.normal(msg);
                }
            });
        }
    }
    
    /**
     * 更新顶部标题
     */
    private void updateTopTitle() {
        String title = CertificateHelper.getCurrentCertificateInfo();
        if (!StringUtils.isEmpty(title)) {
            // 获取顶部标题TextView
            TextView tvUserTest = binding.getRoot().findViewById(R.id.tvUserTest);
            if (tvUserTest != null) {
                tvUserTest.setText(title);
            }
            
            LogUtils.e("更新顶部标题: " + title);
        }
    }
    
    @Override
    public void initObservable() {
        // 暂时不需要处理Observable
    }
    
    /**
     * 设置监听器
     */
    public void setListener(INativeStudyFragment listener) {
        this.listener = listener;
    }
    
    /**
     * 刷新数据
     */
    public void refreshData() {
        // 获取真实的首页数据
        fetchRealHomeData();
    }
    
    /**
     * 更新首页数据
     */
    public void updateHomeData(HomeBean homeBean) {
        if (homeBean == null) return;
        
        // 更新顶部信息（如用户名、头像等）
        // 直接使用homeBean提供的数据更新StudyViewModel，无需getUsers()方法
        if (studyViewModel != null) {
            studyViewModel.updateFromHomeBean(homeBean);
        }
        
        // 处理摸底测评显示状态
        if (evaluationLayout != null) {
            // 使用isShowEvaluation()方法判断是否显示摸底测评
            boolean shouldShowEvaluation = homeBean.isShowEvaluation();
            evaluationLayout.setVisibility(shouldShowEvaluation ? View.VISIBLE : View.GONE);
            
            // 如果不显示摸底测评，则显示开始学习按钮
            if (btnStartLearning != null) {
                btnStartLearning.setVisibility(shouldShowEvaluation ? View.GONE : View.VISIBLE);
            }
        }
        
        // 更新功能按钮区域
        if (homeBean.getMenuNavs() != null && !homeBean.getMenuNavs().isEmpty()) {
            updateFunctionButtons(homeBean.getMenuNavs());
        }
        
        // 如果下拉刷新正在进行中，关闭它
        if (swipeRefreshLayout != null && swipeRefreshLayout.isRefreshing()) {
            swipeRefreshLayout.setRefreshing(false);
        }
        
        // 通知监听器刷新完成
        if (listener != null) {
            listener.onRefreshComplete();
        }
    }

    /**
     * 更新功能按钮区域
     *
     * @param menuNavs 功能按钮数据
     */
    private void updateFunctionButtons(List<MenuNavsBean> menuNavs) {
        if (firstRowContainer == null || secondRowContainer == null) return;
        
        // 清空之前的按钮
        firstRowContainer.removeAllViews();
        secondRowContainer.removeAllViews();
        
        // 分类存储第一排和第二排的按钮数据
        List<MenuNavsBean> firstRowButtons = new ArrayList<>();
        List<MenuNavsBean> secondRowButtons = new ArrayList<>();
        
        // 根据RowNum参数分类
        for (MenuNavsBean menuNav : menuNavs) {
            if (menuNav.getRowNum() == 1) {
                firstRowButtons.add(menuNav);
            } else {
                secondRowButtons.add(menuNav);
            }
        }
        
        // 设置第一排按钮
        setupRowButtons(firstRowButtons, firstRowContainer);
        
        // 设置第二排按钮
        setupRowButtons(secondRowButtons, secondRowContainer);
    }

    /**
     * 设置一排功能按钮
     *
     * @param buttons 按钮数据
     * @param container 容器
     */
    private void setupRowButtons(List<MenuNavsBean> buttons, LinearLayout container) {
        if (buttons.isEmpty()) {
            container.setVisibility(View.GONE);
            return;
        }
        
        container.setVisibility(View.VISIBLE);
        
        // 根据按钮数量决定布局方式
        int buttonCount = buttons.size();
        
        for (int i = 0; i < buttonCount; i++) {
            MenuNavsBean menuNav = buttons.get(i);
            
            // 创建按钮视图
            View buttonView = getLayoutInflater().inflate(R.layout.item_menu_nav_button, container, false);
            
            // 设置布局参数
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    0,
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    1.0f); // 等分宽度
            
            // 设置边距
            int margin = getResources().getDimensionPixelSize(R.dimen.menu_nav_button_margin);
            params.setMargins(margin, margin, margin, margin);
            buttonView.setLayoutParams(params);
            
            // 设置按钮内容
            setupButtonContent(buttonView, menuNav);
            
            // 添加到容器
            container.addView(buttonView);
        }
    }

    /**
     * 设置按钮内容和点击事件
     *
     * @param buttonView 按钮视图
     * @param menuNav 按钮数据
     */
    private void setupButtonContent(View buttonView, MenuNavsBean menuNav) {
        // 获取图标和文本控件
        ImageView iconImageView = buttonView.findViewById(R.id.icon_image_view);
        TextView textView = buttonView.findViewById(R.id.text_view);
        
        // 优先使用API返回的图片URL
        if (menuNav.getImgSrc() != null && !menuNav.getImgSrc().isEmpty()) {
            // 使用Glide加载网络图片
            Glide.with(requireActivity())
                 .load(menuNav.getImgSrc())
                 .into(iconImageView);
        } else {
            // 如果URL为空，则使用本地资源作为备选
            iconImageView.setImageResource(menuNav.getIconResId());
            iconImageView.setColorFilter(getResources().getColor(R.color.menu_nav_icon_color));
        }
        
        // 设置文本
        textView.setText(menuNav.getTitle());
        
        // 设置点击事件
        final MenuNavsBean finalMenuNav = menuNav;
        buttonView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NoDoubleClickUtils.isDoubleClick()) return;
                
                ToastUtils.normal(finalMenuNav.getTitle());
                
                // 根据不同的菜单导航类型执行不同的操作
                handleFunctionButtonClick(finalMenuNav);
            }
        });
    }

    /**
     * 处理功能按钮点击
     *
     * @param menuNav 按钮数据
     */
    private void handleFunctionButtonClick(MenuNavsBean menuNav) {
        // 确保Arguments不为null
        if (getArguments() == null) {
            setArguments(new Bundle());
        }
        
        switch (menuNav.getNavType()) {
            case MenuNavsBean.TYPE_ERROR: // 错题集
                getArguments().putString("flutterRoute", "error");
                startFragment(new com.zizhiguanjia.model_home.fragment.HomeFragment());
                break;
                
            case MenuNavsBean.TYPE_SAVE: // 收藏夹
                getArguments().putString("flutterRoute", "save");
                startFragment(new com.zizhiguanjia.model_home.fragment.HomeFragment());
                break;
                
            case MenuNavsBean.TYPE_PRACTICE: // 题型练习
                getArguments().putInt("index", 1);
                startFragment(ListHelper.toPageExamList());
                break;
                
            case MenuNavsBean.TYPE_EXAM: // 模拟考试
            case 10:
            case 11:
                Map<String, String> params = new HashMap<>();
                params.put("idDo", menuNav.isDo() ? "1" : "0");
                params.put("isDialog", menuNav.getDateBoxStatus() == 1 ? "0" : "1");
                params.put("type", String.valueOf(menuNav.getNavType()));
                
                getArguments().putInt("index", 0);
                getArguments().putInt("mPageType", 1);
                getArguments().putString("pagerType", "7");
                startFragment(ListHelper.toPageExamAuto());
                break;
                
            case 5: // 历年真题
                if (homeViewModel != null && homeViewModel.checkLoginOrAddress(true, BaseConfig.ORDER_BIND_TYPE, true)) {
                    getArguments().putInt("paperType", 1);
                    startFragment(ListHelper.showCommonListPage());
                }
                break;
                
            case 6: // 二建模考
                if (homeViewModel != null && homeViewModel.checkLoginOrAddress(true, BaseConfig.ORDER_BIND_TYPE, true)) {
                    getArguments().putInt("paperType", 2);
                    startFragment(ListHelper.showCommonListPage());
                }
                break;
                
            case 16: // 易错100题
                if (!NoDoubleClickUtils.isDoubleClick() && homeViewModel != null && 
                    homeViewModel.checkLoginOrAddress(true, BaseConfig.ORDER_BIND_TYPE, false)) {
                    getArguments().putString("title", "易错100题");
                    getArguments().putBoolean("restart", false);
                    getArguments().putString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG));
                    startFragment(ListHelper.toPageExamDes());
                }
                break;
                
            case 17: // 学习资料
                if (!NoDoubleClickUtils.isDoubleClick() && homeViewModel != null && 
                    homeViewModel.checkLoginOrAddress(true, BaseConfig.ORDER_BIND_TYPE, false)) {
                    startFragment(ListHelper.toPageDocMajors());
                }
                break;
                
            case 20: // 直播课程
                if (menuNav.getUrl() != null && !menuNav.getUrl().isEmpty()) {
                    // 处理直播课程点击
                    if (homeViewModel != null && homeViewModel.checkLoginOrAddress(true, BaseConfig.ORDER_BIND_TYPE, true)) {
                        if (HomeUtils.getInstance().checkSechemValid(menuNav.getUrl())) {
                            Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(menuNav.getUrl()));
                            startActivity(it);
                        } else {
                            getArguments().putString("routh", "home");
                            getArguments().putString("url", menuNav.getUrl());
                            getArguments().putInt("payType", 1);
                            getArguments().putString("payRouthParams", PayRouthConfig.PAY_BANNER);
                            startFragment(CommonHelper.showCommonWeb());
                        }
                    }
                }
                break;
        }
    }
} 