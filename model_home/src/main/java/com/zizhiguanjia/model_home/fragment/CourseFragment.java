package com.zizhiguanjia.model_home.fragment;

import android.os.Bundle;
import android.view.View;

import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_image_loadcal.ImageManager;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.adapter.HomeAvdFreCourseAdapter;
import com.zizhiguanjia.model_home.bean.ItemsBean;
import com.zizhiguanjia.model_home.databinding.HomeAvdCourseLayoutBinding;
import com.zizhiguanjia.model_home.navigator.HomeCourseNavigator;
import com.zizhiguanjia.model_home.viewmodel.CourseViewModel;
import com.zizhiguanjia.model_video.custommedia.JZMediaAliyun;

import java.util.List;

import androidx.recyclerview.widget.GridLayoutManager;

import static cn.jzvd.Jzvd.SCREEN_NORMAL;

public class CourseFragment extends BaseFragment implements HomeCourseNavigator {
    private HomeAvdFreCourseAdapter homeAvdFreCourseAdapter;
    @BindViewModel
    CourseViewModel model;
    private HomeAvdCourseLayoutBinding binding;

    @Override
    public int initLayoutResId() {
        return R.layout.home_avd_course_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding=getBinding();
            model.initParams(this,this);
//        JzvdStdVolumeAfterFullscreen jzvdStd = (JzvdStdVolumeAfterFullscreen) findViewById(R.id.jz_video);
//        jzvdStd.setUp("https://vcache-mp4.boosj.com/v/2018-09/30/2018-0930CVNE123009480084716332.mp4?t=iSfPqO4LlnNOUdmaGOcnOA&m=1685700843&sign=bbffebc2f7e47e32007858fa05dca640&t1=1685686443&cip=**************&vd=7979069&rate=3138336b"
//                , "饺子闭眼睛");
//
//        List<String> data = new ArrayList<>();
//        data.add("神奇宝贝（精灵宝可梦）有哪些著名的梗？");
//        data.add("我翻开自我保护的书，上面只写了两个大字：证据");
//        data.add("接纳自己，是无条件地爱，包括爱所有的痛苦");
//        data.add("3 岁前，世界对待孩子的一切，都会给他们留下深刻的第一印象");
//        data.add("担心今天没锻炼，现在站起来，做一组完美深蹲");
//
//        XMarqueeView xMarqueeView = (XMarqueeView) findViewById(R.id.upview2);
//        xMarqueeView.setAdapter(new MarqueeViewAdapter(data));
//        //刷新数据
//        //marqueeViewAdapter.setData(data);
//        recyclerView=findViewById(R.id.freecourseRey);
//        homeAvdFreCourseAdapter=new HomeAvdFreCourseAdapter();
//        recyclerView.setNestedScrollingEnabled(false);
//        recyclerView.setLayoutManager(new GridLayoutManager(getActivity(),2));
//        recyclerView.setAdapter(homeAvdFreCourseAdapter);
//        homeAvdFreCourseAdapter.setDataItems(data);


    }

    @Override
    public void initViewData() {
        super.initViewData();
        homeAvdFreCourseAdapter=new HomeAvdFreCourseAdapter();
        binding.freecourse.freecourseRey.setLayoutManager(new GridLayoutManager(getActivity(),2));
        binding.freecourse.freecourseRey.setNestedScrollingEnabled(false);
        binding.freecourse.freecourseRey.setAdapter(homeAvdFreCourseAdapter);
    }

    @Override
    public void onPause() {
        super.onPause();
        binding.jzVideo.releaseAllVideos();
    }


    @Override
    public void showVideoView(String img, String video, String titel) {
        if(StringUtils.isEmpty(video))return;
        binding.jzVideo.setUp(video,titel,SCREEN_NORMAL, JZMediaAliyun.class);
        ImageManager.getInstance().displayImage(img,binding.jzVideo.posterImageView);
        binding.jzVideo.startVideo();
    }

    @Override
    public void showFreeCourse(List<ItemsBean> itemsBeans) {
        if(itemsBeans==null||itemsBeans.size()==0){
            binding.freecourse.freecourseRey.setVisibility(View.GONE);
            binding.freecourse.freeNodata.setVisibility(View.VISIBLE);
        }else {
            binding.freecourse.freecourseRey.setVisibility(View.VISIBLE);
            binding.freecourse.freeNodata.setVisibility(View.GONE);
            homeAvdFreCourseAdapter.setDataItems(itemsBeans);
        }
    }

    @Override
    public void showGjk(ItemsBean itemsBean) {
        if(itemsBean==null){
            binding.gjjcourse.gjkMain.setVisibility(View.GONE);
            binding.gjjcourse.gjkNodata.setVisibility(View.VISIBLE);
        }else {
            binding.gjjcourse.gjkMain.setVisibility(View.VISIBLE);
            binding.gjjcourse.gjkNodata.setVisibility(View.GONE);
        }
    }
}
