package com.zizhiguanjia.model_home.viewmodel;

import static com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils.isDoubleClick;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_network.BaseViewModel;
import com.wb.lib_utils.utils.ClipboardUtils;
import com.wb.lib_utils.utils.DataUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.ListHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.helper.TaskHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.listeners.CoupoinValidationListenter;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.api.HomeSeriveApi;
import com.zizhiguanjia.model_home.bean.BannersBean;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.bean.HomeCourseBean;
import com.zizhiguanjia.model_home.bean.HomeExamDateTipBean;
import com.zizhiguanjia.model_home.bean.HomeShaperBean;
import com.zizhiguanjia.model_home.bean.MenuNavsBean;
import com.zizhiguanjia.model_home.config.HomeFlutterChannerTpis;
import com.zizhiguanjia.model_home.config.RouteConfig;
import com.zizhiguanjia.model_home.fragment.HomeFragment;
import com.zizhiguanjia.model_home.listener.IMsg;
import com.zizhiguanjia.model_home.navigator.HomeActivityNavigator;
import com.zizhiguanjia.model_home.report.HomeMsgReport;
import com.zizhiguanjia.model_home.repository.HomeFlutterRepository;
import com.zizhiguanjia.model_home.repository.MsgRepository;
import com.zizhiguanjia.model_home.ui.HomeNativeActivity;
import com.zizhiguanjia.model_home.utils.HomeUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import io.flutter.plugin.common.MethodChannel;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * 学习页面ViewModel
 * 整合了原HomeViewModel的功能，直接处理所有数据获取和UI更新
 */
public class StudyViewModel extends BaseViewModel {
    private Activity activity;
    private HomeSeriveApi mApi; // API服务引用
    private HomeActivityNavigator navigator;
    private HomeFlutterRepository repository;

    // 完整的首页数据
    private MutableLiveData<HomeBean> homeBean = new MutableLiveData<>();

    // 学习时间
    private MutableLiveData<String> studyTime = new MutableLiveData<>();
    // 学习时间（分钟）
    public MutableLiveData<Integer> studyTimeMinutes = new MutableLiveData<>();
    // 最新模考分数
    public MutableLiveData<Integer> latestExamScore = new MutableLiveData<>();
    // 知识掌握度
    public MutableLiveData<Integer> knowledgeMastery = new MutableLiveData<>();
    // 坚持学习天数
    public MutableLiveData<Integer> studyDays = new MutableLiveData<>();
    // 已完成题目数
    public MutableLiveData<String> currentCount = new MutableLiveData<>();
    // 总题目数
    public MutableLiveData<String> questionCount = new MutableLiveData<>();
    // 正确率
    private MutableLiveData<String> rightStr = new MutableLiveData<>();
    // 做题进度
    private MutableLiveData<Integer> questionMakingProgress = new MutableLiveData<>();
    // 试卷标题
    private MutableLiveData<String> paperTitle = new MutableLiveData<>();
    // 试卷类型
    private MutableLiveData<String> paperType = new MutableLiveData<>();
    // 菜单导航
    private MutableLiveData<List<MenuNavsBean>> menuNavs = new MutableLiveData<>();
    // 是否显示广告
    private MutableLiveData<Boolean> isShowAds = new MutableLiveData<>();
    // 广告内容
    private MutableLiveData<String> midssAds = new MutableLiveData<>();
    // 广告购买状态
    private MutableLiveData<Boolean> midssBuyActice = new MutableLiveData<>();
    // 是否显示考试日期
    private MutableLiveData<Boolean> isShowExamDate = new MutableLiveData<>();
    // 考试日期提示
    private MutableLiveData<String> examDateTip = new MutableLiveData<>();
    // 视频类型
    private MutableLiveData<Integer> videoType = new MutableLiveData<>();
    // 用户VIP状态
    private MutableLiveData<Integer> buyVip = new MutableLiveData<>();

    // VIP试用相关属性
    private MutableLiveData<Integer> trialQuestions = new MutableLiveData<>();
    private MutableLiveData<Integer> trialPapers = new MutableLiveData<>();
    private MutableLiveData<Integer> usedTrialQuestions = new MutableLiveData<>();
    private MutableLiveData<Integer> usedTrialPapers = new MutableLiveData<>();
    private MutableLiveData<Integer> vipUsagePercent = new MutableLiveData<>();

    // Banner相关属性
    private MutableLiveData<List<BannersBean>> banners = new MutableLiveData<>();

    // 通关精讲课数据
    private MutableLiveData<List<com.zizhiguanjia.model_home.bean.ItemsBean>> lives = new MutableLiveData<>();

    // 底部分享功能按钮
    private MutableLiveData<List<com.zizhiguanjia.model_home.bean.HomeShaperBean>> shareNavs = new MutableLiveData<>();

    // 是否官方题库
    private MutableLiveData<Boolean> isOfficial = new MutableLiveData<>();

    // 官方题库通知文本
    private MutableLiveData<String> officialText = new MutableLiveData<>();

    // 章节练习相关数据
    private MutableLiveData<Boolean> hasStartedChapterPractice = new MutableLiveData<>();
    private MsgRepository msgRepository;
    public HomeRepositortListener homeRepositortListener = new HomeRepositortListener();

    // 定义OnHandleException接口，用于处理请求结果
    public interface OnHandleException<T> {
        void success(T data);

        void error(String msg);
    }

    /**
     * 设置Activity引用
     */
    public void setActivity(Activity activity) {
        this.activity = activity;
    }

    /**
     * 设置导航器
     */
    public void setNavigator(HomeActivityNavigator navigator) {
        this.navigator = navigator;
    }

    /**
     * 初始化数据和依赖
     */
    public void init(HomeActivityNavigator navigator) {
        this.navigator = navigator;

        // 初始化API服务
        if (mApi == null) {
            mApi = new Http().create(HomeSeriveApi.class);
        }

        // 初始化Repository
        if (repository == null) {
            repository = new HomeFlutterRepository();
            repository.init(new HomeRepositortListener());
        }

        // 初始化数据
        initData();
    }

    /**
     * 初始化数据
     */
    public void initData() {
        // 不使用默认初始化数据，只初始化paperType确保不为null
        paperType.setValue("");

        // 初始化其他数据
        studyTime.setValue("0小时0分钟");
        studyTimeMinutes.setValue(0);
        latestExamScore.setValue(0);
        knowledgeMastery.setValue(0);
        studyDays.setValue(0);
        currentCount.setValue("0");
        questionCount.setValue("0");
        rightStr.setValue("0%");
        questionMakingProgress.setValue(0);
        paperTitle.setValue("");
        isShowAds.setValue(false);
        midssAds.setValue("");
        midssBuyActice.setValue(false);
        isShowExamDate.setValue(false);
        examDateTip.setValue("");
        videoType.setValue(0);
        buyVip.setValue(0);
        isOfficial.setValue(false);
        officialText.setValue("官方题库");
        trialQuestions.setValue(0);
        trialPapers.setValue(0);
        usedTrialQuestions.setValue(0);
        usedTrialPapers.setValue(0);
        vipUsagePercent.setValue(0);
        hasStartedChapterPractice.setValue(false);
        shareNavs.setValue(new ArrayList<>());

        // 打印初始化信息
        System.out.println("StudyViewModel - initData - paperType初始值: " + paperType.getValue());

        // 检查用户VIP状态
        if (AccountHelper.isUserLogin()) {
            buyVip.setValue(UserHelper.isBecomeVip() ? 1 : 0);
        } else {
            buyVip.setValue(0);
        }
        msgRepository = new MsgRepository();
        HomeMsgReport.getInstance().setMsgType(msgRepository);
    }

    public IMsg IMsgListener = new IMsg() {

        @Override
        public void verificationCodeLogin() {
            openVerificationCodeLogin();
        }

        @Override
        public void accountLoginSuccess() {
            loginSuccess();
        }

        @Override
        public void accountOut() {
            loginSuccess();
        }

        @Override
        public void accountAutoOffline() {
//            if(!mRoute.contains("main")){
//                homeFragment.finish();
//            }
//            accountOffline();
        }

        @Override
        public void successPayInfo() {
//            userPaySuccessInfo();
        }

        @Override
        public void liveSuccess() {
//            onHomeFlutterToJson(null,"livePreSuccess",null);
        }

        @Override
        public void checkCouponid() {
            LogUtils.e("优惠卷----->>>>1");
            checkLocalCoupon();
        }

        @Override
        public void finshPageView() {
//            closeView();
        }

        @Override
        public void finshActivity() {
            activity.finish();
        }

        @Override
        public void nativeToFlutterMouth(String key, String json) {
//            onHomeFlutterToJson(null,key,json);
        }

        @Override
        public void paySuccess() {
//            if(mRoute.contains(RouteConfig.ROUTE_ERROR)){
//                reshSaveOrErrorHttpsDatas();
//            }else if(mRoute.contains(RouteConfig.ROUTE_SAVE)){
//                reshSaveOrErrorHttpsDatas();
//            }else if(mRoute.contains(RouteConfig.ROUTE_LIVEPRE)){
//                if(HomeConfig.isToastPay){
//                    reshLivePreHttpsDatas();
//                }
//            }else if(mRoute.contains(RouteConfig.ROUTE_TESTDATE)){
//                LogUtils.e("日历支付成功");
//                navigator.closeView();
//            } else if(mRoute.contains(RouteConfig.ROUTE_ORDER)){
//                LogUtils.e("开始刷新订单信息------>>>>>");
//                nativeToFlutterMouth("conpionsuccess",null);
//            }else if(mRoute.contains(RouteConfig.ROUTE_HOME)){
//                userPaySuccessInfo();
//                nativeToFlutterMouth("paySuccess",null);
//            }else {
//                nativeToFlutterMouth("paySuccess",null);
//            }
        }

        @Override
        public void checkPageClose() {
//            LogUtils.e("开始关闭------>>>>1"+mRoute);
//            if(mRoute.contains(RouteConfig.ROUTE_HOME))return;
//            navigator.closeView();
        }

        @Override
        public void checkPageRouthFinash() {
//            LogUtils.e("更新了证书状态---->>>"+mRoute);
//            if(mRoute.contains(RouteConfig.ROUTE_INDEXGUIDE)){
//                MainThreadUtils.post(new Runnable() {
//                    @Override
//                    public void run() {
//                        navigator.toMainPage(BaseConfig.MAJOR_PID+"");
//                    }
//                });
//            }
        }

        @Override
        public void payFilaTs() {
//            if(mRoute.contains(RouteConfig.ROUTE_RESULTS)){
//                MessageHelper.openPayFailServer(homeFragment.getActivity(),homeFragment);
//            }
        }
    };

    public void loginSuccess() {
        queryCouponInfo();
//        iHomeFragment.loginSuccess();
    }

    public void queryCouponInfo() {
        if (!isDoubleClick()) {
            MainThreadUtils.postDelayed(new Runnable() {
                @Override
                public void run() {
                    TaskHelper.detectionCoupon(new CoupoinValidationListenter() {
                        @Override
                        public void onCoupoinShow() {
                        }
                    });
                }
            }, 500L);
        }
    }

    public void openVerificationCodeLogin() {
        ((HomeNativeActivity) activity).startFragment(AccountHelper.showAccountLogin());
//        AccountHelper.checkLoginState();
    }

    public void checkLocalCoupon() {
        MainThreadUtils.postDelayed(new Runnable() {
            @Override
            public void run() {
                TaskHelper.detectionCoupon(new CoupoinValidationListenter() {
                    @Override
                    public void onCoupoinShow() {
//                        LogUtils.e("优惠卷----->>>>2"+iHomeFragment==null?"yes":"no");
//                        if(iHomeFragment==null){
//                            Bus.post(new MsgEvent(666666));
//                        }else {
//                            iHomeFragment.clearAllActivity();
//                        }
                    }
                });
            }
        }, 500L);
    }

    /**
     * 获取首页数据 - 从HomeViewModel移植过来的方法
     *
     * @param result          Flutter方法调用结果回调
     * @param passCertificate 是否通过证书验证
     */
    public void getIndexDatas(MethodChannel.Result result, boolean passCertificate) {
        // 确保API服务已初始化
        ensureApi();

        LogUtils.e("StudyViewModel - getIndexDatas - 开始获取首页数据");

        Map<String, String> params = new HashMap<>();
        if (BaseConfig.channelUID != null && !BaseConfig.channelUID.isEmpty()) {
            params.put("fromWhichPerson", BaseConfig.channelUID);
        }

        customLaunchRequest(mApi.getMainInfo(params), new OnHandleException<BaseData<HomeBean>>() {
            @Override
            public void success(BaseData<HomeBean> data) {
                // 更新HomeBean LiveData
                if (data != null && data.Data != null) {
                    LogUtils.e("StudyViewModel - getIndexDatas - 成功获取首页数据");
                    homeBean.setValue(data.Data);

                    // 更新数据到ViewModel
                    updateFromHomeBean(data.Data);

                    // 更新全局配置
                    BaseConfig.ORDER_BIND_TYPE = data.Data.getOrderBindType();
                    LogUtils.e("设置了升级----->>>" + BaseConfig.ORDER_BIND_TYPE);
                } else {
                    LogUtils.e("StudyViewModel - getIndexDatas - 首页数据为空");
                }

                // 通知Activity更新UI
                if (navigator != null && navigator instanceof HomeNativeActivity) {
                    ((HomeNativeActivity) navigator).onHomeDataRefresh(data);
                }

                // 处理Repository回调
                if (repository != null) {
                    repository.successNetWorkDatas(data, result, passCertificate);
                }
            }

            @Override
            public void error(String msg) {
                LogUtils.e("StudyViewModel - getIndexDatas - 获取数据失败: " + msg);
                if (navigator != null) {
                    navigator.showToast(msg);
                }
            }
        });
    }

    /**
     * 自定义的网络请求处理方法
     */
    private <T> Disposable customLaunchRequest(Observable<T> observable, final OnHandleException<T> callback) {
        return observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<T>() {
                    @Override
                    public void accept(T t) throws Exception {
                        if (callback != null) {
                            callback.success(t);
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (callback != null) {
                            callback.error(throwable.getMessage());
                        }
                    }
                });
    }

    /**
     * 刷新首页数据
     *
     * @param passCertificate 是否通过证书验证
     */
    public void reshHomeData(boolean passCertificate) {
        getIndexDatas(null, passCertificate);
    }

    /**
     * 使用HomeBean数据更新AI学习助手模块
     *
     * @param homeBean 从API获取的HomeBean数据
     */
    public void updateFromHomeBean(HomeBean homeBean) {
        if (homeBean == null) {
            LogUtils.e("StudyViewModel - updateFromHomeBean - homeBean为空");
            return;
        }

        try {
            // 更新HomeBean
            this.homeBean.setValue(homeBean);

            // 更新试卷标题
            if (homeBean.getPaperTitle() != null && !homeBean.getPaperTitle().isEmpty()) {
                paperTitle.setValue(homeBean.getPaperTitle());
                LogUtils.e("StudyViewModel - updateFromHomeBean - 设置paperTitle: " + homeBean.getPaperTitle());
            } else {
                paperTitle.setValue("模拟考试");
                LogUtils.e("StudyViewModel - updateFromHomeBean - 设置默认paperTitle: 模拟考试");
            }

            // 更新试卷类型
            inferPaperType(homeBean);

            // 更新Banner数据
            if (homeBean.getBanners() != null) {
                updateBanners(homeBean.getBanners());
            }

            // 更新菜单导航
            if (homeBean.getMenuNavs() != null) {
                LogUtils.e("StudyViewModel - updateFromHomeBean - 收到菜单导航数据，数量：" + homeBean.getMenuNavs().size());
                updateMenuNavs(homeBean.getMenuNavs());
            } else {
                LogUtils.e("StudyViewModel - updateFromHomeBean - 菜单导航数据为null");
            }

            // 更新底部分享按钮
            if (homeBean.getShareNavs() != null) {
                LogUtils.e("StudyViewModel - updateFromHomeBean - 收到底部分享按钮数据，数量：" + homeBean.getShareNavs().size());
                // 打印每个分享按钮的详细信息
                for (HomeShaperBean shareNav : homeBean.getShareNavs()) {
                    LogUtils.e("StudyViewModel - updateFromHomeBean - 分享按钮: " + shareNav.getTitle()
                            + ", 类型: " + shareNav.getNavType()
                            + ", 是否启用: " + shareNav.isAtWork()
                            + ", 图片URL: " + shareNav.getImgSrc());
                }
                updateShareNavs(homeBean.getShareNavs());
            } else {
                LogUtils.e("StudyViewModel - updateFromHomeBean - 底部分享按钮数据为null");
            }

            // 更新通关精讲课数据
            if (homeBean.getLives() != null && homeBean.getLives().getItems() != null) {
                updateLives(homeBean.getLives().getItems());
            }

            // 更新官方题库状态
            isOfficial.setValue(homeBean.isOfficial());

            // 更新官方题库通知文本
            if (homeBean.getOfficialText() != null && !homeBean.getOfficialText().isEmpty()) {
                officialText.setValue(homeBean.getOfficialText());
            }

            // 更新学习数据
            // 学习时间
            if (homeBean.getStudyMinutes() > 0) {
                // 直接使用HomeBean中的StudyMinutes，不调用updateStudyTime()
                studyTimeMinutes.setValue(homeBean.getStudyMinutes());
                studyTime.setValue(String.valueOf(homeBean.getStudyMinutes()));
                LogUtils.e("StudyViewModel - updateFromHomeBean - 设置学习时间: " + homeBean.getStudyMinutes() + "分钟");
            }

            // 最新模考分数
            if (homeBean.getExamScores() != null) {
                try {
                    int score = Integer.parseInt(homeBean.getExamScores());
                    latestExamScore.setValue(score);
                } catch (NumberFormatException e) {
                    LogUtils.e("StudyViewModel - updateFromHomeBean - 解析ExamScores异常: " + e.getMessage());
                }
            }

            // 知识掌握度
            if (homeBean.getMasteryPercent() != null) {
                try {
                    int mastery = Integer.parseInt(homeBean.getMasteryPercent());
                    knowledgeMastery.setValue(mastery);
                } catch (NumberFormatException e) {
                    LogUtils.e("StudyViewModel - updateFromHomeBean - 解析MasteryPercent异常: " + e.getMessage());
                }
            }

            // 坚持学习天数
            studyDays.setValue(homeBean.getStudyDays());

            // 更新做题进度
            if (homeBean.getQuestionMakingProgress() != null) {
                try {
                    int progress = Integer.parseInt(homeBean.getQuestionMakingProgress());
                    questionMakingProgress.setValue(progress);
                } catch (NumberFormatException e) {
                    LogUtils.e("StudyViewModel - updateFromHomeBean - 解析QuestionMakingProgress异常: " + e.getMessage());
                }
            }

            // 更新正确率
            if (homeBean.getCorrectPercent() != null) {
                rightStr.setValue(homeBean.getCorrectPercent());
            }

            // 更新题目数量
            currentCount.setValue(String.valueOf(homeBean.getAnswerNumber()));
            questionCount.setValue(String.valueOf(homeBean.getTotalNumber()));

            // 更新广告相关数据
            isShowAds.setValue(homeBean.isShowAds());

            // 更新考试日期相关数据
            isShowExamDate.setValue(homeBean.isShowExamDate());
            if (homeBean.getExamDateTip() != null) {
                // 构建考试日期提示文本
                HomeExamDateTipBean tipBean = homeBean.getExamDateTip();
                StringBuilder tipText = new StringBuilder();

                if (tipBean.isSetExamDate() && tipBean.getRemainingExamDays() != null) {
                    tipText.append("距离考试还有").append(tipBean.getRemainingExamDays()).append("天");

                    if (tipBean.getRemainingQuestionNum() != null) {
                        tipText.append("，还有").append(tipBean.getRemainingQuestionNum()).append("题未做");
                    }

                    if (tipBean.getRemainingNoClearNum() != null) {
                        tipText.append("，").append(tipBean.getRemainingNoClearNum()).append("题未消除");
                    }
                } else {
                    tipText.append("请设置考试日期");
                }

                examDateTip.setValue(tipText.toString());
                LogUtils.e("StudyViewModel - updateFromHomeBean - 设置考试日期提示: " + tipText.toString());
            }

            // 更新视频类型
            videoType.setValue(homeBean.getLiveVieoType());

            LogUtils.e("StudyViewModel - updateFromHomeBean - 数据更新完成");
        } catch (Exception e) {
            LogUtils.e("StudyViewModel - updateFromHomeBean - 异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 尝试从HomeBean中的其他字段推断paperType
     */
    private void inferPaperType(com.zizhiguanjia.model_home.bean.HomeBean homeBean) {
        // 尝试从其他字段推断paperType
        try {
            // 检查是否有章节练习相关字段
            java.lang.reflect.Field chapterField = homeBean.getClass().getDeclaredField("isShowChapterPractice");
            chapterField.setAccessible(true);
            Boolean isShowChapterPractice = (Boolean) chapterField.get(homeBean);

            if (isShowChapterPractice != null && isShowChapterPractice) {
                paperType.setValue("5"); // 章节练习
                System.out.println("StudyViewModel - inferPaperType - 推断为章节练习(5)");
                return;
            }

            // 检查是否有模拟考试相关字段
            java.lang.reflect.Field examField = homeBean.getClass().getDeclaredField("isShowExam");
            examField.setAccessible(true);
            Boolean isShowExam = (Boolean) examField.get(homeBean);

            if (isShowExam != null && isShowExam) {
                paperType.setValue("4"); // 模拟考试
                System.out.println("StudyViewModel - inferPaperType - 推断为模拟考试(4)");
                return;
            }
        } catch (Exception e) {
            System.out.println("StudyViewModel - inferPaperType - 推断失败: " + e.getMessage());
        }

        // 如果无法推断，则根据answerNumber判断
        if (homeBean.getAnswerNumber() > 0) {
            // 如果已经做过题，则可能是章节练习
            paperType.setValue("5");
            System.out.println("StudyViewModel - inferPaperType - 根据answerNumber推断为章节练习(5)");
        } else {
            // 默认为题型练习
            paperType.setValue("3");
            System.out.println("StudyViewModel - inferPaperType - 设置默认值为题型练习(3)");
        }
    }

    /**
     * 创建菜单导航项
     */
    private MenuNavsBean createMenuNav(int navType, String name, int iconResId) {
        MenuNavsBean bean = new MenuNavsBean();
        bean.setNavType(navType);
        bean.setTitle(name);
        bean.setImgSrc(String.valueOf(iconResId));
        bean.setDo(true);
        bean.setDateBoxStatus(1);
        return bean;
    }

    /**
     * 刷新数据
     */
    public void refreshData() {
        // 从数据库获取最新的证书信息
        String title = CertificateHelper.initCertificate(AccountHelper.isUserLogin());
        LogUtils.e("StudyViewModel - refreshData - 更新证书信息: " + title);

        // 更新顶部标题（如果在HomeActivity中）
        if (activity instanceof HomeNativeActivity) {
            HomeNativeActivity homeActivity = (HomeNativeActivity) activity;
            // 更新顶部标题
            View homeHead = homeActivity.findViewById(R.id.home_head);
            if (homeHead != null) {
                TextView titleTopic = homeHead.findViewById(R.id.titleTopic);
                if (titleTopic != null) {
                    titleTopic.setText(title);
                    LogUtils.e("StudyViewModel - refreshData - 更新顶部标题成功: " + title);
                }
            }
        }

        // 重新初始化数据
        initData();

        // 重新请求首页数据，确保获取最新的学习进度
//        LogUtils.e("StudyViewModel - refreshData - 重新请求首页数据");
//        getIndexDatas();
    }

    /**
     * 完成一道题目
     */
    public void completeOneQuestion(boolean correct) {
        String currentStr = currentCount.getValue();
        if (currentStr == null) {
            currentCount.setValue("1");
        } else {
            try {
                int current = Integer.parseInt(currentStr);
                current++;
                currentCount.setValue(String.valueOf(current));
            } catch (NumberFormatException e) {
                LogUtils.e("StudyViewModel - completeOneQuestion - 数值转换异常: " + e.getMessage());
                currentCount.setValue("1");
            }
        }

        // 更新进度
        try {
            String currentCountStr = currentCount.getValue();
            String totalCountStr = questionCount.getValue();

            if (currentCountStr != null && totalCountStr != null) {
                int current = Integer.parseInt(currentCountStr);
                int total = Integer.parseInt(totalCountStr);
                int progress = (int) (((float) current / total) * 100);
                questionMakingProgress.setValue(progress);
            }
        } catch (NumberFormatException e) {
            LogUtils.e("StudyViewModel - completeOneQuestion - 进度计算异常: " + e.getMessage());
            questionMakingProgress.setValue(1);
        }

        // 更新正确率（简单模拟）
        int randomCorrect = new Random().nextInt(20) + 80; // 80-100之间的随机数
        rightStr.setValue(String.valueOf(randomCorrect));

        // 更新已使用的试题数量
        Integer used = usedTrialQuestions.getValue();
        usedTrialQuestions.setValue(used != null ? used + 1 : 1);

        // 更新VIP使用进度
        updateVipUsagePercent();

        // 更新章节练习状态
        hasStartedChapterPractice.setValue(true);

        // 更新学习时间
        Integer currentMinutes = studyTimeMinutes.getValue();
        int newMinutes = (currentMinutes != null ? currentMinutes : 0) + 1; // 每完成一题增加1分钟
        updateStudyTimeMinutes(newMinutes);
    }

    /**
     * 更新学习时间
     */
    private void updateStudyTime() {
        Integer currentMinutes = studyTimeMinutes.getValue();
        int newMinutes = (currentMinutes != null ? currentMinutes : 0) + 1; // 每完成一题增加1分钟
        studyTimeMinutes.setValue(newMinutes);
        studyTime.setValue(String.valueOf(newMinutes));
    }

    /**
     * 获取章节练习的格式化学习时间
     */
    public String getFormattedStudyTime() {
        Integer minutesValue = studyTimeMinutes.getValue();
        if (minutesValue == null) {
            return "学习时间:0分钟";
        }

        int minutes = minutesValue;
        if (minutes < 60) {
            return "学习时间:" + minutes + "分钟";
        } else {
            int hours = minutes / 60;
            int remainingMinutes = minutes % 60;
            return "学习时间:" + hours + "小时" + (remainingMinutes > 0 ? remainingMinutes + "分钟" : "");
        }
    }

    /**
     * 获取进度百分比文本
     */
    public String getProgressPercentText() {
        String currentStr = currentCount.getValue();
        String totalStr = questionCount.getValue();

        if (currentStr == null || totalStr == null) {
            return "0.00%";
        }

        try {
            int current = Integer.parseInt(currentStr);
            int total = Integer.parseInt(totalStr);
            float percent = total > 0 ? ((float) current / total) * 100 : 0;
            return String.format("%.2f%%", percent);
        } catch (NumberFormatException e) {
            LogUtils.e("StudyViewModel - getProgressPercentText - 数值转换异常: " + e.getMessage());
            return "0.00%";
        }
    }

    /**
     * 获取"开始练习"或"继续练习"的按钮文本
     */
    public String getPracticeButtonText() {
        Boolean hasStarted = hasStartedChapterPractice.getValue();
        return (hasStarted != null && hasStarted) ? "继续练习" : "开始练习";
    }

    /**
     * 重置章节练习状态
     */
    public void resetChapterPractice() {
        LogUtils.e("StudyViewModel - resetChapterPractice - 开始重置章节练习数据");

        // 确保在主线程执行
        MainThreadUtils.post(() -> {
            // 强制使用不同值来触发LiveData更新
            Random random = new Random();
            int randomValue = random.nextInt(5) - 10; // 生成-10到-5之间的随机数

            hasStartedChapterPractice.setValue(false);
            currentCount.setValue("0");
            questionMakingProgress.setValue(randomValue); // 使用随机负值，确保与前值不同
            rightStr.setValue("0%");
            studyTimeMinutes.setValue(randomValue);
            studyTime.setValue(String.valueOf(randomValue));

            LogUtils.e("StudyViewModel - resetChapterPractice - 章节练习数据已重置，使用随机负值: " + randomValue);

            // 再次设置正确的值，确保触发两次更新
            new Handler().postDelayed(() -> {
                hasStartedChapterPractice.setValue(false);
                currentCount.setValue("0");
                questionMakingProgress.setValue(0);
                rightStr.setValue("0%");
                studyTimeMinutes.setValue(0);
                studyTime.setValue("0");
                LogUtils.e("StudyViewModel - resetChapterPractice - 章节练习数据已重置为最终值");
            }, 100);
        });
    }

    /**
     * 判断是否已开始章节练习
     */
    public boolean hasStartedChapterPractice() {
        Boolean hasStarted = hasStartedChapterPractice.getValue();
        return (hasStarted != null && hasStarted);
    }

    /**
     * 设置章节练习状态
     *
     * @param hasStarted 是否已开始章节练习
     */
    public void setHasStartedChapterPractice(boolean hasStarted) {
        hasStartedChapterPractice.setValue(hasStarted);
    }

    /**
     * 更新已完成题目数
     *
     * @param count 已完成题目数
     */
    public void updateCurrentCount(String count) {
        currentCount.setValue(count);
    }

    /**
     * 更新总题目数
     *
     * @param count 总题目数
     */
    public void updateQuestionCount(String count) {
        questionCount.setValue(count);
    }

    /**
     * 更新正确率
     *
     * @param rightPercent 正确率百分比
     */
    public void updateRightStr(String rightPercent) {
        rightStr.setValue(rightPercent);
    }

    /**
     * 更新学习时间（分钟）
     *
     * @param minutes 学习时间（分钟）
     */
    public void updateStudyTimeMinutes(int minutes) {
        studyTimeMinutes.setValue(minutes);
        studyTime.setValue(String.valueOf(minutes));
    }

    /**
     * 更新做题进度
     *
     * @param progress 进度值（0-100）
     */
    public void updateQuestionMakingProgress(int progress) {
        questionMakingProgress.setValue(progress);
    }

    /**
     * 批量更新章节练习数据
     *
     * @param currentCount 已完成题目数
     * @param totalCount   总题目数
     * @param rightPercent 正确率
     * @param studyMinutes 学习时间（分钟）
     * @param progress     进度值（0-100）
     * @param hasStarted   是否已开始章节练习
     */
    public void updateChapterPracticeData(String currentCount, String totalCount,
                                          String rightPercent, int studyMinutes,
                                          int progress, boolean hasStarted) {
        this.currentCount.setValue(currentCount);
        this.questionCount.setValue(totalCount);
        this.rightStr.setValue(rightPercent);
        this.studyTimeMinutes.setValue(studyMinutes);
        this.studyTime.setValue(String.valueOf(studyMinutes));
        this.questionMakingProgress.setValue(progress);
        this.hasStartedChapterPractice.setValue(hasStarted);
    }

    /**
     * 更新VIP使用进度百分比
     */
    private void updateVipUsagePercent() {
        Integer totalQuestions = trialQuestions.getValue();
        Integer usedQuestions = usedTrialQuestions.getValue();
        Integer totalPapers = trialPapers.getValue();
        Integer usedPapers = usedTrialPapers.getValue();

        // 默认值处理
        int tq = totalQuestions != null ? totalQuestions : 100;
        int uq = usedQuestions != null ? usedQuestions : 0;
        int tp = totalPapers != null ? totalPapers : 3;
        int up = usedPapers != null ? usedPapers : 0;

        // 计算总体使用百分比（简化计算，实际可能需要更复杂的逻辑）
        float questionPercent = tq > 0 ? (float) uq / tq : 0;
        float paperPercent = tp > 0 ? (float) up / tp : 0;
        int percent = (int) ((questionPercent * 0.7 + paperPercent * 0.3) * 100);

        vipUsagePercent.setValue(percent);
    }

    /**
     * 处理点击事件
     */
    public void click(View view) {
        if (view.getId() == R.id.imgMainUser) {
            handleUserIconClick();
        } else if (view.getId() == R.id.llHomeSwitch) {
            handleAreaSwitchClick();
        }
    }

    /**
     * 处理用户头像点击
     */
    private void handleUserIconClick() {
        if (!NoDoubleClickUtils.isDoubleClick()) {
            LogUtils.e("点击头像，直接跳转到个人信息页面");
            // 直接跳转到个人信息页面，不检查登录状态
            UserHelper.start(activity);
        }
    }

    /**
     * 处理区域切换点击
     */
    private void handleAreaSwitchClick() {
        if (!NoDoubleClickUtils.isDoubleClick()) {
            LogUtils.e("点击区域和科目选择");
            // 判断是否选择过证书
            if (CertificateHelper.userChoiceCertificate()) {
                // 已选择证书，跳转到证书选择页面
                String address = CertificateHelper.getCurrentCertificateAddressName();
                if (activity instanceof HomeNativeActivity) {
                    ((HomeNativeActivity) activity).toGoChoiceCretificate(
                            address,
                            BaseConfig.cityCode,
                            true
                    );
                }
            } else {
                // 未选择证书，跳转到地区选择页面
                if (activity instanceof HomeNativeActivity) {
                    ((HomeNativeActivity) activity).toGoChoiceAddress();
                }
            }
        }
    }

    /**
     * 处理Banner点击事件
     *
     * @param banner 被点击的Banner
     */
    public void handleBannerClick(BannersBean banner) {
//        if (!NoDoubleClickUtils.isDoubleClick()) {
        LogUtils.e("点击Banner: " + banner.getRouteUrl());
        if (!AccountHelper.checkLoginState()) {
            return;
        }
        // 检查是否需要登录
//            if (banner.getLoginStatus() == 1 && !AccountHelper.isUserLogin()) {
//                // 需要登录但用户未登录，提示用户登录
//                MessageHelper.openGeneralCentDialog(activity, "登录提示", "请先登录后再使用此功能", "取消", "去登录", false, false, new GeneralDialogListener() {
//                    @Override
//                    public void onConfim() {
//                        // 跳转登录页面
////                        AccountHelper.showAccountLogin();
//                        AccountHelper.checkLoginState();
//                    }
//
//                    @Override
//                    public void onCancel() {
//                        // 取消操作
//                    }
//
//                    @Override
//                    public void onDismiss() {
//                        // 对话框消失
//                    }
//                });
//                return;
//            }

        // 根据路由类型处理点击事件
        switch (banner.getRouteType()) {
            case 1: // 应用内跳转
                if (activity instanceof HomeNativeActivity) {
                    // 如果是VIP购买页面
                    if (banner.getRouteUrl().contains("/pay")) {
                        // 统计点击事件
                        com.zizhiguanjia.lib_base.helper.PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_BANNER_REFUND, false);
                        // 跳转到支付页面
                        ((HomeNativeActivity) activity).showPayFragment(PayRouthConfig.PAY_BANNER);
                    } else {
                        // 其他应用内页面跳转
                        ((HomeNativeActivity) activity).handleBannerNavigation(banner.getRouteUrl());
                    }
                }
                break;
            case 2: // 外部网页跳转
                if (activity instanceof HomeNativeActivity) {
                    ((HomeNativeActivity) activity).openWebView(banner.getRouteUrl(), "");
                }
                break;
            case 3:
                if (!banner.getRouteUrl().isEmpty()) {
                    ((HomeNativeActivity) activity).handleBannerNavigation(banner.getRouteUrl());
                }
                break;
            default:
                ToastUtils.normal("不支持的跳转类型");
                break;
        }
//        }
    }

    /**
     * 确保API服务已初始化
     */
    private void ensureApi() {
        if (mApi == null) {
            mApi = new Http().create(HomeSeriveApi.class);
        }
    }

    /**
     * 关闭摸底测评的网络请求
     *
     * @param handler 回调处理器
     */
    public void closeEvaluation(OnHandleException<BaseData> handler) {
        ensureApi();
        Map<String, String> params = new HashMap<>();
        Observable<BaseData> baseDataObservable = mApi.closeEvaluation(params);
        // 使用自定义的请求处理方法替代launchOnlyResult
        customLaunchRequest(baseDataObservable, handler);
    }

    /**
     * 打开课程详情页面
     *
     * @param liveId 直播ID
     * @param userId 用户ID
     * @return 课程详情页面的Fragment
     */
    public IFragment openCourseDetail(String liveId, String userId) {
        // 检查用户是否已登录
        if (!AccountHelper.isUserLogin()) {
            ToastUtils.normal("请先登录");
            return AccountHelper.showAccountLogin();
        }

        LogUtils.e("StudyViewModel - 打开课程详情，liveId: " + liveId + ", userId: " + userId);

        // 使用ARouterUtils导航到课程详情页面
        try {
            // 创建参数Bundle
            android.os.Bundle args = new android.os.Bundle();
            args.putString("index", liveId);
            args.putString("useId", userId);

            // 返回导航到课程详情页面的Fragment
            IFragment fragment = com.example.lib_common.arouter.ARouterUtils.navFragment("home/course_detail");

            // 注意：由于IFragment接口没有setArguments方法，我们需要在调用方设置参数
            // 返回Fragment和参数
            return fragment;
        } catch (Exception e) {
            LogUtils.e("StudyViewModel - 导航到课程详情页面失败：" + e.getMessage());
            return null;
        }
    }

    /**
     * 获取课程详情页面的参数Bundle
     *
     * @param liveId 直播ID
     * @param userId 用户ID
     * @return 参数Bundle
     */
    public android.os.Bundle getCourseDetailArgs(String liveId, String userId) {
        android.os.Bundle args = new android.os.Bundle();
        args.putString("index", liveId);
        args.putString("useId", userId);
        return args;
    }

    /**
     * 打开课程列表页面
     *
     * @return 课程列表页面的Fragment
     */
    public IFragment openCourseList() {
        // 检查用户是否已登录
        if (!AccountHelper.isUserLogin()) {
            ToastUtils.normal("请先登录");
            return AccountHelper.showAccountLogin();
        }

        LogUtils.e("StudyViewModel - 打开课程列表页面");

        // 使用ListHelper跳转到章节练习页面
        try {
            // 创建参数Bundle
            android.os.Bundle args = new android.os.Bundle();
            args.putInt("index", 1);  // 设置索引参数

            // 返回导航到章节练习页面的Fragment
            IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamList();

            // 注意：由于IFragment接口没有setArguments方法，我们需要在调用方设置参数
            // 返回Fragment和参数
            return fragment;
        } catch (Exception e) {
            LogUtils.e("StudyViewModel - 导航到章节练习页面失败：" + e.getMessage());
            return null;
        }
    }

    /**
     * 获取课程列表页面的参数Bundle
     *
     * @return 参数Bundle
     */
    public android.os.Bundle getCourseListArgs() {
        android.os.Bundle args = new android.os.Bundle();
        args.putInt("index", 1);  // 设置索引参数
        return args;
    }

    // Getters for LiveData
    public LiveData<String> getStudyTime() {
        return studyTime;
    }

    public LiveData<Integer> getStudyTimeMinutes() {
        return studyTimeMinutes;
    }

    public LiveData<Integer> getLatestExamScore() {
        return latestExamScore;
    }

    public LiveData<Integer> getKnowledgeMastery() {
        return knowledgeMastery;
    }

    public LiveData<Integer> getStudyDays() {
        return studyDays;
    }

    public LiveData<String> getCurrentCount() {
        return currentCount;
    }

    public LiveData<String> getQuestionCount() {
        return questionCount;
    }

    public LiveData<String> getRightStr() {
        return rightStr;
    }

    public LiveData<Integer> getQuestionMakingProgress() {
        return questionMakingProgress;
    }

    /**
     * 获取试卷标题
     */
    public LiveData<String> getPaperTitle() {
        LogUtils.e("StudyViewModel - getPaperTitle - 当前值: " + (paperTitle.getValue() != null ? paperTitle.getValue() : "null"));
        return paperTitle;
    }

    public LiveData<String> getPaperType() {
        return paperType;
    }

    public LiveData<List<MenuNavsBean>> getMenuNavs() {
        return menuNavs;
    }

    public LiveData<Boolean> getIsShowAds() {
        return isShowAds;
    }

    public LiveData<String> getMidssAds() {
        return midssAds;
    }

    public LiveData<Boolean> getMidssBuyActice() {
        return midssBuyActice;
    }

    public LiveData<Boolean> getIsShowExamDate() {
        return isShowExamDate;
    }

    public LiveData<String> getExamDateTip() {
        return examDateTip;
    }

    public LiveData<Integer> getVideoType() {
        return videoType;
    }

    public LiveData<Integer> getBuyVip() {
        return buyVip;
    }

    public LiveData<Integer> getTrialQuestions() {
        return trialQuestions;
    }

    public LiveData<Integer> getTrialPapers() {
        return trialPapers;
    }

    public LiveData<Integer> getUsedTrialQuestions() {
        return usedTrialQuestions;
    }

    public LiveData<Integer> getUsedTrialPapers() {
        return usedTrialPapers;
    }

    public LiveData<Integer> getVipUsagePercent() {
        return vipUsagePercent;
    }

    /**
     * 获取完整的HomeBean数据
     */
    public LiveData<HomeBean> getHomeBean() {
        return homeBean;
    }

    /**
     * 获取Banner数据
     */
    public LiveData<List<BannersBean>> getBanners() {
        return banners;
    }

    /**
     * 获取通关精讲课数据
     */
    public LiveData<List<com.zizhiguanjia.model_home.bean.ItemsBean>> getLives() {
        return lives;
    }

    /**
     * 获取底部分享功能按钮数据
     */
    public LiveData<List<com.zizhiguanjia.model_home.bean.HomeShaperBean>> getShareNavs() {
        return shareNavs;
    }

    /**
     * 获取是否官方题库状态
     */
    public LiveData<Boolean> getIsOfficial() {
        return isOfficial;
    }

    /**
     * 获取官方题库通知文本
     */
    public LiveData<String> getOfficialText() {
        return officialText;
    }

    /**
     * 更新Banner数据
     */
    public void updateBanners(List<BannersBean> newBanners) {
        if (newBanners != null) {
            banners.setValue(newBanners);
        }
    }

    /**
     * 更新通关精讲课数据
     */
    public void updateLives(List<com.zizhiguanjia.model_home.bean.ItemsBean> newLives) {
        if (newLives != null) {
            lives.setValue(newLives);
            LogUtils.e("StudyViewModel - updateLives - 更新通关精讲课数据，数量: " + newLives.size());
        }
    }

    /**
     * 更新菜单导航数据
     */
    public void updateMenuNavs(List<MenuNavsBean> newMenuNavs) {
        if (newMenuNavs != null) {
            // 克隆一个新列表，避免直接引用可能被修改的列表
            List<MenuNavsBean> clonedNavs = new ArrayList<>(newMenuNavs);

            // 日志输出当前和新值
            List<MenuNavsBean> currentNavs = menuNavs.getValue();
            LogUtils.e("StudyViewModel - updateMenuNavs - 当前菜单项数量: " +
                    (currentNavs != null ? currentNavs.size() : 0) +
                    ", 新菜单项数量: " + clonedNavs.size());

            // 确保在主线程中更新值，以便观察者能正确接收
            MainThreadUtils.post(() -> {
                menuNavs.setValue(clonedNavs);
                LogUtils.e("StudyViewModel - updateMenuNavs - setValue已调用，当前线程: " +
                        (android.os.Looper.myLooper() == android.os.Looper.getMainLooper() ? "主线程" : "非主线程"));
            });

            // 打印所有菜单项的标题和类型，方便调试
            for (MenuNavsBean nav : clonedNavs) {
                LogUtils.e("StudyViewModel - 菜单项: " + nav.getTitle() + ", 类型: " + nav.getNavType());
            }

            // 手动触发一次更新（为了解决某些情况下观察者不触发的问题）
            new Handler().postDelayed(() -> {
                LogUtils.e("StudyViewModel - updateMenuNavs - 尝试再次触发观察者");
                List<MenuNavsBean> currentList = menuNavs.getValue();
                if (currentList != null) {
                    List<MenuNavsBean> newList = new ArrayList<>(currentList);
                    menuNavs.setValue(newList);
                }
            }, 500);
        } else {
            LogUtils.e("StudyViewModel - updateMenuNavs - 菜单导航数据为null");
        }
    }

    /**
     * 更新底部分享功能按钮数据
     */
    public void updateShareNavs(List<com.zizhiguanjia.model_home.bean.HomeShaperBean> newShareNavs) {
        if (newShareNavs != null) {
            // 克隆一个新列表，避免直接引用可能被修改的列表
            List<com.zizhiguanjia.model_home.bean.HomeShaperBean> clonedNavs = new ArrayList<>(newShareNavs);

            // 日志输出当前和新值
            List<com.zizhiguanjia.model_home.bean.HomeShaperBean> currentNavs = shareNavs.getValue();
            LogUtils.e("StudyViewModel - updateShareNavs - 当前分享按钮数量: " +
                    (currentNavs != null ? currentNavs.size() : 0) +
                    ", 新分享按钮数量: " + clonedNavs.size());

            // 确保在主线程中更新值，以便观察者能正确接收
            MainThreadUtils.post(() -> {
                shareNavs.setValue(clonedNavs);
                LogUtils.e("StudyViewModel - updateShareNavs - setValue已调用，当前线程: " +
                        (android.os.Looper.myLooper() == android.os.Looper.getMainLooper() ? "主线程" : "非主线程"));
            });

            // 打印所有分享按钮的标题和类型，方便调试
            for (com.zizhiguanjia.model_home.bean.HomeShaperBean nav : clonedNavs) {
                LogUtils.e("StudyViewModel - 分享按钮: " + nav.getTitle() +
                        ", 类型: " + nav.getNavType() +
                        ", 是否启用: " + nav.isAtWork() +
                        ", 图标URL: " + nav.getImgSrc());
            }

            // 手动触发一次更新（为了解决某些情况下观察者不触发的问题）
            new Handler().postDelayed(() -> {
                LogUtils.e("StudyViewModel - updateShareNavs - 尝试再次触发观察者");
                List<com.zizhiguanjia.model_home.bean.HomeShaperBean> currentList = shareNavs.getValue();
                if (currentList != null) {
                    List<com.zizhiguanjia.model_home.bean.HomeShaperBean> newList = new ArrayList<>(currentList);
                    shareNavs.setValue(newList);
                }
            }, 500);
        } else {
            LogUtils.e("StudyViewModel - updateShareNavs - 分享按钮数据为null");
        }
    }

    /**
     * 处理功能区按钮点击事件
     * 从HomeFlutterViewModel移植过来的方法
     *
     * @param bean 菜单导航数据
     */
    public void onMainNavFunctionClick(MenuNavsBean bean) {
        if (bean == null || activity == null) {
            System.out.println("StudyViewModel - onMainNavFunctionClick - bean或activity为null");
            return;
        }

        // 添加日志输出
        // LogUtils.e("StudyViewModel - onMainNavFunctionClick - 按钮类型: " + bean.getType() + ", 标题: " + bean.getTitle());
        System.out.println("StudyViewModel - onMainNavFunctionClick - 按钮类型: " + bean.getType() + ", 标题: " + bean.getTitle());

        // 检查是否已登录（除了区域/科目选择和头像按钮外，其他按钮都需要登录）
        if (!AccountHelper.isUserLogin()) {
            ToastUtils.normal("请先登录");
            if (activity instanceof HomeNativeActivity) {
//                ((HomeNativeActivity) activity).startFragment(AccountHelper.showAccountLogin());
                AccountHelper.checkLoginState();
            }
            return;
        }

        // 强制主线程执行
        if (android.os.Looper.myLooper() != android.os.Looper.getMainLooper()) {
            System.out.println("StudyViewModel - onMainNavFunctionClick - 当前不在主线程，切换到主线程");
            com.wb.lib_utils.utils.MainThreadUtils.post(() -> {
                handleNavButtonClick(bean);
            });
        } else {
            handleNavButtonClick(bean);
        }
    }

    /**
     * 实际处理按钮点击的方法
     */
    private void handleNavButtonClick(MenuNavsBean bean) {
        if (bean == null || activity == null) return;

        int buttonType = bean.getType();
        System.out.println("StudyViewModel - handleNavButtonClick - 处理按钮类型: " + buttonType);

        // 根据不同的按钮类型执行不同的操作
        switch (buttonType) {
            case MenuNavsBean.TYPE_ERROR: // 错题集
                System.out.println("StudyViewModel - 处理错题集按钮点击");
                if (activity instanceof HomeNativeActivity) {
                    android.os.Bundle args = new android.os.Bundle();
                    args.putString("flutterRoute", RouteConfig.ROUTE_ERROR);
                    HomeFragment homeFragment = new HomeFragment();
                    homeFragment.setArguments(args);
                    ((HomeNativeActivity) activity).startFragment(homeFragment);
//                    ((HomeNativeActivity) activity).startFragment(CoreExamHelper.mainPage(activity));
                }
                break;

            case MenuNavsBean.TYPE_SAVE: // 收藏夹
                System.out.println("StudyViewModel - 处理收藏夹按钮点击");
                if (activity instanceof HomeNativeActivity) {
                    try {
                        com.zizhiguanjia.model_home.fragment.HomeFragment fragment = new com.zizhiguanjia.model_home.fragment.HomeFragment();
                        android.os.Bundle args = new android.os.Bundle();
                        args.putString("flutterRoute", RouteConfig.ROUTE_SAVE);
                        fragment.setArguments(args);
                        ((HomeNativeActivity) activity).startFragment(fragment);
                    } catch (Exception e) {
                        System.out.println("StudyViewModel - 处理收藏夹按钮点击 - 异常: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                break;

            case MenuNavsBean.TYPE_PRACTICE: // 题型练习
                System.out.println("StudyViewModel - 处理题型练习按钮点击");
                if (activity instanceof HomeNativeActivity) {
                    try {
                        com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamList();
                        if (fragment == null) {
                            System.out.println("StudyViewModel - 处理题型练习按钮点击 - fragment为null");
                            ToastUtils.normal("无法打开题型练习页面");
                            return;
                        }

                        android.os.Bundle args = new android.os.Bundle();
                        args.putInt("index", 0);
                        args.putBoolean("chapter", false);
                        ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                        ((HomeNativeActivity) activity).startFragment(fragment);
                    } catch (Exception e) {
                        System.out.println("StudyViewModel - 处理题型练习按钮点击 - 异常: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                break;

            case MenuNavsBean.TYPE_EXAM: // 模拟考试
            case 10:
            case 11:
                System.out.println("StudyViewModel - 处理模拟考试按钮点击");
                if (activity instanceof HomeNativeActivity) {
                    boolean isDialog = bean.getDateBoxStatus() != 1;
//                    if(bean.isDo()){
                    goToAutoExam(bean.isDo(), isDialog, String.valueOf(bean.getNavType()));
//                    }else{
////                        try {
////                            com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamAuto();
////                            android.os.Bundle args = new android.os.Bundle();
////                            args.putInt("index", 0);
////                            args.putInt("mPageType", 1);
////                            args.putString("pagerType", "7");
////                            ((androidx.fragment.app.Fragment) fragment).setArguments(args);
////                            ((HomeNativeActivity) activity).startFragment(fragment);
////                        } catch (Exception e) {
////                            System.out.println("StudyViewModel - 处理模拟考试按钮点击 - 异常: " + e.getMessage());
////                            e.printStackTrace();
////                        }
//                        com.zizhiguanjia.model_home.fragment.HomeFragment fragment = new com.zizhiguanjia.model_home.fragment.HomeFragment();
////                        android.os.Bundle args = new android.os.Bundle();
////                        args.putString("flutterRoute", RouteConfig.ROUTE_RESULTS);
////                        fragment.setArguments(args);
////                        ((HomeNativeActivity) activity).startFragment(fragment);
//
//                        fragment.initArguments().putString("flutterRoute",RouteConfig.ROUTE_RESULTS+"/"+(UserHelper.isBecomeVip() ? "1" : "0") + "/" +7);
//                        fragment.initArguments().putBoolean("autoDialog",true);
//                        fragment.initArguments().putInt("mPageType",1);
//                        fragment.initArguments().putString("pagerType","7");
//                        ((HomeNativeActivity) activity).startFragment(fragment);
//                    }

//                    try {
//                        com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamAuto();
//                        android.os.Bundle args = new android.os.Bundle();
//                        args.putInt("index", 0);
//                        args.putInt("mPageType", 1);
//                        args.putString("pagerType", "7");
//                        ((androidx.fragment.app.Fragment)fragment).setArguments(args);
//                        ((HomeNativeActivity) activity).startFragment(fragment);
//                    } catch (Exception e) {
//                        System.out.println("StudyViewModel - 处理模拟考试按钮点击 - 异常: " + e.getMessage());
//                        e.printStackTrace();
//                    }
                }
                break;

            case MenuNavsBean.TYPE_REAL_EXAM: // 历年真题
                System.out.println("StudyViewModel - 处理历年真题按钮点击");
                if (activity instanceof HomeNativeActivity) {
                    try {
                        com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.showCommonListPage();
                        android.os.Bundle args = new android.os.Bundle();
                        args.putInt("paperType", 1);
                        ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                        ((HomeNativeActivity) activity).startFragment(fragment);
                    } catch (Exception e) {
                        System.out.println("StudyViewModel - 处理历年真题按钮点击 - 异常: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                break;

            case MenuNavsBean.TYPE_JIANZHU: // 二建模考
                System.out.println("StudyViewModel - 处理二建模考按钮点击");
                if (activity instanceof HomeNativeActivity) {
                    try {
                        com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.showCommonListPage();
                        android.os.Bundle args = new android.os.Bundle();
                        args.putInt("paperType", 2);
                        ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                        ((HomeNativeActivity) activity).startFragment(fragment);
                    } catch (Exception e) {
                        System.out.println("StudyViewModel - 处理二建模考按钮点击 - 异常: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                break;

            case MenuNavsBean.TYPE_HIGH_FREQ: // 高频考题
                System.out.println("StudyViewModel - 处理高频考题按钮点击");
                if (activity instanceof HomeNativeActivity) {
                    try {
                        // 创建高频考题Fragment
                        com.wb.lib_arch.base.IFragment fragment = new com.zizhiguanjia.model_core.fragment.HighFrequencyFragment();
                        // 跳转到高频考题页面
                        ((HomeNativeActivity) activity).startFragment(fragment);
                    } catch (Exception e) {
                        System.out.println("StudyViewModel - 处理高频考题按钮点击 - 异常: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                break;

            case 16: // 易错100题
                System.out.println("StudyViewModel - 处理易错100题按钮点击");
                if (activity instanceof HomeNativeActivity) {
                    try {
                        com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamDes();
                        android.os.Bundle args = new android.os.Bundle();
                        args.putString("title", "易错100题");
                        args.putBoolean("restart", false);
                        args.putString("paperType", String.valueOf(com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG));
                        ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                        ((HomeNativeActivity) activity).startFragment(fragment);
                    } catch (Exception e) {
                        System.out.println("StudyViewModel - 处理易错100题按钮点击 - 异常: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                break;

            case 17: // 学习资料
                System.out.println("StudyViewModel - 处理学习资料按钮点击");
                if (activity instanceof HomeNativeActivity) {
                    try {
                        com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageDocMajors();
                        ((HomeNativeActivity) activity).startFragment(fragment);
                    } catch (Exception e) {
                        System.out.println("StudyViewModel - 处理学习资料按钮点击 - 异常: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                break;
            case 21: //智能练习
                IFragment iFragment = CoreExamHelper.mainPage(activity);
                iFragment.initArguments().putString("title", "智能练习");
                iFragment.initArguments().putBoolean("restart", false);
                iFragment.initArguments().putString("paperType", "21");
                ((HomeNativeActivity) activity).startFragment(iFragment);
                break;

            case MenuNavsBean.TYPE_LIVE: // 直播课程
                System.out.println("StudyViewModel - 处理直播课程按钮点击");
                if (bean.getUrl() != null && !bean.getUrl().isEmpty() && activity instanceof HomeNativeActivity) {
                    try {
                        if (com.zizhiguanjia.model_home.utils.HomeUtils.getInstance().checkSechemValid(bean.getUrl())) {
                            android.content.Intent it = new android.content.Intent(android.content.Intent.ACTION_VIEW, android.net.Uri.parse(bean.getUrl()));
                            activity.startActivity(it);
                        } else {
                            com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.CommonHelper.showCommonWeb();
                            android.os.Bundle args = new android.os.Bundle();
                            args.putString("routh", "home");
                            args.putString("url", bean.getUrl());
                            args.putInt("payType", 1);
                            args.putString("payRouthParams", PayRouthConfig.PAY_BANNER);
                            ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                            ((HomeNativeActivity) activity).startFragment(fragment);
                        }
                    } catch (Exception e) {
                        System.out.println("StudyViewModel - 处理直播课程按钮点击 - 异常: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                break;
            case 99:
                if (!AccountHelper.checkLoginState()) {
                    return;
                }
                Bundle args = new Bundle();
//                args.putString("index", liveId);
//                args.putString("useId", userId);

                // 直接使用ARouterUtils导航到课程详情页面
                ((HomeNativeActivity) activity).startFragment(ARouterUtils.navFragment(HomeRoutherPath.COURSE_DETAIL));
                break;
            default:
                // 对于未处理的类型，尝试使用通用方法处理
                System.out.println("StudyViewModel - 尝试通用方法处理按钮类型: " + buttonType);
                handleUnknownButtonType(bean);
                break;
        }
    }

    public String getReroutType(String mRoute) {
        if (mRoute.equals(RouteConfig.ROUTE_ERROR)) {
            return "5";
        } else if (mRoute.equals(RouteConfig.ROUTE_SAVE)) {
            return "6";
        }
        return "0";
    }

    public void goToAutoExam(boolean toresults, boolean dialog, String type) {
        PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_SIMULATION, false);
//        if (repository.checkLoginOrAddress(true, BaseConfig.ORDER_BIND_TYPE)) {
//            int pageType = (Integer.parseInt(type) == 10 ) ? 1 : 0;
        int pageType = (Integer.parseInt(type) == 10 || Integer.parseInt(type) == 11) ? 1 : 0;
        String pagerType = Integer.parseInt(type) == 10 || Integer.parseInt(type) == 4 ? "7" : type;
        com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamDes();
        android.os.Bundle args = new android.os.Bundle();
//            args.putString("title", "易错100题");
//            args.putBoolean("restart", false);
//            args.putString("paperType", String.valueOf(com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG));
//            ((androidx.fragment.app.Fragment)fragment).setArguments(args);
//            ((HomeNativeActivity) activity).startFragment(fragment);
        if (toresults) {
            LogUtils.e("看看传递的111" + dialog);
            args.putString("flutterRoute", RouteConfig.ROUTE_RESULTS + "/" + (UserHelper.isBecomeVip() ? "1" : "0") + "/" + pagerType);
            args.putBoolean("autoDialog", dialog);
            args.putInt("mPageType", pageType);
            args.putString("pagerType", pagerType);
            HomeFragment homeFragment = new HomeFragment();
            homeFragment.setArguments(args);
            ((HomeNativeActivity) activity).startFragment(homeFragment);
        } else {
            args.putBoolean("autoDialog", dialog);
            args.putInt("mPageType", pageType);
            args.putString("pagerType", pagerType);
            ((HomeNativeActivity) activity).startFragment(ListHelper.toPageExamAuto());
        }
//        }
    }

    /**
     * 处理未知类型的按钮
     */
    private void handleUnknownButtonType(MenuNavsBean bean) {
        if (bean == null || activity == null) return;

        // 尝试使用通用方法处理
        try {
            // 如果有URL，尝试打开WebView
            if (bean.getUrl() != null && !bean.getUrl().isEmpty()) {
                System.out.println("StudyViewModel - 使用URL打开WebView: " + bean.getUrl());
                com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.CommonHelper.showCommonWeb();
                android.os.Bundle args = new android.os.Bundle();
                args.putString("routh", "home");
                args.putString("url", bean.getUrl());
                args.putInt("payType", 1);
                ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                ((HomeNativeActivity) activity).startFragment(fragment);
                return;
            }

            // 如果是题型练习类型，尝试打开题型练习页面
            if (bean.getTitle() != null && bean.getTitle().contains("练习")) {
                System.out.println("StudyViewModel - 尝试打开题型练习页面");
                com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamList();
                android.os.Bundle args = new android.os.Bundle();
                args.putInt("index", 1);
                ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                ((HomeNativeActivity) activity).startFragment(fragment);
                return;
            }

            // 如果是考试类型，尝试打开模拟考试页面
            if (bean.getTitle() != null && (bean.getTitle().contains("考试") || bean.getTitle().contains("模拟"))) {
                System.out.println("StudyViewModel - 尝试打开模拟考试页面");
                com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamAuto();
                android.os.Bundle args = new android.os.Bundle();
                args.putInt("index", 0);
                args.putInt("mPageType", 1);
                args.putString("pagerType", "7");
                ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                ((HomeNativeActivity) activity).startFragment(fragment);
                return;
            }

            // 其他情况，显示Toast提示
            System.out.println("StudyViewModel - 未知按钮类型，显示提示");
            ToastUtils.normal("暂不支持此功能: " + bean.getTitle());
        } catch (Exception e) {
            System.out.println("StudyViewModel - 处理未知按钮类型异常: " + e.getMessage());
            e.printStackTrace();
            ToastUtils.normal("操作失败，请稍后再试");
        }
    }

    /**
     * 处理底部分享功能按钮点击事件
     *
     * @param shareNav 被点击的按钮数据
     */
    public void handleShareNavClick(HomeShaperBean shareNav) {
        LogUtils.e("StudyViewModel - handleShareNavClick - 点击底部分享功能按钮: " + shareNav.getTitle());

        try {
            String shareData = shareNav.getShareData();
            if (TextUtils.isEmpty(shareData)) {
                shareData = "";
            }

            switch (shareNav.getNavType()) {
                case 1: // 微信好友
                    // 使用SdkHelper处理微信分享
                    com.zizhiguanjia.lib_base.helper.SdkHelper.shareWechatByType(shareData, 1);
                    LogUtils.e("StudyViewModel - handleShareNavClick - 调用微信好友分享");
                    break;

                case 2: // 朋友圈
                    // 使用SdkHelper处理朋友圈分享
                    com.zizhiguanjia.lib_base.helper.SdkHelper.shareWechatByType(shareData, 2);
                    LogUtils.e("StudyViewModel - handleShareNavClick - 调用朋友圈分享");
                    break;

                case 3: // 拨打电话
                    if (shareNav.isAtWork()) {
                        // 在工作时间，直接拨打电话
                        PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_CONTACTUS_CALLBT, false);
                        DeviceUtils.dial(activity, shareNav.getShareData());
                    } else {
                        // 不在工作时间，弹出对话框并跳转到在线客服
                        LogUtils.e("StudyViewModel - handleShareNavClick - 拨打电话 - 非工作时间，弹出提示框");
                        String title = "小安老师不在线~";
                        String sub = "工作日8:30-17:30";
                        String url = shareData;

                        try {
                            if (activity == null) {
                                LogUtils.e("StudyViewModel - handleShareNavClick - 拨打电话 - activity为null");
                                return;
                            }

                            // 直接使用MessageHelper显示提示框
                            MessageHelper.openOffOnileService(activity, title, sub, url, new com.zizhiguanjia.lib_base.listeners.HomeFootListener() {
                                @Override
                                public void onToUrl(String url) {
                                    LogUtils.e("StudyViewModel - handleShareNavClick - 拨打电话 - 用户点击了跳转，跳转到在线客服");
                                    // 在这里直接调用在线客服功能，参考case 4的逻辑
                                    // 检查微信是否安装
//                                    if (!HomeUtils.getInstance().isWxInstall()) {
//                                        ToastUtils.normal("请先安装微信客户端");
//                                        return;
//                                    }

                                    // 记录积分
//                                    PointHelper.joinPointData(PointerMsgType.POINTER_A_WECHATCOMMUNITY_BT, false);

                                    // 找到在线客服的ShareNav
                                    List<HomeShaperBean> shareNavsList = shareNavs.getValue();
                                    if (shareNavsList != null) {
                                        HomeShaperBean onlineServiceNav = null;
                                        for (HomeShaperBean nav : shareNavsList) {
                                            if (nav.getNavType() == 4) { // 在线客服类型
                                                onlineServiceNav = nav;
                                                break;
                                            }
                                        }

                                        // 如果找到在线客服按钮，使用它的数据
                                        if (onlineServiceNav != null) {
                                            String serviceUrl = onlineServiceNav.getShareData();
                                            if (HomeUtils.getInstance().checkSechemValid(serviceUrl)) {
                                                Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(serviceUrl));
                                                activity.startActivity(it);
                                            } else {
                                                if (activity instanceof HomeNativeActivity) {
                                                    IFragment iFragment = CommonHelper.showCommonWeb();
                                                    iFragment.initArguments().putString("routh", "home");
                                                    iFragment.initArguments().putString("url", serviceUrl);
                                                    iFragment.initArguments().putInt("payType", 1);
                                                    ((HomeNativeActivity) activity).startFragment(iFragment);
                                                }
                                            }
                                            return;
                                        }
                                    }

                                    // 如果没有找到在线客服按钮，就使用提供的URL
                                    if (activity instanceof HomeNativeActivity) {
                                        IFragment iFragment = CommonHelper.showCommonWeb();
                                        iFragment.initArguments().putString("routh", "home");
                                        iFragment.initArguments().putString("url", url);
                                        iFragment.initArguments().putInt("payType", 1);
                                        ((HomeNativeActivity) activity).startFragment(iFragment);
                                        LogUtils.e("StudyViewModel - handleShareNavClick - 拨打电话 - 已跳转到客服页面");
                                    }
                                }
                            });
                            LogUtils.e("StudyViewModel - handleShareNavClick - 拨打电话 - 已调用显示提示框");
                        } catch (Exception e) {
                            LogUtils.e("StudyViewModel - handleShareNavClick - 拨打电话 - 弹出提示框异常: " + e.getMessage());
                            e.printStackTrace();
                        }
                    }
                    LogUtils.e("StudyViewModel - handleShareNavClick - 拨打电话 - 处理完成");
                    break;

                case 4: // 在线客服
                    // 检查微信是否安装
                    if (!HomeUtils.getInstance().isWxInstall()) {
                        ToastUtils.normal("请先安装微信客户端");
                        return;
                    }

                    // 记录积分
                    PointHelper.joinPointData(PointerMsgType.POINTER_A_WECHATCOMMUNITY_BT, false);

                    // 检查state参数
                    String state = "1"; // 默认开启URL跳转
                    if (state == null || state.isEmpty() || !DataUtils.isInteger(state)) {
                        return;
                    }

                    int states = Integer.parseInt(state);
                    if (states == 1) {
                        // 开启url跳转
                        if (HomeUtils.getInstance().checkSechemValid(shareData)) {
                            Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(shareData));
                            activity.startActivity(it);
                        } else {
                            // 使用WebView打开
                            if (activity instanceof HomeNativeActivity) {
                                IFragment iFragment = CommonHelper.showCommonWeb();
                                iFragment.initArguments().putString("routh", "home");
                                iFragment.initArguments().putString("url", shareData);
                                iFragment.initArguments().putInt("payType", 1);
                                ((HomeNativeActivity) activity).startFragment(iFragment);
                            }
                        }
                    } else {
                        // 不开启URL跳转，复制微信号并跳转到微信
                        String title = shareNav.getTitle();
                        ClipboardUtils.copyText(activity, title);
                        ToastUtils.normal("微信号码复制成功，请前往微信添加客服", Gravity.CENTER);

                        Intent intent = new Intent();
                        ComponentName cmp = new ComponentName("com.tencent.mm", "com.tencent.mm.ui.LauncherUI");
                        intent.setAction(Intent.ACTION_MAIN);
                        intent.addCategory(Intent.CATEGORY_LAUNCHER);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        intent.setComponent(cmp);
                        activity.startActivity(intent);
                    }
                    LogUtils.e("StudyViewModel - handleShareNavClick - 调用在线客服");
                    break;

                default:
                    LogUtils.e("StudyViewModel - handleShareNavClick - 未知按钮类型: " + shareNav.getNavType());
                    ToastUtils.normal("暂不支持该功能");
                    break;
            }
        } catch (Exception e) {
            LogUtils.e("StudyViewModel - handleShareNavClick - 异常: " + e.getMessage());
            e.printStackTrace();
            ToastUtils.normal("操作失败，请稍后再试");
        }
    }

    /**
     * 处理jumpByTips方法的功能，用于处理从Flutter返回的事件
     * 从FlutterChannerRepository移植过来的方法
     *
     * @param methodName 方法名称
     * @param arguments  参数
     */
    public void handleFlutterEvent(String methodName, Object arguments) {
        if (activity == null) return;

        switch (methodName) {
            case HomeFlutterChannerTpis.HOME_FLUTTER_SAVELISTPAGE:
                // 跳转到收藏夹页面
                if (activity instanceof HomeNativeActivity) {
                    com.zizhiguanjia.model_home.fragment.HomeFragment fragment = new com.zizhiguanjia.model_home.fragment.HomeFragment();
                    android.os.Bundle args = new android.os.Bundle();
                    args.putString("flutterRoute", "save");
                    fragment.setArguments(args);
                    ((HomeNativeActivity) activity).startFragment(fragment);
                }
                break;

            case HomeFlutterChannerTpis.HOME_FLUTTER_ERRORLISTPAGE:
                // 跳转到错题集页面
                if (activity instanceof HomeNativeActivity) {
                    com.zizhiguanjia.model_home.fragment.HomeFragment fragment = new com.zizhiguanjia.model_home.fragment.HomeFragment();
                    android.os.Bundle args = new android.os.Bundle();
                    args.putString("flutterRoute", "error");
                    fragment.setArguments(args);
                    ((HomeNativeActivity) activity).startFragment(fragment);
                }
                break;

            case HomeFlutterChannerTpis.HOME_FLUTTER_TOAST:
                // 显示Toast提示
                String msg = (String) arguments;
                if (msg != null && !msg.isEmpty()) {
                    ToastUtils.normal(msg);
                }
                break;

            case HomeFlutterChannerTpis.HOME_FLUTTER_TXLXLISTPAGE:
                // 跳转到题型练习页面
                if (activity instanceof HomeNativeActivity) {
                    com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamList();
                    android.os.Bundle args = new android.os.Bundle();
                    args.putInt("index", 0);
                    ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                    ((HomeNativeActivity) activity).startFragment(fragment);
                }
                break;

            case HomeFlutterChannerTpis.HOME_FLUTTER_NOMREMISS_VIP:
                // 显示VIP购买对话框
                String payType = (String) arguments;
                MessageHelper.openNoPremissBuyDialog(activity, true, payType);
                break;

            case HomeFlutterChannerTpis.HOME_FLUTTER_GOPAYPAGE:
                // 跳转到支付页面
                String url = (String) arguments;
                if (url != null && !url.isEmpty() && activity instanceof HomeNativeActivity) {
                    com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.CommonHelper.showCommonWeb();
                    android.os.Bundle args = new android.os.Bundle();
                    args.putString("routh", "home");
                    args.putString("url", url);
                    args.putInt("payType", 1);
                    args.putString("payRouthParams", PayRouthConfig.PAY_UPDATE);
                    ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                    ((HomeNativeActivity) activity).startFragment(fragment);
                }
                break;

            case HomeFlutterChannerTpis.HOME_FLUTTER_CHAPPTERLISTPAGE:
                // 跳转到章节练习页面
                if (activity instanceof HomeNativeActivity) {
                    com.wb.lib_arch.base.IFragment fragment = com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamList();
                    android.os.Bundle args = new android.os.Bundle();
                    args.putInt("index", 1);
                    ((androidx.fragment.app.Fragment) fragment).setArguments(args);
                    ((HomeNativeActivity) activity).startFragment(fragment);
                }
                break;

            case HomeFlutterChannerTpis.HOME_NAV_FUNCTION_JUMP:
                // 处理功能区按钮点击事件
                if (arguments instanceof String) {
                    MenuNavsBean bean = com.wb.lib_network.utils.JsonUtils.gsonToBean((String) arguments, MenuNavsBean.class);
                    if (bean != null) {
                        onMainNavFunctionClick(bean);
                    }
                }
                break;

            // 其他事件可以根据需要添加
        }
    }

    /**
     * 跳转到考试日期设置页面
     */
    public void goToAscend() {
        LogUtils.e("StudyViewModel - goToAscend - 跳转到考试日期设置页面");

        if (activity instanceof HomeNativeActivity) {
            try {
                HomeFragment homeFragment = new HomeFragment();
                boolean autoDialog = homeFragment.getBoolean("autoDialog", false);
                homeFragment.initArguments().putBoolean("goToAscend", autoDialog);
                homeFragment.initArguments().putString("flutterRoute", RouteConfig.ROUTE_TESTDATE);
                ((HomeNativeActivity) activity).startFragment(homeFragment);
            } catch (Exception e) {
                LogUtils.e("StudyViewModel - goToAscend - 异常: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            LogUtils.e("StudyViewModel - goToAscend - activity不是HomeNativeActivity类型");
        }
    }

    /**
     * HomeRepository监听器实现
     */
    private class HomeRepositortListener implements com.zizhiguanjia.model_home.listener.IHomeRepositortListener {
        @Override
        public void initFlutter(String route) {
            // 实现方法
        }

        @Override
        public void queryCouponInfo() {
            // 实现方法
        }

        @Override
        public void onHomeTips(String des, boolean paperType, boolean passCertificate) {
            // 实现方法
        }

        @Override
        public void onHomeFlutterToJson(MethodChannel.Result result, String key, String json) {
            // 实现方法
        }

        @Override
        public void goToAddess() {
            if (navigator != null) {
                navigator.toGoChoiceAddress();
            }
        }

        @Override
        public void openVerificationCodeLogin() {
            // 实现方法
        }

        @Override
        public void loginSuccess() {
            if (navigator != null) {
                // 检查navigator是否有loginSuccess方法
                try {
                    java.lang.reflect.Method method = navigator.getClass().getMethod("loginSuccess");
                    method.invoke(navigator);
                } catch (Exception e) {
                    LogUtils.e("StudyViewModel - 调用loginSuccess失败: " + e.getMessage());
                }
            }
        }

        @Override
        public void accountOffline() {
            if (navigator != null) {
                // 检查navigator是否有accountOffline方法
                try {
                    java.lang.reflect.Method method = navigator.getClass().getMethod("accountOffline");
                    method.invoke(navigator);
                } catch (Exception e) {
                    LogUtils.e("StudyViewModel - 调用accountOffline失败: " + e.getMessage());
                }
            }
        }

        @Override
        public void userPaySuccessInfo() {
            // 实现方法
        }

        @Override
        public void getIndexDatas(MethodChannel.Result result, boolean passCertificate) {
            StudyViewModel.this.getIndexDatas(result, passCertificate);
        }

        @Override
        public void setTopTitle(boolean show, String title) {
            if (navigator != null) {
                navigator.setTopTitleInfo(show, title);
            }
        }

        @Override
        public void checkLocalCoupon() {
            // 实现方法
        }

        @Override
        public void reshSaveOrErrorHttpsDatas() {
            // 实现方法
        }

        @Override
        public void reshLivePreHttpsDatas() {
            // 实现方法
        }

        @Override
        public void reshResultHttpsDatas(int paperType) {
            // 实现方法
        }

        @Override
        public void showGuideView() {
            if (navigator != null) {
                navigator.showGuide();
            }
        }

        @Override
        public void openTimeSelect() {
            // 实现方法
        }

        @Override
        public void goToBindSubject() {
            // 实现方法
        }

        @Override
        public void getBackVideoListData(MethodChannel.Result result) {
            // 实现通关精讲课数据获取
            if (mApi != null) {
                Map<String, String> params = new HashMap<>();
                customLaunchRequest(mApi.getBackVideoHttpData(params), new OnHandleException<BaseData<HomeCourseBean>>() {
                    @Override
                    public void success(BaseData<HomeCourseBean> data) {
                        if (data != null && data.Data != null) {
                            // 获取课程项列表
                            List<com.zizhiguanjia.model_home.bean.ItemsBean> courseItems = null;
                            try {
                                // 尝试反射获取items属性
                                java.lang.reflect.Method getItemsMethod = data.Data.getClass().getMethod("getItems");
                                if (getItemsMethod != null) {
                                    Object items = getItemsMethod.invoke(data.Data);
                                    if (items instanceof List) {
                                        courseItems = (List<com.zizhiguanjia.model_home.bean.ItemsBean>) items;
                                    }
                                }
                            } catch (Exception e) {
                                LogUtils.e("StudyViewModel - 反射获取items失败: " + e.getMessage());
                            }

                            if (courseItems != null) {
                                updateLives(courseItems);
                            }
                        }

                        // 处理Flutter回调
                        if (result != null) {
                            // 使用GsonUtils转换为JSON
                            try {
                                // 使用Gson直接转换
                                com.google.gson.Gson gson = new com.google.gson.Gson();
                                String json = gson.toJson(data);
                                result.success(json);
                            } catch (Exception e) {
                                LogUtils.e("StudyViewModel - JSON转换失败: " + e.getMessage());
                                result.success("{}");
                            }
                        }
                    }

                    @Override
                    public void error(String msg) {
                        if (navigator != null) {
                            navigator.showToast(msg);
                        }

                        if (result != null) {
                            result.success("{}");
                        }
                    }
                });
            }
        }

        @Override
        public void initExamTimeInfo(MethodChannel.Result result, String key) {
            // 实现考试时间信息获取
            if (mApi != null) {
                Map<String, String> params = new HashMap<>();
                customLaunchRequest(mApi.getExamTimeInfo(params), new OnHandleException<BaseData>() {
                    @Override
                    public void success(BaseData data) {
                        if (data != null && data.getResult() != null) {
                            // 使用GsonUtils转换为JSON
                            String json = "";
                            try {
                                // 使用Gson直接转换
                                com.google.gson.Gson gson = new com.google.gson.Gson();
                                json = gson.toJson(data.getResult());
                            } catch (Exception e) {
                                LogUtils.e("StudyViewModel - JSON转换失败: " + e.getMessage());
                                json = "{}";
                            }

                            // 处理Flutter回调
                            if (result != null) {
                                result.success(json);
                            } else if (repository != null) {
                                // 如果repository不支持onHomeFlutterToJson方法，则不调用
                                try {
                                    java.lang.reflect.Method method = repository.getClass().getMethod("onHomeFlutterToJson", MethodChannel.Result.class, String.class, String.class);
                                    method.invoke(repository, result, key, json);
                                } catch (Exception e) {
                                    LogUtils.e("StudyViewModel - 调用onHomeFlutterToJson失败: " + e.getMessage());
                                }
                            }

                            // 解析JSON数据
                            try {
                                org.json.JSONObject jsonObject = new org.json.JSONObject(json);
                                boolean autoDialog = 0 == Double.valueOf(jsonObject.getJSONObject("CalendarInfo").getString("DateBoxStatus")).intValue();

                                // 更新UI
                                if (navigator != null && navigator instanceof HomeNativeActivity) {
                                    // 如果HomeNativeActivity不支持initArguments方法，则不调用
                                    try {
                                        Object args = ((HomeNativeActivity) navigator).getClass().getMethod("initArguments").invoke(navigator);
                                        if (args instanceof android.os.Bundle) {
                                            ((android.os.Bundle) args).putBoolean("autoDialog", autoDialog);
                                        }
                                    } catch (Exception e) {
                                        LogUtils.e("StudyViewModel - 调用initArguments失败: " + e.getMessage());
                                    }
                                }

                                // 打开时间选择
                                try {
                                    if (repository != null) {
                                        java.lang.reflect.Method method = repository.getClass().getMethod("openTimeSelect");
                                        method.invoke(repository);
                                    }
                                } catch (Exception e) {
                                    LogUtils.e("StudyViewModel - 调用openTimeSelect失败: " + e.getMessage());
                                }
                            } catch (org.json.JSONException ignore) {
                                LogUtils.e("解析考试时间信息失败: " + ignore.getMessage());
                            }
                        }
                    }

                    @Override
                    public void error(String msg) {
                        if (navigator != null) {
                            navigator.showToast(msg);
                        }

                        if (result != null) {
                            result.success("{}");
                        }
                    }
                });
            }
        }
    }

    /**
     * 更新paperTitle LiveData值
     *
     * @param title 新的标题值
     */
    public void updatePaperTitle(String title) {
        LogUtils.e("StudyViewModel - updatePaperTitle - 更新paperTitle为: " + title);
        if (paperTitle != null) {
            MainThreadUtils.post(() -> {
                paperTitle.setValue(title);
                LogUtils.e("StudyViewModel - updatePaperTitle - 设置完成，在" +
                        (android.os.Looper.myLooper() == android.os.Looper.getMainLooper() ? "主线程" : "非主线程"));
            });
        } else {
            LogUtils.e("StudyViewModel - updatePaperTitle - paperTitle LiveData为空");
        }
    }

    public void test() {
//        launchOnlyResult(mApi.getUserInfo(new HashMap<>()), new BaseViewModel.OnHandleException<BaseData<UserInfoNetworkBean>>() {
//            @Override
//            public void success(BaseData<UserInfoNetworkBean> data) {
//                reshUserInfo(data.Data, true, data.Data.getCouponTips());
//                //                navigator.reshListDes(data.Data.getCouponTips());
//            }
//
//            @Override
//            public void error(String msg) {
//                reshUserInfo(null, false, null);
//            }
//        });
    }
} 