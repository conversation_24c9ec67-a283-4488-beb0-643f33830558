package com.zizhiguanjia.model_home.viewmodel;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_home.api.HomeSeriveApi;
import com.zizhiguanjia.model_home.bean.HomeCourseBean;
import com.zizhiguanjia.model_home.bean.LiveIndexBean;
import com.zizhiguanjia.model_home.fragment.CourseFragment;
import com.zizhiguanjia.model_home.navigator.HomeCourseNavigator;

import java.util.HashMap;

public class CourseViewModel extends CommonViewModel {
    private CourseFragment courseFragment;
    private HomeCourseNavigator navigator;
    private HomeSeriveApi api=new Http().create(HomeSeriveApi.class);
    public void initParams(CourseFragment courseFragment,HomeCourseNavigator navigator){
        this.courseFragment=courseFragment;
        this.navigator=navigator;
        getHttpData();
    }
    public void getHttpData(){
       launchOnlyResult(api.getCourseHttpData(new HashMap<>()), new OnHandleException<BaseData<HomeCourseBean>>() {
           @Override
           public void success(BaseData<HomeCourseBean> data) {
            navigator.showVideoView(data.getResult().getLiveIndex().getVideo().getCoverUrl(),data.getResult().getLiveIndex().getVideo().getVideoUrl(),data.getResult().getLiveIndex().getVideo().getTitle());
            navigator.showFreeCourse(data.getResult().getLiveIndex().getLives());
            navigator.showGjk(data.getResult().getLiveIndex().getLiveHigher());
           }

           @Override
           public void error(String msg) {

           }
       });
    }
}
