package com.zizhiguanjia.model_home.viewmodel;

import static com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils.isDoubleClick;

import android.content.ComponentName;
import android.content.Intent;
import android.net.Uri;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_network.BaseViewModel;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_network.utils.JsonUtils;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.ClipboardUtils;
import com.wb.lib_utils.utils.DataUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.AddressHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.HomeHelper;
import com.zizhiguanjia.lib_base.helper.ListHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.lib_base.helper.TaskHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.helper.VideoHelper;
import com.zizhiguanjia.lib_base.listeners.CoupoinValidationListenter;
import com.zizhiguanjia.lib_base.listeners.HomeFootListener;
import com.zizhiguanjia.lib_base.listeners.OnChoiceTimeListenter;
import com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.lib_base.msgconfig.UserType;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.api.HomeSeriveApi;
import com.zizhiguanjia.model_home.bean.CommonLivePreBean;
import com.zizhiguanjia.model_home.bean.DefaultByAddressBean;
import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;
import com.zizhiguanjia.model_home.bean.FlutterCounponsBean;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.bean.HomeCourseBean;
import com.zizhiguanjia.model_home.bean.LiveBackBean;
import com.zizhiguanjia.model_home.bean.MainSaveOrErrorBean;
import com.zizhiguanjia.model_home.bean.MenuNavsBean;
import com.zizhiguanjia.model_home.config.HomeConfig;
import com.zizhiguanjia.model_home.config.HomeFlutterChannerTpis;
import com.zizhiguanjia.model_home.config.RouteConfig;
import com.zizhiguanjia.model_home.fragment.HomeFragment;
import com.zizhiguanjia.model_home.fragment.NativeStudyAIFragment;
import com.zizhiguanjia.model_home.listener.IFlutter;
import com.zizhiguanjia.model_home.listener.IFlutterChannerListener;
import com.zizhiguanjia.model_home.listener.IHomeFlutterListener;
import com.zizhiguanjia.model_home.listener.IHomeFlutterNativeListener;
import com.zizhiguanjia.model_home.listener.IHomeFragment;
import com.zizhiguanjia.model_home.listener.IHomeRepositortListener;
import com.zizhiguanjia.model_home.listener.IMsg;
import com.zizhiguanjia.model_home.navigator.HomeFlutterNavigator;
import com.zizhiguanjia.model_home.report.HomeFlutterReport;
import com.zizhiguanjia.model_home.report.HomeMsgReport;
import com.zizhiguanjia.model_home.repository.FlutterChannerRepository;
import com.zizhiguanjia.model_home.repository.HomeFlutterRepository;
import com.zizhiguanjia.model_home.repository.MsgRepository;
import com.zizhiguanjia.model_home.utils.HomeUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.flutter.embedding.android.FlutterView;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.reactivex.functions.Consumer;

public class HomeFlutterNativeViewModel extends BaseViewModel implements IHomeFlutterNativeListener,IHomeRepositortListener, IFlutterChannerListener, IFlutter {
    private HomeSeriveApi mApi=new Http().create(HomeSeriveApi.class);
    private HomeFlutterRepository repository;
    private NativeStudyAIFragment homeFragment;
    private HomeFlutterNavigator navigator;
    private IHomeFragment iHomeFragment;
    public String mRoute;
    private MsgRepository msgRepository;
    private FlutterChannerRepository flutterChannerRepository;
    public FlutterEngine flutterEngine;
    public ObservableField<Boolean> chapter = new ObservableField<>();

    public HomeBean mHomeBean;
    private boolean statePage=false;

    private int mPaperType;
    public ObservableField<DefaultByAddressBean> defaultByAddressBean=new ObservableField<>();
    //    public ObservableField<Integer>  orderBindType=new ObservableField<>();
    public void setiHomeFragment(IHomeFragment iHomeFragment) {
//        this.iHomeFragment = iHomeFragment;
    }
    @Override
    public void initData(String route) {
        flutterEngine=new FlutterEngine(homeFragment.getActivity());
        repository=new HomeFlutterRepository();
        msgRepository=new MsgRepository();
        flutterChannerRepository=new FlutterChannerRepository();
        repository.init(this);
        if(mRoute.contains(RouteConfig.ROUTE_HOME)){
            repository.initGuide();
        }
        repository.setTopTitleInfo(mRoute);
        HomeFlutterReport.getInstance().setUploadType(flutterChannerRepository);
        HomeMsgReport.getInstance().setMsgType(msgRepository);
        initFlutter(route);
    }
    @Override
    public void initParams(HomeFlutterNavigator homeFlutterNavigator, NativeStudyAIFragment mActivity,String route) {
        this.homeFragment=mActivity;
        this.mRoute=route;
        mPaperType=5;
//        mPaperType = homeFragment.getArguments().containsKey("paperType") ?
//                Integer.parseInt(homeFragment.getArguments().get("paperType").toString()):
//                homeFragment.getArguments().containsKey("pagerType") ? Integer.parseInt(homeFragment.getArguments().get("pagerType").toString()) : -1;
        this.navigator=homeFlutterNavigator;
        initData(route);
//        if(route.contains(RouteConfig.ROUTE_HOME)){
            checkPremiss();
//        }
    }

    @Override
    public void checkPremiss() {
        CommonHelper.upDataApp(homeFragment.getActivity());
        IMsgListener.checkCouponid();
        repository.checkPremiss(homeFragment.getActivity());

    }
    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        HomeFlutterReport.getInstance().getUploadType().jumpByTips(call,result,this);
    }
    @Override
    public void onClick(View mView) {
        if(mView.getId()== R.id.imgMainUser){
        }else if(mView.getId()==R.id.llHomeSwitch){
        }
    }

    @Override
    public void checkUserPayState() {
        boolean buyPresmiss=homeFragment.initArguments().getBoolean("premiss",false);
        if(!buyPresmiss)return;
        if(!statePage)return;
        if(!AccountHelper.isUserLogin())return;
        if(!UserHelper.isBecomeVip()){
            RxJavaUtils.delay(1, new Consumer<Long>() {
                @Override
                public void accept(Long aLong) throws Exception {
                    HomeConfig.isToastPay=true;
                    MessageHelper.openNoPremissBuyDialog(homeFragment.getActivity(), true, PayRouthConfig.PAY_BACKPLAY_PAY);
                }
            });
        }
    }

    @Override
    public void getIndexDatas(MethodChannel.Result result,boolean passCertificate) {
        Map<String,String> params=new HashMap<>();
        if(BaseConfig.channelUID!=null&&!BaseConfig.channelUID.isEmpty()){
            params.put("fromWhichPerson",BaseConfig.channelUID);
        }
        launchOnlyResult(mApi.getMainInfo(params), new OnHandleException<BaseData<HomeBean>>() {
            @Override
            public void success(BaseData<HomeBean> data) {
                mHomeBean = data.Data;
//                iHomeFragment.onHomeDataRefresh(data);
                BaseConfig.ORDER_BIND_TYPE=data.Data.getOrderBindType();
                LogUtils.e("设置了升级----->>>"+BaseConfig.ORDER_BIND_TYPE);
                repository.successNetWorkDatas(data,result,passCertificate);
            }
            @Override
            public void error(String msg) {
                navigator.showToast(msg);
            }
        });

    }

    @Override
    public void setTopTitle(boolean show, String title) {
        navigator.setTopTitleInfo(show,title);
    }

    @Override
    public void checkLocalCoupon() {
        MainThreadUtils.postDelayed(new Runnable() {
            @Override
            public void run() {
                TaskHelper.detectionCoupon(new CoupoinValidationListenter() {
                    @Override
                    public void onCoupoinShow() {
                        LogUtils.e("优惠卷----->>>>2"+iHomeFragment==null?"yes":"no");
                        if(iHomeFragment==null){
                            Bus.post(new MsgEvent(666666));
                        }else {
//                            iHomeFragment.clearAllActivity();
                        }
                    }
                });
            }
        }, 500L);
    }

    @Override
    public void reshSaveOrErrorHttpsDatas() {
        onHomeFlutterToJson(null,"vipState",UserHelper.isBecomeVip() ? "1" : "0");
        RxJavaUtils.delay(1, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                LogUtils.e("开始刷新收藏");
                initSaveOrErrorHttpsDatas();
            }
        });

    }

    @Override
    public void reshLivePreHttpsDatas() {
        getLivePreData(null);
    }

    @Override
    public void reshResultHttpsDatas(int paperType) {
        Map<String,String> params=new HashMap<>();
        params.put("paperType",String.valueOf(paperType));
        launchOnlyResult(mApi.getGradleInfo(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                LogUtils.e("开始刷新数据");
                onHomeFlutterToJson(null,"reshDatas",GsonUtils.gsonString(data));
            }

            @Override
            public void error(String msg) {
                ToastUtils.normal(msg);
            }
        });
    }

    @Override
    public void showGuideView() {
        navigator.showGuide();
    }

    @Override
    public void openTimeSelect() {
        boolean dialog= homeFragment.initArguments().getBoolean("autoDialog",false);
        if(dialog){
            RxJavaUtils.delay(1, new Consumer<Long>() {
                @Override
                public void accept(Long aLong) throws Exception {
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            setExamTime();
                        }
                    });
                }
            });
        }
    }

    @Override
    public void goToBindSubject() {
        if(CertificateHelper.userChoiceCertificate()){
            navigator.showLoading(true,null);
            launchOnlyResult(mApi.getUpgradeReminder(new HashMap<>()), new OnHandleException<BaseData<DyVipUpdateHintBean>>() {
                @Override
                public void success(BaseData<DyVipUpdateHintBean> data) {
                    if(data.Data != null){
                        navigator.showDyVipUpdateHintDialog(data.Data);
                    }else {
                        homeFragment.startFragment(CertificateHelper.startChoseCertificateZzByAddress());
                    }
                    navigator.showLoading(false,null);
                }

                @Override
                public void error(String msg) {
                    navigator.showLoading(false,null);
                }
            });
        }
    }

    @Override
    public void getBackVideoListData(MethodChannel.Result result) {
        launchOnlyResult(mApi.getBackVideoHttpData(new HashMap<>()), new OnHandleException<BaseData<HomeCourseBean>>() {
            @Override
            public void success(BaseData<HomeCourseBean> data) {
                LiveBackBean liveBackBean=data.getResult().getLiveBack();
                String json=GsonUtils.gsonString(liveBackBean);
                LogUtils.e("json"+json);
                onHomeFlutterToJson(result,null,json);
            }

            @Override
            public void error(String msg) {

            }
        });
    }

    @Override
    public void goToLnzt() {
        if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
            homeFragment.initArguments().putInt("paperType", 1);
            homeFragment.startFragment(ListHelper.showCommonListPage());
        }

    }

    @Override
    public void goToEjMnks() {
        if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
            homeFragment.initArguments().putInt("paperType", 2);
            homeFragment.startFragment(ListHelper.showCommonListPage());
        }

    }

    @Override
    public void goToEjAddress() {
        LogUtils.e("进来了选择地区");
//        homeFragment.startFragment(CertificateHelper.startChoseCertificateByAddress());
        AddressHelper.start(homeFragment.getActivity());
    }

    @Override
    public void goToEjSubject(String json) {
        if(!NoDoubleClickUtils.isDoubleClick()){
            Map maps=GsonUtils.gsonToMaps(json);
            CertificateHelper.startCertificateActivity(maps.get("address").toString(),maps.get("addressIds").toString(), true);
        }
    }

    @Override
    public void userClickActiveSubjectClose(MethodChannel.Result result,String states1) {

        launchOnlyResult(mApi.closeTip(new HashMap<>()), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                if(StringUtils.isEmpty(data.getMsg()))return;
                ToastUtils.normal(data.Message);
                if (states1.equals("0")){
                    getIndexDatas(result,false);
                }
            }
            @Override
            public void error(String msg) {

            }
        });
    }

    @Override
    public void toExamResult(String paperType, String paperValues) {
        if(StringUtils.isEmpty(paperValues)||StringUtils.isEmpty(paperType)){
            ToastUtils.normal("参数不合法",Gravity.CENTER);
            return;
        }
        homeFragment.initArguments().putInt("paperType",Integer.parseInt(paperType));
        homeFragment.initArguments().putString("paperValue",(paperValues));
        homeFragment.startFragment(ListHelper.toPageExamResult());
    }

    @Override
    public void openOffOnlieServiceTips(String title, String sub, String url) {
        MessageHelper.openOffOnileService(homeFragment.getActivity(), title, sub, url, new HomeFootListener() {
            @Override
            public void onToUrl(String url) {
                homeFragment.initArguments().putString("routh", "home");
                homeFragment.initArguments().putString("url", url);
                homeFragment.initArguments().putInt("payType", 1);
//                homeFragment.initArguments().putString("payRouthParams", PayRouthConfig.PAY_UPDATE);
                homeFragment.startFragment(CommonHelper.showCommonWeb());
            }
        });
    }

    /**
     * 打开课程详情
     * @param index 第几个课程，从0开始，更多操作的时候使用的是-1
     * @param useId 使用的课程id，更多操作的时候使用的是空
     */
    @Override
    public void openCourseDetail(String index, String useId) {
        if(repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
            homeFragment.initArguments().putString("index", index);
            homeFragment.initArguments().putString("useId", useId);
            homeFragment.startFragment(ARouterUtils.navFragment(HomeRoutherPath.COURSE_DETAIL));
        }
    }
    @Override
    public void onMainNavFunctionClick(MenuNavsBean bean) {
        if (bean != null) {
            MethodCall methodCall = null;
            Map<String, String> params = new HashMap<>();
            params.put("idDo", bean.isDo() ? "1" : "0");
            params.put("isDialog", bean.getDateBoxStatus() == 1 ? "0" : "1");
            params.put("type", String.valueOf(bean.getNavType()));
            switch (bean.getNavType()) {
                case 1://错题集
                    methodCall = new MethodCall(HomeFlutterChannerTpis.HOME_FLUTTER_ERRORLISTPAGE, null);
                    break;
                case 2://收藏夹
                    methodCall = new MethodCall(HomeFlutterChannerTpis.HOME_FLUTTER_SAVELISTPAGE, null);
                    break;
                case 3://题型练习
                    methodCall = new MethodCall(HomeFlutterChannerTpis.HOME_FLUTTER_TXLXLISTPAGE, null);
                    break;
                case 4://模拟考试
                case 10:
                case 11:
                    methodCall = new MethodCall(HomeFlutterChannerTpis.HOME_FLUTTER_MNKSLISTPAGE, params);
                    break;
                case 5:
                    methodCall = new MethodCall(HomeFlutterChannerTpis.HOME_LNZT_FLUTTER, null);
                    break;
                case 6:
                    methodCall = new MethodCall(HomeFlutterChannerTpis.HOME_EJMNKS_FLUTTER, null);
                    break;
                case 20:
                    if (bean.getUrl() != null && !bean.getUrl().isEmpty()) {
                        methodCall = new MethodCall(HomeFlutterChannerTpis.HOME_FLUTTER_GOLIVE, null);
                    }
                    break;
                case 16://易错100题
                    if (!NoDoubleClickUtils.isDoubleClick()&&repository.checkLoginOrAddress(true, BaseConfig.ORDER_BIND_TYPE,false)) {
                        homeFragment.initArguments().putString("title", "易错100题");
                        homeFragment.initArguments().putBoolean("restart", false);
                        homeFragment.initArguments().putString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG));
                        homeFragment.startFragment(ListHelper.toPageExamDes());
                    }
                    break;
                case 17://学习资料
                    if (!NoDoubleClickUtils.isDoubleClick() && repository.checkLoginOrAddress(true, BaseConfig.ORDER_BIND_TYPE,false)) {
                        homeFragment.startFragment(ListHelper.toPageDocMajors());
                    }
                    break;
            }
            if (methodCall != null) {
                flutterChannerRepository.jumpByTips(methodCall, null, this);
            }
        }
    }

    @Override
    public void initFlutter(String route) {
        LogUtils.e("看下路由----->>>"+route);
        HomeFlutterReport.getInstance().getUploadType().initFlutterEngine(route,homeFragment.getActivity(),this,flutterEngine);
    }
    @Override
    public void initFlutterViewSuccess(FlutterView methodChannel) {
        navigator.successFlutterView(methodChannel);
    }
    @Override
    public void initFlutterChannerSuccess(MethodChannel flutterView) {
        navigator.successMethodChannel(flutterView);
    }
    @Override
    public void onHomeTips(String des, boolean paperType,boolean passCertificate) {
        chapter.set(paperType);
//        if(passCertificate){
//        iHomeFragment.originalTitle(des);
//        }
    }
    @Override
    public void onHomeFlutterToJson(MethodChannel.Result result, String key,String json) {
        if(result==null){
            HomeFlutterReport.getInstance().getUploadType().nativeToFlutterMouth(key, json==null?null:json,flutterEngine);
        }else {
            result.success(json);
        }
    }
    @Override
    public void goToAddess() {
        homeFragment.startFragment(AddressHelper.mainPage(homeFragment.getContext()));
    }
    @Override
    public void goToPageByRoute(String route) {
        if(repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)){
            homeFragment.initArguments().putString("flutterRoute", route);
            homeFragment.startFragment(new HomeFragment());
        }
    }
    /**
     * 设置当前的majorPId
     */
    @Override
    public void setMajorParentId(String majorParentId) {
        if(majorParentId != null && majorParentId.matches("[0-9]+")){
            BaseConfig.MAJOR_PID = Integer.parseInt(majorParentId);
            KvUtils.save("mjPid", majorParentId);
        }
    }
    @Override
    public void toast(String msg) {
        if(msg==null||msg.isEmpty())return;
        ToastUtils.normal(msg, Gravity.CENTER);
    }

    @Override
    public void goToTopicList(boolean statistical) {
        if (statistical) {
//            PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_QUESTION, false);
        }
        //0 4   1 2 3
        if (!NoDoubleClickUtils.isDoubleClick()&&repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE,false)) {
            homeFragment.initArguments().putInt("index", 0);
            if(chapter != null && chapter.get() != null) {
                homeFragment.initArguments().putBoolean("chapter", chapter.get());
            }
            homeFragment.startFragment(ListHelper.toPageExamList());
        }
    }

    @Override
    public void noBuyDialog(String payType) {
        if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
            HomeConfig.isToastPay=true;
            MessageHelper.openNoPremissBuyDialog(homeFragment.getActivity(), true, payType);
        }
    }

    @Override
    public void goToBuyUrl(String url) {
        PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_UPGRADE, false);
        if(url==null||url.isEmpty())return;
        if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
            HomeConfig.isToastPay=false;
            homeFragment.initArguments().putString("routh", "home");
            homeFragment.initArguments().putString("url", url);
            homeFragment.initArguments().putInt("payType", 1);
            homeFragment.initArguments().putString("payRouthParams", PayRouthConfig.PAY_UPDATE);
            homeFragment.startFragment(CommonHelper.showCommonWeb());
        }
    }

    @Override
    public void goToChapterList() {
        if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
            homeFragment.initArguments().putInt("index", 1);
            homeFragment.startFragment(ListHelper.toPageExamList());
        }
    }

    @Override
    public void dioTel(String tel) {
        PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_CONTACTUS_CALLBT, false);
        DeviceUtils.dial(homeFragment.getContext(), tel);
//        PermissionUtils.with(homeFragment.getActivity()).addPermissions(Manifest.permission.CALL_PHONE)
//                .setPermissionsCheckListener(new PermissionListener() {
//                    @Override
//                    public void permissionRequestSuccess() {
//                        DeviceUtils.callPhone(homeFragment.getContext(), tel);
//                    }
//
//                    @Override
//                    public void permissionRequestFail(String[] grantedPermissions, String[] deniedPermissions, String[] forceDeniedPermissions) {
//                        MessageHelper.permissionsTips(homeFragment.getActivity(), "温馨提示",
//                                "请前往设置->应用->【" + PermissionUtils.getAppName(homeFragment.getActivity()) + "】->权限中打开打电话权限，否则功能无法正常运行！", "确定");
//                    }
//                })
//                .createConfig()
//                .setForceAllPermissionsGranted(false)
//                .setForceDeniedPermissionTips("请前往设置->应用->【" + PermissionUtils.getAppName(homeFragment.getContext()) + "】->权限中打开相关权限，否则功能无法正常运行！")
//                .buildConfig()
//                .startCheckPermission();
    }

    @Override
    public void goToVideo(String type) {

        if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
            if(mRoute.contains(RouteConfig.ROUTE_HOME)){
                if (UserHelper.isBecomeVip()){
                    VideoHelper.showVideoActivity(type);
                }else {
                    goToMore(false);
                }
            }

        }
    }

    @Override
    public void goToVideo(String type, String url,boolean checkVip) {
        if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
            if(mRoute.contains(RouteConfig.ROUTE_HOME)){
                if(checkVip){
                    VideoHelper.showVideoActivity(type,url);
//                    if (UserHelper.isBecomeVip()){
//                        VideoHelper.showVideoActivity(type,url);
//                    }else {
//                        goToMore(true);
//                    }
                }else {
                    goToMore(true);
                }

            }else {
                VideoHelper.showVideoActivity(type,url);
            }
        }
    }

    @Override
    public void paySuccessState(String state) {
        if (UserHelper.isBecomeVip()) {
            return;
        }
        UserHelper.updataUserVipState(state.equals("1") ? UserType.USER_TYPE_VIP : UserType.USER_TYPE_COMMON);
    }

    @Override
    public void closeView() {
        System.exit(0);
    }

    @Override
    public void toWebUrl(String state, String routeType, String type) {
        if (routeType.equals("1")) {
            if (type.equals("1")) {
                if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
                    PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_BANNER_REFUND, false);
                    homeFragment.initArguments().putString("routh", "home");
                    homeFragment.initArguments().putString("url", state);
                    homeFragment.initArguments().putInt("payType", 1);
                    homeFragment.initArguments().putString("payRouthParams", PayRouthConfig.PAY_BANNER);
                    homeFragment.startFragment(CommonHelper.showCommonWeb());
                }
            } else {
                PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_BANNER_REFUND, false);
                homeFragment.initArguments().putString("routh", "home");
                homeFragment.initArguments().putString("url", state);
                homeFragment.initArguments().putInt("payType", 1);
                homeFragment.initArguments().putString("payRouthParams", PayRouthConfig.PAY_BANNER);
                homeFragment.startFragment(CommonHelper.showCommonWeb());
            }
        } else {
            homeFragment.initArguments().putString("url", state);
            homeFragment.initArguments().putInt("payType", 1);
            homeFragment.startFragment(CommonHelper.showCommonWeb());
        }
    }

    @Override
    public void toOtherWebUrl(String url) {
        final Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        intent.setData(Uri.parse(url));
        if (intent.resolveActivity(homeFragment.getContext().getPackageManager()) != null) {
            final ComponentName componentName = intent.resolveActivity(homeFragment.getContext().getPackageManager());
            homeFragment.getContext().startActivity(Intent.createChooser(intent, "请选择浏览器"));
        } else {
            ToastUtils.normal("链接错误或无浏览器", Gravity.CENTER);
        }
    }

    @Override
    public void shareNavs(String wxurls, String states) {
        SdkHelper.shareWechatByType(wxurls, Integer.parseInt(states));
    }

    @Override
    public void openKfUrl(String wxurl, String state, String title) {
        if (!HomeUtils.getInstance().isWxInstall()) {
            toast("请先安装微信客户端");
            return;
        }
        PointHelper.joinPointData(PointerMsgType.POINTER_A_WECHATCOMMUNITY_BT, false);
        if (state == null || state.isEmpty()) {
            return;
        }
        if (!DataUtils.isInteger(state)) {
            return;
        }
        int states = Integer.parseInt(state);
        if (states == 1) {
            //开启url跳转
            if (HomeUtils.getInstance().checkSechemValid(wxurl)) {
                Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(wxurl));
                homeFragment.startActivity(it);
            } else {
                toWebUrl(wxurl, "1", "1");
            }
        } else {
            //不开启
            ClipboardUtils.copyText(homeFragment.getContext(), title);
            ToastUtils.normal("微信号码复制成功，请前往微信添加客服", Gravity.CENTER);
            Intent intent = new Intent();
            ComponentName cmp = new ComponentName("com.tencent.mm", "com.tencent.mm.ui.LauncherUI");
            intent.setAction(Intent.ACTION_MAIN);
            intent.addCategory(Intent.CATEGORY_LAUNCHER);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setComponent(cmp);
            homeFragment.startActivity(intent);
        }
    }

    @Override
    public void homeStartCount(String type) {
        PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_START, false);
        if(repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)){
            if (type.equals("1")) {
                //章节
                PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_STARTPRACTICE_CHAPTER, true);
                goToChapterList();
            } else {
                //题型
                goToTopicList(false);
            }
        }

    }

    @Override
    public void goToMore(boolean checkBuyPress) {
        if(repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)){
            if (!isDoubleClick()) {
                homeFragment.initArguments().putBoolean("premiss", checkBuyPress);
                goToPageByRoute(RouteConfig.ROUTE_LIVEPRE);
            }
        }
    }
    @Override
    public void goToBlackPlay() {
        if (!isDoubleClick()) {
            if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
                goToPageByRoute(RouteConfig.ROUTE_PLAYBLACK);
            }
        }
    }

    @Override
    public void goToLiving(String ids,MethodChannel.Result result) {
        if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
            Map<String, String> params = new HashMap<>();
            params.put("LiveId", ids);
            launchOnlyResult(mApi.orderLive(params), new OnHandleException<BaseData>() {
                @Override
                public void success(BaseData data) {
                    result.success(1);
                    toast(data.getMsg());
                }

                @Override
                public void error(String msg) {
                    ToastUtils.normal(msg);
                }
            });
        }
    }

    @Override
    public void goToLiveVideo(String url) {
        if(repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)){
            if (HomeUtils.getInstance().checkSechemValid(url)) {
                Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                homeFragment.startActivity(it);
            } else {
                toWebUrl(url, "1", "1");
            }
        }
    }

    @Override
    public void initGradleData(MethodChannel.Result result, String pagerType) {
        Map<String,String> params=new HashMap<>();
        params.put("paperType",pagerType);
        launchOnlyResult(mApi.getGradleInfo(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                result.success(GsonUtils.gsonString(data));
            }

            @Override
            public void error(String msg) {
                ToastUtils.normal(msg);
            }
        });
    }

    @Override
    public void choiceAddress(String jsonStr) {
        if(!NoDoubleClickUtils.isDoubleClick()){
            Map maps=GsonUtils.gsonToMaps(jsonStr);
            homeFragment.initArguments().putBoolean("isSave", false);
            homeFragment.initArguments().putBoolean("byAddress", true);
            homeFragment.initArguments().putString("citificateName",maps.get("name").toString());
            homeFragment.initArguments().putString("citificateIds",maps.get("ids").toString());
            homeFragment.startFragment(AddressHelper.mainPage(homeFragment.getContext()));
        }
    }

    @Override
    public void getLocalAddress(MethodChannel.Result result) {
        Map<String,String> params=new HashMap<>();
        String str= CertificateHelper.getCurrentCertificateAddressName();
        LogUtils.e("地址名称----"+str);
        if(str==null||str.isEmpty())return;
//        String[] address=HomeUtils.getInstance().convertStrToArray2(str);
        params.put("addressName", str);
        params.put("addressIds",BaseConfig.cityCode);
        String jsonStrs=GsonUtils.gsonString(params);
        result.success(jsonStrs);
    }

    @Override
    public void getDafalutDatas(MethodChannel.Result result) {
        LogUtils.e("看下路由-----》》》");
        if(defaultByAddressBean==null||defaultByAddressBean.get()==null){
            getDefalutConfig(result);
        }else {
            String json=GsonUtils.gsonString(defaultByAddressBean.get());
            result.success(json);
        }
    }

    @Override
    public void getDefalutConfig(MethodChannel.Result result) {
        launchOnlyResult(mApi.getDefalutAddressConfig(new HashMap<>()), new OnHandleException<BaseData<DefaultByAddressBean>>() {
            @Override
            public void success(BaseData<DefaultByAddressBean> data) {
                defaultByAddressBean.set(data.Data);
                if(result==null){
                }else {
                    String json=GsonUtils.gsonString(defaultByAddressBean.get());
                    LogUtils.e("看看传递的"+json);
                    result.success(json);
                }
            }

            @Override
            public void error(String msg) {
                defaultByAddressBean.set(null);
            }
        });
    }

    @Override
    public void resultCertificate(String json) {
        if(!NoDoubleClickUtils.isDoubleClick()){
            Map maps=GsonUtils.gsonToMaps(json);
            CertificateHelper.updateCertificate(maps.get("addressIds").toString(),maps.get("address").toString(),maps.get("ids").toString(),maps.get("name").toString(),6,maps.get("address").toString()+"·"+maps.get("name").toString());
            navigator.toMainPage("1100");
        }
    }

    @Override
    public void getLivePreData(MethodChannel.Result result) {
        launchOnlyResult(mApi.getLivePreData(new HashMap<>()), new OnHandleException<BaseData<CommonLivePreBean>>() {
            @Override
            public void success(BaseData<CommonLivePreBean> data) {
                if(data.getResult()==null){
                    toast("暂无数据");
                }else {
                    result.success(GsonUtils.gsonString(data.getResult()));
                }
                statePage=true;
                checkUserPayState();
            }

            @Override
            public void error(String msg) {
                toast(msg);
                statePage=true;
                checkUserPayState();
            }
        });
    }


    @Override
    public void goToMnks(boolean restart, String jsonData) {
        Map<String, String> map = JsonUtils.gsonToBean(jsonData, Map.class);
        homeFragment.initArguments().putString("title", "模拟考试");
        homeFragment.initArguments().putBoolean("restart", restart);
        homeFragment.initArguments().putString("paperType", map.get("paperType"));
        homeFragment.initArguments().putString("paperValue", map.get("paperValue"));
        homeFragment.initArguments().putString("paperId", map.get("paperValue"));
        homeFragment.startFragment(CoreExamHelper.mainPage(homeFragment.getActivity()));
    }

    @Override
    public void goToWebPay(String routh, String url, int payType, String payRouthParams) {
        if(repository.checkLoginOrAddress(false,BaseConfig.ORDER_BIND_TYPE)){
            homeFragment.initArguments().putString("routh", routh);
            homeFragment.initArguments().putString("url", url);
            homeFragment.initArguments().putInt("payType", payType);
            homeFragment.initArguments().putString("payRouthParams", payRouthParams);
            homeFragment.startFragment(CommonHelper.showCommonWeb());
        }
    }

    @Override
    public void goToAllPage(String num) {
        if (num.equals("0")) {
            ToastUtils.normal("暂无试题！", Gravity.CENTER);
            return;
        }
        homeFragment.initArguments().putString("title", getReroutType().equals("5") ? "全部错题" : "全部收藏");
        homeFragment.initArguments().putBoolean("restart", false);
        homeFragment.initArguments().putString("paperType", getReroutType());
        homeFragment.initArguments().putString("paperId", "all");
        homeFragment.startFragment(CoreExamHelper.mainPage(homeFragment.getContext()));
    }

    @Override
    public void goToDayPage(String num) {
        if (num.equals("0")) {
            ToastUtils.normal("暂无试题！", Gravity.CENTER);
            return;
        }
        homeFragment.initArguments().putString("title", getReroutType().equals("5") ? "今日错题" : "今日收藏");
        homeFragment.initArguments().putBoolean("restart", false);
        homeFragment.initArguments().putString("paperType", getReroutType());
        homeFragment.initArguments().putString("paperId", "day");
        homeFragment.startFragment(CoreExamHelper.mainPage(homeFragment.getContext()));
    }
    @Override
    public void cleanExamData() {
        Map<String, String> params = new HashMap<>();
        params.put("PaperType", getReroutType());
        navigator.showLoading(true, "清除中...");
        launchOnlyResult(mApi.clearErrorOrSaveData(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                navigator.showLoading(false, "切换中...");
                onHomeFlutterToJson(null,"resh",getReroutType());
                ToastUtils.normal("清除成功！", Gravity.CENTER);
            }
            @Override
            public void error(String msg) {
                navigator.showLoading(false, "切换中...");
                ToastUtils.normal(msg, Gravity.CENTER);
            }
        });
    }

    @Override
    public String getReroutType() {
        if(mRoute.equals(RouteConfig.ROUTE_ERROR)){
            return "5";
        }else if(mRoute.equals(RouteConfig.ROUTE_SAVE)){
            return "6";
        }
        return "0";
    }

    @Override
    public void initSaveOrErrorHttpsDatas() {
        Map<String, String> params = new HashMap<>();
        params.put("paperType", getReroutType());
        launchOnlyResult(mApi.getMainSaveOrErrorData(params), new OnHandleException<BaseData<MainSaveOrErrorBean>>() {
            @Override
            public void success(BaseData<MainSaveOrErrorBean> data) {
                if (data.getResult() == null) return;
                String json = GsonUtils.gsonString(data.Data);
                onHomeFlutterToJson(null,"jsonMain", json);
            }
            @Override
            public void error(String msg) {
            }
        });
    }

    @Override
    public void setExamTime(String times) {
        Map<String,String> params=new HashMap<>();
        params.put("examDate",times);
        navigator.showLoading(true,"请稍后....");
        launchOnlyResult(mApi.updateExamTime(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                navigator.showLoading(false,null);
                navigator.showToast(data.getMsg());
                initExamTimeInfo(null,"reshExamInfo");
            }
            @Override
            public void error(String msg) {
                navigator.showLoading(false,null);
                navigator.showToast(msg);
            }
        });
    }


    @Override
    public void switchRemoveState(String json) {
        navigator.showLoading(true, "切换中...");
        launchOnlyResult(mApi.clearSwitch(new HashMap<>()), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                navigator.showLoading(false, "切换中...");
                ToastUtils.normal("切换成功！", Gravity.CENTER);
            }

            @Override
            public void error(String msg) {
                navigator.showLoading(false, "切换中...");
                ToastUtils.normal(msg, Gravity.CENTER);
            }
        });
    }

    @Override
    public void gotoPay() {
        HomeConfig.isToastPay=true;
        PointHelper.joinPointData(PointerMsgType.POINTER_A_WRONGTOPIC_TRIGGERBOUNDCED, true);
        MessageHelper.openNoPremissBuyDialog(homeFragment.getActivity(), true, PayRouthConfig.PAY_ERROR_PAY);
    }

    @Override
    public void gotoChapterExam(String num, String ids) {
        if (num.equals("0")) {
            ToastUtils.normal("暂无试题！", Gravity.CENTER);
            return;
        }
        homeFragment.initArguments().putString("title", getReroutType().equals("5") ? "错题集" : "收藏夹");
        homeFragment.initArguments().putBoolean("restart", false);
        homeFragment.initArguments().putString("paperType", getReroutType());
        homeFragment.initArguments().putString("paperId", ids);
        homeFragment.startFragment(CoreExamHelper.mainPage(homeFragment.getContext()));
    }

    @Override
    public void gotoQuestionExam(String num, String type, String ids) {
        if (num.equals("0")) {
            ToastUtils.normal("暂无试题！", Gravity.CENTER);
            return;
        }
        homeFragment.initArguments().putString("title", getReroutType().equals("5") ? "错题集" : "收藏夹");
        homeFragment.initArguments().putBoolean("restart", false);
        homeFragment.initArguments().putString("paperType", getReroutType());
        homeFragment.initArguments().putString("paperId", ids);
        homeFragment.startFragment(CoreExamHelper.mainPage(homeFragment.getContext()));
    }

    @Override
    public void cleanExam(String type) {
        navigator.showTsDialog(getReroutType().equals("5") ? 5 : 6);
    }

    @Override
    public void initSaveOrErrorData(MethodChannel.Result result) {
        LogUtils.e("进来初始化-----收藏");
        result.success(UserHelper.isBecomeVip() ? "1" : "0");
        if(mRoute.contains(RouteConfig.ROUTE_SAVE)||mRoute.contains(RouteConfig.ROUTE_ERROR)){
            initSaveOrErrorHttpsDatas();
        }
    }

    @Override
    public void goToAutoExam(boolean toresults,boolean dialog,String type) {
        PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_SIMULATION, false);
        if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
//            int pageType = (Integer.parseInt(type) == 10 ) ? 1 : 0;
            int pageType = (Integer.parseInt(type) == 10 || Integer.parseInt(type) == 11) ? 1 : 0;
            String pagerType = Integer.parseInt(type) == 10 || Integer.parseInt(type) == 4 ? "7" : type;
            if(toresults){
                LogUtils.e("看看传递的111"+dialog);
                homeFragment.initArguments().putString("flutterRoute",RouteConfig.ROUTE_RESULTS+"/"+(UserHelper.isBecomeVip() ? "1" : "0") + "/" +pagerType);
                homeFragment.initArguments().putBoolean("autoDialog",dialog);
                homeFragment.initArguments().putInt("mPageType",pageType);
                homeFragment.initArguments().putString("pagerType",pagerType);
                homeFragment.startFragment(new HomeFragment());
            }else {
                homeFragment.initArguments().putBoolean("autoDialog",dialog);
                homeFragment.initArguments().putInt("mPageType",pageType);
                homeFragment.initArguments().putString("pagerType",pagerType);
                homeFragment.startFragment(ListHelper.toPageExamAuto());
            }
        }
    }

    @Override
    public void goToMessagePage() {
        homeFragment.initArguments().putString("flutterRoute",RouteConfig.ROUTE_MESSAGE);
        homeFragment.startFragment(new HomeFragment());
    }

    @Override
    public void initMessageListData(MethodChannel.Result result) {
        LogUtils.e("开始请求接口----->>>>>");
        launchOnlyResult(mApi.getMessageList(new HashMap<>()), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                List lsit= (List) data.getResult();
                String kson= com.wb.lib_utils.utils.GsonUtils.newInstance().listToJson(lsit);
                LogUtils.e("开始请求接口----->>>>>"+kson);
                onHomeFlutterToJson(result,null,kson );
            }
            @Override
            public void error(String msg) {
            }
        });
    }

    @Override
    public void appointment(String ids,String index) {
        Map<String,String> params=new HashMap<>();
        params.put("examIds",ids);
        navigator.showLoading(true,"预约中....");
        launchOnlyResult(mApi.appointment(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                onHomeFlutterToJson(null,"appointmentSuccess",index);
                navigator.showLoading(false,"");
                navigator.showToast(data.getMsg());
            }

            @Override
            public void error(String msg) {
                navigator.showLoading(false,"");
                navigator.showToast(msg);
            }
        });
    }

    @Override
    public void initOrderListData(MethodChannel.Result result) {
        launchOnlyResult(mApi.orderList(new HashMap<>()), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                List lsit= (List) data.getResult();
                String kson= com.wb.lib_utils.utils.GsonUtils.newInstance().listToJson(lsit);
                LogUtils.e("开始请求接口----->>>>>"+kson);
                onHomeFlutterToJson(result,null,kson );
            }

            @Override
            public void error(String msg) {

            }
        });
    }

    @Override
    public void initExamTimeInfo(MethodChannel.Result result,String key) {
        launchOnlyResult(mApi.getExamTimeInfo(new HashMap<>()), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                Object obj=data.getResult();
                String json= GsonUtils.gsonString(obj);
                onHomeFlutterToJson(result,key,json);
                try {
                    JSONObject jsonObject = new JSONObject(json);
                    homeFragment.initArguments().putBoolean("autoDialog",
                            0 == Double.valueOf(jsonObject.getJSONObject("CalendarInfo").getString("DateBoxStatus")).intValue());
                    openTimeSelect();
                } catch (JSONException ignore) {
                }
            }

            @Override
            public void error(String msg) {

            }
        });
    }

    @Override
    public void goToBuyWebByPoint(String point,boolean isToastPays) {
        String payUrl=ConfigHelper.getPayUrl();
        if(payUrl==null||payUrl.isEmpty())return;
        if (repository.checkLoginOrAddress(true,BaseConfig.ORDER_BIND_TYPE)) {
            HomeConfig.isToastPay=isToastPays;
            homeFragment.initArguments().putString("routh", "examtime");
            homeFragment.initArguments().putString("url", payUrl);
            homeFragment.initArguments().putInt("payType", 1);
            homeFragment.initArguments().putBoolean("paySuccessAutoClose",point.equals("15")||point.equals("16")||point.equals("17")?false:true);
            homeFragment.initArguments().putString("payRouthParams", point);
            homeFragment.startFragment(CommonHelper.showCommonWeb());
        }
    }

    @Override
    public void setExamTime() {
        MessageHelper.openTimeChoiceDialog(homeFragment.getActivity(), new OnChoiceTimeListenter() {
            @Override
            public void getChoiceTime(String year, String mouther, String day) {
                LogUtils.e("选择了---->>>"+year+""+mouther+""+day);
                String time=year+"-"+mouther+"-"+day;
                setExamTime(time);
            }
        });
    }

    @Override
    public void goToAscend() {
        if (!NoDoubleClickUtils.isDoubleClick()&&repository.checkLoginOrAddress(true, BaseConfig.ORDER_BIND_TYPE,false)) {
            boolean autoDialog = homeFragment.getBoolean("autoDialog", false);
            LogUtils.e("------>>>>点击了" + autoDialog);
            homeFragment.initArguments().putBoolean("goToAscend", autoDialog);
            homeFragment.initArguments().putString("flutterRoute", RouteConfig.ROUTE_TESTDATE);
            homeFragment.startFragment(new HomeFragment());
        }
    }

    @Override
    public void finshPage() {
        navigator.closeView();
    }

    @Override
    public void goToMnksByHpttp(boolean restart, String jsonData) {
        Map<String,String> params=new HashMap<>();
        params.put("isContinue",(!restart)+"");
        launchOnlyResult(mApi.toMnksPostRecords(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
            }

            @Override
            public void error(String msg) {
            }
        });
        if(restart){
            onHomeFlutterToJson(null,"restart","1");
        }else {
            goToMnks(restart, jsonData);
//            navigator.closeView();
        }
    }

    @Override
    public void test() {
        LogUtils.e("点击了");
//        iHomeFragment.closeActivity();
        HomeHelper.start(homeFragment.getActivity());
    }

    @Override
    public void getCoupionListData(MethodChannel.Result result) {
        LogUtils.e("开始获取优惠卷----->>>>");
        launchOnlyResult(mApi.getCouponcanterDatas(new HashMap<>()), new OnHandleException<BaseData<FlutterCounponsBean>>() {
            @Override
            public void success(BaseData<FlutterCounponsBean> data) {
                onHomeFlutterToJson(result,null,GsonUtils.gsonString(data.getResult().getCoupons()));
            }

            @Override
            public void error(String msg) {

            }
        });
    }

    @Override
    public void passVipByRouth(String rouths,boolean needs) {
        MessageHelper.openNoPremissBuyDialog(homeFragment.getActivity(),needs,rouths);
    }
    @Override
    public void openLearning(String pass) {
        RxJavaUtils.delay(1, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        MessageHelper.openLearningPlanDialog(homeFragment.getActivity(),pass,2);
                    }
                });
            }
        });
    }

    @Override
    public void goToEj() {
        navigator.toMainPage("2000");
        LogUtils.e("进入到二建---->>>>>>");
    }

    @Override
    public void openVerificationCodeLogin() {
        homeFragment.startFragment(AccountHelper.showAccountLogin());
    }
    @Override
    public void loginSuccess() {
        queryCouponInfo();
//        iHomeFragment.loginSuccess();
    }

    @Override
    public void accountOffline() {
        AccountHelper.exitAccount();
        LogUtils.e("------>>>账号-----》》》accountOffline");
//        iHomeFragment.accountOffline();
    }

    @Override
    public void userPaySuccessInfo() {
        Map<String,String> params=new HashMap<>();
        if(BaseConfig.channelUID!=null&&!BaseConfig.channelUID.isEmpty()){
            params.put("fromWhichPerson",BaseConfig.channelUID);
        }
        launchOnlyResult(mApi.getMainInfo(params), new OnHandleException<BaseData<HomeBean>>() {
            @Override
            public void success(BaseData<HomeBean> data) {
                mHomeBean = data.Data;
//                iHomeFragment.onHomeDataRefresh(data);
                if(data.getResult().getOrderBindType()!=0||data.getResult().getOrderBindType()!=4){
                    LogUtils.e("开始打开页面");
                    //0-不弹 1-二建单科 2-二建套餐_购买 3二建套餐_赠送 4-安全员赠送（买了二建套餐选择安全员时）
                    BaseConfig.payWaitClass=data.Data.getOrderBindType();
                    Bus.post(new MsgEvent(PayMsgTypeConfig.PAY_MSG_TS_BASE));
                }
            }
            @Override
            public void error(String msg) {
                navigator.showToast(msg);
            }
        });
//        getIndexDatas(null,false);
//        MessageHelper.openPaySuccessDialog(homeFragment.getActivity(), new PaySuccessListen() {
//            @Override
//            public void paySunncess(int type, String url) {
//                if(type==1){
//                    HomeConfig.isToastPay=false;
//                    homeFragment.initArguments().putString("routh", "home");
//                    homeFragment.initArguments().putString("url", url);
//                    homeFragment.initArguments().putInt("payType", 1);
//                    homeFragment.initArguments().putString("payRouthParams", PayRouthConfig.PAY_BANNER);
//                    homeFragment.startFragment(CommonHelper.showCommonWeb());
//                }
//            }
//        });
    }

    @Override
    public void getHttpsData(MethodChannel.Result result,boolean passCertificate) {
        repository.routingHop(mRoute,mPaperType,result,passCertificate);
    }
    public void queryCouponInfo(){
        if (!isDoubleClick()) {
            MainThreadUtils.postDelayed(new Runnable() {
                @Override
                public void run() {
                    TaskHelper.detectionCoupon(new CoupoinValidationListenter() {
                        @Override
                        public void onCoupoinShow() {
                        }
                    });
                }
            },500L);
        }
    }
    public IMsg IMsgListener =new  IMsg(){

        @Override
        public void verificationCodeLogin() {
            openVerificationCodeLogin();
        }

        @Override
        public void accountLoginSuccess() {
            loginSuccess();
        }

        @Override
        public void accountOut() {
            loginSuccess();
        }

        @Override
        public void accountAutoOffline() {
            if(!mRoute.contains("main")){
                homeFragment.finish();
            }
            accountOffline();
        }

        @Override
        public void successPayInfo() {
            userPaySuccessInfo();
        }

        @Override
        public void liveSuccess() {
            onHomeFlutterToJson(null,"livePreSuccess",null);
        }

        @Override
        public void checkCouponid() {
            LogUtils.e("优惠卷----->>>>1");
            checkLocalCoupon();
        }

        @Override
        public void finshPageView() {
            closeView();
        }

        @Override
        public void finshActivity() {
//            iHomeFragment.closeActivity();
        }

        @Override
        public void nativeToFlutterMouth(String key, String json) {
            onHomeFlutterToJson(null,key,json);
        }
        @Override
        public void paySuccess() {
            if(mRoute.contains(RouteConfig.ROUTE_ERROR)){
                reshSaveOrErrorHttpsDatas();
            }else if(mRoute.contains(RouteConfig.ROUTE_SAVE)){
                reshSaveOrErrorHttpsDatas();
            }else if(mRoute.contains(RouteConfig.ROUTE_LIVEPRE)){
                if(HomeConfig.isToastPay){
                    reshLivePreHttpsDatas();
                }
            }else if(mRoute.contains(RouteConfig.ROUTE_TESTDATE)){
                LogUtils.e("日历支付成功");
                navigator.closeView();
            } else if(mRoute.contains(RouteConfig.ROUTE_ORDER)){
                LogUtils.e("开始刷新订单信息------>>>>>");
                nativeToFlutterMouth("conpionsuccess",null);
            }else if(mRoute.contains(RouteConfig.ROUTE_HOME)){
                userPaySuccessInfo();
                nativeToFlutterMouth("paySuccess",null);
            }else {
                nativeToFlutterMouth("paySuccess",null);
            }
        }

        @Override
        public void checkPageClose() {
            LogUtils.e("开始关闭------>>>>1"+mRoute);
            if(mRoute.contains(RouteConfig.ROUTE_HOME))return;
            navigator.closeView();
        }

        @Override
        public void checkPageRouthFinash() {
            LogUtils.e("更新了证书状态---->>>"+mRoute);
            if(mRoute.contains(RouteConfig.ROUTE_INDEXGUIDE)){
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        navigator.toMainPage(BaseConfig.MAJOR_PID+"");
                    }
                });
            }
        }

        @Override
        public void payFilaTs() {
//            if(mRoute.contains(RouteConfig.ROUTE_RESULTS)){
//                MessageHelper.openPayFailServer(homeFragment.getActivity(),homeFragment);
//            }
        }
    };

    /**
     * 检查用户登录状态和地址信息
     * @param needLogin 是否需要登录
     * @param orderBindType 订单绑定类型
     * @return 是否通过检查
     */
    public boolean checkLoginOrAddress(boolean needLogin, int orderBindType) {
        if (repository != null) {
            return repository.checkLoginOrAddress(needLogin, orderBindType);
        }
        return false;
    }
}
