package com.zizhiguanjia.model_home.viewmodel;

import android.view.View;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.wb.lib_network.BaseViewModel;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.model_home.api.HomeSeriveApi;
import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.listener.IHome;
import com.zizhiguanjia.model_home.listener.IHomeActivity;
import com.zizhiguanjia.model_home.listener.IHomeRepositortListener;
import com.zizhiguanjia.model_home.navigator.HomeActivityNavigator;
import com.zizhiguanjia.model_home.repository.HomeFlutterRepository;
import com.zizhiguanjia.model_home.ui.HomeNativeActivity;

import java.util.HashMap;
import java.util.Map;

import androidx.databinding.ObservableField;
import io.flutter.plugin.common.MethodChannel;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public class HomeViewModel extends BaseViewModel {
    private HomeSeriveApi mApi = new Http().create(HomeSeriveApi.class);
    private HomeActivityNavigator navigator;
    private IHome repository;
    private HomeNativeActivity iHomeFragment;
    private MutableLiveData<HomeBean> homeBean = new MutableLiveData<>();

    // 定义OnHandleException接口，用于处理请求结果
    public interface OnHandleException<T> {
        void success(T data);
        void error(String msg);
    }

    public void init(HomeActivityNavigator navigator, HomeNativeActivity iHomeFragment) {
        this.navigator = navigator;
        this.iHomeFragment = iHomeFragment;
        repository = new HomeFlutterRepository();
        repository.init(new HomeRepositortListener());
    }

    // 移除@Override注解，因为BaseViewModel中可能没有这个方法
    public void click(View view) {

    }

    public void refreshTitle(String title) {
        // 添加日志记录每次调用，以便追踪
        com.wb.lib_utils.utils.log.LogUtils.d("HomeViewModel - refreshTitle: " + title);
        
        // 添加判断，确保title不为空
        if (navigator != null && title != null) {
            navigator.originalTitle(title);
        }
    }

    public void initCretificate() {
        String title = CertificateHelper.initCertificate(true);
        if (navigator != null) {
            navigator.originalTitle(title);
        }
    }

    /**
     * 自定义的网络请求处理方法，替代BaseViewModel中的launchOnlyResult
     */
    private <T> Disposable customLaunchRequest(Observable<T> observable, final OnHandleException<T> callback) {
        return observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<T>() {
                    @Override
                    public void accept(T t) throws Exception {
                        if (callback != null) {
                            callback.success(t);
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (callback != null) {
                            callback.error(throwable.getMessage());
                        }
                    }
                });
    }

    public void getIndexDatas(MethodChannel.Result result, boolean passCertificate) {
        Map<String, String> params = new HashMap<>();
        if (BaseConfig.channelUID != null && !BaseConfig.channelUID.isEmpty()) {
            params.put("fromWhichPerson", BaseConfig.channelUID);
        }
        customLaunchRequest(mApi.getMainInfo(params), new OnHandleException<BaseData<HomeBean>>() {
            @Override
            public void success(BaseData<HomeBean> data) {
                // 更新HomeBean LiveData
                if (data != null && data.Data != null) {
                    homeBean.setValue(data.Data);
                }
                
                if (iHomeFragment != null) {
                    iHomeFragment.onHomeDataRefresh(data);
                }
                
                if (data != null && data.Data != null) {
                    BaseConfig.ORDER_BIND_TYPE = data.Data.getOrderBindType();
                    LogUtils.e("设置了升级----->>>" + BaseConfig.ORDER_BIND_TYPE);
                }
                
                if (repository != null) {
                    repository.successNetWorkDatas(data, result, passCertificate);
                }
            }

            @Override
            public void error(String msg) {
                if (navigator != null) {
                    navigator.showToast(msg);
                }
            }
        });
    }

    /**
     * 获取HomeBean数据的LiveData对象
     * @return HomeBean数据的LiveData对象
     */
    public LiveData<HomeBean> getHomeBean() {
        return homeBean;
    }

    /**
     * 刷新首页数据
     * @param passCertificate 是否通过证书验证
     */
    public void reshHomeData(boolean passCertificate) {
        // 调用获取首页数据的方法
        getIndexDatas(null, passCertificate);
    }
    
    /**
     * 检查登录状态和地址信息
     * @param address 是否需要检查地址
     * @param orderBindType 绑定类型
     * @return 是否通过检查
     */
    public boolean checkLoginOrAddress(boolean address, int orderBindType) {
        if (repository != null) {
            return repository.checkLoginOrAddress(address, orderBindType);
        }
        return false;
    }
    
    /**
     * 检查登录状态和地址信息（带一键登录双击检查参数）
     * @param address 是否需要检查地址
     * @param orderBindType 绑定类型
     * @param oneKeyLoginCheckDoubleClick 是否检查一键登录双击
     * @return 是否通过检查
     */
    public boolean checkLoginOrAddress(boolean address, int orderBindType, boolean oneKeyLoginCheckDoubleClick) {
        if (repository != null) {
            return repository.checkLoginOrAddress(address, orderBindType, oneKeyLoginCheckDoubleClick);
        }
        return false;
    }
    
    /**
     * 设置顶部标题信息
     * @param show 是否显示标题
     * @param title 标题文本
     */
    public void setTopTitleInfo(boolean show, String title) {
        if (navigator != null) {
            navigator.setTopTitleInfo(show, title);
        }
    }
    
    /**
     * 获取抖音VIP更新提示信息
     */
    public void getDyVipUpdateHint() {
        Map<String, String> params = new HashMap<>();
        customLaunchRequest(mApi.getUpgradeReminder(params), new OnHandleException<BaseData<DyVipUpdateHintBean>>() {
            @Override
            public void success(BaseData<DyVipUpdateHintBean> data) {
                if (data != null && data.Data != null && navigator != null) {
                    navigator.showDyVipUpdateHintDialog(data.Data);
                }
            }

            @Override
            public void error(String msg) {
                if (navigator != null) {
                    navigator.showToast(msg);
                }
            }
        });
    }

    // HomeRepositortListener实现不变
    private class HomeRepositortListener implements IHomeRepositortListener {
        @Override
        public void initFlutter(String route) {
            // 初始化Flutter
        }

        @Override
        public void queryCouponInfo() {
            // 查询优惠券信息
        }

        @Override
        public void onHomeTips(String des, boolean paperType, boolean passCertificate) {
            // 处理首页提示
        }

        @Override
        public void onHomeFlutterToJson(MethodChannel.Result result, String key, String json) {
            // 将数据转换为JSON并发送到Flutter
        }

        @Override
        public void goToAddess() {
            // 跳转到地址选择页面
        }

        @Override
        public void openVerificationCodeLogin() {
            // 打开验证码登录页面
        }

        @Override
        public void loginSuccess() {
            // 登录成功处理
        }

        @Override
        public void accountOffline() {
            // 账号离线处理
        }

        @Override
        public void userPaySuccessInfo() {
            // 用户支付成功信息处理
        }

        @Override
        public void getIndexDatas(MethodChannel.Result result, boolean passCertificate) {
            // 获取首页数据
            HomeViewModel.this.getIndexDatas(result, passCertificate);
        }

        @Override
        public void setTopTitle(boolean show, String title) {
            // 设置顶部标题
            if (navigator != null) {
                navigator.setTopTitleInfo(show, title);
            }
        }

        @Override
        public void checkLocalCoupon() {
            // 检查本地优惠券
        }

        @Override
        public void reshSaveOrErrorHttpsDatas() {
            // 刷新保存或错误数据
        }

        @Override
        public void reshLivePreHttpsDatas() {
            // 刷新直播预览数据
        }

        @Override
        public void reshResultHttpsDatas(int paperType) {
            // 刷新结果数据
        }

        @Override
        public void showGuideView() {
            // 显示引导视图
            if (navigator != null) {
                navigator.showGuide();
            }
        }

        @Override
        public void openTimeSelect() {
            // 打开时间选择
        }

        @Override
        public void goToBindSubject() {
            // 跳转到绑定科目页面
        }

        @Override
        public void getBackVideoListData(MethodChannel.Result result) {
            // 获取回放视频列表数据
        }

        @Override
        public void initExamTimeInfo(MethodChannel.Result result, String key) {
            // 初始化考试时间信息
        }
    }
}
