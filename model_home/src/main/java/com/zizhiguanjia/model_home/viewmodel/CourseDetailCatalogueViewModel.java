package com.zizhiguanjia.model_home.viewmodel;

import android.annotation.SuppressLint;
import android.view.Gravity;

import com.caimuhao.rxpicker.RxPicker;
import com.caimuhao.rxpicker.bean.ImageItem;
import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_common.manager.UploadManager;
import com.zizhiguanjia.model_home.api.HomeSeriveApi;
import com.zizhiguanjia.model_home.bean.CourseDetailCatalogueBean;
import com.zizhiguanjia.model_home.fragment.CourseDetailCatalogueFragment;
import com.zizhiguanjia.model_home.navigator.CourseDetailCatalogueNavigator;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import androidx.lifecycle.MutableLiveData;

/**
 * 功能作用：课程详情ViewModel
 * 初始注释时间： 2023/11/22 20:54
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class CourseDetailCatalogueViewModel extends CommonViewModel {
    private CourseDetailCatalogueFragment courseFragment;
    private CourseDetailCatalogueNavigator navigator;
    /**
     * 使用的实体
     */
    private CourseDetailCatalogueBean mUseBean;
    /**
     * 当前播放的
     */
    private CourseDetailCatalogueBean.Item mCurrentBean;
    private HomeSeriveApi api = new Http().create(HomeSeriveApi.class);
    /**
     * 评价信息是否提交成功
     */
    public MutableLiveData<Boolean> mCommentSubmitSuccess = new MutableLiveData<>();

    /**
     * 是否允许提交评论信息
     */
    public MutableLiveData<Boolean> mCommentAllowSubmit = new MutableLiveData<>();


    /**
     * 检测是否完成了课程
     *
     * @param childBean 当前节数据
     * @return 完成返回true
     */
    public boolean checkFinish(CourseDetailCatalogueBean.Item childBean) {
        return childBean.getComplete() && 0 == childBean.getUnDoQuestionCount();
    }


    /**
     * 获取未完成问题数量
     *
     * @param childBean 当前节数据
     * @return 有未完成的返回拼接好的字符串，否则返回null
     */
    public String getUnDoQuestion(CourseDetailCatalogueBean.Item childBean) {
        if (childBean.getComplete() && 0 < childBean.getUnDoQuestionCount()) {
            return "剩余" + childBean.getUnDoQuestionCount() + "道练习题";
        } else {
            return null;
        }
    }

    /**
     * 获取上次看到进度
     *
     * @param childBean 当前节数据
     * @return 有未完成的返回拼接好的字符串，否则返回null
     */
    public String getLastPlayTime(CourseDetailCatalogueBean.Item childBean) {
        if (!childBean.getComplete() && 0 < childBean.getLastDuration() * 1000 && childBean.getLastDuration() * 1000 < timeStrToMill(
                childBean.getTotalDuration())) {
            return "上次看到 " + millToTimeStr(childBean.getLastDuration() * 1000);
        } else if(childBean.getComplete() && 0 == childBean.getUnDoQuestionCount()){
            return "已学完";
        }else {
            return null;
        }
    }

    /**
     * 判断是否是正在播放的
     *
     * @param childBean 当前节数据
     * @return 有未完成的返回拼接好的字符串，否则返回null
     */
    public boolean isPlayIng(CourseDetailCatalogueBean.Item childBean) {
        return mCurrentBean != null && Objects.equals(mCurrentBean.getVIdeoId(), childBean.getVIdeoId());
    }

    /**
     * 设置当前正在播放的
     *
     * @param needPlayChild 当前正在播放的
     */
    public void setCurrentPlayBean(CourseDetailCatalogueBean.Item needPlayChild) {
        this.mCurrentBean = needPlayChild;
    }

    /**
     * 时间字符串转毫秒值
     *
     * @param time 时间字符串
     * @return 毫秒值
     */
    public Long timeStrToMill(String time) {
        String[] split = time.split(":");
        return (Long.parseLong(split[0]) * 60 + Long.parseLong(split[1])) * 1000L;
    }

    /**
     * 毫秒值转显示字符串
     *
     * @param mill 毫秒值
     * @return 显示字符串
     */
    public String millToTimeStr(long mill) {
        long mine = mill / 60000;
        long second = mill % 60000 / 1000;
        return (mine < 10 ? "0" + mine : mine) + ":" + (second < 10 ? "0" + second : second);
    }

    public void initParams(CourseDetailCatalogueFragment courseFragment, CourseDetailCatalogueNavigator navigator) {
        this.courseFragment = courseFragment;
        this.navigator = navigator;
    }

    public void setUseBean(CourseDetailCatalogueBean bean) {
        mUseBean = bean;
    }


    /*-----------------------------------------评价相关-----------------------------------------------*/

    /**
     * 评价选择的等级
     */
    private Integer mCommentSelectLevel;

    /**
     * 评价选择的类型，是评价本节还是全部
     */
    private int mCommentSelectType;

    /**
     * 评价输入的内容
     */
    private String mCommentInoutContent;

    /**
     * 评价选择的图片
     */
    private final List<ImageItem> mCommentSelectImages = new ArrayList<>();

    /**
     * 设置评论选择的等级
     *
     * @param level 等级
     */
    public void setCommentSelectLevel(Integer level) {
        mCommentSelectLevel = level;
        checkCommentAllowSubmit();
    }

    /**
     * 设置评价选择的类型
     *
     * @param type 是评价本节还是全部
     */
    public void setCommentSelectType(int type) {
        mCommentSelectType = type;
        checkCommentAllowSubmit();
    }

    /**
     * 设置评价输入的内容
     *
     * @param content 输入的内容
     */
    public void setCommentInputContent(String content) {
        mCommentInoutContent = content;
        checkCommentAllowSubmit();
    }

    /**
     * 检测是否允许提交评价
     */
    public void checkCommentAllowSubmit() {
        mCommentAllowSubmit.postValue(mCommentSelectLevel != null);
    }

    /**
     * 打开评价图片选择
     *
     * @param currentMaxSize 最大选择数量
     */
    @SuppressLint("CheckResult")
    public void openCommentImageSelect(int currentMaxSize) {
        RxPicker.of().single(false).camera(false).limit(1, currentMaxSize).start(courseFragment.getActivity()).subscribe(images -> {
            mCommentSelectImages.addAll(images);
            checkCommentAllowSubmit();
            navigator.updateCommentImage(images);
        });
    }

    /**
     * 移除选择的评价图片
     *
     * @param ids 要被移除的图片id
     */
    public void removeCommentImage(int ids) {
        if (mCommentSelectImages.size() == 0) {
            return;
        }
        Iterator<ImageItem> iterator = mCommentSelectImages.iterator();
        while (iterator.hasNext()) {
            ImageItem imageItem = iterator.next();
            if (imageItem.getId() == ids) {
                iterator.remove();
            }
        }
    }

    /**
     * 清除选择的评价图片
     */
    public void clearSelectCommonImages(){
        mCommentSelectImages.clear();
    }

    /**
     * 提交评价信息
     */
    public void submitCommentInfo() {
        if (mCommentSelectLevel == null) {
            ToastUtils.normal("请先选择评分！", Gravity.CENTER);
            return;
        }
        navigator.showLoading(true);
        if (mCommentSelectImages.size() > 0) {
            uploadCommentImage();
        } else {
            uploadCommentInfo(null);
        }
    }

    /**
     * 上传评价信息
     *
     * @param urls 有图片的话是图片链接
     */
    private void uploadCommentInfo(String urls) {
        navigator.showLoading(true);
        Map<String, String> params = new HashMap<>();
        params.put("type", String.valueOf(mCommentSelectType));//本节：21，整体：22
        if (!StringUtils.isEmpty(mCommentInoutContent)) {
            params.put("content", mCommentInoutContent);
        }
        if (!StringUtils.isEmpty(urls)) {
            params.put("images", urls);
        }
        params.put("refferPage", "6");
        params.put("score", String.valueOf(mCommentSelectLevel));
        params.put("courseCode", mCurrentBean.getCode());
        launchOnlyResult(api.uploadImageOrTxt(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                ToastUtils.normal("感谢您的建议", Gravity.CENTER);
                navigator.showLoading(false);
                mCommentSubmitSuccess.postValue(true);
            }

            @Override
            public void error(String msg) {
                ToastUtils.normal(msg, Gravity.CENTER);
                navigator.showLoading(false);
            }
        });
    }

    /**
     * 上传评价图片
     */
    private void uploadCommentImage() {
        List<File> files = new ArrayList<>();
        for (ImageItem imageItem : mCommentSelectImages) {
            if (imageItem.getId() != 0) {
                files.add(new File(imageItem.getPath()));
            }
        }
        Map<String, String> params = new HashMap<>();
        params.put("type", "2");
        UploadManager.getInstance().uploadMulteImage(BaseAPI.VERSION_DES + "/API/Common/UploadImages", files, params,
                (uploadState, urls) -> MainThreadUtils.post(() -> {
                    if (uploadState) {
                        uploadCommentInfo(urls);
                    } else {
                        ToastUtils.normal(urls, Gravity.CENTER);
                        navigator.showLoading(false);
                    }
                }));

    }
}
