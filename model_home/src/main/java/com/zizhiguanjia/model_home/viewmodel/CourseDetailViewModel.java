package com.zizhiguanjia.model_home.viewmodel;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_home.api.HomeSeriveApi;
import com.zizhiguanjia.model_home.bean.CourseDetailCatalogueBean;
import com.zizhiguanjia.model_home.bean.CourseDetailGoodsBean;
import com.zizhiguanjia.model_home.fragment.CourseDetailFragment;
import com.zizhiguanjia.model_home.navigator.CourseDetailNavigator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 功能作用：课程详情ViewModel
 * 初始注释时间： 2023/11/22 20:54
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class CourseDetailViewModel extends CommonViewModel {
    private CourseDetailFragment courseFragment;
    private CourseDetailNavigator navigator;
    private HomeSeriveApi api = new Http().create(HomeSeriveApi.class);
    /**
     * 排序
     */
    private Integer mIndex = null;
    /**
     * 课程code
     */
    private String mCourseCode;

    /**
     * 详情数据
     */
    private CourseDetailCatalogueBean detailCatalogueBean;

    /**
     * 商品数据
     */
    private CourseDetailGoodsBean mCourseDetailGoodsBean;

    /**
     * 下一次要显示的数据
     */
    private CourseDetailCatalogueBean.Item mNextShowBean;

    /**
     * 加载中显示次数
     */
    private int mLoadingCount = 0;

    /**
     * 获取下一节要显示的数据
     *
     * @param code 编码，要查找的编码
     * @return
     */
    public CourseDetailCatalogueBean.Item getNextShowData(String code) {
        if (code != null) {
            //生成全部的子列表
            List<CourseDetailCatalogueBean.Item> childList = getChildList();
            for (int i = 0; i < childList.size(); i++) {
                if (Objects.equals(code, childList.get(i).getCode())) {
                    if (i + 1 < childList.size()) {
                        return childList.get(i + 1);
                    }
                }
            }
        }
        return null;
    }
    /**
     * 获取下一节要显示的数据
     *
     * @param bean
     * @return
     */
    public CourseDetailCatalogueBean.Item getNextShowData(CourseDetailCatalogueBean.Item bean) {
        if (bean != null) {
            //生成全部的子列表
            List<CourseDetailCatalogueBean.Item> childList = getChildList();
            for (int i = 0; i < childList.size(); i++) {
                if (bean.getVIdeoId() != null && Objects.equals(bean.getVIdeoId(), childList.get(i).getVIdeoId())) {
                    if (i + 1 < childList.size()) {
                        return childList.get(i + 1);
                    }
                }
            }
        }
        return null;
    }

    public void initParams(CourseDetailFragment courseFragment, CourseDetailNavigator navigator) {
        this.courseFragment = courseFragment;
        this.navigator = navigator;
        if (courseFragment.getArguments() != null && courseFragment.getArguments().containsKey("index")) {
            String index = courseFragment.getArguments().getString("index", null);
            if (index != null && index.matches("[0-9]+")) {
                mIndex = Integer.parseInt(index);
            }
        }
        mCourseCode = courseFragment.getArguments().getString("useId", "");
    }

    public void getHttpData() {
        if (mLoadingCount == 0) {
            navigator.showLoading(true);
            mLoadingCount++;
        }
        launchOnlyResult(api.getCourseDetailCatalogue(new HashMap<>()), new OnHandleException<BaseData<CourseDetailCatalogueBean>>() {
            @Override
            public void success(BaseData<CourseDetailCatalogueBean> data) {
                detailCatalogueBean = data.Data;
                navigator.setShowData(data.Data);
                navigator.showLoading(false);
            }

            @Override
            public void error(String msg) {
                navigator.showLoading(false);

            }
        });
        launchOnlyResult(api.getCourseDetailGoods(new HashMap<>()), new OnHandleException<BaseData<CourseDetailGoodsBean>>() {
            @Override
            public void success(BaseData<CourseDetailGoodsBean> data) {
                mCourseDetailGoodsBean = data.Data;
                navigator.setShowData(data.Data);
            }

            @Override
            public void error(String msg) {

            }
        });
    }

    /**
     * 重新加载数据，并加载当前数据
     *
     * @param bean 当前数据
     */
    public void loadCurrentShowData(CourseDetailCatalogueBean.Item bean) {
        mNextShowBean = bean;
        getHttpData();
    }

    /**
     * 上传进度
     *
     * @param bean     当前播放的实体
     * @param duration 播放进度
     */
    public void submitVideoDuration(CourseDetailCatalogueBean.Item bean, Long duration) {
        if (bean != null) {
            bean.setLastDuration(duration / 1000);
            HashMap<String, String> map = new HashMap<>();
            map.put("duration", String.valueOf(duration / 1000));
            map.put("courseCode", bean.getCode());
            launchOnlyResult(api.submitVideoDuration(map), new OnHandleException<BaseData>() {
                @Override
                public void success(BaseData data) {

                }

                @Override
                public void error(String msg) {

                }
            });
        }
    }

    /**
     * 获取需要播放的章节
     *
     * @return 需要播放的节信息
     */
    public CourseDetailCatalogueBean.Item getNeedPlayChild() {
        if (detailCatalogueBean != null) {
            //生成全部的子列表
            List<CourseDetailCatalogueBean.Item> childList = getChildList();

            //切换显示处理
            if(mNextShowBean != null){
                for (CourseDetailCatalogueBean.Item item : childList) {
                    if (mNextShowBean.getCode().equals(item.getCode())) {
                        return item;
                    }
                }
            }

            if (detailCatalogueBean.getVip()) {
                //是vip则看选的那一个
                if (mIndex != null && mIndex >= 0 && mCourseCode != null) {
                    for (CourseDetailCatalogueBean.Item item : childList) {
                        if (mCourseCode.equals(item.getCode())) {
                            return item;
                        }
                    }
                } else {
                    //按进度播放最后一个进度
                    if (!childList.isEmpty()) {
                        return childList.get(detailCatalogueBean.getLocation() != null && detailCatalogueBean.getLocation() <= childList.size() ?
                                detailCatalogueBean.getLocation() - 1 : 0);
                    }
                }
            } else {
                //不是vip则只能看第一节
                if (!childList.isEmpty()) {
                    return childList.get(0);
                }
            }
        }
        return null;
    }

    /**
     * 获取所有的子数据列表
     *
     * @return 子数据列表
     */
    private List<CourseDetailCatalogueBean.Item> getChildList() {
        List<CourseDetailCatalogueBean.Item> childList = new ArrayList<>();
        if (detailCatalogueBean.getItems() != null) {
            for (CourseDetailCatalogueBean.Item item : detailCatalogueBean.getItems()) {
                if (item.getChilds() != null) {
                    childList.addAll(item.getChilds());
                }
            }
        }
        return childList;
    }

    public CourseDetailGoodsBean getmCourseDetailGoodsBean() {
        return mCourseDetailGoodsBean;
    }

    public CourseDetailCatalogueBean getDetailCatalogueBean() {
        return detailCatalogueBean;
    }
}
