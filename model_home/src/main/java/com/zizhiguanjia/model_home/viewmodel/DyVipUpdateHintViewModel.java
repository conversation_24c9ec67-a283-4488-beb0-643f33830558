package com.zizhiguanjia.model_home.viewmodel;

import android.graphics.Color;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.tb.TableCertificate;
import com.zizhiguanjia.model_home.api.HomeSeriveApi;
import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;
import com.zizhiguanjia.model_message.bean.CityPickerBean;
import com.zizhiguanjia.model_message.bean.CityPickerData;
import com.zizhiguanjia.model_message.bean.ParentSubjectsBean;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import androidx.lifecycle.MutableLiveData;

/**
 * 功能作用：抖音vip升级提示ViewModel
 * 初始注释时间： 2023/12/20 16:17
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class DyVipUpdateHintViewModel extends CommonViewModel {
    private final HomeSeriveApi mApi = new Http().create(HomeSeriveApi.class);

    /**
     * 显示的数据
     */
    private final MutableLiveData<DyVipUpdateHintBean> mHintBean = new MutableLiveData<>();

    /**
     * 地区列表
     */
    private final MutableLiveData<List<CityPickerData>> mAreaList = new MutableLiveData<>();

    /**
     * 考试类型列表
     */
    private final MutableLiveData<List<ParentSubjectsBean.Item>> mSubjectList = new MutableLiveData<>();

    /**
     * 科目列表
     */
    private final MutableLiveData<List<CityPickerData>> mLevelList = new MutableLiveData<>();

    /**
     * 使用的地区信息
     */
    private final MutableLiveData<CityPickerData> mAreaInfo = new MutableLiveData<>();

    /**
     * 使用的类型信息
     */
    private final MutableLiveData<ParentSubjectsBean.Item> mSubjectInfo = new MutableLiveData<>();

    /**
     * 使用的科目列表
     */
    private final MutableLiveData<CityPickerData> mLevelInfo = new MutableLiveData<>();

    /**
     * 是否成功了
     */
    private final MutableLiveData<Boolean> mSuccess = new MutableLiveData<>();

    public DyVipUpdateHintViewModel() {
        loadParentSubjects();
        loadAreas();
    }

    /**
     * 开通vip
     */
    public void openVip() {
        Map<String, String> map = new HashMap<>();
        map.put("majorPid", mSubjectInfo.getValue() != null ? mSubjectInfo.getValue().getId() : mHintBean.getValue().getMajorPid());
        map.put("majorId", mLevelInfo.getValue() != null ? mLevelInfo.getValue().getId() : mHintBean.getValue().getMajorId());
        map.put("areaPid", mAreaInfo.getValue() != null ? mAreaInfo.getValue().getId() : mHintBean.getValue().getAreaPid());
        launchOnlyResult(mApi.activateVipOtherPlatform(map), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                BaseConfig.majId = map.get("majorId");
                BaseConfig.MAJOR_PID = Integer.parseInt(map.get("majorPid"));
                BaseConfig.cityCode = map.get("areaPid");
                TableCertificate tableCertificate = CertificateHelper.getUserCertificate(AccountHelper.getCurrentLoginAccount());
                String address = mAreaInfo.getValue() != null ? mAreaInfo.getValue().getName() : tableCertificate.getCityName();
                String levelName = mLevelInfo.getValue() != null ? mLevelInfo.getValue().getName() : tableCertificate.getCertificateName();
                CertificateHelper.updateCertificate(BaseConfig.cityCode, address, BaseConfig.majId, levelName, 6, address + "·" + levelName);
                mSuccess.postValue(true);
            }

            @Override
            public void error(String msg) {
                mSuccess.postValue(false);
            }
        });
    }


    /**
     * 设置显示数据
     *
     * @param data 数据
     */
    public void setShowData(DyVipUpdateHintBean data) {
        mHintBean.setValue(data);
        mHintBean.postValue(data);
        mSubjectInfo.setValue(null);
        mAreaInfo.setValue(null);
        mLevelInfo.setValue(null);
        mSuccess.setValue(false);
        resetSubjectsInfo();
        resetAreaInfo();
    }
    /**
     * 清除选择
     */
    public void clearSelect() {
        mSubjectInfo.setValue(null);
        mAreaInfo.setValue(null);
        mLevelInfo.setValue(null);
        resetSubjectsInfo();
        resetAreaInfo();
    }

    /**
     * 获取考试类型
     */
    public void loadParentSubjects() {
        launchOnlyResult(mApi.getParentSubjects(new HashMap<>()), new OnHandleException<BaseData<ParentSubjectsBean>>() {
            @Override
            public void success(BaseData<ParentSubjectsBean> data) {
                if (data.getResult() == null || data.getResult().getRecords() == null || data.getResult().getRecords().size() == 0) {
                    return;
                }
                List<ParentSubjectsBean.Item> list = data.getResult().getRecords();
                Iterator<ParentSubjectsBean.Item> iterator = list.iterator();
                while (iterator.hasNext()) {
                    ParentSubjectsBean.Item item = iterator.next();
                    if (!(item != null && item.getName() != null && item.getId() != null)) {
                        iterator.remove();
                    }
                }
                mSubjectList.setValue(list);
                mSubjectList.postValue(list);
                resetSubjectsInfo();
            }

            @Override
            public void error(String msg) {

            }
        });
    }

    /**
     * 获取省份信息
     */
    public void loadAreas() {
        launchOnlyResult(mApi.getCityList(new HashMap<>()), new OnHandleException<BaseData<CityPickerBean>>() {
            @Override
            public void success(BaseData<CityPickerBean> data) {
                if (data.getResult() == null || data.getResult().getRecords() == null || data.getResult().getRecords().size() == 0) {
                    return;
                }
                List<CityPickerData> list = data.getResult().getRecords();
                Iterator<CityPickerData> iterator = list.iterator();
                while (iterator.hasNext()) {
                    CityPickerData item = iterator.next();
                    if (!(item != null && item.getName() != null && item.getId() != null)) {
                        iterator.remove();
                    }
                }
                mAreaList.setValue(list);
                mAreaList.postValue(list);
                resetAreaInfo();
            }

            @Override
            public void error(String msg) {

            }
        });
    }

    /**
     * 获取科目信息
     */
    private void getMajorsByAreaId() {
        if (mAreaInfo.getValue() == null || mSubjectInfo.getValue() == null) {
            return;
        }
        mLevelList.postValue(new ArrayList<>());
        Map<String, String> params = new HashMap<>();
        params.put("areaId", mAreaInfo.getValue().getId());
        params.put("majorPid", mSubjectInfo.getValue().getId());
        launchOnlyResult(mApi.getSecondConstruction(params), new OnHandleException<BaseData<CityPickerBean>>() {
            @Override
            public void success(BaseData<CityPickerBean> data) {
                if (data.getResult() == null || data.getResult().getRecords() == null || data.getResult().getRecords().size() == 0) {
                    return;
                }
                List<CityPickerData> list = data.getResult().getRecords();
                Iterator<CityPickerData> iterator = list.iterator();
                while (iterator.hasNext()) {
                    CityPickerData item = iterator.next();
                    if (item.getName() == null || item.getId() == null) {
                        iterator.remove();
                    }
                }
                mLevelList.setValue(list);
                mLevelList.postValue(list);
                resetLevelInfo();
            }

            @Override
            public void error(String msg) {

            }
        });
    }

    /**
     * 获取显示的描述文本
     */
    public SpannableString getShowDesInfo() {
        SpannableString showText = new SpannableString(
                MessageFormat.format(mHintBean.getValue().getUpgradeReminder().replaceAll("\n", "").replaceAll("\t", ""),
                        mHintBean.getValue().getPhone(), mHintBean.getValue().getOrderSource(), mHintBean.getValue().getAreaMajor()));
        int indexOf = showText.toString().indexOf(mHintBean.getValue().getAreaMajor());
        showText.setSpan(new ForegroundColorSpan(Color.parseColor("#D52930")), indexOf, indexOf + mHintBean.getValue().getAreaMajor().length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return showText;
    }

    public MutableLiveData<List<CityPickerData>> getAreaList() {
        return mAreaList;
    }

    public MutableLiveData<List<ParentSubjectsBean.Item>> getSubjectList() {
        return mSubjectList;
    }

    public MutableLiveData<List<CityPickerData>> getLevelList() {
        return mLevelList;
    }

    public MutableLiveData<CityPickerData> getAreaInfo() {
        return mAreaInfo;
    }

    public MutableLiveData<ParentSubjectsBean.Item> getSubjectInfo() {
        return mSubjectInfo;
    }

    public MutableLiveData<CityPickerData> getLevelInfo() {
        return mLevelInfo;
    }

    public MutableLiveData<Boolean> getSuccess() {
        return mSuccess;
    }

    /**
     * 设置选中的考试类型
     *
     * @param item 等级信息
     */
    public void setSelectSubject(ParentSubjectsBean.Item item) {
        mSubjectInfo.setValue(item);
        mSubjectInfo.postValue(item);
        setSelectLevel(null);
    }

    /**
     * 设置选中的地区
     *
     * @param item 等级信息
     */
    public void setSelectLocation(CityPickerData item) {
        mAreaInfo.setValue(item);
        mAreaInfo.postValue(item);
        setSelectLevel(null);
    }

    /**
     * 设置选中的科目
     *
     * @param item 等级信息
     */
    public void setSelectLevel(CityPickerData item) {
        if (item != null) {
            mLevelInfo.postValue(item);
        } else {
            getMajorsByAreaId();
        }
    }

    /**
     * 重置考试类型
     */
    private void resetSubjectsInfo() {
        if (mSubjectList.getValue() != null) {
            List<ParentSubjectsBean.Item> list = mSubjectList.getValue();
            if (mSubjectInfo.getValue() == null && mHintBean.getValue() != null && mHintBean.getValue().getMajorPid() != null) {
                for (ParentSubjectsBean.Item item : list) {
                    if (mHintBean.getValue().getMajorPid().equals(item.getId())) {
                        mSubjectInfo.setValue(item);
                        mSubjectInfo.postValue(item);
                        getMajorsByAreaId();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 重置地区
     */
    private void resetAreaInfo() {
        if (mAreaList.getValue() != null) {
            List<CityPickerData> list = mAreaList.getValue();
            if (mAreaInfo.getValue() == null && mHintBean.getValue() != null && mHintBean.getValue().getAreaPid() != null) {
                for (CityPickerData item : list) {
                    if (mHintBean.getValue().getAreaPid().equals(item.getId())) {
                        mAreaInfo.setValue(item);
                        mAreaInfo.postValue(item);
                        getMajorsByAreaId();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 重置科目信息
     */
    private void resetLevelInfo() {
        if (mLevelList.getValue() != null) {
            List<CityPickerData> list = mLevelList.getValue();
            if (mLevelInfo.getValue() == null && mHintBean.getValue() != null && mHintBean.getValue().getMajorId() != null) {
                for (CityPickerData item : list) {
                    if (mHintBean.getValue().getMajorId().equals(item.getId())) {
                        mLevelInfo.postValue(item);
                        break;
                    }
                }
            } else {
                if (!list.isEmpty()) {
                    mLevelInfo.postValue(list.get(0));
                }
            }
            mLevelList.postValue(list);
        }
    }

}
