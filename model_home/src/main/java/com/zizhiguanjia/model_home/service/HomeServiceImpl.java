package com.zizhiguanjia.model_home.service;

import android.content.Context;
import android.view.Gravity;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.lib_base.constants.MainRouterPath;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.lib_base.service.HomeService;
import com.zizhiguanjia.model_home.report.HomeMsgReport;

import java.util.Map;

import io.reactivex.functions.Consumer;

@Route(path = HomeRoutherPath.SERVICE)
public class HomeServiceImpl implements HomeService {
    @Override
    public void start(Context context) {
        ARouterUtils.navActivity(HomeRoutherPath.MAIN_ACTIVITY);
    }

    @Override
    public IFragment mainPage(Context context) {
        return ARouterUtils.navFragment(HomeRoutherPath.MAIN_FRAGMENT);
    }
    @Override
    public IFragment gotoAvd() {
        return ARouterUtils.navFragment(HomeRoutherPath.AVD_FRAGMENT);
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {
    }
    @Override
    public IFragment startMessageFragment() {
        return ARouterUtils.navFragment(HomeRoutherPath.MAIN_FRAGMENT);
    }

    @Override
    public void startHomeActivityByRoute(String router) {
        ARouter.getInstance()
                .build(HomeRoutherPath.MAIN_ACTIVITY)
                .withString("routePath",router)
                .navigation();
    }

    @Override
    public void start(Context context, String extraStr, String msgId) {
        ARouterUtils.navActivity(HomeRoutherPath.MAIN_ACTIVITY);
        RxJavaUtils.delay(2, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                if(msgId==null||msgId.isEmpty()){
                    ToastUtils.normal("消息Id错误", Gravity.CENTER);
                    return;
                }
                SdkHelper.startSdkWebview(context,extraStr,msgId);
            }
        });
    }
}
