package com.zizhiguanjia.model_home.repository;

import android.app.Activity;

import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_network.utils.JsonUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.model_home.bean.MenuNavsBean;
import com.zizhiguanjia.model_home.config.ChannelConfig;
import com.zizhiguanjia.model_home.config.HomeConfig;
import com.zizhiguanjia.model_home.config.HomeFlutterChannerTpis;
import com.zizhiguanjia.model_home.config.RouteConfig;
import com.zizhiguanjia.model_home.listener.IFlutter;
import com.zizhiguanjia.model_home.listener.IFlutterChanner;
import com.zizhiguanjia.model_home.listener.IFlutterChannerListener;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.android.FlutterView;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.embedding.engine.dart.DartExecutor;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public class FlutterChannerRepository implements IFlutterChanner {
    private FlutterEngine flutterEngine;
    private IFlutter iFlutter;
    @Override
    public void initFlutterEngine(String raouth, Activity activity, IFlutter iFlutter,FlutterEngine flutterEngine) {
        this.iFlutter=iFlutter;
        this.flutterEngine=flutterEngine;
        flutterEngine.getNavigationChannel().setInitialRoute(raouth);
        flutterEngine.getDartExecutor().executeDartEntrypoint(DartExecutor.DartEntrypoint.createDefault());
        initFlutterView(activity);
    }

    @Override
    public void initFlutterView(Activity activity) {
        FlutterView flutterView = new FlutterView(activity);
        if (flutterEngine != null) {
            flutterView.attachToFlutterEngine(flutterEngine);
        }
        if(iFlutter!=null){
            iFlutter.initFlutterViewSuccess(flutterView);
        }
        initMethodChannel(flutterEngine);
    }

    @Override
    public void initMethodChannel(FlutterEngine flutterEngine) {
        MethodChannel nativeChannel = new MethodChannel(flutterEngine.getDartExecutor()
                .getBinaryMessenger(), ChannelConfig.CHANNEL_NATIVE);
        if(iFlutter!=null){
            iFlutter.initFlutterChannerSuccess(nativeChannel);
        }
    }

    @Override
    public void jumpByTips(@NonNull MethodCall methodCall, @NonNull MethodChannel.Result result, IFlutterChannerListener iFlutterChannerListener) {
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                switch (methodCall.method) {
                    case HomeFlutterChannerTpis.HOME_FLUTTER_PAGEFINSH:
                        iFlutterChannerListener.getHttpsData(result,false);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_SAVELISTPAGE:
                        iFlutterChannerListener.goToPageByRoute(RouteConfig.ROUTE_SAVE);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_ERRORLISTPAGE:
                        iFlutterChannerListener.goToPageByRoute(RouteConfig.ROUTE_ERROR);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_TOAST:
                        String msg = (String) methodCall.arguments;
                        if (msg == null || msg.isEmpty()) return;
                        iFlutterChannerListener.toast(msg);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_TXLXLISTPAGE:
                        iFlutterChannerListener.goToTopicList(true);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_NOMREMISS_VIP:
                        HomeConfig.isToastPay=true;
                        String payType = (String) methodCall.arguments;
                        iFlutterChannerListener.noBuyDialog(payType);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_GOPAYPAGE:
                        String urls = (String) methodCall.arguments;
                        iFlutterChannerListener.goToBuyUrl(urls);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_CHAPPTERLISTPAGE:
                        iFlutterChannerListener.goToChapterList();
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_CALLTEL:
                        String tel = (String) methodCall.arguments;
                        if(checkParams(tel,iFlutterChannerListener)){
                            iFlutterChannerListener.dioTel(tel);
                        }
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_GOVIDEO:
                        String videotType = (String) methodCall.arguments;
                        if(checkParams(videotType,iFlutterChannerListener)){
                            iFlutterChannerListener.goToVideo(videotType);
                        }
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_PAYSUCCESS:
                        String success = (String) methodCall.arguments;
                        if(checkParams(success,iFlutterChannerListener)){
                            iFlutterChannerListener.paySuccessState(success);
                        }
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_CLOSEVIEW:
                    case HomeFlutterChannerTpis.LIVE_FLUTTER_FINSH:
                        iFlutterChannerListener.finshPage();
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_BANNER:
                        String url = methodCall.argument("url");
                        String routeType = methodCall.argument("routeType");
                        String loginStatus = methodCall.argument("loginStatus");
                        iFlutterChannerListener.toWebUrl(url, routeType, loginStatus);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_ORTHERRWEB:
                        iFlutterChannerListener.toOtherWebUrl((String) methodCall.arguments);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_MNKSLISTPAGE:
//                        String results = (String) methodCall.arguments;
                        String isDo = methodCall.argument("idDo");
                        String isDialog = methodCall.argument("isDialog");
                        String naType = methodCall.argument("type");
                        LogUtils.e("看看接受的----->>>>"+isDialog+"*****"+isDo+"*****"+naType);
                        if(checkParams(isDo,iFlutterChannerListener)&&checkParams(isDialog,iFlutterChannerListener)){
                            iFlutterChannerListener.goToAutoExam(isDo.equals("1")?true:false,isDialog.equals("1")?true:false,naType);
                        }
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_SHAPER:
                        String states = methodCall.argument("state");
                        String wxurls = methodCall.argument("url");
                        iFlutterChannerListener.shareNavs(wxurls, states);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_OPENWX:
                        String state = methodCall.argument("state");
                        String wxurl = methodCall.argument("url");
                        String title = methodCall.argument("title");
                        iFlutterChannerListener.openKfUrl(wxurl, state, title);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_HOME_START:
                        String type = (String) methodCall.arguments;
                        iFlutterChannerListener.homeStartCount(type);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_MORE:
                        iFlutterChannerListener.goToMore(false);
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_GOPLAYVIEWING:
                        iFlutterChannerListener.goToBlackPlay();
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_ORDERLIVE:
                    case HomeFlutterChannerTpis.HOME_FLUTTER_CANCELORDERLIVE:
                        String ids = (String) methodCall.arguments;
                        if(checkParams(ids,iFlutterChannerListener)){
                            iFlutterChannerListener.goToLiving(ids,result);
                        }
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_PLAYVIDEO:
                        String liveId = (String) methodCall.arguments;
                        if(StringUtils.isEmpty(liveId)){
                            iFlutterChannerListener.goToVideo("3",liveId,false);
                        }else {
                            iFlutterChannerListener.goToVideo("3",liveId,true);
                        }
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_LIVEVIDEO:
                        String liveUrl = (String) methodCall.arguments;
                        if(checkParams(liveUrl,iFlutterChannerListener)){
                            iFlutterChannerListener.goToLiveVideo(liveUrl);
                        }
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_EXAMGUIDE:
                        iFlutterChannerListener.goToMessagePage();
                        break;
                    case HomeFlutterChannerTpis.HOME_FLUTTER_GOLIVE:
                        String liveGoUrl = (String) methodCall.arguments;
                        if(checkParams(liveGoUrl,iFlutterChannerListener)){
                            iFlutterChannerListener.toWebUrl(liveGoUrl, "1", "1");
                        }
                        break;
                    case HomeFlutterChannerTpis.LIVE_FLUTTER_GRADLEDATA:
                        iFlutterChannerListener.initGradleData(result,(String) methodCall.arguments);
                        break;
                    case HomeFlutterChannerTpis.LIVE_FLUTTER_SELECTADDRESS:
                        String jsonStr = (String) methodCall.arguments;
                        if(checkParams(jsonStr,iFlutterChannerListener)){
                            iFlutterChannerListener.choiceAddress(jsonStr);
                        }
                        break;
                    case HomeFlutterChannerTpis.LIVE_FLUTTER_GETADDRESSINFO:
                        iFlutterChannerListener.getLocalAddress(result);
                        break;
                    case HomeFlutterChannerTpis.LIVE_FLUTTER_DEFLULTDATAS:
                        iFlutterChannerListener.getDafalutDatas(result);
                        break;
                    case HomeFlutterChannerTpis.LIVE_FLUTTER_RESULTCERTIFICATE:
                        String addressStr = (String) methodCall.arguments;
                        if(checkParams(addressStr,iFlutterChannerListener)){
                            iFlutterChannerListener.resultCertificate(addressStr);
                        }
                        break;
                    case HomeFlutterChannerTpis.LIVE_FLUTTER_GETLIVEPRE:
                        iFlutterChannerListener.getLivePreData(result);
                        break;
                    case HomeFlutterChannerTpis.LIVE_FLUTTER_MNKS:
                        Map<String,String> map = new HashMap<>();
                        map.put("paperType", (String) methodCall.arguments);
                        map.put("paperValue","");
                        iFlutterChannerListener.goToMnks(true,JsonUtils.beanToJson(map));
                        break;
                    case HomeFlutterChannerTpis.LIVE_FLUTTER_WEBPAY:
                        HomeConfig.isToastPay=false;
                        iFlutterChannerListener.goToWebPay("mnks_web",ConfigHelper.getPayUrl(),1,PayRouthConfig.PAY_MNKS_UPDATE_PAY);
                        break;
                    case HomeFlutterChannerTpis.SAVE_FLUTTER_VIPSTATE:
                        iFlutterChannerListener.initSaveOrErrorData(result);
                        break;
                    case HomeFlutterChannerTpis.SAVE_FLUTTER_ALLERROR:
                       String allerror= (String) methodCall.arguments;
                       if(checkParams(allerror,iFlutterChannerListener)){
                           iFlutterChannerListener.goToAllPage(allerror);
                       }
                        break;
                    case HomeFlutterChannerTpis.SAVE_FLUTTER_TODAYERROR:
                        String toDay= (String) methodCall.arguments;
                        if(checkParams(toDay,iFlutterChannerListener)){
                            iFlutterChannerListener.goToDayPage(toDay);
                        }
                        break;
                    case HomeFlutterChannerTpis.SAVE_FLUTTER_SWITCHREMOVE:
                        String switchJson= (String) methodCall.arguments;
                        if(checkParams(switchJson,iFlutterChannerListener)){
                            iFlutterChannerListener.switchRemoveState(switchJson);
                        }
                        break;
                    case  HomeFlutterChannerTpis.SAVE_FLUTTER_GOBUYVIP:
                        iFlutterChannerListener.gotoPay();
                        break;
                    case HomeFlutterChannerTpis.SAVE_FLUTTER_GOCHAPTEREXAM:
                        String chapterNums=methodCall.argument("num");
                        String chapterIds=methodCall.argument("ids");
                        if(checkParams(chapterNums,iFlutterChannerListener)&&checkParams(chapterIds,iFlutterChannerListener)){
                            iFlutterChannerListener.gotoChapterExam(chapterNums,chapterIds);
                        }
                        break;
                    case HomeFlutterChannerTpis.SAVE_FLUTTER_GOEXAM:
                        String examNum=methodCall.argument("num");
                        String examType=methodCall.argument("type");
                        String examIds= methodCall.argument("ids");
                        if(checkParams(examNum,iFlutterChannerListener)&&checkParams(examType,iFlutterChannerListener)&&checkParams(examIds,iFlutterChannerListener)){
                            iFlutterChannerListener.gotoQuestionExam(examNum,examType,examIds);
                        }
                        break;
                    case HomeFlutterChannerTpis.SAVE_FLUTTER_CLEAREXAM:
                        String clearIds=(String) methodCall.arguments;
                        if(checkParams(clearIds,iFlutterChannerListener)){
                            iFlutterChannerListener.cleanExam(clearIds);
                        }
                        break;
                    case HomeFlutterChannerTpis.MESSAGE_FLUTTER_LIST:
                        iFlutterChannerListener.initMessageListData(result);
                        break;
                    case HomeFlutterChannerTpis.MESSAGE_FLUTTER_APPOINTMENT:
                        String messageIds=(String) methodCall.arguments;
                        if(checkParams(messageIds,iFlutterChannerListener)){
                            Map maps= GsonUtils.gsonToMaps(messageIds);
                            iFlutterChannerListener.appointment(maps.get("ids").toString(), maps.get("indexs").toString());
                        }
                        break;
                    case HomeFlutterChannerTpis.MESSAGE_FLUTTER_TESTMESSAGE:
                        String testUrl = (String) methodCall.arguments;
                        if(checkParams(testUrl,iFlutterChannerListener)){
                            iFlutterChannerListener.toWebUrl(testUrl, "2", "2");
                        }
                        break;
                    case HomeFlutterChannerTpis.ORDER_FLUTTER_ORDERLIST:
                        iFlutterChannerListener.initOrderListData(result);
                        break;
                    case HomeFlutterChannerTpis.EXAMTIME_FLUTTER_RILI:
                        iFlutterChannerListener.initExamTimeInfo(result,null);
                        break;
                    case HomeFlutterChannerTpis.EXAMTIME_FLUTTER_BUY:
                        String point = (String) methodCall.arguments;
                        iFlutterChannerListener.goToBuyWebByPoint(point==null||point.isEmpty()?"":point,true);
                        break;
                    case HomeFlutterChannerTpis.EXAMTIME_FLUTTER_SETEXAMTIME:
                        iFlutterChannerListener.setExamTime();
                        break;
                    case HomeFlutterChannerTpis.RESULT_FLUTTER_GOTOASCEND:
                        iFlutterChannerListener.goToAscend();
                        break;
                    case HomeFlutterChannerTpis.RESULT_FLUTTER_GIVEUP:
                        iFlutterChannerListener.goToMnksByHpttp(true,(String) methodCall.arguments);
                        break;
                    case HomeFlutterChannerTpis.RESULT_FLUTTER_CONTINUEEXAM:
                        iFlutterChannerListener.goToMnksByHpttp(false, (String) methodCall.arguments);
                        break;
                    case HomeFlutterChannerTpis.RESULT_TEST:
                        iFlutterChannerListener.test();
                        break;
                    case HomeFlutterChannerTpis.COUPION_FLUTTER_LIST:
                        iFlutterChannerListener.getCoupionListData(result);
                        break;
                    case HomeFlutterChannerTpis.COUPION_FLUTTER_PASSVIPBYINDEX:
                        iFlutterChannerListener.passVipByRouth(PayRouthConfig.PAY_COUPON_LIST_PAY,false);
                        break;
                    case HomeFlutterChannerTpis.RESULT_FLUTTER_LEARING:
                        String pass = (String) methodCall.arguments;
                        iFlutterChannerListener.openLearning(pass);
                        break;
                    case HomeFlutterChannerTpis.GUILDE_FLUTTER_GOTOEJ:
                        iFlutterChannerListener.goToEj();
                        break;
                    case HomeFlutterChannerTpis.HOME_BINDSUNJECT_FLUTTER:
                        iFlutterChannerListener.goToBindSubject();
                        break;
                    case HomeFlutterChannerTpis.HOME_LNZT_FLUTTER:
                        iFlutterChannerListener.goToLnzt();
                        break;
                    case HomeFlutterChannerTpis.HOME_EJMNKS_FLUTTER:
                        iFlutterChannerListener.goToEjMnks();
                        break;
                    case HomeFlutterChannerTpis.HOME_EJADDRESS_FLUTTER:
                        iFlutterChannerListener.goToEjAddress();
                        break;
                    case HomeFlutterChannerTpis.HOME_EJSUBJECT_FLUTTER:
                        String addressStr1 = (String) methodCall.arguments;
                        iFlutterChannerListener.goToEjSubject(addressStr1);
                        break;
                    case HomeFlutterChannerTpis.HOME_SUBJECTACTIVE_FLUTTER:
                        String states1 = (String) methodCall.arguments;
                        iFlutterChannerListener.userClickActiveSubjectClose(result,states1);
//                        iFlutterChannerListener.getHttpsData(result,false);
                        break;
                    case HomeFlutterChannerTpis.HOME_TOEXAMRESULT_FLUTTER:
                        String paramsId=(String) methodCall.arguments;
                        if(StringUtils.isEmpty(paramsId))return;
                        Map maps= GsonUtils.gsonToMaps(paramsId);
                        iFlutterChannerListener.toExamResult(maps.get("paperType").toString(),maps.get("paperValues").toString());
                        break;
                    case HomeFlutterChannerTpis.HOME_OFFONLINETEL_FLUTTER:
                        String Titl = methodCall.argument("title");
                        String Sti = methodCall.argument("subject");
                        String Urls = methodCall.argument("url");
                        iFlutterChannerListener.openOffOnlieServiceTips(Titl,Sti,Urls);
                        break;
                    case HomeFlutterChannerTpis.HOME_MOREPASSCOURSEINFO_FLUTTER:
                        String index = methodCall.hasArgument("index") ? methodCall.argument("index") : null;
                        String useId = methodCall.hasArgument("useId") ? methodCall.argument("useId") : null;
                        iFlutterChannerListener.openCourseDetail(index,useId);
                        break;
                    case HomeFlutterChannerTpis.HOME_NAV_FUNCTION_JUMP:
                        iFlutterChannerListener.onMainNavFunctionClick(JsonUtils.gsonToBean((String) methodCall.arguments, MenuNavsBean.class));
                        break;
                    case HomeFlutterChannerTpis.SET_MAJOR_PARENT_ID:
                        iFlutterChannerListener.setMajorParentId((String) methodCall.arguments);
                        break;
                }
            }
        });
    }

    @Override
    public void nativeToFlutterMouth(String str, String json,FlutterEngine flutterEngines) {
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                MethodChannel flutterChannel= new MethodChannel(flutterEngines.getDartExecutor().getBinaryMessenger(), ChannelConfig.CHANNEL_FLUTTER);
                flutterChannel.invokeMethod(str, json==null?null:json);
            }
        });
    }

    @Override
    public boolean checkParams(String params,IFlutterChannerListener iFlutterChannerListener) {
        if(params==null||params.isEmpty()){
            iFlutterChannerListener.toast("参数不合法!");
            return false;
        }
        return true;
    }
}
