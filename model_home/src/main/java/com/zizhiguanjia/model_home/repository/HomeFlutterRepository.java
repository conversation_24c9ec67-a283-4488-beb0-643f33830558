package com.zizhiguanjia.model_home.repository;

import android.app.Activity;

import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.bean.OrderDialogBean;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CarseHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.msgconfig.UserType;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.config.RouteConfig;
import com.zizhiguanjia.model_home.listener.IHome;
import com.zizhiguanjia.model_home.listener.IHomeRepositortListener;
import com.zizhiguanjia.model_home.utils.HomeUtils;

import java.lang.ref.WeakReference;

import io.flutter.plugin.common.MethodChannel;

public class HomeFlutterRepository implements IHome {
    private WeakReference<IHomeRepositortListener> iHomeRepositortListenerWeakReference;
    private MethodChannel methodChannel;
    
    public MethodChannel getMethodChannel() {
        return methodChannel;
    }
    
    public void setMethodChannel(MethodChannel methodChannel) {
        this.methodChannel = methodChannel;
    }

    @Override
    public void init(IHomeRepositortListener iHomeRepositortListener) {
        if (iHomeRepositortListener != null) {
            iHomeRepositortListenerWeakReference = new WeakReference<>(iHomeRepositortListener);
        }
    }

    @Override
    public IHomeRepositortListener getListener() {
        return iHomeRepositortListenerWeakReference == null ? null : iHomeRepositortListenerWeakReference.get();
    }

    @Override
    public void successNetWorkDatas(BaseData<HomeBean> data, MethodChannel.Result result, boolean passCertificate) {
        BaseData<HomeBean> newData = onAgainUpdateTxt(data);
        try {
            BaseConfig.IsExamine = newData.Data.getIsExamine();
        } catch (Exception e) {
            BaseConfig.IsExamine = 0;
        }
        if (data.getResult().getMiddleAds().getCloseStatus() == 1) {
            //不存在
            data.getResult().setSubjectActive(true);
        } else {
            //存在
            data.getResult().setSubjectActive(false);
        }
        String json = GsonUtils.gsonString(newData);
        try {
            String des = data.Data.getDefaultNav();
            //            String[] codeStrs = HomeUtils.getInstance().convertStrToArray2(des);
            getListener().onHomeTips(des, data.Data.getPaperType() == 3 ? false : true, passCertificate);
            KvUtils.save("currentName", data.Data.getDefaultNav());
            if(data.Data.getMajorPid() != null) {
                BaseConfig.MAJOR_PID = data.Data.getMajorPid();
                KvUtils.save("mjPid", BaseConfig.MAJOR_PID + "");
            }
            CertificateHelper.updateCertificate(String.valueOf(data.Data.getAreaId()), String.valueOf(data.Data.getAreaName()),
                    String.valueOf(data.Data.getMajorId()), data.Data.getMajorName(), 5, data.Data.getDefaultNav());
            if (passCertificate) {
                LogUtils.e("开始更新111" + des);
                //                if(des.contains("二建")){
                //                    CertificateHelper.updateCertificate(String.valueOf(data.Data.getAreaId()), StringUtils.isEmpty(data.Data.getAreaName())?"":data.Data.getAreaName(), String.valueOf(data.Data.getMajorId()), codeStrs[1],5);
                //                }else {
                //                    CertificateHelper.updateCertificate(String.valueOf(data.Data.getAreaId()), codeStrs[0], String.valueOf(data.Data.getMajorId()), codeStrs[1],5);
                //                }

            } else {
                LogUtils.e("开始更新222");
                if (AccountHelper.isUserLogin()) {
                    UserHelper.updataUserVipState(data.Data.isVip() ? UserType.USER_TYPE_VIP : UserType.USER_TYPE_COMMON);
                }
            }


            if (getListener() != null) {
                getListener().onHomeFlutterToJson(result, "jsonMain", json);
            } else {
                LogUtils.e("看看证书情况----->>>yes`");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (data.Data.getExamTip() == null || data.Data == null) {
            return;
        }
        OrderDialogBean orderDialogBean = new OrderDialogBean();
        orderDialogBean.setIndex(2);
        orderDialogBean.setTitle(data.Data.getExamTip().getTitle());
        orderDialogBean.setSubTitle(data.Data.getExamTip().getSubTitle());
        orderDialogBean.setContentTitle(data.Data.getExamTip().getContentTitle());
        orderDialogBean.setContent(data.Data.getExamTip().getContent());
        orderDialogBean.setSubContent(data.Data.getExamTip().getSubContent());
        orderDialogBean.setIds(data.Data.getExamTip().getId());
        MessageHelper.orderOpenDialog(orderDialogBean);
    }

    @Override
    public BaseData<HomeBean> onAgainUpdateTxt(BaseData<HomeBean> data) {
        return HomeUtils.getInstance().resetHomeDatas(data);
    }

    @Override
    public void checkPremiss(Activity activity) {
        CarseHelper.uploadCarse();
        //
        //        RxJavaUtils.delay(1, new Consumer<Long>() {
        //            @Override
        //            public void accept(Long aLong) throws Exception {
        //                MainThreadUtils.post(new Runnable() {
        //                    @Override
        //                    public void run() {
        //                        boolean needDialog=KvUtils.get("homePermiss",false);
        //                        LogUtils.e("看下needDialog"+needDialog);
        //                        if(needDialog){
        //                            CarseHelper.uploadCarse();
        //                        }else {
        //                            MessageHelper.openGeneralCentDialog(activity, "为了选择图片收集用户反馈，需要访问您设备上的照片", "",
        //                                    "取消", "确定", false, true, new GeneralDialogListener() {
        //                                        @Override
        //                                        public void onCancel() {
        //                                            KvUtils.save("homePermiss",true);
        //                                        }
        //                                        @Override
        //                                        public void onConfim() {
        //                                            KvUtils.save("homePermiss",true);
        //                                            permissionsPrompt(activity);
        //                                        }
        //                                    });
        //                        }
        //                    }
        //                });
        //            }
        //        });
        //
    }

    //    private void  permissionsPrompt(Activity activity){
    //        PermissionUtils
    //                .with(activity)
    //                .addPermissions(Manifest.permission.WRITE_EXTERNAL_STORAGE)
    //                .addPermissions(Manifest.permission.READ_EXTERNAL_STORAGE)
    //                .addPermissions(Manifest.permission.READ_PHONE_STATE)
    //                .setPermissionsCheckListener(new PermissionListener() {
    //                    @Override
    //                    public void permissionRequestSuccess() {
    ////                        initHumSdk(flutterMainFragment.getActivity());
    //                        KvUtils.save("homePermiss",true);
    //                        CarseHelper.uploadCarse();
    //                    }
    //                    @Override
    //                    public void permissionRequestFail(String[] grantedPermissions, String[] deniedPermissions, String[] forceDeniedPermissions) {
    //
    //                    }
    //                }).createConfig()
    //                .setForceAllPermissionsGranted(false)
    //                .setForceDeniedPermissionTips("请前往设置->应用->【" + PermissionUtils.getAppName(activity) + "】->权限中打开相关权限，否则功能无法正常运行！")
    //                .buildConfig()
    //                .startCheckPermission();
    //    }
    @Override
    public boolean checkLoginOrAddress(boolean address, int OrderBindType) {
        return checkLoginOrAddress(address, OrderBindType, true);
    }

    /**
     * @param address
     * @param OrderBindType
     * @param oneKeyLoginCheckDoubleClick 一键登录是否验证二次点击，防止其他地方的点击操作加了二次验证导致这里无法触发弹窗
     * @return
     */
    @Override
    public boolean checkLoginOrAddress(boolean address, int OrderBindType, boolean oneKeyLoginCheckDoubleClick) {
        if (AccountHelper.loginSuperintendent(oneKeyLoginCheckDoubleClick)) {
            //登录校监完成
            if (address) {
                if (CertificateHelper.userChoiceCertificate()) {
                    if (OrderBindType == 0 || OrderBindType == 4) {
                        return true;
                    } else {
                        getListener().goToBindSubject();
                        return false;
                    }
                } else {
                    getListener().goToAddess();
                    return false;
                }
            } else {
                return true;
            }

        }
        return false;
    }

    @Override
    public void routingHop(String route, int paperType, MethodChannel.Result result, boolean passCertificate) {
        LogUtils.e("-----》》》luyo" + route);
        if (route.equals(RouteConfig.ROUTE_HOME)) {
            getListener().getIndexDatas(result, passCertificate);
        } else if (route.contains(RouteConfig.ROUTE_LIVEPRE)) {
            getListener().reshLivePreHttpsDatas();
        } else if (route.contains(RouteConfig.ROUTE_ERROR)) {
            getListener().reshSaveOrErrorHttpsDatas();
        } else if (route.contains(RouteConfig.ROUTE_SAVE)) {
            getListener().reshSaveOrErrorHttpsDatas();
        } else if (route.contains(RouteConfig.ROUTE_RESULTS)) {
            getListener().reshResultHttpsDatas(paperType);
        } else if (route.contains(RouteConfig.ROUTE_AVD)) {
            getListener().getBackVideoListData(result);
        } else if (route.contains(RouteConfig.ROUTE_TESTDATE)) {
            getListener().initExamTimeInfo(null, "reshExamInfo");
        } else {
        }
    }

    @Override
    public void setTopTitleInfo(String route) {
        if (route.contains(RouteConfig.ROUTE_HOME)) {
            getListener().setTopTitle(false, null);
        } else if (route.contains(RouteConfig.ROUTE_ERROR)) {
            getListener().setTopTitle(true, "错题集");
        } else if (route.contains(RouteConfig.ROUTE_SAVE)) {
            getListener().setTopTitle(true, "收藏夹");
        } else if (route.contains(RouteConfig.ROUTE_PLAYBLACK)) {
            getListener().setTopTitle(false, null);
        } else if (route.contains(RouteConfig.ROUTE_LIVEPRE)) {
            getListener().setTopTitle(false, null);
        } else if (route.contains(RouteConfig.ROUTE_TESTDATE)) {
            getListener().setTopTitle(false, null);
            getListener().openTimeSelect();
        } else if (route.contains(RouteConfig.ROUTE_ORDER)) {
            getListener().setTopTitle(true, "订单与优惠券");
        } else if (route.contains(RouteConfig.ROUTE_ORDER)) {
            getListener().setTopTitle(true, "消息");
        } else if (route.contains(RouteConfig.ROUTE_MESSAGE)) {
            String json = CertificateHelper.getCertificateDes();
            String[] address = HomeUtils.getInstance().convertStrToArray2(json);
            String addressDes = null;
            if (address == null || address.length == 0) {
                addressDes = "通知消息";
            } else {
                addressDes = address[0] + "最新考试通知";
            }
            getListener().setTopTitle(true, addressDes);
        } else if (route.contains(RouteConfig.ROUTE_RESULTS)) {
            getListener().setTopTitle(false, "通知消息");
        } else if (route.contains(RouteConfig.ROUTE_INDEXGUIDE)) {
            getListener().setTopTitle(false, "");
        } else if (route.contains(RouteConfig.ROUTE_INDEXGUIDE)) {
            getListener().setTopTitle(false, "");
        } else {
            getListener().setTopTitle(false, "");
        }
    }

    @Override
    public void initGuide() {
        boolean hasShownGuide = KvUtils.get("main_guilder", false);
        LogUtils.e("HomeFlutterRepository - initGuide - 是否已显示过引导页: " + hasShownGuide);
        
        if (!hasShownGuide) {
            LogUtils.e("HomeFlutterRepository - initGuide - 显示引导页");
            getListener().showGuideView();
        } else {
            LogUtils.e("HomeFlutterRepository - initGuide - 已显示过引导页，不再显示");
        }
    }

    public void successMethodChannel(MethodChannel methodChannel) {
        this.methodChannel = methodChannel;
        LogUtils.e("HomeFlutterRepository - successMethodChannel - 设置MethodChannel");
    }
}
