package com.zizhiguanjia.model_home.repository;

import static com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig.PAY_MSG_FAIL;

import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.AddressMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.CommonMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.SdkMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.UserMsgTypeConfig;
import com.zizhiguanjia.model_home.config.HomeConfig;
import com.zizhiguanjia.model_home.listener.IMsg;
import com.zizhiguanjia.model_home.listener.IMsgChanner;

public  class MsgRepository implements IMsgChanner {
    @Override
    public void busMsgChanner(int code, String msg,IMsg iMsg) {
        if(code== SdkMsgTypeConfig.SDK_MSG_RESHLOGIN){
            iMsg.verificationCodeLogin();
        }else if(code== AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS){
            iMsg.accountLoginSuccess();
        }else if(code== UserMsgTypeConfig.USER_MSG_EXITACCOUNT){
            iMsg.accountOut();
        }else if(code == AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT){
            LogUtils.e("账号提示过期-----");
            iMsg.accountAutoOffline();
        }else if(code== PayMsgTypeConfig.PAY_MSG_RESHHOME){
            iMsg.successPayInfo();
        }else if(code== CommonMsgTypeConfig.COMMON_LIVE_PRE_SUCCESS){
            iMsg.liveSuccess();
        }else if(code==AccountMsgTypeConfig.MSG_COUPON_TS){
            iMsg.checkCouponid();
        }else if(code== AddressMsgTypeConfig.ADDRESS_USER_SELECT_ADDRESS_BY){
            if(msg==null){
                iMsg.finshActivity();
            }else {
                iMsg.nativeToFlutterMouth("userSelectRegion",msg);
            }
        }else if(code==CommonMsgTypeConfig.COMMON_LIVE_PRE_SUCCESS){
            iMsg.nativeToFlutterMouth("livePreSuccess",null);
        }else if(code==PayMsgTypeConfig.PAY_MSG_SUCCESS){
            LogUtils.e("看看支付成功了22222------->>>>>"+HomeConfig.isToastPay);
            iMsg.paySuccess();
        }else if(code==5123){
            LogUtils.e("开始关闭------>>>>2");
            iMsg.checkPageClose();
        }else  if(code==10106){
            LogUtils.e("消息到了---->>>>"+msg);
            iMsg.checkPageRouthFinash();
        }else if(code==0x98885555){
            iMsg.checkPageClose();
            LogUtils.e("账号提示过期1-----"+msg);
            if(msg.equals("1")){

                iMsg.accountAutoOffline();
            }
        }else if(code==PAY_MSG_FAIL){
            iMsg.payFilaTs();
        }
    }
}
