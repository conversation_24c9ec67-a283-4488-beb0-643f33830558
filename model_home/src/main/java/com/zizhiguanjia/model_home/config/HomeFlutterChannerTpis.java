package com.zizhiguanjia.model_home.config;

public interface HomeFlutterChannerTpis {
    String HOME_FLUTTER_PAGEFINSH="pageFinsh";
    String HOME_FLUTTER_TOAST="toast";
    String HOME_FLUTTER_ERRORLISTPAGE="toErrorListPage";
    String HOME_FLUTTER_SAVELISTPAGE="toSaveListPage";
    String HOME_FLUTTER_TXLXLISTPAGE="toTxlxListPage";
    String HOME_FLUTTER_MNKSLISTPAGE="toMnksListPage";
    String HOME_FLUTTER_NOMREMISS_VIP="nopremiss";
    String HOME_FLUTTER_GOPAYPAGE="payToPage";
    String HOME_FLUTTER_CHAPPTERLISTPAGE="toChapterListPage";
    String HOME_FLUTTER_CALLTEL="onTel";
    String HOME_FLUTTER_GOVIDEO="video";
    String HOME_FLUTTER_PAYSUCCESS="paySuccess";
    String HOME_FLUTTER_CLOSEVIEW="closeView";
    String HOME_FLUTTER_BANNER="toWebUrlByBanner";
    String HOME_FLUTTER_ORTHERRWEB="toOtherWebUrlByBanner";
    String HOME_FLUTTER_SHAPER="wechatShare";
    String HOME_FLUTTER_OPENWX="wxCall";
    String HOME_FLUTTER_HOME_START="home_start";
    String HOME_FLUTTER_MORE="homeMore";
    String HOME_FLUTTER_GOPLAYVIEWING="playVideoing";
    String HOME_FLUTTER_ORDERLIVE="orderLive";
    String HOME_FLUTTER_PLAYVIDEO="playVideo";
    String HOME_FLUTTER_LIVEVIDEO="liveVideo";
    String HOME_FLUTTER_CANCELORDERLIVE="cancelOrderLive";
    String HOME_FLUTTER_GOLIVE="onToLiveGo";
    String HOME_FLUTTER_EXAMGUIDE="onExamGuide";
    String LIVE_FLUTTER_GRADLEDATA="initGraleData";
    String LIVE_FLUTTER_SELECTADDRESS="userSelectAddress";
    String LIVE_FLUTTER_GETADDRESSINFO="getAddressInfo";
    String LIVE_FLUTTER_DEFLULTDATAS="defultDatas";
    String LIVE_FLUTTER_RESULTCERTIFICATE="resultCertificate";
    String LIVE_FLUTTER_GETLIVEPRE="getLivePreData";
    String LIVE_FLUTTER_FINSH="finsh";
    String LIVE_FLUTTER_MNKS="toMnks";
    String LIVE_FLUTTER_WEBPAY="web_pay";
    String SAVE_FLUTTER_VIPSTATE="vipState";
    String SAVE_FLUTTER_ALLERROR="allError";
    String SAVE_FLUTTER_TODAYERROR="todayError";
    String SAVE_FLUTTER_SWITCHREMOVE="switchRemoveExam";
    String SAVE_FLUTTER_GOBUYVIP="goBuyVip";
    String SAVE_FLUTTER_GOCHAPTEREXAM="goChapterExam";
    String SAVE_FLUTTER_GOEXAM="goExam";
    String SAVE_FLUTTER_CLEAREXAM="clearExam";
    String MESSAGE_FLUTTER_LIST="getMessageJson";
    String MESSAGE_FLUTTER_TESTMESSAGE="gotoTestMessage";
    String MESSAGE_FLUTTER_APPOINTMENT="appointment";
    String ORDER_FLUTTER_ORDERLIST="getOrderJson";
    String EXAMTIME_FLUTTER_RILI="getExamInfo";
    String EXAMTIME_FLUTTER_BUY="goToWebBuy";
    String EXAMTIME_FLUTTER_SETEXAMTIME="setExamTime";
    String RESULT_FLUTTER_GOTOASCEND="goToAscend";
    String RESULT_FLUTTER_GIVEUP="giveUp";
    String RESULT_FLUTTER_LEARING="openLearing";
    String RESULT_FLUTTER_CONTINUEEXAM="continueExam";
    String RESULT_TEST="test";
    String COUPION_FLUTTER_LIST="getConpuinJson";
    String COUPION_FLUTTER_PASSVIPBYINDEX="passVipByIndex";
    String GUILDE_FLUTTER_GOTOEJ="goToEj";
    String HOME_BINDSUNJECT_FLUTTER="goToBinSubject";
    String HOME_LNZT_FLUTTER="onToLnzt";
    String HOME_EJMNKS_FLUTTER="onToEjMnks";
    String HOME_EJADDRESS_FLUTTER="goToEjAddress";
    String HOME_EJSUBJECT_FLUTTER="goToEjSubject";
    String HOME_SUBJECTACTIVE_FLUTTER="userClickActiveCLose";
    String HOME_TOEXAMRESULT_FLUTTER="toExamResult";
    String HOME_OFFONLINETEL_FLUTTER="offOnileTel";
    /**
     * 课程详情页面
     */
    String HOME_MOREPASSCOURSEINFO_FLUTTER="morePassCourseInfo";
    /**
     * 首页金刚区功能跳转
     */
    String HOME_NAV_FUNCTION_JUMP="navFunctionJump";
    /**
     * 设置当前的majorPId
     */
    String SET_MAJOR_PARENT_ID="setMajorParentId";
}
