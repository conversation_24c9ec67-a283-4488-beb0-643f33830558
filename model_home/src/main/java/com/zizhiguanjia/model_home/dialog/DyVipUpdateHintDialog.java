package com.zizhiguanjia.model_home.dialog;

import android.annotation.SuppressLint;
import android.content.DialogInterface;
import android.graphics.Paint;
import android.os.Bundle;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.base.BaseDialogFragment;
import com.zizhiguanjia.lib_base.msgconfig.CertificateMsgTypeConfig;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;
import com.zizhiguanjia.model_home.viewmodel.DyVipUpdateHintViewModel;
import com.zizhiguanjia.model_message.bean.CityPickerData;
import com.zizhiguanjia.model_message.bean.ParentSubjectsBean;
import com.zizhiguanjia.model_message.manager.MessageManager;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.fragment.app.FragmentManager;

/**
 * 功能作用：搜夜vip升级提醒弹窗
 * 初始注释时间： 2023/11/26 14:23
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class DyVipUpdateHintDialog extends BaseDialogFragment {
    /**
     * 控件部分
     */
    private AppCompatImageButton mIvBack;
    private AppCompatTextView mTvTitle;
    private AppCompatTextView mTvDes;
    private AppCompatButton mBtnChange;
    private LinearLayoutCompat mLnType;
    private AppCompatTextView mTvTypeDes;
    private LinearLayoutCompat mLnLocation;
    private AppCompatTextView mTvLocationDes;
    private LinearLayoutCompat mLnLevel;
    private AppCompatTextView mTvLevelDes;
    private AppCompatButton mBtnOpen;
    /**
     * 加载中
     */
    private BasePopupView mLoadingView;

    /**
     * 数据处理
     */
    private final DyVipUpdateHintViewModel mViewModel = new DyVipUpdateHintViewModel();

    private DialogInterface.OnDismissListener mDismissListener;

    public void setDismissListener(DialogInterface.OnDismissListener onDismissListener) {
        this.mDismissListener = onDismissListener;
    }

    @Override
    protected int setUpLayoutId() {
        return R.layout.dialog_dy_vip_update_hint;
    }

    @SuppressLint("SetTextI18n")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View view = super.onCreateView(inflater, container, savedInstanceState);
        mIvBack = view.findViewById(R.id.iv_back);
        mTvTitle = view.findViewById(R.id.tv_title);
        mTvDes = view.findViewById(R.id.tv_des);
        mBtnChange = view.findViewById(R.id.btn_change);
        mLnType = view.findViewById(R.id.ln_type);
        mTvTypeDes = view.findViewById(R.id.tv_type_des);
        mLnLocation = view.findViewById(R.id.ln_location);
        mTvLocationDes = view.findViewById(R.id.tv_location_des);
        mLnLevel = view.findViewById(R.id.ln_level);
        mTvLevelDes = view.findViewById(R.id.tv_level_des);
        mBtnOpen = view.findViewById(R.id.btn_open);
        mBtnChange.setPaintFlags(mBtnChange.getPaintFlags() | Paint.UNDERLINE_TEXT_FLAG);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //加载中
        mLoadingView = new PopupManager.Builder(getContext()).asLoading("", R.layout.popup_center_impl_loading);
        setGravity(Gravity.BOTTOM);
        setDialogSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        mIvBack.setOnClickListener(v -> closeChangeView());
        mBtnChange.setOnClickListener(v -> openChangeView());
        closeChangeView();
        getDialog().setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK || keyCode == KeyEvent.KEYCODE_ESCAPE) {
                    if (mIvBack.getVisibility() == View.VISIBLE) {
                        event.startTracking();
                        closeChangeView();
                        return true;
                    }
                }
                return false;
            }
        });
        mLnType.setOnClickListener(v -> {
            List<ParentSubjectsBean.Item> list = mViewModel.getSubjectList().getValue();
            if (list != null) {
                MessageManager.getInstance().openSelectSubjectOrCity(getActivity(), list, (name, ids, type) -> {
                    for (ParentSubjectsBean.Item item : list) {
                        if (item.getId().equals(ids)) {
                            mViewModel.setSelectSubject(item);
                        }
                    }
                }, 3);
            }
        });
        mLnLocation.setOnClickListener(v -> {
            List<CityPickerData> list = mViewModel.getAreaList().getValue();
            if (list != null) {
                MessageManager.getInstance().openSelectSubjectOrCity(getActivity(), list, (name, ids, type) -> {
                    for (CityPickerData item : list) {
                        if (item.getId().equals(ids)) {
                            mViewModel.setSelectLocation(item);
                        }
                    }
                }, 1);
            }
        });
        mLnLevel.setOnClickListener(v -> {
            List<CityPickerData> list = mViewModel.getLevelList().getValue();
            if (list != null) {
                MessageManager.getInstance().openSelectSubjectOrCity(getActivity(), list, (name, ids, type) -> {
                    for (CityPickerData item : list) {
                        if (item.getId().equals(ids)) {
                            mViewModel.setSelectLevel(item);
                        }
                    }
                }, 2);
            }
        });
        mBtnOpen.setOnClickListener(v -> {
            if (mLoadingView.isShow()) {
                mLoadingView.dismiss();
            }
            mLoadingView.show();
            mViewModel.openVip();
        });
        mViewModel.getSubjectInfo().observe(this, item -> mTvTypeDes.setText(item != null ? item.getName() : ""));
        mViewModel.getAreaInfo().observe(this, item -> mTvLocationDes.setText(item != null ? item.getName() : ""));
        mViewModel.getLevelInfo().observe(this, item -> mTvLevelDes.setText(item != null ? item.getName() : ""));
        mViewModel.getSuccess().observe(this, success -> {
            if (mLoadingView != null) {
                mLoadingView.dismiss();
            }
            if (success) {
                ToastUtils.normal("升级成功～", Gravity.CENTER);
                Bus.post(new MsgEvent(CertificateMsgTypeConfig.CERTIFICATE_MSG_BIND_SUCCESS));
                dismiss();
            }
        });
    }

    /**
     * 开启修改视图
     */
    private void openChangeView() {
        mTvTitle.setText("修改科目与类型");
        mIvBack.setVisibility(View.VISIBLE);
        mLnLevel.setVisibility(View.VISIBLE);
        mLnLocation.setVisibility(View.VISIBLE);
        mLnType.setVisibility(View.VISIBLE);
        mTvDes.setVisibility(View.GONE);
        mBtnChange.setVisibility(View.GONE);
    }

    /**
     * 关闭修改视图
     */
    private void closeChangeView() {
        mTvTitle.setText("升级提醒");
        mIvBack.setVisibility(View.GONE);
        mLnLevel.setVisibility(View.GONE);
        mLnLocation.setVisibility(View.GONE);
        mLnType.setVisibility(View.GONE);
        mTvDes.setVisibility(View.VISIBLE);
        mBtnChange.setVisibility(View.VISIBLE);
        mTvDes.setText(mViewModel.getShowDesInfo());
        mViewModel.clearSelect();
    }

    @Override
    public BaseDialogFragment show(FragmentManager manager) {
        if (mTvTitle != null) {
            closeChangeView();
        }
        return super.show(manager);
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        if (mDismissListener != null) {
            mDismissListener.onDismiss(dialog);
        }
    }

    /**
     * 设置显示数据
     *
     * @param data 数据
     */
    public void setShowData(DyVipUpdateHintBean data) {
        mViewModel.setShowData(data);
    }


}
