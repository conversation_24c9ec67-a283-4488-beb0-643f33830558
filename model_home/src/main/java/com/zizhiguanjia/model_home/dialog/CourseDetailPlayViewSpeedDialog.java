package com.zizhiguanjia.model_home.dialog;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.base.BaseDialogFragment;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.view.CourseDetailPlayView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;

/**
 * 功能作用：课程详情播放控件调节倍速的弹窗
 * 初始注释时间： 2023/11/26 14:23
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class CourseDetailPlayViewSpeedDialog extends BaseDialogFragment {
    /**
     * 列表容器
     */
    private LinearLayoutCompat mLnList;

    /**
     * 速度列表
     */
    private final float[] mSpeeds;

    /**
     * 选择的位置
     */
    private int mCurrentIndex;

    /**
     * 状态改变回调
     */
    private final CourseDetailPlayView.OnStateChangeInterface mOnStateChangeInterface;

    public CourseDetailPlayViewSpeedDialog(float[] speeds, int currentSpeedIndex,
            CourseDetailPlayView.OnStateChangeInterface onStateChangeInterface) {
        this.mSpeeds = speeds;
        mCurrentIndex = currentSpeedIndex;
        mOnStateChangeInterface = onStateChangeInterface;
    }

    @Override
    protected int setUpLayoutId() {
        return R.layout.dialog_course_detail_play_view_speed;
    }

    @SuppressLint("SetTextI18n")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View view = super.onCreateView(inflater, container, savedInstanceState);
        mLnList = view.findViewById(R.id.ln_list);
        for (int j = 0; j < mSpeeds.length; j++) {
            TextView speedItem = (TextView) View.inflate(mLnList.getContext(), R.layout.jz_layout_clarity_item, null);
            speedItem.setPadding(0, DpUtils.dp2px(AppUtils.getApp(), 15), 0, DpUtils.dp2px(AppUtils.getApp(), 15));
            speedItem.setText(mSpeeds[j] + "X");
            speedItem.setTag(j);
            speedItem.setTextColor(Color.parseColor("#121414"));
            speedItem.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15);
            mLnList.addView(speedItem, 0);
            if (j < mSpeeds.length - 1) {
                View lineView = new View(getContext());
                lineView.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, DpUtils.dp2px(AppUtils.getApp(), 1)));
                mLnList.addView(lineView);
            }
            speedItem.setOnClickListener(v -> {
                dismiss();
                if (mOnStateChangeInterface != null) {
                    mOnStateChangeInterface.changeSpeedToValue((Integer) v.getTag(), mSpeeds[(int) v.getTag()]);
                }
            });
            if (j == mCurrentIndex) {
                speedItem.setTextColor(Color.parseColor("#007AFF"));
            }
        }
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setGravity(Gravity.BOTTOM);
        setDialogSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    /**
     * 设置速度列表
     *
     * @param currentSpeedIndex 当前的
     */
    @SuppressLint("SetTextI18n")
    public void setSpeeds(int currentSpeedIndex) {
        this.mCurrentIndex = currentSpeedIndex;
        if (mLnList != null) {
            int childCount = mLnList.getChildCount();
            View view;
            for (int i = 0; i < childCount; i++) {
                view = mLnList.getChildAt(i);
                if (view instanceof TextView) {
                    if ((int) view.getTag() == currentSpeedIndex) {
                        ((TextView) view).setTextColor(Color.parseColor("#007AFF"));
                    } else {
                        ((TextView) view).setTextColor(Color.parseColor("#121414"));
                    }
                }
            }
        }
    }
}
