package com.zizhiguanjia.model_home.listener;

import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;

/**
 * Flutter学习页面Fragment的监听器接口
 */
public interface IFlutterStudyFragment {
    /**
     * 显示加载中对话框
     */
    void showLoading(boolean visition, String msg);
    
    /**
     * 显示提示对话框
     */
    void showTsDialog(int type);
    
    /**
     * 显示引导
     */
    void showGuide();
    
    /**
     * 关闭视图
     */
    void closeView();
    
    /**
     * 跳转到主页
     */
    void toMainPage(String pid);
    
    /**
     * 显示VIP更新提示对话框
     */
    void showDyVipUpdateHintDialog(Object data);
    
    /**
     * 刷新首页数据
     */
    void reshHomeData(boolean passCertificate);
    
    /**
     * 设置顶部标题信息
     */
    void setTopTitleInfo(boolean show, String title);
} 