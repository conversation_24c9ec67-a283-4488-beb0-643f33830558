package com.zizhiguanjia.model_home.listener;

public interface IMsg {
    void verificationCodeLogin();
    void accountLoginSuccess();
    void accountOut();
    void accountAutoOffline();
    void successPayInfo();
    void liveSuccess();
    void checkCouponid();
    void finshPageView();
    void finshActivity();
    void nativeToFlutterMouth(String key,String json);
    void paySuccess();
    void checkPageClose();
    void checkPageRouthFinash();
    void payFilaTs();
}
