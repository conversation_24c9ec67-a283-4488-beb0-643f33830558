package com.zizhiguanjia.model_home.listener;

import android.app.Activity;

import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.lib_base.bean.BaseData;

import io.flutter.plugin.common.MethodChannel;

public interface IHome {
    void init(IHomeRepositortListener iHomeRepositortListener);
    IHomeRepositortListener getListener();
    void successNetWorkDatas(BaseData<HomeBean> data,MethodChannel.Result result,boolean passCertificate);
    BaseData<HomeBean> onAgainUpdateTxt(BaseData<HomeBean> data);
    void checkPremiss(Activity activity);
    boolean checkLoginOrAddress(boolean address,int OrderBindType);
    boolean checkLoginOrAddress(boolean address,int OrderBindType,boolean oneKeyLoginCheckDoubleClick);
    void routingHop(String route,int paperType,MethodChannel.Result result,boolean passCertificate);
    void setTopTitleInfo(String route);
    void initGuide();
}
