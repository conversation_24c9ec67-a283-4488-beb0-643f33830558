package com.zizhiguanjia.model_home.listener;

import android.app.Activity;

import androidx.annotation.NonNull;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public interface IFlutterChanner {
    void initFlutterEngine(String raouth, Activity activity,IFlutter iFlutter,FlutterEngine flutterEngine);
    void initFlutterView(Activity activity );
    void initMethodChannel(FlutterEngine flutterEngine);
    void jumpByTips(@NonNull MethodCall methodCall, @NonNull MethodChannel.Result result,IFlutterChannerListener iFlutterChannerListener);
    void nativeToFlutterMouth(String str,String json,FlutterEngine flutterEngines);
    boolean checkParams(String params,IFlutterChannerListener iFlutterChannerListener);
}
