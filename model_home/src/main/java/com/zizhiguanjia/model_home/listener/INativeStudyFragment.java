package com.zizhiguanjia.model_home.listener;

/**
 * 原生学习Fragment的监听接口
 */
public interface INativeStudyFragment {
    /**
     * 显示加载中对话框
     */
    void showLoading(boolean visition, String msg);
    
    /**
     * 显示提示对话框
     */
    void showTsDialog(int type);
    
    /**
     * 显示引导
     */
    void showGuide();
    
    /**
     * 关闭视图
     */
    void closeView();
    
    /**
     * 跳转到主页
     */
    void toMainPage(String pid);
    
    /**
     * 显示VIP更新提示对话框
     */
    void showDyVipUpdateHintDialog(Object data);
    
    /**
     * 刷新完成回调
     */
    void onRefreshComplete();
    
    /**
     * 点击事件回调
     * @param actionType 点击类型
     * @param data 附加数据
     */
    void onActionClick(int actionType, String data);
    
    /**
     * 数据状态变化回调
     * @param dataType 数据类型
     * @param newValue 新值
     */
    void onDataStatusChanged(int dataType, String newValue);
} 