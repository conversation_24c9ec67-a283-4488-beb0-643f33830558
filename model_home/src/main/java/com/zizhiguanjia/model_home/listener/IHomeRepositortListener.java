package com.zizhiguanjia.model_home.listener;

import io.flutter.plugin.common.MethodChannel;

public interface IHomeRepositortListener {
    void initFlutter(String route);
    void queryCouponInfo();
    void onHomeTips(String des,boolean paperType,boolean passCertificate);
    void onHomeFlutterToJson(MethodChannel.Result result,String key,String json);
    void goToAddess();
    void openVerificationCodeLogin();
    void loginSuccess();
    void accountOffline();
    void userPaySuccessInfo();
    void getIndexDatas(MethodChannel.Result result,boolean passCertificate);
    void setTopTitle(boolean show,String title);
    void checkLocalCoupon();
    void reshSaveOrErrorHttpsDatas();
    void reshLivePreHttpsDatas();
    void reshResultHttpsDatas(int paperType);
    void showGuideView();
    void openTimeSelect();
    void goToBindSubject();
    void getBackVideoListData(MethodChannel.Result result);
    void initExamTimeInfo(MethodChannel.Result result,String key);
}
