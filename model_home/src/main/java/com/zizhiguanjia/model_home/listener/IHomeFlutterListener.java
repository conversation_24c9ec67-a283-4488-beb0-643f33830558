package com.zizhiguanjia.model_home.listener;

import android.app.Activity;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import com.zizhiguanjia.model_home.fragment.HomeFragment;
import com.zizhiguanjia.model_home.navigator.HomeFlutterNavigator;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public interface IHomeFlutterListener {
    void initParams(HomeFlutterNavigator homeFlutterNavigator, HomeFragment mActivity,String route);
    void initData(String route);
    void checkPremiss();
    void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result);
    void onClick(View mView);
    void checkUserPayState();
    void cleanExamData();
    String getReroutType();
    void initSaveOrErrorHttpsDatas();
    void setExamTime(String times);
}
