package com.zizhiguanjia.model_home.listener;

import android.view.View;

import androidx.annotation.NonNull;

import com.zizhiguanjia.model_home.fragment.HomeFragment;
import com.zizhiguanjia.model_home.fragment.NativeStudyAIFragment;
import com.zizhiguanjia.model_home.navigator.HomeFlutterNavigator;

import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public interface IHomeFlutterNativeListener {
    void initParams(HomeFlutterNavigator homeFlutterNavigator, NativeStudyAIFragment mActivity, String route);
    void initData(String route);
    void checkPremiss();
    void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result);
    void onClick(View mView);
    void checkUserPayState();
    void cleanExamData();
    String getReroutType();
    void initSaveOrErrorHttpsDatas();
    void setExamTime(String times);
}
