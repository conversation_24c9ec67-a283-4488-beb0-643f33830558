package com.zizhiguanjia.model_home.listener;

import com.zizhiguanjia.model_home.bean.MenuNavsBean;

import io.flutter.plugin.common.MethodChannel;

public interface IFlutterChannerListener {
    void getHttpsData(MethodChannel.Result result,boolean passCertificate);
    void goToPageByRoute(String route);

    /**
     * 设置当前的majorPId
     */
    void setMajorParentId(String majorParentId);
    void toast(String msg);
    void goToTopicList(boolean statistical);
    void noBuyDialog(String payType);
    void goToBuyUrl(String url);
    void goToChapterList();
    void dioTel(String tel);
    void goToVideo(String type);
    void goToVideo(String type,String url,boolean checkVip);
    void paySuccessState(String state);
    void closeView();
    void toWebUrl(String url, String routeType, String loginStatus);
    void toOtherWebUrl(String url);
    void  shareNavs(String wxurls,String states);
    void  openKfUrl(String wxurl, String state, String title);
    void homeStartCount(String type);
    void goToMore(boolean checkBuyPress);
    void goToBlackPlay();
    void goToLiving(String ids,MethodChannel.Result result);
    void goToLiveVideo(String url);
    void initGradleData(MethodChannel.Result result, String pagerType);
    void choiceAddress(String jsonStr);
    void getLocalAddress(MethodChannel.Result result);
    void getDafalutDatas(MethodChannel.Result result);
    void getDefalutConfig(MethodChannel.Result result);
    void resultCertificate(String json);
    void getLivePreData(MethodChannel.Result result);
    void goToMnks(boolean restart, String pagerType);
    void goToWebPay(String routh,String url,int payType,String  payRouthParams);
    void goToAllPage(String json);
    void goToDayPage(String num);
    void switchRemoveState(String json);
    void gotoPay();
    void gotoChapterExam(String num, String ids);
    void gotoQuestionExam(String num, String type, String ids );
    void cleanExam(String ids);
    void initSaveOrErrorData(MethodChannel.Result result);
    void goToAutoExam(boolean toresults,boolean dialog,String type);
    void goToMessagePage();
    void  initMessageListData(MethodChannel.Result result);
    void appointment(String ids,String index);
    void initOrderListData(MethodChannel.Result result);
    void initExamTimeInfo(MethodChannel.Result result,String key);
    void goToBuyWebByPoint(String point,boolean isToastPays);
    void setExamTime();
    void goToAscend();
    void finshPage();
    void goToMnksByHpttp(boolean restart, String pagerType);
    void test();
    void getCoupionListData(MethodChannel.Result result);
    void passVipByRouth(String rouths,boolean need);
    void openLearning(String pass);
    void goToEj();
    void goToBindSubject();
    void goToLnzt();
    void goToEjMnks();
    void goToEjAddress();
    void goToEjSubject(String json);
    void userClickActiveSubjectClose(MethodChannel.Result result,String states1);
    void toExamResult(String paperType,String paperValues);
    void openOffOnlieServiceTips(String title,String sub,String url);

    /**
     * 打开课程详情页面
     * @param index 第几个课程，从0开始，更多操作的时候使用的是-1
     * @param useId 使用的课程id，更多操作的时候使用的是空
     */
    void openCourseDetail(String index, String useId);

    /**
     * 首页金刚区应用点击
     * @param bean 金刚圈数
     */
    void onMainNavFunctionClick(MenuNavsBean bean);
}
