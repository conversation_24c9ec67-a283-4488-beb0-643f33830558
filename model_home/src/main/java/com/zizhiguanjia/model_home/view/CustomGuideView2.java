package com.zizhiguanjia.model_home.view;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import com.binioter.guideview.Component;
import com.zizhiguanjia.model_home.R;

public class CustomGuideView2 implements Component{
    @Override
    public View getView(LayoutInflater inflater) {
        LinearLayout ll = (LinearLayout) inflater.inflate(R.layout.home_guide_two_layout, null);
        return ll;
    }

    @Override
    public int getAnchor() {
        return Component.ANCHOR_BOTTOM;
    }

    @Override
    public int getFitPosition() {
        return Component.FIT_END;
    }

    @Override
    public int getXOffset() {
        return 10;
    }

    @Override
    public int getYOffset() {
        return 10;
    }
}
