package com.zizhiguanjia.model_home.view;

import android.app.Activity;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.bumptech.glide.Glide;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.ItemsBean;
import com.zizhiguanjia.model_home.databinding.HomeFlutterStyleLayoutBinding;
import com.zizhiguanjia.model_home.ui.HomeNativeActivity;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;

import java.util.List;

/**
 * 负责管理NativeStudyAIFragment的通关精讲课功能
 */
public class NativeStudyAILiveCoursesManager {
    private final HomeFlutterStyleLayoutBinding binding;
    private final StudyViewModel studyViewModel;
    private final Activity activity;
    private final LifecycleOwner lifecycleOwner;

    public NativeStudyAILiveCoursesManager(@NonNull HomeFlutterStyleLayoutBinding binding,
                                           @NonNull StudyViewModel studyViewModel,
                                           @NonNull Activity activity,
                                           @NonNull LifecycleOwner lifecycleOwner) {
        this.binding = binding;
        this.studyViewModel = studyViewModel;
        this.activity = activity;
        this.lifecycleOwner = lifecycleOwner;

        // 设置"查看更多"点击事件
        setupMoreButtonClickListener();

        // 设置直播课数据观察者
        setupLivesObserver();
    }

    /**
     * 设置"查看更多"点击事件
     */
    private void setupMoreButtonClickListener() {
        if (binding != null && binding.tvMore != null) {
            binding.tvMore.setOnClickListener(view -> {
//                if (NoDoubleClickUtils.isDoubleClick()) return;
                if (!AccountHelper.checkLoginState()) {
                    return;
                }
                if (studyViewModel != null) {
                    List<ItemsBean> lives = studyViewModel.getLives().getValue();
                    if (lives != null && !lives.isEmpty()) {
                        String liveId = String.valueOf(lives.get(0).getLiveId());
                        String userId = AccountHelper.isUserLogin() ? AccountHelper.getUserId() : "0";

                        // 如果activity是HomeNativeActivity，则使用Fragment跳转
                        if (activity instanceof HomeNativeActivity) {
                            Bundle args = new Bundle();
                            args.putString("index", liveId);
                            args.putString("useId", userId);

                            // 直接使用ARouterUtils导航到课程详情页面
                            ((HomeNativeActivity) activity).startFragment(ARouterUtils.navFragment(HomeRoutherPath.COURSE_DETAIL));
                        }
                    } else {
                        ToastUtils.normal("暂无课程数据");
                    }
                }
            });
        }
    }

    /**
     * 设置直播课数据观察者
     */
    private void setupLivesObserver() {
        if (studyViewModel != null) {
            studyViewModel.getLives().observe(lifecycleOwner, this::updateLivesView);
        }
    }

    /**
     * 更新通关精讲课视图
     *
     * @param lives 通关精讲课数据
     */
    public void updateLivesView(List<ItemsBean> lives) {
        if (lives == null || lives.isEmpty() || binding == null) return;

        try {
            // 获取视频课程容器
            LinearLayout livesContainer = binding.getRoot().findViewById(R.id.live_course_container);
            if (livesContainer == null) {
                LogUtils.e("NativeStudyAILiveCoursesManager - updateLivesView - 找不到直播课程容器");
                return;
            }

            // 清除旧的内容
            livesContainer.removeAllViews();

            // 创建水平滚动视图
            HorizontalScrollView scrollView = new HorizontalScrollView(activity);
            scrollView.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT));
            scrollView.setHorizontalScrollBarEnabled(false); // 隐藏滚动条

            // 创建水平排列的内容容器
            LinearLayout contentLayout = new LinearLayout(activity);
            contentLayout.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT));
            contentLayout.setOrientation(LinearLayout.HORIZONTAL);

            // 最多显示3个精讲课条目
            int displayCount = Math.min(lives.size(), 3);
            for (int i = 0; i < displayCount; i++) {
                ItemsBean live = lives.get(i);
                addLiveCourseItem(contentLayout, live, i);
            }

            // 添加课程目录按钮
            addCatalogButton(contentLayout, lives);

            // 添加水平滚动视图到主容器
            scrollView.addView(contentLayout);
            livesContainer.addView(scrollView);

            // 显示通关精讲课容器
            livesContainer.setVisibility(View.VISIBLE);
            LogUtils.e("NativeStudyAILiveCoursesManager - updateLivesView - 已更新" + displayCount + "个直播课程");
        } catch (Exception e) {
            LogUtils.e("NativeStudyAILiveCoursesManager - updateLivesView - 异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 添加直播课程项到容器
     */
    private void addLiveCourseItem(LinearLayout container, ItemsBean live, int position) {
        // 创建视频课程项布局
        LinearLayout itemLayout = new LinearLayout(activity);
        LinearLayout.LayoutParams itemParams = new LinearLayout.LayoutParams(
                activity.getResources().getDimensionPixelSize(R.dimen.course_item_width),
                LinearLayout.LayoutParams.WRAP_CONTENT);
        itemParams.rightMargin = activity.getResources().getDimensionPixelSize(R.dimen.video_item_margin);
        itemLayout.setLayoutParams(itemParams);
        itemLayout.setOrientation(LinearLayout.VERTICAL);

        // 创建CardView
        androidx.cardview.widget.CardView cardView = new androidx.cardview.widget.CardView(activity);
        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                activity.getResources().getDimensionPixelSize(R.dimen.video_height));
        cardView.setLayoutParams(cardParams);
        cardView.setRadius(activity.getResources().getDimensionPixelSize(R.dimen.card_corner_radius));
        cardView.setCardElevation(0);

        // 创建ImageView
        ImageView imageView = new ImageView(activity);
        imageView.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT));
        imageView.setScaleType(ImageView.ScaleType.FIT_XY);

        // 使用Glide加载图片
        Glide.with(imageView).load(live.getLiveCover()).into(imageView);

        // 添加状态标签
        if (live.getStatus() == 2) { // 直播中
            TextView liveTag = new TextView(activity);
            FrameLayout.LayoutParams tagParams = new FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.WRAP_CONTENT,
                    FrameLayout.LayoutParams.WRAP_CONTENT);
            tagParams.gravity = Gravity.TOP | Gravity.START;
            tagParams.setMargins(8, 8, 0, 0);
            liveTag.setLayoutParams(tagParams);
            liveTag.setPadding(8, 4, 8, 4);
            liveTag.setText("直播中");
            liveTag.setTextColor(Color.WHITE);
            liveTag.setTextSize(10);
            liveTag.setBackgroundResource(R.drawable.bg_live_tag);

            FrameLayout frameLayout = new FrameLayout(activity);
            frameLayout.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.MATCH_PARENT));
            frameLayout.addView(imageView);
            frameLayout.addView(liveTag);
            cardView.addView(frameLayout);
        } else {
            cardView.addView(imageView);
        }

        // 创建标题TextView
        TextView titleTextView = new TextView(activity);
        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        titleParams.topMargin = activity.getResources().getDimensionPixelSize(R.dimen.title_top_margin);
        titleTextView.setLayoutParams(titleParams);
        titleTextView.setEllipsize(TextUtils.TruncateAt.END);
        titleTextView.setMaxLines(2);
        titleTextView.setText(live.getTitle());
        titleTextView.setTextColor(activity.getResources().getColor(R.color.text_primary));
        titleTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);

        // 添加视图到布局
        itemLayout.addView(cardView);
        itemLayout.addView(titleTextView);

        // 设置点击事件
        itemLayout.setOnClickListener(v -> handleLiveCourseClick(live, position));

        // 添加到容器
        container.addView(itemLayout);
    }

    /**
     * 添加课程目录按钮
     */
    private void addCatalogButton(LinearLayout container, List<ItemsBean> lives) {
        LinearLayout catalogLayout = new LinearLayout(activity);
        LinearLayout.LayoutParams catalogParams = new LinearLayout.LayoutParams(
                activity.getResources().getDimensionPixelSize(R.dimen.course_catalog_width),
                activity.getResources().getDimensionPixelSize(R.dimen.video_height) +
                        activity.getResources().getDimensionPixelSize(R.dimen.title_top_margin) +
                        activity.getResources().getDimensionPixelSize(R.dimen.catalog_extra_height));
        catalogLayout.setLayoutParams(catalogParams);
        catalogLayout.setOrientation(LinearLayout.HORIZONTAL);
        catalogLayout.setGravity(Gravity.CENTER);
        catalogLayout.setBackgroundResource(R.drawable.bg_course_catalog);

        // 添加课程目录文本
        TextView catalogText = new TextView(activity);
        catalogText.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT));
        catalogText.setText("课\n程\n目\n录");
        catalogText.setTextColor(activity.getResources().getColor(R.color.catalog_text));
        catalogText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        catalogText.setLineSpacing(8, 1);

        // 添加箭头图标
        ImageView arrowIcon = new ImageView(activity);
        LinearLayout.LayoutParams arrowParams = new LinearLayout.LayoutParams(
                activity.getResources().getDimensionPixelSize(R.dimen.arrow_icon_size),
                activity.getResources().getDimensionPixelSize(R.dimen.arrow_icon_size));
        arrowParams.topMargin = activity.getResources().getDimensionPixelSize(R.dimen.arrow_margin_top);
        arrowIcon.setLayoutParams(arrowParams);
        arrowIcon.setImageResource(R.drawable.ic_arrow_right_blue);

        catalogLayout.addView(catalogText);
        catalogLayout.addView(arrowIcon);

        // 设置课程目录点击事件
        catalogLayout.setOnClickListener(v -> {
            // 检查用户是否已登录
            if (!AccountHelper.checkLoginState()) {
                // 未登录，跳转到登录页面
//                ToastUtils.normal("请先登录");
//                if (activity instanceof HomeNativeActivity) {
//                    ((HomeNativeActivity) activity).startFragment(AccountHelper.showAccountLogin());
////                    AccountHelper.checkLoginState();
//                }
                return;
            }

            // 用户已登录，跳转到课程目录页面
            ToastUtils.normal("查看课程目录");

            if (lives != null && !lives.isEmpty() && activity instanceof HomeNativeActivity) {
                Bundle args = new Bundle();
                String userId = AccountHelper.isUserLogin() ? AccountHelper.getUserId() : "0";

                // 设置参数
                args.putString("index", String.valueOf(lives.get(0).getLiveId()));
                args.putString("useId", userId);

                // 直接使用ARouterUtils导航到课程详情页面
                ((HomeNativeActivity) activity).startFragment(ARouterUtils.navFragment(HomeRoutherPath.COURSE_DETAIL));
            }
        });

        // 添加到容器
        container.addView(catalogLayout);
    }

    /**
     * 处理通关精讲课点击事件
     */
    private void handleLiveCourseClick(ItemsBean live, int position) {
        if (live == null) return;

        // 防止重复点击
//        if (NoDoubleClickUtils.isDoubleClick()) return;

        // 检查用户是否已登录
        if (!AccountHelper.checkLoginState()) {
            // 未登录，跳转到登录页面
//            ToastUtils.normal("请先登录");
//            if (activity instanceof HomeNativeActivity) {
//                ((HomeNativeActivity) activity).startFragment(AccountHelper.showAccountLogin());
////                AccountHelper.checkLoginState();
//            }
            return;
        }

        int status = live.getStatus();
        if (status == 1) {
            // 预约中
            if (live.isIsOrder()) {
                ToastUtils.normal("您已预约该课程，请在直播开始时观看");
            } else {
                // 预约直播课程
                if (live.getLiveUrl() != null) {
                    ToastUtils.normal("预约成功");
                    // 更新预约状态
                    live.setIsOrder(true);
                    // 预约人数+1
                    live.setLiveSubscribedCount(live.getLiveSubscribedCount() + 1);
                    // 更新UI
                    List<ItemsBean> currentLives = studyViewModel.getLives().getValue();
                    if (currentLives != null) {
                        studyViewModel.updateLives(currentLives);
                    }
                }
            }
        } else if (status == 2 || status == 4 || status == 0) {
            // 直播中、回放或普通视频
            LogUtils.e("NativeStudyAILiveCoursesManager - 打开课程详情，liveId: " + live.getLiveId() + ", status: " + status);

            String liveId = String.valueOf(live.getLiveId());
            String userId = AccountHelper.isUserLogin() ? AccountHelper.getUserId() : "0";

            try {
                if (activity instanceof HomeNativeActivity) {
                    Bundle args = new Bundle();
                    args.putString("index", liveId);
                    args.putString("useId", userId);

                    // 直接使用ARouterUtils导航到课程详情页面
                    ((HomeNativeActivity) activity).startFragment(ARouterUtils.navFragment(HomeRoutherPath.COURSE_DETAIL));

                    // 更新观看人数
                    if (status == 2) {
                        live.setLivePlayedCount(live.getLivePlayedCount() + 1);
                        // 更新UI
                        List<ItemsBean> currentLives = studyViewModel.getLives().getValue();
                        if (currentLives != null) {
                            studyViewModel.updateLives(currentLives);
                        }
                    }
                }
            } catch (Exception e) {
                LogUtils.e("NativeStudyAILiveCoursesManager - 使用ARouterUtils导航失败: " + e.getMessage());

                // 备选方案：如果有URL，使用WebView打开
                if (live.getLiveUrl() != null && activity instanceof HomeNativeActivity) {
                    String liveUrl = String.valueOf(live.getLiveUrl());
                    if (!liveUrl.isEmpty()) {
                        Bundle args = new Bundle();
                        args.putString("url", liveUrl);
                        args.putString("title", live.getTitle());
                        args.putString("routh", "home");
                        args.putInt("payType", 1);
                        ((HomeNativeActivity) activity).startFragment(com.zizhiguanjia.lib_base.helper.CommonHelper.showCommonWeb());
                    } else {
                        ToastUtils.normal("直播链接无效");
                    }
                } else {
                    ToastUtils.normal("无法打开课程详情");
                }
            }
        }
    }
} 