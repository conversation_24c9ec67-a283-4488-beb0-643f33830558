package com.zizhiguanjia.model_home.view;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.bean.HomeExamDateTipBean;
import com.zizhiguanjia.model_home.bean.MenuNavsBean;
import com.zizhiguanjia.model_home.config.RouteConfig;
import com.zizhiguanjia.model_home.databinding.HomeFlutterStyleLayoutBinding;
import com.zizhiguanjia.model_home.fragment.HomeFragment;
import com.zizhiguanjia.model_home.ui.HomeNativeActivity;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;

/**
 * 负责管理NativeStudyAIFragment的学习计划功能
 */
public class NativeStudyAIStudyPlanManager {
    private final HomeFlutterStyleLayoutBinding binding;
    private final StudyViewModel studyViewModel;
    private final Activity activity;
    private final LifecycleOwner lifecycleOwner;

    public NativeStudyAIStudyPlanManager(@NonNull HomeFlutterStyleLayoutBinding binding, @NonNull StudyViewModel studyViewModel, @NonNull Activity activity, @NonNull LifecycleOwner lifecycleOwner) {
        this.binding = binding;
        this.studyViewModel = studyViewModel;
        this.activity = activity;
        this.lifecycleOwner = lifecycleOwner;

        // 设置学习计划观察者
        setupStudyPlanObserver();
    }

    /**
     * 设置学习计划观察者
     */
    private void setupStudyPlanObserver() {
        if (studyViewModel != null) {
            studyViewModel.getHomeBean().observe(lifecycleOwner, this::updateStudyPlanView);

            // 观察考试日期显示状态
            studyViewModel.getIsShowExamDate().observe(lifecycleOwner, isShowExamDate -> {
                // 处理考试日期显示状态
                if (binding != null && binding.layoutStudyPlan != null) {
                    binding.layoutStudyPlan.setVisibility(isShowExamDate != null && isShowExamDate ? View.VISIBLE : View.GONE);
                    LogUtils.e("NativeStudyAIStudyPlanManager - 是否显示考试日期模块: " + (isShowExamDate != null && isShowExamDate));
                }
            });

            // 观察考试日期提示
            studyViewModel.getExamDateTip().observe(lifecycleOwner, examDateTip -> {
                // 处理考试日期提示
                if (binding != null && examDateTip != null) {
                    binding.tvExamDateTip.setText(examDateTip);
                    LogUtils.e("NativeStudyAIStudyPlanManager - 更新考试日期提示: " + examDateTip);
                }
            });
        }
    }

    /**
     * 更新学习计划模块
     *
     * @param homeBean 首页数据
     */
    public void updateStudyPlanView(HomeBean homeBean) {
        if (binding == null || homeBean == null) return;

        try {
            // 获取学习计划模块
            LinearLayout layoutStudyPlan = binding.getRoot().findViewById(R.id.layout_study_plan);
            if (layoutStudyPlan == null) {
                LogUtils.e("NativeStudyAIStudyPlanManager - updateStudyPlanView - 找不到学习计划模块");
                return;
            }

            // 获取未设置考试日期的视图和已设置考试日期的视图
            LinearLayout layoutUnsetExamDate = layoutStudyPlan.findViewById(R.id.layout_unset_exam_date);
            FrameLayout layoutSetExamDate = layoutStudyPlan.findViewById(R.id.layout_set_exam_date);

            // 根据isShowExamDate决定是否显示学习计划模块
            if (homeBean.isShowExamDate()) {
                layoutStudyPlan.setVisibility(View.VISIBLE);

                // 获取考试日期信息
                HomeExamDateTipBean examDateTip = homeBean.getExamDateTip();
                if (examDateTip != null) {
                    // 根据是否设置了考试日期显示不同的视图
                    if (examDateTip.isSetExamDate()) {
                        // 已设置考试日期
                        layoutUnsetExamDate.setVisibility(View.GONE);
                        layoutSetExamDate.setVisibility(View.VISIBLE);

                        // 更新剩余题目信息
                        TextView tvRemainingQuestions = layoutSetExamDate.findViewById(R.id.tv_remaining_questions);
                        String remainingText = "剩余" + examDateTip.getRemainingQuestionNum() + "道题未练习，" + examDateTip.getRemainingNoClearNum() + "道错题未清空";
                        tvRemainingQuestions.setText(remainingText);

                        // 获取剩余天数相关视图
                        TextView tvExamDayPrefix = layoutSetExamDate.findViewById(R.id.tv_exam_day_prefix);
                        TextView tvRemainingDays = layoutSetExamDate.findViewById(R.id.tv_remaining_days);
                        TextView tvExamDaySuffix = layoutSetExamDate.findViewById(R.id.tv_exam_day_suffix);
                        TextView tvExamDayStatus = layoutSetExamDate.findViewById(R.id.tv_exam_day_status);
                        // 根据剩余天数更新显示
                        int remainingDays = examDateTip.getRemainingExamDays();
                        updateExamDayDisplay(tvExamDayPrefix, tvRemainingDays, tvExamDaySuffix, tvExamDayStatus, remainingDays);

                        // 设置立即学习按钮点击事件
                        Button btnStartStudy = layoutSetExamDate.findViewById(R.id.btn_start_study);
                        setupStartStudyButton(btnStartStudy);

                        // 设置考试日状态点击事件
                        setupExamDayStatusClickListener(tvExamDayStatus);
                    } else {
                        // 未设置考试日期
                        layoutUnsetExamDate.setVisibility(View.VISIBLE);
                        layoutSetExamDate.setVisibility(View.GONE);

                        // 设置设置考试日期按钮点击事件
                        TextView btnSetExamDate = layoutUnsetExamDate.findViewById(R.id.btn_set_exam_date);
                        setupSetExamDateButton(btnSetExamDate);
                    }
                    LogUtils.e("NativeStudyAIStudyPlanManager - updateStudyPlanView - 考试日期状态: " + (examDateTip.isSetExamDate() ? "已设置" : "未设置"));
                } else {
                    // 没有考试日期信息，隐藏整个模块
                    layoutStudyPlan.setVisibility(View.GONE);
                    LogUtils.e("NativeStudyAIStudyPlanManager - updateStudyPlanView - 没有考试日期信息，隐藏模块");
                }
            } else {
                // 不显示学习计划模块
                layoutStudyPlan.setVisibility(View.GONE);
                LogUtils.e("NativeStudyAIStudyPlanManager - updateStudyPlanView - 设置为不显示学习计划模块");
            }
        } catch (Exception e) {
            LogUtils.e("NativeStudyAIStudyPlanManager - updateStudyPlanView - 异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 根据剩余天数更新显示
     */
    private void updateExamDayDisplay(TextView prefix, TextView days, TextView suffix, TextView status, int remainingDays) {
        if (remainingDays > 0) {
            // 距离考试还有天数
            prefix.setText("距考试剩");
            prefix.setVisibility(View.VISIBLE);
            days.setText(String.valueOf(remainingDays));
            days.setVisibility(View.VISIBLE);
            suffix.setText("天");
            suffix.setVisibility(View.VISIBLE);
            status.setVisibility(View.GONE);
            LogUtils.e("NativeStudyAIStudyPlanManager - updateExamDayDisplay - 距离考试还有" + remainingDays + "天");
        } else if (remainingDays == 0) {
            // 考试日
            prefix.setText("今天是");
            prefix.setVisibility(View.VISIBLE);
            days.setVisibility(View.GONE);
            suffix.setVisibility(View.GONE);
            status.setText("考试日");
            status.setVisibility(View.VISIBLE);
            LogUtils.e("NativeStudyAIStudyPlanManager - updateExamDayDisplay - 今天是考试日");
        } else {
            // 考试已结束
            prefix.setText("考试结束");
            prefix.setVisibility(View.VISIBLE);
            days.setVisibility(View.GONE);
            suffix.setVisibility(View.GONE);
            status.setText("重新设置");
            status.setVisibility(View.VISIBLE);
            LogUtils.e("NativeStudyAIStudyPlanManager - updateExamDayDisplay - 考试已结束");
        }
    }

    /**
     * 设置立即学习按钮点击事件
     */
    private void setupStartStudyButton(Button btnStartStudy) {
        if (btnStartStudy == null) {
            LogUtils.e("NativeStudyAIStudyPlanManager - setupStartStudyButton - 找不到立即学习按钮");
            return;
        }

        btnStartStudy.setOnClickListener(v -> {
            if (!NoDoubleClickUtils.isDoubleClick()) {
                studyViewModel.goToAscend();
//                android.os.Bundle args = new android.os.Bundle();
//                args.putString("flutterRoute", "/testdate");
//                HomeFragment homeFragment = new HomeFragment();
////                homeFragment.setArguments(args);
////                ((HomeNativeActivity) activity).startFragment(homeFragment);
//
//                boolean autoDialog = homeFragment.getBoolean("autoDialog", false);
//                LogUtils.e("------>>>>点击了" + autoDialog);
//                homeFragment.initArguments().putBoolean("goToAscend", autoDialog);
//                homeFragment.initArguments().putString("flutterRoute", RouteConfig.ROUTE_TESTDATE);
//                ((HomeNativeActivity) activity).startFragment(homeFragment);
//                // 跳转到模拟考试页面
//                if (studyViewModel != null) {
//                    // 创建模拟考试的菜单导航项
//                    MenuNavsBean menuNav = new MenuNavsBean();
//                    menuNav.setNavType(4); // 模拟考试类型
//                    menuNav.setTitle("模拟考试");
//                    menuNav.setDo(false);
//                    menuNav.setDateBoxStatus(1);
//
//                    // 调用StudyViewModel处理模拟考试点击事件
//                    studyViewModel.onMainNavFunctionClick(menuNav);
//                } else if (activity instanceof HomeNativeActivity) {
//                    // 直接跳转到模拟考试页面
//                    Bundle args = new Bundle();
//                    args.putInt("index", 0);
//                    args.putInt("mPageType", 1);
//                    args.putString("pagerType", "7");
//                    ((HomeNativeActivity) activity).startFragment(com.zizhiguanjia.lib_base.helper.ListHelper.toPageExamAuto());
//                }
//
//                // 添加埋点统计
//                PointHelper.joinPointData(PointerMsgType.POINTER_A_HOMEPAGE_SIMULATION, false);
//                LogUtils.e("NativeStudyAIStudyPlanManager - setupStartStudyButton - 点击立即学习按钮");
            }
        });
    }

    /**
     * 设置考试日状态点击事件
     */
    private void setupExamDayStatusClickListener(TextView tvExamDayStatus) {
        if (tvExamDayStatus == null) return;

        tvExamDayStatus.setOnClickListener(v -> {
//            if (!NoDoubleClickUtils.isDoubleClick()) {
                // 调用ViewModel中的goToAscend方法
                if (studyViewModel != null) {
                    studyViewModel.goToAscend();
                    LogUtils.e("NativeStudyAIStudyPlanManager - setupExamDayStatusClickListener - 点击重新设置考试日期");
                }
//            }
        });
    }

    /**
     * 设置"设置考试日期"按钮点击事件
     */
    private void setupSetExamDateButton(TextView btnSetExamDate) {
        if (btnSetExamDate == null) return;

        btnSetExamDate.setOnClickListener(v -> {
            // 调用ViewModel中的goToAscend方法
            if (!AccountHelper.checkLoginState()) {
                return;
            }
            if (studyViewModel != null) {
                studyViewModel.goToAscend();
                LogUtils.e("NativeStudyAIStudyPlanManager - setupSetExamDateButton - 点击设置考试日期按钮");
            }
        });
    }
} 