package com.zizhiguanjia.model_home.view;

import android.app.Activity;
import android.os.Handler;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.LifecycleOwner;
import androidx.viewpager.widget.ViewPager;

import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.adapter.BannerAdapter;
import com.zizhiguanjia.model_home.bean.BannersBean;
import com.zizhiguanjia.model_home.databinding.HomeFlutterStyleLayoutBinding;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;

import java.util.List;

/**
 * 负责管理NativeStudyAIFragment的Banner轮播图功能
 */
public class NativeStudyAIBannerHelper {
    private final HomeFlutterStyleLayoutBinding binding;
    private final StudyViewModel studyViewModel;
    private final Activity activity;
    private final LifecycleOwner lifecycleOwner;
    
    private BannerAdapter bannerAdapter;
    private ViewPager bannerViewPager;
    private LinearLayout indicatorContainer;
    
    // 自动轮播相关
    private static final int AUTO_SCROLL_DELAY = 3000; // 轮播间隔，单位毫秒
    private Handler autoScrollHandler;
    private Runnable autoScrollRunnable;
    private boolean isAutoScrollEnabled = true;

    public NativeStudyAIBannerHelper(@NonNull HomeFlutterStyleLayoutBinding binding,
                                     @NonNull StudyViewModel studyViewModel,
                                     @NonNull Activity activity,
                                     @NonNull LifecycleOwner lifecycleOwner) {
        this.binding = binding;
        this.studyViewModel = studyViewModel;
        this.activity = activity;
        this.lifecycleOwner = lifecycleOwner;
        
        // 初始化自动轮播Handler
        autoScrollHandler = new Handler();
    }

    /**
     * 初始化Banner
     */
    public void initBanner() {
        try {
            LogUtils.e("NativeStudyAIBannerHelper - initBanner - 开始初始化Banner");

            // 直接从binding中查找ViewPager和指示器容器
            View bannerLayout = binding.getRoot().findViewById(R.id.home_banner_layout);
            if (bannerLayout != null) {
                bannerViewPager = bannerLayout.findViewById(R.id.banner_view_pager);
                indicatorContainer = bannerLayout.findViewById(R.id.indicator_container);
                LogUtils.e("NativeStudyAIBannerHelper - initBanner - 找到Banner布局和控件");

                if (bannerViewPager != null) {
                    // 获取当前Banner数据
                    List<BannersBean> currentBanners = null;
                    if (studyViewModel != null) {
                        currentBanners = studyViewModel.getBanners().getValue();
                        LogUtils.e("NativeStudyAIBannerHelper - initBanner - 从ViewModel获取Banner数据: " +
                                (currentBanners != null ? currentBanners.size() + "条" : "空"));
                    }

                    // 创建并设置Banner适配器
                    bannerAdapter = new BannerAdapter(activity, currentBanners, studyViewModel);
                    bannerViewPager.setAdapter(bannerAdapter);
                    LogUtils.e("NativeStudyAIBannerHelper - initBanner - 设置Banner适配器完成");

                    // 设置ViewPager的页面切换监听器
                    bannerViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                        private int previousState = ViewPager.SCROLL_STATE_IDLE;
                        private boolean userScrollChange = false;
                        
                        @Override
                        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                            // 不需要处理
                        }

                        @Override
                        public void onPageSelected(int position) {
                            // 更新指示器
                            updateIndicator(position);
                            LogUtils.e("NativeStudyAIBannerHelper - onPageSelected - 选中页面: " + position);
                        }

                        @Override
                        public void onPageScrollStateChanged(int state) {
                            // 检测用户滑动
                            if (previousState == ViewPager.SCROLL_STATE_DRAGGING 
                                    && state == ViewPager.SCROLL_STATE_SETTLING) {
                                userScrollChange = true;
                                LogUtils.e("NativeStudyAIBannerHelper - onPageScrollStateChanged - 用户手动滑动");
                            } else if (previousState == ViewPager.SCROLL_STATE_SETTLING 
                                    && state == ViewPager.SCROLL_STATE_IDLE) {
                                userScrollChange = false;
                                LogUtils.e("NativeStudyAIBannerHelper - onPageScrollStateChanged - 滑动完成");
                            }
                            
                            // 当用户手动滑动时，暂停自动轮播
                            if (state == ViewPager.SCROLL_STATE_DRAGGING) {
                                stopAutoScroll();
                                LogUtils.e("NativeStudyAIBannerHelper - onPageScrollStateChanged - 开始拖动，暂停轮播");
                            } else if (state == ViewPager.SCROLL_STATE_IDLE) {
                                // 滑动停止后，继续自动轮播
                                startAutoScroll();
                                LogUtils.e("NativeStudyAIBannerHelper - onPageScrollStateChanged - 滑动停止，恢复轮播");
                            }
                            
                            previousState = state;
                        }
                    });

                    // 初始化指示器
                    setupIndicator();
                    LogUtils.e("NativeStudyAIBannerHelper - initBanner - 初始化指示器完成");

                    // 设置Banner数据观察
                    observeBannerData();
                    
                    // 启动自动轮播
                    startAutoScroll();
                    LogUtils.e("NativeStudyAIBannerHelper - initBanner - 启动自动轮播");
                } else {
                    LogUtils.e("NativeStudyAIBannerHelper - initBanner - ViewPager为空");
                }
            } else {
                LogUtils.e("NativeStudyAIBannerHelper - initBanner - 找不到banner布局");
            }
        } catch (Exception e) {
            LogUtils.e("NativeStudyAIBannerHelper - initBanner - 异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 设置指示器
     */
    private void setupIndicator() {
        if (indicatorContainer == null || bannerAdapter == null) {
            return;
        }

        // 清除所有指示器
        indicatorContainer.removeAllViews();

        // 获取Banner数量
        int count = bannerAdapter.getCount();
        
        if (count <= 0) {
            LogUtils.e("NativeStudyAIBannerHelper - setupIndicator - Banner数量为0，不显示指示器");
            return;
        }

        // 创建指示器
        for (int i = 0; i < count; i++) {
            ImageView indicator = new ImageView(activity);
            int size = activity.getResources().getDimensionPixelSize(R.dimen.indicator_size);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(size, size);
            params.setMargins(8, 0, 8, 0);
            indicator.setLayoutParams(params);

            // 设置指示器的背景
            indicator.setImageResource(i == 0 ? R.drawable.indicator_selected : R.drawable.indicator_normal);

            // 添加到容器
            indicatorContainer.addView(indicator);
        }
        
        LogUtils.e("NativeStudyAIBannerHelper - setupIndicator - 创建了" + count + "个指示器");
    }

    /**
     * 更新指示器
     */
    private void updateIndicator(int position) {
        if (indicatorContainer == null) {
            return;
        }

        // 更新指示器状态
        int count = indicatorContainer.getChildCount();
        for (int i = 0; i < count; i++) {
            ImageView indicator = (ImageView) indicatorContainer.getChildAt(i);
            indicator.setImageResource(i == position ? R.drawable.indicator_selected : R.drawable.indicator_normal);
        }
    }
    
    /**
     * 观察Banner数据变化
     */
    private void observeBannerData() {
        studyViewModel.getBanners().observe(lifecycleOwner, this::updateBannerData);
    }

    /**
     * 更新Banner数据
     */
    public void updateBannerData(List<BannersBean> banners) {
        LogUtils.e("NativeStudyAIBannerHelper - updateBannerData - 开始更新Banner数据，数量: " + 
                (banners != null ? banners.size() : 0));

        try {
            if (bannerAdapter == null) {
                LogUtils.e("NativeStudyAIBannerHelper - updateBannerData - bannerAdapter为空，尝试重新初始化");
                initBanner();
                return;
            }

            // 记住当前位置
            int currentPosition = 0;
            if (bannerViewPager != null) {
                currentPosition = bannerViewPager.getCurrentItem();
            }

            LogUtils.e("NativeStudyAIBannerHelper - updateBannerData - 使用bannerAdapter.updateData更新数据");
            bannerAdapter.updateData(banners);

            // 确保指示器也更新
            setupIndicator();

            // 不再重新设置适配器，而是通知数据更新
            if (bannerViewPager != null) {
                // 强制刷新ViewPager，但保持当前位置
                bannerAdapter.notifyDataSetChanged();
                
                // 限制位置在有效范围内
                int newPosition = Math.min(currentPosition, bannerAdapter.getCount() - 1);
                if (newPosition >= 0) {
                    bannerViewPager.setCurrentItem(newPosition, false);
                    LogUtils.e("NativeStudyAIBannerHelper - updateBannerData - 保持位置：" + newPosition);
                }
                
                // 更新数据后重新启动自动轮播
                restartAutoScroll();
            } else {
                LogUtils.e("NativeStudyAIBannerHelper - updateBannerData - bannerViewPager为空");
            }
        } catch (Exception e) {
            LogUtils.e("NativeStudyAIBannerHelper - updateBannerData - 异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 开始自动轮播
     */
    public void startAutoScroll() {
        if (!isAutoScrollEnabled || bannerViewPager == null || bannerAdapter == null) {
            return;
        }
        
        // 如果Banner数量小于2，不需要轮播
        if (bannerAdapter.getCount() <= 1) {
            LogUtils.e("NativeStudyAIBannerHelper - startAutoScroll - Banner数量不足，不启动轮播");
            return;
        }
        
        stopAutoScroll(); // 先停止之前的轮播
        
        // 创建轮播任务
        autoScrollRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    if (bannerViewPager != null && bannerAdapter != null && bannerAdapter.getCount() > 1) {
                        int currentItem = bannerViewPager.getCurrentItem();
                        int nextItem = (currentItem + 1) % bannerAdapter.getCount();
                        LogUtils.e("NativeStudyAIBannerHelper - autoScroll - 从位置 " + currentItem + " 滚动到 " + nextItem);
                        bannerViewPager.setCurrentItem(nextItem, true);
                        
                        // 继续下一次轮播
                        autoScrollHandler.postDelayed(this, AUTO_SCROLL_DELAY);
                    }
                } catch (Exception e) {
                    LogUtils.e("NativeStudyAIBannerHelper - startAutoScroll - 异常: " + e.getMessage());
                }
            }
        };
        
        // 启动轮播
        autoScrollHandler.postDelayed(autoScrollRunnable, AUTO_SCROLL_DELAY);
        LogUtils.e("NativeStudyAIBannerHelper - startAutoScroll - 启动自动轮播");
    }
    
    /**
     * 停止自动轮播
     */
    public void stopAutoScroll() {
        if (autoScrollHandler != null && autoScrollRunnable != null) {
            autoScrollHandler.removeCallbacks(autoScrollRunnable);
            LogUtils.e("NativeStudyAIBannerHelper - stopAutoScroll - 停止自动轮播");
        }
    }
    
    /**
     * 重新启动自动轮播
     */
    public void restartAutoScroll() {
        stopAutoScroll();
        startAutoScroll();
        LogUtils.e("NativeStudyAIBannerHelper - restartAutoScroll - 重新启动自动轮播");
    }
    
    /**
     * 设置是否启用自动轮播
     */
    public void setAutoScrollEnabled(boolean enabled) {
        this.isAutoScrollEnabled = enabled;
        if (enabled) {
            startAutoScroll();
        } else {
            stopAutoScroll();
        }
        LogUtils.e("NativeStudyAIBannerHelper - setAutoScrollEnabled - 设置自动轮播状态: " + (enabled ? "启用" : "禁用"));
    }
    
    /**
     * 在Fragment或Activity生命周期暂停时调用
     */
    public void onPause() {
        stopAutoScroll();
        LogUtils.e("NativeStudyAIBannerHelper - onPause - 暂停轮播");
    }
    
    /**
     * 在Fragment或Activity生命周期恢复时调用
     */
    public void onResume() {
        startAutoScroll();
        LogUtils.e("NativeStudyAIBannerHelper - onResume - 恢复轮播");
    }
    
    /**
     * 在Fragment或Activity销毁时调用
     */
    public void onDestroy() {
        stopAutoScroll();
        autoScrollHandler = null;
        autoScrollRunnable = null;
        LogUtils.e("NativeStudyAIBannerHelper - onDestroy - 清理资源");
    }
} 