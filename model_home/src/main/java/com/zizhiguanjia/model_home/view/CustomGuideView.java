package com.zizhiguanjia.model_home.view;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import com.binioter.guideview.Component;
import com.zizhiguanjia.model_home.R;

public class CustomGuideView implements Component {
    @Override
    public View getView(LayoutInflater inflater) {
        LinearLayout ll = (LinearLayout) inflater.inflate(R.layout.home_guide_one_layout, null);
        return ll;
    }

    @Override
    public int getAnchor() {
        return Component.ANCHOR_OVER;
    }

    @Override
    public int getFitPosition() {
        return Component.ANCHOR_RIGHT;
    }

    @Override
    public int getXOffset() {
        return 0;
    }

    @Override
    public int getYOffset() {
        return -18;
    }
}
