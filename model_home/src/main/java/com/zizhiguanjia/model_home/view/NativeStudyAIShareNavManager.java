package com.zizhiguanjia.model_home.view;

import android.app.Activity;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.bumptech.glide.Glide;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.HomeShaperBean;
import com.zizhiguanjia.model_home.databinding.HomeFlutterStyleLayoutBinding;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 负责管理NativeStudyAIFragment的底部分享导航功能
 */
public class NativeStudyAIShareNavManager {
    private final HomeFlutterStyleLayoutBinding binding;
    private final StudyViewModel studyViewModel;
    private final Activity activity;
    private final LifecycleOwner lifecycleOwner;

    public NativeStudyAIShareNavManager(@NonNull HomeFlutterStyleLayoutBinding binding,
                                        @NonNull StudyViewModel studyViewModel,
                                        @NonNull Activity activity,
                                        @NonNull LifecycleOwner lifecycleOwner) {
        this.binding = binding;
        this.studyViewModel = studyViewModel;
        this.activity = activity;
        this.lifecycleOwner = lifecycleOwner;
        
        // 设置底部分享功能按钮数据观察者
        setupShareNavsObserver();
    }
    
    /**
     * 设置底部分享功能按钮数据观察者
     */
    private void setupShareNavsObserver() {
        if (studyViewModel != null) {
            studyViewModel.getShareNavs().observe(lifecycleOwner, this::updateShareNavs);
        }
    }

    /**
     * 更新底部分享功能按钮
     *
     * @param shareNavs 底部分享功能按钮数据
     */
    public void updateShareNavs(List<HomeShaperBean> shareNavs) {
        LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 开始更新底部分享功能按钮，数量: " + 
                  (shareNavs != null ? shareNavs.size() : 0));

        try {
            // 获取底部功能按钮容器
            LinearLayout bottomFunctionsContainer = binding.getRoot().findViewById(R.id.bottom_functions_container);
            if (bottomFunctionsContainer == null) {
                LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 找不到底部功能按钮容器");
                return;
            }

            LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 成功获取到底部功能按钮容器");

            // 清除旧的按钮
            bottomFunctionsContainer.removeAllViews();
            LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 已清除旧按钮");

            // 如果没有数据，不显示
            if (shareNavs == null || shareNavs.isEmpty()) {
                LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 底部分享功能按钮数据为空");
                bottomFunctionsContainer.setVisibility(View.GONE);
                return;
            }

            // 设置容器可见性
            bottomFunctionsContainer.setVisibility(View.VISIBLE);
            
            // 不过滤按钮，显示所有按钮
            LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 显示所有按钮，无论是否启用");
            
            // 计算每个按钮的权重
            float buttonWeight = 1.0f / shareNavs.size();
            LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 按钮数量: " + shareNavs.size() + 
                      ", 每个按钮的权重: " + buttonWeight);

            // 添加新的按钮
            int addedButtonCount = 0;
            for (HomeShaperBean shareNav : shareNavs) {
                LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 添加按钮: " + shareNav.getTitle() + 
                          ", 类型: " + shareNav.getNavType() + 
                          ", 是否启用: " + shareNav.isAtWork());

                // 创建按钮布局
                LinearLayout buttonLayout = new LinearLayout(activity);
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                        0, LinearLayout.LayoutParams.WRAP_CONTENT, buttonWeight);
                buttonLayout.setLayoutParams(layoutParams);
                buttonLayout.setGravity(android.view.Gravity.CENTER);
                buttonLayout.setOrientation(LinearLayout.VERTICAL);
                // 设置内边距
                int padding = DpUtils.dp2px(activity, 8);
                buttonLayout.setPadding(padding, padding, padding, padding);

                // 创建图标
                ImageView iconView = new ImageView(activity);
                int iconSize = DpUtils.dp2px(activity, 40); // 统一的图标大小
                iconView.setLayoutParams(new LinearLayout.LayoutParams(iconSize, iconSize));
                iconView.setScaleType(ImageView.ScaleType.FIT_CENTER); // 确保图标适应大小并居中

                // 加载图标
                boolean iconLoaded = false;
                if (!TextUtils.isEmpty(shareNav.getImgSrc())) {
                    // 使用Glide加载网络图片
                    try {
                        LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 加载网络图片: " + shareNav.getImgSrc());
                        Glide.with(Objects.requireNonNull(activity))
                                .load(shareNav.getImgSrc())
                                .into(iconView);
                        iconLoaded = true;
                    } catch (Exception e) {
                        LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 加载图片失败: " + e.getMessage());
                    }
                }
                
                // 如果网络图片加载失败，使用默认图标
                if (!iconLoaded) {
                    // 使用默认图标
                    int iconResId = getShareNavIconResId(shareNav.getNavType());
                    LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 使用默认图标，类型: " + 
                              shareNav.getNavType() + ", 资源ID: " + iconResId);
                    try {
                        iconView.setImageResource(iconResId);
                        // 设置颜色过滤器
                        iconView.setColorFilter(activity.getResources().getColor(R.color.menu_nav_icon_color));
                    } catch (Exception e) {
                        LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 设置默认图标失败: " + e.getMessage());
                    }
                }

                // 创建文本
                TextView textView = new TextView(activity);
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT);
                textParams.topMargin = DpUtils.dp2px(activity, 4);
                textView.setLayoutParams(textParams);
                textView.setText(shareNav.getTitle());
                textView.setTextColor(android.graphics.Color.parseColor("#333333"));
                textView.setTextSize(12);
                textView.setGravity(android.view.Gravity.CENTER);
                textView.setMaxLines(1);
                textView.setEllipsize(android.text.TextUtils.TruncateAt.END);

                // 添加到布局
                buttonLayout.addView(iconView);
                buttonLayout.addView(textView);

                // 设置点击事件 - 无论按钮是否启用，都设置点击事件
                final HomeShaperBean finalShareNav = shareNav;
                buttonLayout.setOnClickListener(v -> {
                    if (!NoDoubleClickUtils.isDoubleClick()) {
                        // 直接处理点击事件，不检查按钮启用状态
                        handleShareNavClick(finalShareNav);
                    }
                });

                // 添加到容器
                bottomFunctionsContainer.addView(buttonLayout);
                addedButtonCount++;
                LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 已添加按钮: " + shareNav.getTitle());
            }

            LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 底部分享功能按钮更新完成，共添加了 " + 
                      addedButtonCount + " 个按钮");

            // 设置可见性（即使没有添加按钮，也确保正确设置可见性）
            bottomFunctionsContainer.setVisibility(addedButtonCount > 0 ? View.VISIBLE : View.GONE);
            LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 设置容器可见性: " + 
                      (addedButtonCount > 0 ? "VISIBLE" : "GONE"));
        } catch (Exception e) {
            LogUtils.e("NativeStudyAIShareNavManager - updateShareNavs - 异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理底部分享功能按钮点击事件
     *
     * @param shareNav 被点击的按钮数据
     */
    private void handleShareNavClick(HomeShaperBean shareNav) {
        LogUtils.e("NativeStudyAIShareNavManager - handleShareNavClick - 点击底部分享功能按钮: " + shareNav.getTitle());

        try {
            if (studyViewModel != null) {
                studyViewModel.handleShareNavClick(shareNav);
            } else {
                LogUtils.e("NativeStudyAIShareNavManager - handleShareNavClick - studyViewModel为null");
                ToastUtils.normal("操作失败，请稍后再试");
            }
        } catch (Exception e) {
            LogUtils.e("NativeStudyAIShareNavManager - handleShareNavClick - 异常: " + e.getMessage());
            e.printStackTrace();
            ToastUtils.normal("操作失败，请稍后再试");
        }
    }
    
    /**
     * 获取分享导航图标资源ID
     * 
     * @param navType 导航类型
     * @return 图标资源ID
     */
    private int getShareNavIconResId(int navType) {
        LogUtils.e("NativeStudyAIShareNavManager - getShareNavIconResId - 获取图标资源ID，导航类型: " + navType);
        int iconResId;
        
        switch (navType) {
            case 1: // 微信好友
                iconResId = R.drawable.ic_wechat;
                LogUtils.e("NativeStudyAIShareNavManager - getShareNavIconResId - 选择微信好友图标");
                break;
            case 2: // 朋友圈
                iconResId = R.drawable.ic_moments;
                LogUtils.e("NativeStudyAIShareNavManager - getShareNavIconResId - 选择朋友圈图标");
                break;
            case 3: // 拨打电话
                iconResId = R.drawable.ic_phone;
                LogUtils.e("NativeStudyAIShareNavManager - getShareNavIconResId - 选择拨打电话图标");
                break;
            case 4: // 在线客服
                iconResId = R.drawable.ic_service;
                LogUtils.e("NativeStudyAIShareNavManager - getShareNavIconResId - 选择在线客服图标");
                break;
            default:
                // 默认图标 - 使用电话图标作为默认值
                iconResId = R.drawable.ic_phone;
                LogUtils.e("NativeStudyAIShareNavManager - getShareNavIconResId - 使用默认图标");
                break;
        }
        
        // 记录选择的图标资源ID
        LogUtils.e("NativeStudyAIShareNavManager - getShareNavIconResId - 最终选择的图标资源ID: " + iconResId);
        return iconResId;
    }
} 