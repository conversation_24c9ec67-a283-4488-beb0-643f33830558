package com.zizhiguanjia.model_home.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.CourseDetailCatalogueBean;
import com.zizhiguanjia.model_home.viedeo.JzvdStdVolumeAfterFullscreen;
import com.zizhiguanjia.model_video.custommedia.JZMediaAliyun;

import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import cn.jzvd.JZUtils;

/**
 * 功能作用：课程详情播放控件
 * 初始注释时间： 2023/11/23 18:13
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class CourseDetailPlayView extends JzvdStdVolumeAfterFullscreen {
    /**
     * 结束重新播放视图
     */
    private ConstraintLayout mClReplay;

    /**
     * 下一节
     */
    private AppCompatImageButton mBtnNext;

    /**
     * 去做题
     */
    private TextView mQuestionText;

    /**
     * 当前播放的节信息
     */
    private CourseDetailCatalogueBean.Item mCurrentBean;
    /**
     * 当前课程总体信息
     */
    private CourseDetailCatalogueBean mDetailCatalogueBean;

    /**
     * 状态改变回调
     */
    private OnStateChangeInterface mOnStateChangeInterface;
    /**
     * 倍速控件
     */
    private TextView mTvSpeed;
    /**
     * 试听按钮
     */
    private AppCompatTextView mTvAudition;

    /**
     * 倍速列表
     */
    private final float[] mSpeeds = new float[]{ 0.75F, 1F, 1.25F, 1.5F,  2F};

    /**
     * 当前倍速
     */
    private int mCurrentSpeedIndex = 1;

    /**
     * 是否是全屏显示
     */
    private boolean mFullShow = false;

    /**
     * 速度弹窗
     */
    public PopupWindow speedPopWindow;

    public CourseDetailPlayView(Context context) {
        super(context);
    }

    public CourseDetailPlayView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void init(Context context) {
        super.init(context);
        mClReplay = findViewById(R.id.cl_replay);
        mBtnNext = findViewById(R.id.btn_next);
        mQuestionText = findViewById(R.id.question_text);
        mTvSpeed = findViewById(R.id.tv_speed);
        mTvAudition = findViewById(R.id.tv_audition);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            mTvSpeed.setVisibility(View.VISIBLE);
        } else {
            mTvSpeed.setVisibility(View.GONE);
        }
        //重新播放点击
        replayTextView.setOnClickListener(v -> {
            mCurrentBean.setLastDuration(0L);
            if (mOnStateChangeInterface != null) {
                mOnStateChangeInterface.uploadPlayProgress(mCurrentBean, 0L);
            }
            resetProgressAndTime();
            mRetryBtn.performClick();
        });
        //速度点击
        mTvSpeed.setOnClickListener(v -> {
            if (mFullShow) {
                onCLickUiToggleToClear();
                LayoutInflater inflater = (LayoutInflater) jzvdContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
                final LinearLayout layout = (LinearLayout) inflater.inflate(R.layout.course_detail_play_view_speed, null);
                layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

                OnClickListener mQualityListener = v1 -> {
                    int index = (int) v1.getTag();
                    setSpeed(index);
                    if (speedPopWindow != null) {
                        speedPopWindow.dismiss();
                    }
                };

                for (int j = 0; j < mSpeeds.length; j++) {
                    TextView speedItem = (TextView) View.inflate(jzvdContext, R.layout.jz_layout_clarity_item, null);
                    speedItem.setText(mSpeeds[j] + "X");
                    speedItem.setTag(j);
                    speedItem.setTextColor(Color.WHITE);
                    speedItem.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15);
                    layout.addView(speedItem, 0);
                    speedItem.setOnClickListener(mQualityListener);
                    if (j == mCurrentSpeedIndex) {
                        speedItem.setTextColor(Color.parseColor("#007AFF"));
                    }
                }

                speedPopWindow = new PopupWindow(layout, JZUtils.dip2px(jzvdContext, 240), LayoutParams.MATCH_PARENT, true);
                speedPopWindow.setContentView(layout);
                speedPopWindow.setAnimationStyle(R.style.pop_animation);
                speedPopWindow.showAtLocation(findViewById(R.id.view_bottom_right), Gravity.END, 0, -100);
            } else {
                if (mOnStateChangeInterface != null) {
                    mOnStateChangeInterface.onSpeedClick();
                }
            }
        });
    }

    /**
     * 设置当前播放数据
     *
     * @param detailCatalogueBean
     * @param bean                当前数据
     */
    public void setCurrentShowData(CourseDetailCatalogueBean detailCatalogueBean, CourseDetailCatalogueBean.Item bean,
            OnClickListener toQuestionOnClick, OnClickListener nextOnClick) {
        mDetailCatalogueBean = detailCatalogueBean;
        mCurrentBean = bean;
        if (bean.getVIdeoUrl() == null) {
            return;
        }
        setUp(bean.getVIdeoUrl(), bean.getTitle(),SCREEN_NORMAL, JZMediaAliyun.class);
        startVideo();
        //重置从头开始，后续再设置进度
        replayTextView.performClick();
        //是否显示试听
        if (detailCatalogueBean.getVip()) {
            mTvAudition.setVisibility(View.GONE);
        } else {
            mTvAudition.setVisibility(View.VISIBLE);
        }
        //设置进度
        if (bean.getLastDuration() != null) {
            try {
                mediaInterface.seekTo(bean.getLastDuration() * 1000);
            } catch (Exception ignore) {
            }
        }
        //下一节处理
        if (0 == bean.getHasNext() || !detailCatalogueBean.getVip()) {
            mBtnNext.setVisibility(View.GONE);
        } else {
            mBtnNext.setVisibility(View.VISIBLE);
            mBtnNext.setOnClickListener(nextOnClick);
        }
        //做题点击
        mQuestionText.setOnClickListener(toQuestionOnClick);
    }


    public CourseDetailCatalogueBean.Item getCurrentBean() {
        return mCurrentBean;
    }

    /**
     * 设置回调
     */
    public void setOnStateChangeInterface(OnStateChangeInterface mOnStateChangeInterface) {
        this.mOnStateChangeInterface = mOnStateChangeInterface;
    }

    @Override
    public void setAllControlsVisiblity(int topCon, int bottomCon, int startBtn, int loadingPro, int posterImg, int bottomPro, int retryLayout) {
        super.setAllControlsVisiblity(topCon, bottomCon, startBtn, loadingPro, posterImg, bottomPro, retryLayout);
        mTvAudition.setVisibility(mDetailCatalogueBean != null && mDetailCatalogueBean.getVip() ? View.GONE : bottomCon);
    }

    @Override
    public void dissmissControlView() {
        super.dissmissControlView();
        if (state != STATE_NORMAL && state != STATE_ERROR && state != STATE_AUTO_COMPLETE) {
            post(() -> {
                mTvAudition.setVisibility(View.INVISIBLE);
            });
        }
    }

    @Override
    public void updateStartImage() {
        super.updateStartImage();
        if (state == STATE_PLAYING) {
            mClReplay.setVisibility(GONE);
            startButton.setVisibility(VISIBLE);
        } else if (state == STATE_ERROR) {
            mClReplay.setVisibility(GONE);
            startButton.setVisibility(VISIBLE);
        } else if (state == STATE_AUTO_COMPLETE) {
            mClReplay.setVisibility(VISIBLE);
            startButton.setVisibility(GONE);
            if (mOnStateChangeInterface != null) {
                mOnStateChangeInterface.onAutoComplete();
            }
        } else {
            mClReplay.setVisibility(GONE);
            startButton.setVisibility(VISIBLE);
        }
    }

    @Override
    public void setVolumeClose() {
        super.setVolumeClose();
        if (mOnStateChangeInterface != null) {
            mOnStateChangeInterface.onVolumeClose();
        }
    }

    @Override
    public void setVolumeOpen() {
        super.setVolumeOpen();
        if (mOnStateChangeInterface != null) {
            mOnStateChangeInterface.onVolumeOpen();
        }
    }

    @Override
    public void setScreenNormal() {
        super.setScreenNormal();
        mFullShow = false;
        batteryTimeLayout.setVisibility(View.GONE);
    }

    @Override
    public void onStatePlaying() {
        if (state == STATE_PREPARED) {//如果是准备完成视频后第一次播放，设置要播放的进度。
            seekToInAdvance = mCurrentBean.getLastDuration() * 1000;
        }
        super.onStatePlaying();
    }

    /**
     * 继续视频播放
     */
    public void resumeVideo(){
        if (state == STATE_PAUSE) {
            clickStart();
        }
    }
    /**
     * 暂停视频播放
     */
    public void pauseVideo(){
        if (state == STATE_PLAYING) {
            clickStart();
        }
    }

    public void setScreenFullscreen() {
        super.setScreenFullscreen();
        mFullShow = true;
    }

    /**
     * 上传播放进度
     */
    public void uploadPlayProgress() {
        try {
            if (mOnStateChangeInterface != null) {
                if(state == STATE_AUTO_COMPLETE){
                    mOnStateChangeInterface.uploadPlayProgress(mCurrentBean, timeStrToMill(mCurrentBean.getTotalDuration()));
                }else {
                    mOnStateChangeInterface.uploadPlayProgress(mCurrentBean, mediaInterface.getCurrentPosition());
                }
            }
        }catch (Exception ignore){}
    }

    @Override
    public int getLayoutId() {
        return R.layout.course_detail_play_view;
    }

    /**
     * 获取速度列表
     *
     * @return 速度列表
     */
    public float[] getSpeeds() {
        return mSpeeds;
    }

    /**
     * 获取当前倍速下标
     *
     * @return 倍速下标
     */
    public int getCurrentSpeedIndex() {
        return mCurrentSpeedIndex;
    }

    /**
     * 设置倍速
     *
     * @param index 倍速的下标
     */
    @SuppressLint("SetTextI18n")
    public void setSpeed(int index) {
        mCurrentSpeedIndex = index;
        mediaInterface.setSpeed(mSpeeds[mCurrentSpeedIndex]);
        if (1F == mSpeeds[mCurrentSpeedIndex]) {
            mTvSpeed.setText("倍速");
        } else {
            mTvSpeed.setText(mSpeeds[mCurrentSpeedIndex] + "X");
        }
        if (jzDataSource.objects == null) {
            Object[] object = {mCurrentSpeedIndex};
            jzDataSource.objects = object;
        } else {
            jzDataSource.objects[0] = mCurrentSpeedIndex;
        }
    }

    /**
     * 时间字符串转毫秒值
     *
     * @param time 时间字符串
     * @return 毫秒值
     */
    private Long timeStrToMill(String time) {
        String[] split = time.split(":");
        return (Long.parseLong(split[0]) * 60 + Long.parseLong(split[1])) * 1000L;
    }

    /**
     * 状态改变回调
     */
    public interface OnStateChangeInterface {
        /**
         * 修改播放速度
         *
         * @param j     下标
         * @param speed 速度
         */
        void changeSpeedToValue(int j, float speed);

        /**
         * 上传播放进度
         *
         * @param item     当前播放实例
         * @param millTime 时间戳
         */
        void uploadPlayProgress(CourseDetailCatalogueBean.Item item, long millTime);

        /**
         * 自动完成
         */
        void onAutoComplete();

        /**
         * 倍速点击
         */
        void onSpeedClick();

        /**
         * 打开声音
         */
        void onVolumeOpen();

        /**
         * 声音关闭
         */
        void onVolumeClose();
    }
}
