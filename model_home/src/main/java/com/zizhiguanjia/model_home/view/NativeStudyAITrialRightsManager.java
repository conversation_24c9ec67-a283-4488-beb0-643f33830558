package com.zizhiguanjia.model_home.view;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.wb.lib_arch.base.IFragment;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.MiddleAdsBean;
import com.zizhiguanjia.model_home.databinding.HomeFlutterStyleLayoutBinding;
import com.zizhiguanjia.model_home.ui.HomeNativeActivity;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;

/**
 * 负责管理NativeStudyAIFragment的试用权益功能
 */
public class NativeStudyAITrialRightsManager {
    private final HomeFlutterStyleLayoutBinding binding;
    private final StudyViewModel studyViewModel;
    private final Activity activity;
    private final LifecycleOwner lifecycleOwner;

    public NativeStudyAITrialRightsManager(@NonNull HomeFlutterStyleLayoutBinding binding,
                                           @NonNull StudyViewModel studyViewModel,
                                           @NonNull Activity activity,
                                           @NonNull LifecycleOwner lifecycleOwner) {
        this.binding = binding;
        this.studyViewModel = studyViewModel;
        this.activity = activity;
        this.lifecycleOwner = lifecycleOwner;
        
        // 设置升级题库按钮点击事件
        setupUpgradeButtonClickListener();
        
        // 设置观察者
        setupTrialRightsObserver();
    }
    
    /**
     * 设置试用权益观察者
     */
    private void setupTrialRightsObserver() {
        LogUtils.e("NativeStudyAITrialRightsManager - 设置试用权益观察者");
        
        // 观察HomeBean数据变化，从中提取试用权益数据
        studyViewModel.getHomeBean().observe(lifecycleOwner, homeBean -> {
            if (homeBean != null && homeBean.getMiddleAds() != null) {
                LogUtils.e("NativeStudyAITrialRightsManager - 收到新的试用权益数据");
                updateTrialRightsView(homeBean.getMiddleAds());
            }
        });
    }
    
    /**
     * 设置升级题库按钮点击事件
     */
    private void setupUpgradeButtonClickListener() {
        if (binding == null) return;
        
        // 获取试用权益卡片
        LinearLayout layoutAd = binding.getRoot().findViewById(R.id.layout_ad);
        if (layoutAd == null) {
            LogUtils.e("NativeStudyAITrialRightsManager - setupUpgradeButtonClickListener - 找不到试用权益卡片");
            return;
        }
        
        // 获取升级题库按钮
        Button btnUpgradeDatabase = layoutAd.findViewById(R.id.btn_upgrade_database);
        if (btnUpgradeDatabase == null) {
            LogUtils.e("NativeStudyAITrialRightsManager - setupUpgradeButtonClickListener - 找不到升级题库按钮");
            return;
        }
        
        btnUpgradeDatabase.setOnClickListener(v -> {
//            if (!NoDoubleClickUtils.isDoubleClick()) {
                // 检查用户登录状态
                if (AccountHelper.checkLoginState()) {
                    // 获取当前的MiddleAds数据
                    MiddleAdsBean middleAds = null;
                    if (studyViewModel != null && studyViewModel.getHomeBean().getValue() != null) {
                        middleAds = studyViewModel.getHomeBean().getValue().getMiddleAds();
                    }

                    if (middleAds != null && middleAds.getUrl() != null && !middleAds.getUrl().isEmpty()) {
                        // 使用返回的URL链接
                        ToastUtils.normal("升级题库");
                        
                        // 如果activity是HomeNativeActivity，则使用Fragment跳转
                        if (activity instanceof HomeNativeActivity) {
                            Bundle args = new Bundle();
//                            args.putString("routh", "home");
//                            args.putString("url", middleAds.getUrl());
//                            args.putInt("payType", 1);
//                            args.putString("payRouthParams", PayRouthConfig.PAY_UPDATE);
                            IFragment iFragment = CommonHelper.showCommonWeb();
                            iFragment.initArguments().putString("routh", "home");
                            iFragment.initArguments().putString("url", middleAds.getUrl());
                            iFragment.initArguments().putString("payType", "1");
                            iFragment.initArguments().putString("payRouthParams", PayRouthConfig.PAY_UPDATE);
                            ((HomeNativeActivity) activity).startFragment(iFragment);
                        }
                    } else {
                        // 如果URL为空，使用默认的购买对话框
                        ToastUtils.normal("升级题库");
                        MessageHelper.openNoPremissBuyDialog(activity, true, "PAY_UPDATE");
                    }
                }
//                else {
//                    // 直接跳转到登录页面
//                    if (activity instanceof HomeNativeActivity) {
//                        ToastUtils.normal("请先登录");
//                        ((HomeNativeActivity) activity).startFragment(AccountHelper.showAccountLogin());
////                        AccountHelper.checkLoginState();
//                    }
//                }
//            }
        });
        
        LogUtils.e("NativeStudyAITrialRightsManager - 设置升级题库按钮点击事件完成");
    }

    /**
     * 更新试用权益视图
     *
     * @param middleAds 试用权益数据
     */
    public void updateTrialRightsView(MiddleAdsBean middleAds) {
        if (binding == null) return;

        try {
            // 获取试用权益卡片
            LinearLayout layoutAd = binding.getRoot().findViewById(R.id.layout_ad);
            if (layoutAd == null) {
                LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 找不到试用权益卡片");
                return;
            }

            // 从StudyViewModel获取HomeBean，检查isShowAds属性
            boolean shouldShowAds = true; // 默认显示
            if (studyViewModel != null && studyViewModel.getHomeBean().getValue() != null) {
                shouldShowAds = studyViewModel.getHomeBean().getValue().isShowAds();
                LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - HomeBean中isShowAds值: " + shouldShowAds);
            }

            // 如果isShowAds为false，隐藏试用权益卡片并返回
            if (!shouldShowAds) {
                layoutAd.setVisibility(View.GONE);
                LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 根据isShowAds隐藏试用权益卡片");
                return;
            }

            // 获取内容文本视图
            TextView tvAdContent = layoutAd.findViewById(R.id.tv_ad_content);
            if (tvAdContent == null) {
                LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 找不到试用权益内容文本视图");
                return;
            }

            // 获取标题文本视图（第一个TextView）
            TextView tvAdTitle = (TextView) ((LinearLayout) tvAdContent.getParent()).getChildAt(0);
            if (tvAdTitle == null) {
                LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 找不到试用权益标题文本视图");
                return;
            }

            // 获取升级题库按钮
            Button btnUpgradeDatabase = layoutAd.findViewById(R.id.btn_upgrade_database);
            if (btnUpgradeDatabase == null) {
                LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 找不到升级题库按钮");
                return;
            }

            // 更新标题
            if (!TextUtils.isEmpty(middleAds.getTitle())) {
                tvAdTitle.setText(middleAds.getTitle());
                LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 更新标题: " + middleAds.getTitle());
            }

            // 更新内容
            if (!TextUtils.isEmpty(middleAds.getSubTitle())) {
                // 检查文本是否包含HTML标签
                if (middleAds.getSubTitle().contains("<") && middleAds.getSubTitle().contains(">")) {
                    // 使用Html.fromHtml处理HTML格式文本
                    tvAdContent.setText(android.text.Html.fromHtml(middleAds.getSubTitle()));
                    LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 更新HTML格式内容: " + middleAds.getSubTitle());
                } else {
                    // 普通文本直接设置
                    tvAdContent.setText(middleAds.getSubTitle());
                    LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 更新普通文本内容: " + middleAds.getSubTitle());
                }
            }

            // 更新按钮文本
            if (!TextUtils.isEmpty(middleAds.getBtnTxt())) {
                btnUpgradeDatabase.setText(middleAds.getBtnTxt());
                LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 更新按钮文本: " + middleAds.getBtnTxt());
            }

            // 同时考虑CloseStatus和isShowAds两个条件
            // 如果CloseStatus为1或isShowAds为false，则隐藏试用权益卡片
            layoutAd.setVisibility((middleAds.getCloseStatus() == 1 || !shouldShowAds) ? View.GONE : View.VISIBLE);
            LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 是否显示试用权益卡片: " + 
                      (middleAds.getCloseStatus() != 1 && shouldShowAds));
            
        } catch (Exception e) {
            LogUtils.e("NativeStudyAITrialRightsManager - updateTrialRightsView - 异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 