package com.zizhiguanjia.model_home.navigator;

import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;

public interface HomeActivityNavigator {
    void toGoUserInfo();
    void toGoChoiceCretificate(String addressName,String addressId,boolean tgAddress);
    void toGoChoiceAddress();
    void initFragment();
    void restart(boolean passCertificate);
    void init();
    void initListener();
    void clientOffline();
    String getAcvivityRoute();
    void reshTxtSize(String size);
    
    /**
     * 设置原始标题
     * @param title 标题文本
     */
    void originalTitle(String title);
    
    /**
     * 显示Toast消息
     * @param msg 消息内容
     */
    void showToast(String msg);
    
    /**
     * 显示引导页面
     */
    void showGuide();
    
    /**
     * 显示抖音VIP更新提示对话框
     * @param data 对话框数据
     */
    void showDyVipUpdateHintDialog(DyVipUpdateHintBean data);
    
    /**
     * 刷新首页数据
     * @param passCertificate 是否通过证书验证
     */
    void reshHomeData(boolean passCertificate);
    
    /**
     * 设置顶部标题信息
     * @param show 是否显示标题
     * @param title 标题文本
     */
    void setTopTitleInfo(boolean show, String title);
}
