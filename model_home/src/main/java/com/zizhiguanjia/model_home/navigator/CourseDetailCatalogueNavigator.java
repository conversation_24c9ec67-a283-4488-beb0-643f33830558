package com.zizhiguanjia.model_home.navigator;

import com.caimuhao.rxpicker.bean.ImageItem;
import com.zizhiguanjia.model_home.bean.CourseDetailCatalogueBean;

import java.util.List;

/**
 * 功能作用：课程详情-目录操作
 * 初始注释时间： 2023/11/23 10:41
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public interface CourseDetailCatalogueNavigator {
    void updateCommentImage(List<ImageItem> imageItems);
    void showLoading(boolean isVision);
}
