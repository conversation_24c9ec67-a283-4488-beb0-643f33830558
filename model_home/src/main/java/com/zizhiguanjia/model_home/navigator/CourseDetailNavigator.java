package com.zizhiguanjia.model_home.navigator;

import com.zizhiguanjia.model_home.bean.CourseDetailCatalogueBean;
import com.zizhiguanjia.model_home.bean.CourseDetailGoodsBean;

/**
 * 功能作用：课程详情操作
 * 初始注释时间： 2023/11/23 10:41
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public interface CourseDetailNavigator {
    /**
     * 获取当前视频播放进度
     * @return 播放进度，毫秒值
     */
    Long getCurrentPlayProgress();

    /**
     * 设置显示数据
     *
     * @param bean 实际的课程数据
     */
    void setShowData(CourseDetailCatalogueBean bean);

    /**
     * 设置商品信息
     *
     * @param data 商品信息
     */
    void setShowData(CourseDetailGoodsBean data);

    /**
     * 设置当前要播放的数据
     *
     * @param bean 当前要播放的数据
     * @param reloadBaseData 是否重新加载数据
     */
    void setCurrentShowData(CourseDetailCatalogueBean.Item bean, boolean reloadBaseData);

    /**
     * 打开答题页面
     */
    void openQuestionPage(CourseDetailCatalogueBean.Item childBean);

    /**
     * 加载中显示
     */
    void showLoading(boolean isVision);

    /**
     * 上传播放进度
     */
    void uploadPlayProgress();

}
