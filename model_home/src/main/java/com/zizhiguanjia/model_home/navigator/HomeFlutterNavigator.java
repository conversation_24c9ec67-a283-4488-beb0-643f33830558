package com.zizhiguanjia.model_home.navigator;

import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;

import io.flutter.embedding.android.FlutterView;
import io.flutter.plugin.common.MethodChannel;

public interface HomeFlutterNavigator {
    /**
     * 显示抖音vip更新提醒弹窗
     * @param data 数据
     */
    void showDyVipUpdateHintDialog(DyVipUpdateHintBean data);

    void successMethodChannel(MethodChannel methodChannel);
    void successFlutterView(FlutterView flutterView);
    void showToast(String msg);
    void reshHomeData(boolean passCertificate);
    void setTopTitleInfo(boolean show,String title);
    String initRoute();
    void showLoading(boolean visition, String msg);
    void showTsDialog(int type);
    void showGuide();
    void closeView();
    void toMainPage(String pid);
}
