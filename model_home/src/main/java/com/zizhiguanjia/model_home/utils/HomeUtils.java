package com.zizhiguanjia.model_home.utils;

import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.bean.ItemsBean;
import com.zizhiguanjia.lib_base.bean.BaseData;

import java.util.List;
import java.util.StringTokenizer;

public class HomeUtils {
    private static HomeUtils homeUtils;
    public static HomeUtils getInstance(){
        if(homeUtils==null){
            synchronized (HomeUtils.class){
                return homeUtils=new HomeUtils();
            }
        }
        return homeUtils;
    }
    public boolean checkSechemValid(String uri) {
        if (uri.startsWith("http") || uri.startsWith("https")) {
            return false;
        } else {
            return true;
        }
    }
    public boolean isWxInstall(){
        final PackageManager packageManager = AppUtils.getApp().getPackageManager();// 获取packagemanager
        List<PackageInfo> pinfo = packageManager.getInstalledPackages(0);// 获取所有已安装程序的包信息
        if (pinfo != null) {
            for (int i = 0; i < pinfo.size(); i++) {
                String pn = pinfo.get(i).packageName;
                if (pn.equals("com.tencent.mm")) {
                    return true;
                }
            }
        }
        return false;
    }
    public  String[] convertStrToArray2(String str) {
        StringTokenizer st = new StringTokenizer(str, "·");
        String[] strArray = new String[st.countTokens()];
        int i = 0;
        while (st.hasMoreTokens()) {
            strArray[i++] = st.nextToken().trim();
        }
        return strArray;
    }
    public BaseData<HomeBean> resetHomeDatas(BaseData<HomeBean> mainBean){
        try {
            String limeCor="<font color='#"+mainBean.getResult().getMiddleAds().getLimitNumColor()+"'>";
            String limePageCor="<font color='#"+mainBean.getResult().getMiddleAds().getLimitPaperNumColor()+"'>";
            String newStr2="";
            //StringUtils.isEmpty(mainBean.getResult().getMiddleAds().getUrl())
            //1 去升级 2去选择
            if(Integer.parseInt(mainBean.getResult().getMiddleAds().getBtnType())!=1){
                String limeCor1="<font color='#"+mainBean.getResult().getMiddleAds().getLimitNumColor()+"'>";
                newStr2=mainBean.getResult().getMiddleAds().getSubTitle().replace(mainBean.getResult().getMiddleAds().getTitle(),limeCor1+mainBean.getResult().getMiddleAds().getTitle()+"</font>");
            }else {
                String newStr1=mainBean.getResult().getMiddleAds().getSubTitle().replace(mainBean.getResult().getMiddleAds().getLimitNum()+"道",limeCor+mainBean.getResult().getMiddleAds().getLimitNum()+"</font>"+"道");
                newStr2=newStr1.replace(mainBean.getResult().getMiddleAds().getLimitPaperNum()+"套",limePageCor+mainBean.getResult().getMiddleAds().getLimitPaperNum()+"</font>"+"套");
            }
            mainBean.getResult().getMiddleAds().setSubTitle(newStr2);
            if(mainBean.getResult().getLives()==null){
            }else {
                if (mainBean.getResult().getLives().getItems()==null||mainBean.getResult().getLives().getItems().size()==0){
                }else{
                    for(ItemsBean itemsBean:mainBean.getResult().getLives().getItems()){
                        mainBean.getResult().getpLives().add(itemsBean);
                    }
                    mainBean.getResult().setLiveVieoType(mainBean.getResult().getLives().getType());
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return mainBean;
    }
}
