package com.zizhiguanjia.model_home.utils;

import android.content.Context;
import android.util.DisplayMetrics;
import android.util.TypedValue;

public class SizeUtils {

    private SizeUtils() {
        // 防止实例化
        throw new AssertionError("No instances.");
    }

    /**
     * 将dp转换为px
     *
     * @param dpValue dp值
     * @param context 上下文
     * @return px值
     */
    public static int dpToPx(float dpValue, Context context) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        return (int) (dpValue * (metrics.densityDpi / DisplayMetrics.DENSITY_DEFAULT));
    }

    /**
     * 将px转换为dp
     *
     * @param pxValue px值
     * @param context 上下文
     * @return dp值
     */
    public static float pxToDp(float pxValue, Context context) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        return pxValue / (metrics.densityDpi / DisplayMetrics.DENSITY_DEFAULT);
    }

    /**
     * 将sp转换为px
     *
     * @param spValue sp值
     * @param context 上下文
     * @return px值
     */
    public static int spToPx(float spValue, Context context) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, spValue, context.getResources().getDisplayMetrics());
    }

    /**
     * 将px转换为sp
     *
     * @param pxValue px值
     * @param context 上下文
     * @return sp值
     */
    public static float pxToSp(float pxValue, Context context) {
        return pxValue / context.getResources().getDisplayMetrics().scaledDensity;
    }

    /**
     * 获取屏幕宽度（单位：px）
     *
     * @param context 上下文
     * @return 屏幕宽度
     */
    public static int getScreenWidth(Context context) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        return metrics.widthPixels;
    }

    /**
     * 获取屏幕高度（单位：px）
     *
     * @param context 上下文
     * @return 屏幕高度
     */
    public static int getScreenHeight(Context context) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        return metrics.heightPixels;
    }

    /**
     * 获取屏幕密度
     *
     * @param context 上下文
     * @return 屏幕密度
     */
    public static float getScreenDensity(Context context) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        return metrics.density;
    }

    /**
     * 获取屏幕密度DPI
     *
     * @param context 上下文
     * @return 屏幕密度DPI
     */
    public static int getScreenDensityDpi(Context context) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        return metrics.densityDpi;
    }
}