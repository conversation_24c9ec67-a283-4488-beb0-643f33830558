//package com.zizhiguanjia.model_home.base;
//
//import android.app.Activity;
//
//import androidx.annotation.NonNull;
//
//import com.wb.lib_utils.utils.MainThreadUtils;
//import com.wb.lib_utils.utils.log.LogUtils;
//import com.zizhiguanjia.model_home.config.ChannelConfig;
//import com.zizhiguanjia.model_home.config.HomeFlutterChannerTpis;
//import com.zizhiguanjia.model_home.config.RouteConfig;
//import com.zizhiguanjia.model_home.listener.IFlutterChanner;
//import com.zizhiguanjia.model_home.listener.IFlutterChannerListener;
//
//import io.flutter.embedding.android.FlutterView;
//import io.flutter.embedding.engine.FlutterEngine;
//import io.flutter.embedding.engine.dart.DartExecutor;
//import io.flutter.plugin.common.MethodCall;
//import io.flutter.plugin.common.MethodChannel;
//
//public abstract class BaseFlutterChanner implements IFlutterChanner {
//    private FlutterEngine flutterEngine;
//    public abstract void initFlutterChannerSuccess(MethodChannel nativeChannel);
//    public abstract void initFlutterViewSuccess(FlutterView flutterView);
//    private MethodChannel flutterChannel;
//    @Override
//    public void jumpByTips(@NonNull MethodCall methodCall, @NonNull MethodChannel.Result result, IFlutterChannerListener iFlutterChannerListener) {
//        MainThreadUtils.post(new Runnable() {
//            @Override
//            public void run() {
//                switch (methodCall.method) {
//                    case HomeFlutterChannerTpis.HOME_FLUTTER_PAGEFINSH:
//                        iFlutterChannerListener.getHttpsData(result);
//                        break;
//                    case HomeFlutterChannerTpis.HOME_FLUTTER_SAVELISTPAGE:
//                        iFlutterChannerListener.goToPageByRoute(RouteConfig.ROUTE_SAVE);
//                        break;
//                    case HomeFlutterChannerTpis.HOME_FLUTTER_ERRORLISTPAGE:
//                        iFlutterChannerListener.goToPageByRoute(RouteConfig.ROUTE_ERROR);
//                        break;
//                }
//            }
//        });
//    }
//    @Override
//    public FlutterEngine getFlutterEngine() {
//        return flutterEngine;
//    }
//    @Override
//    public void clearFluterEngine() {
//        flutterEngine=null;
//    }
//    @Override
//    public void nativeToFlutterMouth(String str,String json) {
//        LogUtils.e("看下flutter即将传递"+str+"***"+(json==null?null:json));
//        if(flutterChannel==null){
//            flutterChannel= new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), ChannelConfig.CHANNEL_FLUTTER);
//        }
//        flutterChannel.invokeMethod(str, json==null?null:json);
//    }
//    @Override
//    public void initFlutterEngine(String raouth, Activity activity) {
//        this.flutterEngine=new FlutterEngine(activity);
//        flutterEngine.getNavigationChannel().setInitialRoute(raouth);
//        flutterEngine.getDartExecutor().executeDartEntrypoint(DartExecutor.DartEntrypoint.createDefault());
//        initFlutterView(activity);
//    }
//
//    @Override
//    public void initMethodChannel(FlutterEngine flutterEngine) {
//        MethodChannel nativeChannel = new MethodChannel(flutterEngine.getDartExecutor()
//                .getBinaryMessenger(), ChannelConfig.CHANNEL_NATIVE);
//        initFlutterChannerSuccess(nativeChannel);
//    }
//
//    @Override
//    public void initFlutterView(Activity activity) {
//        FlutterView flutterView = new FlutterView(activity);
//        if (flutterEngine != null) {
//            flutterView.attachToFlutterEngine(flutterEngine);
//        }
//        initFlutterViewSuccess(flutterView);
//        initMethodChannel(flutterEngine);
//    }
//}
