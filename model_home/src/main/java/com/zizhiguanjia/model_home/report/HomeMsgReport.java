package com.zizhiguanjia.model_home.report;

import com.zizhiguanjia.model_home.listener.IMsgChanner;

public class HomeMsgReport {
    private static HomeMsgReport homeMsgReport;
    private IMsgChanner iMsgChanner;
    public static HomeMsgReport getInstance(){
        if(homeMsgReport==null){
            synchronized (HomeMsgReport.class){
                return homeMsgReport=new HomeMsgReport();
            }
        }
        return homeMsgReport;
    }
    public HomeMsgReport setMsgType(IMsgChanner iMsgChanner){
        this.iMsgChanner=iMsgChanner;
        return this;
    }

    public IMsgChanner getMsgChanner() {
        return iMsgChanner;
    }
}
