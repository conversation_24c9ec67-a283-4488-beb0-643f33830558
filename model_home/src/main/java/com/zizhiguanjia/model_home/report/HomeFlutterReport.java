package com.zizhiguanjia.model_home.report;

import com.zizhiguanjia.model_home.listener.IFlutterChanner;

public class HomeFlutterReport {
    private static HomeFlutterReport homeFlutterReport;
    private IFlutterChanner iFlutterChanner;
    public static HomeFlutterReport getInstance() {
        if (homeFlutterReport == null) {
            synchronized (HomeFlutterReport.class) {
                if (homeFlutterReport == null) {
                    homeFlutterReport = new HomeFlutterReport();
                }
            }
        }
        return homeFlutterReport;
    }
    public HomeFlutterReport setUploadType(IFlutterChanner logUpload) {
        iFlutterChanner = logUpload;
        return this;
    }

    public IFlutterChanner getUploadType() {
        return iFlutterChanner;
    }

}
