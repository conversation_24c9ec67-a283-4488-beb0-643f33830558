package com.zizhiguanjia.model_home.bean;

import java.util.List;

public class FlutterCounponsBean {

    private List<CouponsBean> Coupons;

    public List<CouponsBean> getCoupons() {
        return Coupons;
    }

    public void setCoupons(List<CouponsBean> Coupons) {
        this.Coupons = Coupons;
    }

    public static class CouponsBean {
        /**
         * Title :
         * AmountMoney : 30
         * TermOfValidity :
         * ExpireTime :
         * CouponType : 1
         */

        private String Title;
        private int AmountMoney;
        private String TermOfValidity;
        private String ExpireTime;
        private int CouponType;

        public String getTitle() {
            return Title;
        }

        public void setTitle(String Title) {
            this.Title = Title;
        }

        public int getAmountMoney() {
            return AmountMoney;
        }

        public void setAmountMoney(int AmountMoney) {
            this.AmountMoney = AmountMoney;
        }

        public String getTermOfValidity() {
            return TermOfValidity;
        }

        public void setTermOfValidity(String TermOfValidity) {
            this.TermOfValidity = TermOfValidity;
        }

        public String getExpireTime() {
            return ExpireTime;
        }

        public void setExpireTime(String ExpireTime) {
            this.ExpireTime = ExpireTime;
        }

        public int getCouponType() {
            return CouponType;
        }

        public void setCouponType(int CouponType) {
            this.CouponType = CouponType;
        }
    }
}
