package com.zizhiguanjia.model_home.bean;

import java.util.List;

/**
 * 功能作用：课程详情-目录信息
 * 初始注释时间： 2023/11/22 20:58
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class CourseDetailCatalogueBean {
    /**
     * 简介h5/共18节
     */
    private String Desc;
    /**
     * 是否有课程目录vip权限
     */
    private Boolean IsVip;
    private List<Item> Items;
    /**
     * 定位下标节
     */
    private Integer Location;

    public String getDesc() {
        return Desc;
    }

    public void setDesc(String Desc) {
        this.Desc = Desc;
    }

    public Boolean getVip() {
        return IsVip;
    }

    public void setVip(Boolean vip) {
        IsVip = vip;
    }

    public List<Item> getItems() {
        return Items;
    }

    public void setItems(List<Item> Items) {
        this.Items = Items;
    }

    public Integer getLocation() {
        return Location;
    }

    public void setLocation(Integer Location) {
        this.Location = Location;
    }


    public static class Item {
        /**
         * 是否可播放
         */
        private Boolean CanGo;
        private List<Item> Childs;
        /**
         * 课程唯一编号
         */
        private String Code;
        /**
         * 是否有下一节：1-有，0-无
         */
        private Integer HasNext;
        private Long Id;
        private Long Index;
        private Boolean IsComplete;
        /**
         * 上次播放时长/秒
         */
        private Long LastDuration;
        private Long Lv;
        private String ParentCode;
        /**
         * 做题进度下标
         */
        private Long QuestionLocation;
        private Long Sort;
        private Long Timeout;
        /**
         * 课程标题
         */
        private String Title;
        /**
         * 视频总时长/秒
         */
        private String TotalDuration;
        /**
         * 未做题数量
         */
        private Long UnDoQuestionCount;
        private String VideoId;
        /**
         * 视频播放地址
         */
        private String VideoUrl;

        public Boolean getCanGo() {
            return CanGo;
        }

        public void setCanGo(Boolean CanGo) {
            this.CanGo = CanGo;
        }

        public List<Item> getChilds() {
            return Childs;
        }

        public void setChilds(List<Item> Childs) {
            this.Childs = Childs;
        }

        public String getCode() {
            return Code;
        }

        public void setCode(String Code) {
            this.Code = Code;
        }

        public Integer getHasNext() {
            return HasNext;
        }

        public void setHasNext(Integer HasNext) {
            this.HasNext = HasNext;
        }

        public Long getId() {
            return Id;
        }

        public void setId(Long Id) {
            this.Id = Id;
        }

        public Long getIndex() {
            return Index;
        }

        public void setIndex(Long Index) {
            this.Index = Index;
        }

        public Boolean getComplete() {
            return IsComplete;
        }

        public void setComplete(Boolean complete) {
            IsComplete = complete;
        }

        public Long getLastDuration() {
            return LastDuration;
        }

        public void setLastDuration(Long LastDuration) {
            this.LastDuration = LastDuration;
        }

        public Long getLv() {
            return Lv;
        }

        public void setLv(Long Lv) {
            this.Lv = Lv;
        }

        public String getParentCode() {
            return ParentCode;
        }

        public void setParentCode(String ParentCode) {
            this.ParentCode = ParentCode;
        }

        public Long getQuestionLocation() {
            return QuestionLocation;
        }

        public void setQuestionLocation(Long QuestionLocation) {
            this.QuestionLocation = QuestionLocation;
        }

        public Long getSort() {
            return Sort;
        }

        public void setSort(Long Sort) {
            this.Sort = Sort;
        }

        public Long getTimeout() {
            return Timeout;
        }

        public void setTimeout(Long Timeout) {
            this.Timeout = Timeout;
        }

        public String getTitle() {
            return Title;
        }

        public void setTitle(String Title) {
            this.Title = Title;
        }

        public String getTotalDuration() {
            return TotalDuration;
        }

        public void setTotalDuration(String TotalDuration) {
            this.TotalDuration = TotalDuration;
        }

        public Long getUnDoQuestionCount() {
            return UnDoQuestionCount;
        }

        public void setUnDoQuestionCount(Long UnDoQuestionCount) {
            this.UnDoQuestionCount = UnDoQuestionCount;
        }

        public String getVIdeoId() {
            return VideoId;
        }

        public void setVIdeoId(String VideoId) {
            this.VideoId = VideoId;
        }

        public String getVIdeoUrl() {
            return VideoUrl;
        }

        public void setVIdeoUrl(String VideoUrl) {
            this.VideoUrl = VideoUrl;
        }

    }
}
