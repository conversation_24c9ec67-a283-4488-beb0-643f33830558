package com.zizhiguanjia.model_home.bean;

import java.util.List;

public class LivesBean {
    /**
     * Items : [{"LiveCover":"default.jpg","LiveId":345,"Status":2,"StatusStr":"直播中","Title":"山西安全员考前集训直播","LivePlayedCount":0,"LiveSubscribedCount":0,"LiveUrl":null,"LiveStartTime":"07-08 02:00","LiveEndTime":"07-08 22:00","Sort":3,"IsOrder":false,"TeacherName":""},{"LiveCover":"default.jpg","LiveId":456,"Status":1,"StatusStr":"预约中","Title":"河南安全员考前冲刺直播","LivePlayedCount":0,"LiveSubscribedCount":0,"LiveUrl":"http://aqy.zizhiguanjia.net","LiveStartTime":"07-09 08:00","LiveEndTime":"07-09 22:00","Sort":4,"IsOrder":false,"TeacherName":""},{"LiveCover":"default.jpg","LiveId":567,"Status":1,"StatusStr":"预约中","Title":"山东安全员易错题解析直播","LivePlayedCount":0,"LiveSubscribedCount":0,"LiveUrl":"http://aqy.zizhiguanjia.net","LiveStartTime":"07-10 08:00","LiveEndTime":"07-10 22:00","Sort":5,"IsOrder":false,"TeacherName":""}]
     * Type : 2
     */

    private int Type;
    private List<ItemsBean> Items;

    public int getType() {
        return Type;
    }

    public void setType(int Type) {
        this.Type = Type;
    }

    public List<ItemsBean> getItems() {
        return Items;
    }

    public void setItems(List<ItemsBean> Items) {
        this.Items = Items;
    }
}
