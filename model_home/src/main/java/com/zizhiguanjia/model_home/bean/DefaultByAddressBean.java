package com.zizhiguanjia.model_home.bean;

import java.util.List;

public class DefaultByAddressBean {

    private String DefaultArea;
    private String DefaultAreaId;

    public String getDefaultAreaId() {
        return DefaultAreaId;
    }

    public void setDefaultAreaId(String defaultAreaId) {
        DefaultAreaId = defaultAreaId;
    }

    private List<RecordsBean> Records;
    public String getDefaultArea() {
        return DefaultArea;
    }
    public void setDefaultArea(String DefaultArea) {
        this.DefaultArea = DefaultArea;
    }
    public List<RecordsBean> getRecords() {
        return Records;
    }
    public void setRecords(List<RecordsBean> Records) {
        this.Records = Records;
    }
    public static class RecordsBean {
        /**
         * Id : 1100
         * Name : 安全员
         * Icon : null
         * Childs : [{"Id":1101,"Name":"A证","Icon":null,"Childs":null},{"Id":1102,"Name":"B证","Icon":null,"Childs":null},{"Id":1106,"Name":"C证","Icon":null,"Childs":null}]
         */

        private int Id;
        private String Name;
        private Object Icon;
        private List<ChildsBean> Childs;

        public int getId() {
            return Id;
        }

        public void setId(int Id) {
            this.Id = Id;
        }

        public String getName() {
            return Name;
        }

        public void setName(String Name) {
            this.Name = Name;
        }

        public Object getIcon() {
            return Icon;
        }

        public void setIcon(Object Icon) {
            this.Icon = Icon;
        }

        public List<ChildsBean> getChilds() {
            return Childs;
        }

        public void setChilds(List<ChildsBean> Childs) {
            this.Childs = Childs;
        }

        public static class ChildsBean {
            /**
             * Id : 1101
             * Name : A证
             * Icon : null
             * Childs : null
             */

            private int Id;
            private String Name;
            private Object Icon;
            private Object Childs;

            public int getId() {
                return Id;
            }

            public void setId(int Id) {
                this.Id = Id;
            }

            public String getName() {
                return Name;
            }

            public void setName(String Name) {
                this.Name = Name;
            }

            public Object getIcon() {
                return Icon;
            }

            public void setIcon(Object Icon) {
                this.Icon = Icon;
            }

            public Object getChilds() {
                return Childs;
            }

            public void setChilds(Object Childs) {
                this.Childs = Childs;
            }
        }
    }
}
