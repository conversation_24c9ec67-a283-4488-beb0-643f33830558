package com.zizhiguanjia.model_home.bean;

import java.util.List;

public class CommonLivePreBean {

    private PreviewBean Lives;
    private List<PreviewBean> Preview;
    private List<PreviewBean> Playback;

    public PreviewBean getLives() {
        return Lives;
    }

    public void setLives(PreviewBean Lives) {
        this.Lives = Lives;
    }

    public List<PreviewBean> getPreview() {
        return Preview;
    }

    public void setPreview(List<PreviewBean> Preview) {
        this.Preview = Preview;
    }

    public List<PreviewBean> getPlayback() {
        return Playback;
    }

    public void setPlayback(List<PreviewBean> Playback) {
        this.Playback = Playback;
    }

    public static class PreviewBean {
        /**
         * LiveCover : default.jpg
         * LiveId : 345
         * Status : 1
         * StatusStr : 预约中
         * Title : 山西安全员考前集训直播
         * LivePlayedCount : 0
         * LiveSubscribedCount : 0
         * LiveUrl : http://aqy.zizhiguanjia.net
         * LiveStartTime : 07-08 20:00
         * LiveEndTime : 07-08 22:00
         * Sort : 3
         * IsOrder : false
         * TeacherName : null
         */

        private String LiveCover;
        private int LiveId;
        private int Status;
        private String StatusStr;
        private String Title;
        private int LivePlayedCount;
        private int LiveSubscribedCount;
        private String LiveUrl;
        private String LiveStartTime;
        private String LiveEndTime;
        private int Sort;
        private boolean IsOrder;
        private String TeacherName;
        private long AreaId;
        private boolean CanGo;
        public boolean isCanGo() {
            return CanGo;
        }
        public void setCanGo(boolean canGo) {
            CanGo = canGo;
        }

        public boolean isOrder() {
            return IsOrder;
        }

        public void setOrder(boolean order) {
            IsOrder = order;
        }

        public long getAreaId() {
            return AreaId;
        }

        public void setAreaId(long areaId) {
            AreaId = areaId;
        }

        public String getLiveCover() {
            return LiveCover;
        }

        public void setLiveCover(String LiveCover) {
            this.LiveCover = LiveCover;
        }

        public int getLiveId() {
            return LiveId;
        }

        public void setLiveId(int LiveId) {
            this.LiveId = LiveId;
        }

        public int getStatus() {
            return Status;
        }

        public void setStatus(int Status) {
            this.Status = Status;
        }

        public String getStatusStr() {
            return StatusStr;
        }

        public void setStatusStr(String StatusStr) {
            this.StatusStr = StatusStr;
        }

        public String getTitle() {
            return Title;
        }

        public void setTitle(String Title) {
            this.Title = Title;
        }

        public int getLivePlayedCount() {
            return LivePlayedCount;
        }

        public void setLivePlayedCount(int LivePlayedCount) {
            this.LivePlayedCount = LivePlayedCount;
        }

        public int getLiveSubscribedCount() {
            return LiveSubscribedCount;
        }

        public void setLiveSubscribedCount(int LiveSubscribedCount) {
            this.LiveSubscribedCount = LiveSubscribedCount;
        }

        public String getLiveUrl() {
            return LiveUrl;
        }

        public void setLiveUrl(String LiveUrl) {
            this.LiveUrl = LiveUrl;
        }

        public String getLiveStartTime() {
            return LiveStartTime;
        }

        public void setLiveStartTime(String LiveStartTime) {
            this.LiveStartTime = LiveStartTime;
        }

        public String getLiveEndTime() {
            return LiveEndTime;
        }

        public void setLiveEndTime(String LiveEndTime) {
            this.LiveEndTime = LiveEndTime;
        }

        public int getSort() {
            return Sort;
        }

        public void setSort(int Sort) {
            this.Sort = Sort;
        }

        public boolean isIsOrder() {
            return IsOrder;
        }

        public void setIsOrder(boolean IsOrder) {
            this.IsOrder = IsOrder;
        }

        public String getTeacherName() {
            return TeacherName;
        }

        public void setTeacherName(String TeacherName) {
            this.TeacherName = TeacherName;
        }
    }
}
