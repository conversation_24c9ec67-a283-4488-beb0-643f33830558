package com.zizhiguanjia.model_home.bean;

/**
 * 功能作用：抖音vip更新提示实体
 * 初始注释时间： 2023/12/20 15:09
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class DyVipUpdateHintBean {
    private String AreaMajor;
    private String OrderSource;
    private String Phone;
    private String UpgradeReminder;

    //下面三个是本地设置的
    private String majorPid;
    private String majorId;
    private String areaPid;

    public String getMajorPid() {
        return majorPid;
    }

    public void setMajorPid(String majorPid) {
        this.majorPid = majorPid;
    }

    public String getMajorId() {
        return majorId;
    }

    public void setMajorId(String majorId) {
        this.majorId = majorId;
    }

    public String getAreaPid() {
        return areaPid;
    }

    public void setAreaPid(String areaPid) {
        this.areaPid = areaPid;
    }

    public String getAreaMajor() {
        return AreaMajor;
    }

    public void setAreaMajor(String areaMajor) {
        AreaMajor = areaMajor;
    }

    public String getOrderSource() {
        return OrderSource;
    }

    public void setOrderSource(String orderSource) {
        OrderSource = orderSource;
    }

    public String getPhone() {
        return Phone;
    }

    public void setPhone(String phone) {
        Phone = phone;
    }

    public String getUpgradeReminder() {
        return UpgradeReminder;
    }

    public void setUpgradeReminder(String upgradeReminder) {
        UpgradeReminder = upgradeReminder;
    }
}
