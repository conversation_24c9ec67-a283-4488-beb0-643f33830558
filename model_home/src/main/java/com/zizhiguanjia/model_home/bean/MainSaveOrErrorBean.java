package com.zizhiguanjia.model_home.bean;

import java.util.List;

public class MainSaveOrErrorBean {
    private int TotalCount;
    private int TodayCount;
    private int EliminateCount;
    private boolean HasWrongRecord;
    private boolean EliminateSwitch;
    private boolean IsHaveListCode;
    private List<ListCodeQuestionRecordsBean> ListCodeQuestionRecords;
    private List<SpecialQuestionRecordsBean> SpecialQuestionRecords;
    public int getTotalCount() {
        return TotalCount;
    }

    public void setTotalCount(int TotalCount) {
        this.TotalCount = TotalCount;
    }

    public int getTodayCount() {
        return TodayCount;
    }

    public void setTodayCount(int TodayCount) {
        this.TodayCount = TodayCount;
    }

    public int getEliminateCount() {
        return EliminateCount;
    }

    public void setEliminateCount(int EliminateCount) {
        this.EliminateCount = EliminateCount;
    }

    public boolean isHasWrongRecord() {
        return HasWrongRecord;
    }

    public void setHasWrongRecord(boolean HasWrongRecord) {
        this.HasWrongRecord = HasWrongRecord;
    }

    public boolean isEliminateSwitch() {
        return EliminateSwitch;
    }

    public void setEliminateSwitch(boolean EliminateSwitch) {
        this.EliminateSwitch = EliminateSwitch;
    }

    public boolean isIsHaveListCode() {
        return IsHaveListCode;
    }

    public void setIsHaveListCode(boolean IsHaveListCode) {
        this.IsHaveListCode = IsHaveListCode;
    }

    public List<ListCodeQuestionRecordsBean> getListCodeQuestionRecords() {
        return ListCodeQuestionRecords;
    }

    public void setListCodeQuestionRecords(List<ListCodeQuestionRecordsBean> ListCodeQuestionRecords) {
        this.ListCodeQuestionRecords = ListCodeQuestionRecords;
    }

    public List<SpecialQuestionRecordsBean> getSpecialQuestionRecords() {
        return SpecialQuestionRecords;
    }

    public void setSpecialQuestionRecords(List<SpecialQuestionRecordsBean> SpecialQuestionRecords) {
        this.SpecialQuestionRecords = SpecialQuestionRecords;
    }

    public static class ListCodeQuestionRecordsBean {
        /**
         * ExamCode : bj-aqy1628773961
         * ExamCodeNum : 1
         * ExamCodeName : 法律法规 法律法规 法律法规 法律法规 法律法规 法律法规 法律法规 法律法规
         * RecordCount : 1
         */

        private String ExamCode;
        private int ExamCodeNum;
        private String ExamCodeName;
        private int RecordCount;

        public String getExamCode() {
            return ExamCode;
        }

        public void setExamCode(String ExamCode) {
            this.ExamCode = ExamCode;
        }

        public int getExamCodeNum() {
            return ExamCodeNum;
        }

        public void setExamCodeNum(int ExamCodeNum) {
            this.ExamCodeNum = ExamCodeNum;
        }

        public String getExamCodeName() {
            return ExamCodeName;
        }

        public void setExamCodeName(String ExamCodeName) {
            this.ExamCodeName = ExamCodeName;
        }

        public int getRecordCount() {
            return RecordCount;
        }

        public void setRecordCount(int RecordCount) {
            this.RecordCount = RecordCount;
        }
    }

    public static class SpecialQuestionRecordsBean {
        /**
         * PaperType : 0
         * QuestionType : 1
         * TypeName : 单选题
         * RecordCount : 1
         */

        private int PaperType;
        private int QuestionType;
        private String TypeName;
        private int RecordCount;

        public int getPaperType() {
            return PaperType;
        }

        public void setPaperType(int PaperType) {
            this.PaperType = PaperType;
        }

        public int getQuestionType() {
            return QuestionType;
        }

        public void setQuestionType(int QuestionType) {
            this.QuestionType = QuestionType;
        }

        public String getTypeName() {
            return TypeName;
        }

        public void setTypeName(String TypeName) {
            this.TypeName = TypeName;
        }

        public int getRecordCount() {
            return RecordCount;
        }

        public void setRecordCount(int RecordCount) {
            this.RecordCount = RecordCount;
        }
    }
}
