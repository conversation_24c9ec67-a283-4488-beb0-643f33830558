package com.zizhiguanjia.model_home.bean;

/**
 * 功能作用：首页学习计划信息
 * 初始注释时间： 2023/12/17 21:02
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class HomeExamDateTipBean {
    /**
     * 是否设置考试日期
     */
    private boolean IsSetExamDate;
    /**
     * 剩余天数
     */
    private Integer RemainingExamDays;
    /**
     * 剩余x道题未做
     */
    private String RemainingQuestionNum;
    /**
     * 剩余x道题未消除
     */
    private String RemainingNoClearNum;

    public boolean isSetExamDate() {
        return IsSetExamDate;
    }

    public void setSetExamDate(boolean setExamDate) {
        IsSetExamDate = setExamDate;
    }

    public Integer getRemainingExamDays() {
        return RemainingExamDays;
    }

    public void setRemainingExamDays(Integer remainingExamDays) {
        RemainingExamDays = remainingExamDays;
    }

    public String getRemainingQuestionNum() {
        return RemainingQuestionNum;
    }

    public void setRemainingQuestionNum(String remainingQuestionNum) {
        RemainingQuestionNum = remainingQuestionNum;
    }

    public String getRemainingNoClearNum() {
        return RemainingNoClearNum;
    }

    public void setRemainingNoClearNum(String remainingNoClearNum) {
        RemainingNoClearNum = remainingNoClearNum;
    }
}
