package com.zizhiguanjia.model_home.bean;

public class MiddleAdsBean {
    /**
     * Title : 您仅享受100道题免费额度
     * SubTitle : 您可升级题库继续畅快刷题
     * LimitNum : 100
     * BtnTxt : 升级题库
     * Url : http://www.zizhiguanjia.cn/m/pay
     */

    private String Title;
    private String SubTitle;
    private int LimitNum;
    private String BtnTxt;
    private String Url;
    private String LimitPaperNumColor;
    private String LimitNumColor;
    private String LimitPaperNum;
    private String BtnType;
    private int CloseStatus;
    public int getCloseStatus() {
        return CloseStatus;
    }
    public void setCloseStatus(int closeStatus) {
        CloseStatus = closeStatus;
    }

    public String getBtnType() {
        return BtnType;
    }

    public void setBtnType(String btnType) {
        BtnType = btnType;
    }

    public String getLimitPaperNumColor() {
        return LimitPaperNumColor;
    }

    public void setLimitPaperNumColor(String limitPaperNumColor) {
        LimitPaperNumColor = limitPaperNumColor;
    }

    public String getLimitNumColor() {
        return LimitNumColor;
    }

    public void setLimitNumColor(String limitNumColor) {
        LimitNumColor = limitNumColor;
    }

    public String getLimitPaperNum() {
        return LimitPaperNum;
    }

    public void setLimitPaperNum(String limitPaperNum) {
        LimitPaperNum = limitPaperNum;
    }

    public String getTitle() {
        return Title;
    }

    public void setTitle(String Title) {
        this.Title = Title;
    }

    public String getSubTitle() {
        return SubTitle;
    }

    public void setSubTitle(String SubTitle) {
        this.SubTitle = SubTitle;
    }

    public int getLimitNum() {
        return LimitNum;
    }

    public void setLimitNum(int LimitNum) {
        this.LimitNum = LimitNum;
    }

    public String getBtnTxt() {
        return BtnTxt;
    }

    public void setBtnTxt(String BtnTxt) {
        this.BtnTxt = BtnTxt;
    }

    public String getUrl() {
        return Url;
    }

    public void setUrl(String Url) {
        this.Url = Url;
    }
}
