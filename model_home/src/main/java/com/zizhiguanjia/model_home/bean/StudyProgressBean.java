package com.zizhiguanjia.model_home.bean;

/**
 * 学习进度数据Bean类
 */
public class StudyProgressBean {
    // 学习时长（分钟）
    private int studyTime;
    
    // 学习进度百分比
    private int progressPercent;
    
    // 总题目数
    private int totalQuestions;
    
    // 已完成题目数
    private int completedQuestions;
    
    // 最新模考分数
    private int latestExamScore;
    
    // 知识掌握度
    private int knowledgeMastery;
    
    // 坚持学习天数
    private int studyDays;
    
    // 可试用题目数
    private int trialQuestions;
    
    // 可试用试卷数
    private int trialPapers;
    
    // 是否为VIP用户
    private boolean isVip;

    public int getStudyTime() {
        return studyTime;
    }

    public void setStudyTime(int studyTime) {
        this.studyTime = studyTime;
    }

    public int getProgressPercent() {
        return progressPercent;
    }

    public void setProgressPercent(int progressPercent) {
        this.progressPercent = progressPercent;
    }

    public int getTotalQuestions() {
        return totalQuestions;
    }

    public void setTotalQuestions(int totalQuestions) {
        this.totalQuestions = totalQuestions;
    }

    public int getCompletedQuestions() {
        return completedQuestions;
    }

    public void setCompletedQuestions(int completedQuestions) {
        this.completedQuestions = completedQuestions;
    }

    public int getLatestExamScore() {
        return latestExamScore;
    }

    public void setLatestExamScore(int latestExamScore) {
        this.latestExamScore = latestExamScore;
    }

    public int getKnowledgeMastery() {
        return knowledgeMastery;
    }

    public void setKnowledgeMastery(int knowledgeMastery) {
        this.knowledgeMastery = knowledgeMastery;
    }

    public int getStudyDays() {
        return studyDays;
    }

    public void setStudyDays(int studyDays) {
        this.studyDays = studyDays;
    }

    public int getTrialQuestions() {
        return trialQuestions;
    }

    public void setTrialQuestions(int trialQuestions) {
        this.trialQuestions = trialQuestions;
    }

    public int getTrialPapers() {
        return trialPapers;
    }

    public void setTrialPapers(int trialPapers) {
        this.trialPapers = trialPapers;
    }

    public boolean isVip() {
        return isVip;
    }

    public void setVip(boolean vip) {
        isVip = vip;
    }
} 