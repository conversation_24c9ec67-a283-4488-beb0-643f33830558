package com.zizhiguanjia.model_home.bean;

import com.zizhiguanjia.model_home.R;

public class MenuNavsBean {
    /**
     * NavType : 1
     * Title : 错题集
     * ImgSrc : http://apiapp.mayijianzhu.net/images/xcx_anquanyuan/home_type_exercise1.png
     */
    
    // 菜单导航类型常量
    public static final int TYPE_ERROR = 1;      // 错题集
    public static final int TYPE_SAVE = 2;       // 收藏夹
    public static final int TYPE_PRACTICE = 3;   // 题型练习
    public static final int TYPE_EXAM = 4;       // 模拟考试
    public static final int TYPE_REAL_EXAM = 5;  // 历年真题
    public static final int TYPE_JIANZHU = 6;    // 二建模考
    public static final int TYPE_HIGH_FREQ = 22;  // 高频考题
    public static final int TYPE_AI_TEST = 21;  // 智能练习
    public static final int TYPE_LIVE = 20;      // 直播课程

    private int NavType;
    private String Title;
    private String ImgSrc;
    private String Url;
    private boolean IsDo;
    private int DateBoxStatus;
    private int RowNum;
    private String OfficialText;

    public String getOfficialText() {
        return OfficialText;
    }

    public void setOfficialText(String officialText) {
        OfficialText = officialText;
    }

    public int getDateBoxStatus() {
        return DateBoxStatus;
    }

    public void setDateBoxStatus(int dateBoxStatus) {
        DateBoxStatus = dateBoxStatus;
    }

    public int getRowNum() {
        return RowNum;
    }

    public void setRowNum(int rowNum) {
        this.RowNum = rowNum;
    }

    public boolean isDo() {
        return IsDo;
    }

    public void setDo(boolean aDo) {
        IsDo = aDo;
    }

    public String getUrl() {
        return Url;
    }

    public void setUrl(String url) {
        Url = url;
    }

    public int getNavType() {
        return NavType;
    }

    public void setNavType(int NavType) {
        this.NavType = NavType;
    }

    public String getTitle() {
        return Title;
    }

    public void setTitle(String Title) {
        this.Title = Title;
    }

    public String getImgSrc() {
        return ImgSrc;
    }

    public void setImgSrc(String ImgSrc) {
        this.ImgSrc = ImgSrc;
    }
    
    /**
     * 获取菜单导航类型
     * @return 菜单导航类型
     */
    public int getType() {
        return NavType;
    }
    
    /**
     * 获取图标URL
     * @return 图标URL
     */
    public String getIconUrl() {
        return ImgSrc;
    }
    
    /**
     * 获取图标资源ID（仅在ImgSrc为空时使用）
     * @return 图标资源ID
     */
    public int getIconResId() {
        switch (NavType) {
            case TYPE_ERROR:
                return R.drawable.home_error;
            case TYPE_SAVE:
                return R.drawable.home_save;
            case TYPE_PRACTICE:
                return R.drawable.home_txlx;
            case TYPE_EXAM:
                return R.drawable.home_mnks;
            case TYPE_REAL_EXAM:
                return R.drawable.home_order_ytf;
            case TYPE_JIANZHU:
                return R.drawable.home_switch_img;
            case TYPE_HIGH_FREQ:
                return R.drawable.home_error; // 使用错题集图标，可以根据实际情况替换
            case TYPE_LIVE:
                return R.drawable.home_live_tags;
            default:
                return R.drawable.home_switch_img;
        }
    }
}
