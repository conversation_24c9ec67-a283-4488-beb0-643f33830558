package com.zizhiguanjia.model_home.bean;

import java.util.ArrayList;
import java.util.List;

public class HomeBean {
    private Integer MajorId;
    private Integer MajorPid;
    private Integer AreaId;
    private String MajorName;
    private String AreaName;
    private int TotalNumber;
    private int AnswerNumber;
    private int PaperType;
    private String PaperTitle;
    private int CorrectNumber;
    private String CorrectPercent;
    private String QuestionMakingProgress;
    private boolean IsVip;
    private int StudyMinutes;
    private int IsExamine;
    private int OrderBindType;
    private int RemainingQuestionsCount;
    private MiddleAdsBean MiddleAds;
    private FooterBean Footer;
    private String DefaultNav;
    private WeixinsBean Weixins;
    private List<BannersBean> Banners;
    private List<MenuNavsBean> MenuNavs;
    private List<VideoInfo> Videos;
    private List<HomeShaperBean> ShareNavs;
    private HomeExamTipBean ExamTip;
    private LivesBean Lives;
    private KefuBean Kefu;
    private boolean HasAiKnowledgetree;
    private boolean IsOfficial;//是否官方题库
    private String OfficialText;//官方题库提示
    private String ExamScores;//最新模考分数
    private String MasteryPercent;//知识掌握度
    private int StudyDays;//坚持学习天数
    private boolean IsShowEvaluation;//是否展示摸底测评
    private String Headimg;

    public String getHeadimg() {
        return Headimg;
    }

    public void setHeadimg(String headimg) {
        Headimg = headimg;
    }

    public boolean isOfficial() {
        return IsOfficial;
    }

    public String getOfficialText() {
        return OfficialText;
    }

    public void setOfficialText(String officialText) {
        OfficialText = officialText;
    }

    public void setOfficial(boolean official) {
        IsOfficial = official;
    }

    public String getExamScores() {
        return ExamScores;
    }

    public void setExamScores(String examScores) {
        ExamScores = examScores;
    }

    public String getMasteryPercent() {
        return MasteryPercent;
    }

    public void setMasteryPercent(String masteryPercent) {
        MasteryPercent = masteryPercent;
    }

    public int getStudyDays() {
        return StudyDays;
    }

    public void setStudyDays(int studyDays) {
        StudyDays = studyDays;
    }

    public boolean isShowEvaluation() {
        return IsShowEvaluation;
    }

    public void setShowEvaluation(boolean showEvaluation) {
        IsShowEvaluation = showEvaluation;
    }

    public boolean isHasAiKnowledgetree() {
        return HasAiKnowledgetree;
    }

    public void setHasAiKnowledgetree(boolean hasAiKnowledgetree) {
        HasAiKnowledgetree = hasAiKnowledgetree;
    }

    public KefuBean getKefu() {
        return Kefu;
    }

    public void setKefu(KefuBean kefu) {
        Kefu = kefu;
    }

    private int liveVieoType;
    private boolean IsShowAds;
    private boolean subjectActive;
    private boolean IsShowExamDate;

    public void setMajorPid(Integer majorPid) {
        MajorPid = majorPid;
    }

    public Integer getMajorPid() {
        return MajorPid;
    }

    public void setShowExamDate(boolean showExamDate) {
        IsShowExamDate = showExamDate;
    }

    public boolean isShowExamDate() {
        return IsShowExamDate;
    }

    private HomeExamDateTipBean ExamDateTip;

    public void setExamDateTip(HomeExamDateTipBean examDateTip) {
        ExamDateTip = examDateTip;
    }

    public HomeExamDateTipBean getExamDateTip() {
        return ExamDateTip;
    }

    public String getMajorName() {
        return MajorName;
    }

    public void setMajorName(String majorName) {
        MajorName = majorName;
    }

    public boolean isSubjectActive() {
        return subjectActive;
    }

    public void setSubjectActive(boolean subjectActive) {
        this.subjectActive = subjectActive;
    }

    public String getAreaName() {
        return AreaName;
    }

    public void setAreaName(String areaName) {
        AreaName = areaName;
    }

    public boolean isShowAds() {
        return IsShowAds;
    }

    public void setShowAds(boolean showAds) {
        IsShowAds = showAds;
    }

    private List<HomeExamGuideBean> ExamGuide;

    public int getOrderBindType() {
        return OrderBindType;
    }

    public void setOrderBindType(int orderBindType) {
        OrderBindType = orderBindType;
    }

    public List<HomeExamGuideBean> getExamGuide() {
        return ExamGuide;
    }

    public void setExamGuide(List<HomeExamGuideBean> examGuide) {
        ExamGuide = examGuide;
    }

    public int getLiveVieoType() {
        return liveVieoType;
    }

    public void setLiveVieoType(int liveVieoType) {
        this.liveVieoType = liveVieoType;
    }

    private List<ItemsBean> pLives;

    public List<ItemsBean> getpLives() {
        if (pLives == null) {
            return pLives = new ArrayList<>();
        } else {
            return pLives;
        }
    }

    public void setpLives(List<ItemsBean> pLives) {
        this.pLives = pLives;
    }

    public LivesBean getLives() {
        return Lives;
    }

    public void setLives(LivesBean lives) {
        Lives = lives;
    }

    public List<HomeShaperBean> getShareNavs() {
        return ShareNavs;
    }

    public void setShareNavs(List<HomeShaperBean> shareNavs) {
        ShareNavs = shareNavs;
    }

    public boolean isVip() {
        return IsVip;
    }

    public void setVip(boolean vip) {
        IsVip = vip;
    }

    public HomeExamTipBean getExamTip() {
        return ExamTip;
    }

    public void setExamTip(HomeExamTipBean examTip) {
        ExamTip = examTip;
    }

    public Integer getMajorId() {
        return MajorId;
    }

    public void setMajorId(Integer MajorId) {
        this.MajorId = MajorId;
    }

    public Integer getAreaId() {
        return AreaId;
    }

    public void setAreaId(Integer AreaId) {
        this.AreaId = AreaId;
    }

    public int getTotalNumber() {
        return TotalNumber;
    }

    public void setTotalNumber(int TotalNumber) {
        this.TotalNumber = TotalNumber;
    }

    public int getAnswerNumber() {
        return AnswerNumber;
    }

    public void setAnswerNumber(int AnswerNumber) {
        this.AnswerNumber = AnswerNumber;
    }

    public int getPaperType() {
        return PaperType;
    }

    public void setPaperType(int PaperType) {
        this.PaperType = PaperType;
    }

    public String getPaperTitle() {
        return PaperTitle;
    }

    public void setPaperTitle(String PaperTitle) {
        this.PaperTitle = PaperTitle;
    }

    public int getCorrectNumber() {
        return CorrectNumber;
    }

    public void setCorrectNumber(int CorrectNumber) {
        this.CorrectNumber = CorrectNumber;
    }

    public String getCorrectPercent() {
        return CorrectPercent;
    }

    public void setCorrectPercent(String CorrectPercent) {
        this.CorrectPercent = CorrectPercent;
    }

    public String getQuestionMakingProgress() {
        return QuestionMakingProgress;
    }

    public void setQuestionMakingProgress(String QuestionMakingProgress) {
        this.QuestionMakingProgress = QuestionMakingProgress;
    }

    public boolean isIsVip() {
        return IsVip;
    }

    public void setIsVip(boolean IsVip) {
        this.IsVip = IsVip;
    }

    public int getStudyMinutes() {
        return StudyMinutes;
    }

    public void setStudyMinutes(int StudyMinutes) {
        this.StudyMinutes = StudyMinutes;
    }

    public int getIsExamine() {
        return IsExamine;
    }

    public void setIsExamine(int IsExamine) {
        this.IsExamine = IsExamine;
    }

    public int getRemainingQuestionsCount() {
        return RemainingQuestionsCount;
    }

    public void setRemainingQuestionsCount(int RemainingQuestionsCount) {
        this.RemainingQuestionsCount = RemainingQuestionsCount;
    }

    public MiddleAdsBean getMiddleAds() {
        return MiddleAds;
    }

    public void setMiddleAds(MiddleAdsBean MiddleAds) {
        this.MiddleAds = MiddleAds;
    }

    public FooterBean getFooter() {
        return Footer;
    }

    public void setFooter(FooterBean Footer) {
        this.Footer = Footer;
    }

    public String getDefaultNav() {
        return DefaultNav;
    }

    public void setDefaultNav(String DefaultNav) {
        this.DefaultNav = DefaultNav;
    }

    public WeixinsBean getWeixins() {
        return Weixins;
    }

    public void setWeixins(WeixinsBean Weixins) {
        this.Weixins = Weixins;
    }

    public List<BannersBean> getBanners() {
        return Banners;
    }

    public void setBanners(List<BannersBean> Banners) {
        this.Banners = Banners;
    }

    public List<MenuNavsBean> getMenuNavs() {
        return MenuNavs;
    }

    public void setMenuNavs(List<MenuNavsBean> MenuNavs) {
        this.MenuNavs = MenuNavs;
    }

    public List<VideoInfo> getVideos() {
        return Videos;
    }

    public void setVideos(List<VideoInfo> Videos) {
        this.Videos = Videos;
    }

}
