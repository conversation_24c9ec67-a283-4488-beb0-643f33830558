package com.zizhiguanjia.model_home.bean;

public class ItemsBean {
    /**
     * LiveCover : default.jpg
     * LiveId : 345
     * Status : 2
     * StatusStr : 直播中
     * Title : 山西安全员考前集训直播
     * LivePlayedCount : 0
     * LiveSubscribedCount : 0
     * LiveUrl : null
     * LiveStartTime : 07-08 02:00
     * LiveEndTime : 07-08 22:00
     * Sort : 3
     * IsOrder : false
     * TeacherName :
     */

    private String LiveCover;
    private int LiveId;
    private int Status;
    private String StatusStr;
    private String Title;
    private String LiveTime;

    public String getLiveTime() {
        return LiveTime;
    }

    public void setLiveTime(String liveTime) {
        LiveTime = liveTime;
    }

    private int LivePlayedCount;
    private int LiveSubscribedCount;
    private Object LiveUrl;
    private String LiveStartTime;
    private String LiveEndTime;
    private int Sort;
    private boolean IsOrder;
    private String TeacherName;
    private boolean CanGo;
    /**
     * 课程编码
     */
    private String CourceCode;
    /**
     * 图片
     */
    private String TeacherPhoto;
    /**
     * 直播名称
     */
    private String LiveTitle;

    public void setLiveTitle(String liveTitle) {
        LiveTitle = liveTitle;
    }

    public String getLiveTitle() {
        return LiveTitle;
    }

    public void setTeacherPhoto(String teacherPhoto) {
        TeacherPhoto = teacherPhoto;
    }

    public String getTeacherPhoto() {
        return TeacherPhoto;
    }

    public void setCourceCode(String courceCode) {
        CourceCode = courceCode;
    }

    public String getCourceCode() {
        return CourceCode;
    }

    public boolean isOrder() {
        return IsOrder;
    }

    public void setOrder(boolean order) {
        IsOrder = order;
    }

    public boolean isCanGo() {
        return CanGo;
    }

    public void setCanGo(boolean canGo) {
        CanGo = canGo;
    }

    public String getLiveCover() {
        return LiveCover;
    }

    public void setLiveCover(String LiveCover) {
        this.LiveCover = LiveCover;
    }

    public int getLiveId() {
        return LiveId;
    }

    public void setLiveId(int LiveId) {
        this.LiveId = LiveId;
    }

    public int getStatus() {
        return Status;
    }

    public void setStatus(int Status) {
        this.Status = Status;
    }

    public String getStatusStr() {
        return StatusStr;
    }

    public void setStatusStr(String StatusStr) {
        this.StatusStr = StatusStr;
    }

    public String getTitle() {
        return Title;
    }

    public void setTitle(String Title) {
        this.Title = Title;
    }

    public int getLivePlayedCount() {
        return LivePlayedCount;
    }

    public void setLivePlayedCount(int LivePlayedCount) {
        this.LivePlayedCount = LivePlayedCount;
    }

    public int getLiveSubscribedCount() {
        return LiveSubscribedCount;
    }

    public void setLiveSubscribedCount(int LiveSubscribedCount) {
        this.LiveSubscribedCount = LiveSubscribedCount;
    }

    public Object getLiveUrl() {
        return LiveUrl;
    }

    public void setLiveUrl(Object LiveUrl) {
        this.LiveUrl = LiveUrl;
    }

    public String getLiveStartTime() {
        return LiveStartTime;
    }

    public void setLiveStartTime(String LiveStartTime) {
        this.LiveStartTime = LiveStartTime;
    }

    public String getLiveEndTime() {
        return LiveEndTime;
    }

    public void setLiveEndTime(String LiveEndTime) {
        this.LiveEndTime = LiveEndTime;
    }

    public int getSort() {
        return Sort;
    }

    public void setSort(int Sort) {
        this.Sort = Sort;
    }

    public boolean isIsOrder() {
        return IsOrder;
    }

    public void setIsOrder(boolean IsOrder) {
        this.IsOrder = IsOrder;
    }

    public String getTeacherName() {
        return TeacherName;
    }

    public void setTeacherName(String TeacherName) {
        this.TeacherName = TeacherName;
    }
}
