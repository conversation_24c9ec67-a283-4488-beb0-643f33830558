package com.zizhiguanjia.model_home.bean;

import java.util.List;

/**
 * 功能作用：课程详情商品信息
 * 初始注释时间： 2023/11/23 10:41
 * 创建人：王亮（<PERSON>ren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class CourseDetailGoodsBean {
    private List<Detail> Details;
    private Object FreePay;
    private Goods Goods;
    private Boolean IsCanPay;
    private Boolean IsShowPopup;
    private Boolean IsVip;
    private String KeFuPhone;
    private Long QuestionCount;
    private Long RemainingCount;
    private Long RemainingPaperCount;

    public List<Detail> getDetails() {
        return Details;
    }

    public void setDetails(List<Detail> details) {
        Details = details;
    }

    public Object getFreePay() {
        return FreePay;
    }

    public void setFreePay(Object freePay) {
        FreePay = freePay;
    }

    public Goods getGoods() {
        return Goods;
    }

    public void setGoods(Goods goods) {
        Goods = goods;
    }

    public Boolean getCanPay() {
        return IsCanPay;
    }

    public void setCanPay(Boolean canPay) {
        IsCanPay = canPay;
    }

    public Boolean getShowPopup() {
        return IsShowPopup;
    }

    public void setShowPopup(Boolean showPopup) {
        IsShowPopup = showPopup;
    }

    public Boolean getVip() {
        return IsVip;
    }

    public void setVip(Boolean vip) {
        IsVip = vip;
    }

    public String getKeFuPhone() {
        return KeFuPhone;
    }

    public void setKeFuPhone(String keFuPhone) {
        KeFuPhone = keFuPhone;
    }

    public Long getQuestionCount() {
        return QuestionCount;
    }

    public void setQuestionCount(Long questionCount) {
        QuestionCount = questionCount;
    }

    public Long getRemainingCount() {
        return RemainingCount;
    }

    public void setRemainingCount(Long remainingCount) {
        RemainingCount = remainingCount;
    }

    public Long getRemainingPaperCount() {
        return RemainingPaperCount;
    }

    public void setRemainingPaperCount(Long remainingPaperCount) {
        RemainingPaperCount = remainingPaperCount;
    }

    public static class Goods {

        private Object Details;
        private String Duration;
        private String GoodsId;
        private Long GoodsType;
        private String MajorName;
        private Long OrderCount;
        private String OrderNote;
        private String OrderNum;
        private String OrderTitle;
        private String PostPrice;
        private String Price;
        private String ServiceContent;
        private String TimeOutDate;

        public Object getDetails() {
            return Details;
        }

        public void setDetails(Object details) {
            Details = details;
        }

        public String getDuration() {
            return Duration;
        }

        public void setDuration(String duration) {
            Duration = duration;
        }

        public String getGoodsId() {
            return GoodsId;
        }

        public void setGoodsId(String goodsId) {
            GoodsId = goodsId;
        }

        public Long getGoodsType() {
            return GoodsType;
        }

        public void setGoodsType(Long goodsType) {
            GoodsType = goodsType;
        }

        public String getMajorName() {
            return MajorName;
        }

        public void setMajorName(String majorName) {
            MajorName = majorName;
        }

        public Long getOrderCount() {
            return OrderCount;
        }

        public void setOrderCount(Long orderCount) {
            OrderCount = orderCount;
        }

        public String getOrderNote() {
            return OrderNote;
        }

        public void setOrderNote(String orderNote) {
            OrderNote = orderNote;
        }

        public String getOrderNum() {
            return OrderNum;
        }

        public void setOrderNum(String orderNum) {
            OrderNum = orderNum;
        }

        public String getOrderTitle() {
            return OrderTitle;
        }

        public void setOrderTitle(String orderTitle) {
            OrderTitle = orderTitle;
        }

        public String getPostPrice() {
            return PostPrice;
        }

        public void setPostPrice(String postPrice) {
            PostPrice = postPrice;
        }

        public String getPrice() {
            return Price;
        }

        public void setPrice(String price) {
            Price = price;
        }

        public String getServiceContent() {
            return ServiceContent;
        }

        public void setServiceContent(String serviceContent) {
            ServiceContent = serviceContent;
        }

        public String getTimeOutDate() {
            return TimeOutDate;
        }

        public void setTimeOutDate(String timeOutDate) {
            TimeOutDate = timeOutDate;
        }
    }

    public static class Detail {

        private String Desc;
        private String Duration;
        private Long GoodsId;
        private String GoodsName;
        private String Info;
        private String OldPrice;
        private String Price;
        private Boolean Select;
        private Long ShowInfo;
        private String TimeOutDate;

        public String getDesc() {
            return Desc;
        }

        public void setDesc(String desc) {
            Desc = desc;
        }

        public String getDuration() {
            return Duration;
        }

        public void setDuration(String duration) {
            Duration = duration;
        }

        public Long getGoodsId() {
            return GoodsId;
        }

        public void setGoodsId(Long goodsId) {
            GoodsId = goodsId;
        }

        public String getGoodsName() {
            return GoodsName;
        }

        public void setGoodsName(String goodsName) {
            GoodsName = goodsName;
        }

        public String getInfo() {
            return Info;
        }

        public void setInfo(String info) {
            Info = info;
        }

        public String getOldPrice() {
            return OldPrice;
        }

        public void setOldPrice(String oldPrice) {
            OldPrice = oldPrice;
        }

        public String getPrice() {
            return Price;
        }

        public void setPrice(String price) {
            Price = price;
        }

        public Boolean getSelect() {
            return Select;
        }

        public void setSelect(Boolean select) {
            Select = select;
        }

        public Long getShowInfo() {
            return ShowInfo;
        }

        public void setShowInfo(Long showInfo) {
            ShowInfo = showInfo;
        }

        public String getTimeOutDate() {
            return TimeOutDate;
        }

        public void setTimeOutDate(String timeOutDate) {
            TimeOutDate = timeOutDate;
        }
    }
}
