package com.zizhiguanjia.model_home.adapter;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_image_loadcal.GlideImageLoader;
import com.wb.lib_image_loadcal.ImageManager;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.ItemsBean;
import com.zizhiguanjia.model_home.databinding.HomeAvdFreecourseItemBinding;

public class HomeAvdFreCourseAdapter extends BaseAdapter<ItemsBean> {
    private HomeAvdFreecourseItemBinding binding;
    public HomeAvdFreCourseAdapter() {
        super(R.layout.home_avd_freecourse_item);
    }

    @Override
    protected void bind(BaseViewHolder holder, ItemsBean item, int position) {
        binding=holder.getBinding();
        binding.setBean(item);
        GlideImageLoader.loadCircleImage(item.getLiveCover(),binding.txImage);
        if(item.getStatus()==4){

        }
    }
}
