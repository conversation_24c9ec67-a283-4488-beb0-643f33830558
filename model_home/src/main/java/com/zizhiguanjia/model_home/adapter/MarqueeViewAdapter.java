package com.zizhiguanjia.model_home.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.stx.xmarqueeview.XMarqueeView;
import com.stx.xmarqueeview.XMarqueeViewAdapter;
import com.zizhiguanjia.model_home.R;

import java.util.List;

public class MarqueeViewAdapter extends XMarqueeViewAdapter<String> {
    public MarqueeViewAdapter(List<String> datas) {
        super(datas);
    }

    @Override
    public View onCreateView(XMarqueeView parent) {
        return LayoutInflater.from(parent.getContext()).inflate(R.layout.home_marquee_item, null);
    }

    @Override
    public void onBindView(View parent, View view, int position) {
        TextView tvOne = (TextView) view.findViewById(R.id.itemMarqueeTv);
        tvOne.setText(mDatas.get(position));
    }
}
