package com.zizhiguanjia.model_home.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.BannersBean;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Banner适配器，用于处理banner的显示和点击事件
 */
public class BannerAdapter extends PagerAdapter {
    private Context context;
    private List<BannersBean> banners;
    private StudyViewModel viewModel;
    
    public BannerAdapter(Context context, List<BannersBean> banners, StudyViewModel viewModel) {
        this.context = context;
        this.banners = banners;
        this.viewModel = viewModel;
    }
    
    @Override
    public int getCount() {
        return banners == null ? 0 : banners.size();
    }
    
    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }
    
    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_banner, container, false);
        ImageView imageView = view.findViewById(R.id.banner_image);
        
        BannersBean banner = banners.get(position);
        
        // 使用Glide加载图片
        Glide.with(context)
            .load(banner.getImgSrc())
            .apply(new RequestOptions()
                .placeholder(R.drawable.test_date_top_bg)
                .error(R.drawable.test_date_top_bg))
            .into(imageView);
        
        // 设置点击事件
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (viewModel != null) {
                    viewModel.handleBannerClick(banner);
                }
            }
        });
        
        container.addView(view);
        return view;
    }
    
    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView((View) object);
    }
    
    /**
     * 更新Banner数据
     */
    public void updateData(List<BannersBean> newBanners) {
        if (newBanners == null) {
            com.wb.lib_utils.utils.log.LogUtils.e("BannerAdapter - updateData - 传入的Banner数据为空");
            return;
        }
        
        com.wb.lib_utils.utils.log.LogUtils.e("BannerAdapter - updateData - 更新Banner数据，数量: " + newBanners.size());
        
        // 清除旧数据
        if (this.banners != null) {
            this.banners.clear();
        } else {
            this.banners = new ArrayList<>();
        }
        
        // 添加新数据
        this.banners.addAll(newBanners);
        
        // 通知数据集变化
        notifyDataSetChanged();
        
        com.wb.lib_utils.utils.log.LogUtils.e("BannerAdapter - updateData - 数据更新完成，当前数量: " + this.banners.size());
    }
    
    /**
     * 解决ViewPager在数据更新后可能位置错乱的问题
     */
    @Override
    public int getItemPosition(@NonNull Object object) {
        // 这里强制返回POSITION_NONE告诉ViewPager所有项目都已改变，
        // 但我们会在外部手动处理位置，避免回到第一页
        return POSITION_NONE;
    }
} 