package com.zizhiguanjia.model_home.adapter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.zizhiguanjia.model_home.fragment.BackVideoFragment;
import com.zizhiguanjia.model_home.fragment.CourseFragment;
import com.zizhiguanjia.model_home.fragment.HomeFragment;

public class ViewPageAdapter extends FragmentPagerAdapter  {
    public ViewPageAdapter(FragmentManager fm) {
        super(fm);
    }
    @NonNull
    @Override
    public Fragment getItem(int position) {
        if(position==0){
            return new CourseFragment();
        }else {
            return new BackVideoFragment();
        }
    }
    @Nullable
    @Override
    public CharSequence getPageTitle(int position) {
        if(position==0){
            return "直播";
        }else {
            return "课程";
        }
    }
    @Override
    public int getCount() {
        return 2;
    }
}
