package com.zizhiguanjia.model_home.adapter;

import android.view.Gravity;
import android.view.View;

import com.caimuhao.rxpicker.bean.ImageItem;
import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_image_loadcal.GlideImageLoader;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.databinding.ItemListCourseDetailCommentImageBinding;
import com.zizhiguanjia.model_home.fragment.CourseDetailCatalogueFragment;
import com.zizhiguanjia.model_home.listener.CourseDetailCommentImageListener;

import java.util.ArrayList;
import java.util.List;

public class CourseDetailCommentImageAdapter extends BaseAdapter<ImageItem> {
    private ItemListCourseDetailCommentImageBinding imageItemBinding;
    private CourseDetailCatalogueFragment mFragment;
    private CourseDetailCommentImageListener imageLookListener;

    public CourseDetailCommentImageAdapter(CourseDetailCatalogueFragment mFragment,CourseDetailCommentImageListener imageLookListener) {
        super(R.layout.item_list_course_detail_comment_image);
        this.imageLookListener = imageLookListener;
        this.mFragment = mFragment;
        List<ImageItem> strings = new ArrayList<>();
        ImageItem imageItem = new ImageItem();
        imageItem.setId(0);
        strings.add(imageItem);
        setDataItems(strings);
    }

    @Override
    protected void bind(BaseViewHolder holder, ImageItem item, int position) {
        imageItemBinding = holder.getBinding();
        imageItemBinding.setModel(this);
        imageItemBinding.setIds(item.getId());
        if (item.getId() == 0) {
            imageItemBinding.commonLookimageImgAdd.setVisibility(View.VISIBLE);
            imageItemBinding.commonLookimageImg.setVisibility(View.GONE);
            imageItemBinding.delectImage.setVisibility(View.GONE);
        } else {
            imageItemBinding.commonLookimageImgAdd.setVisibility(View.GONE);
            imageItemBinding.commonLookimageImg.setVisibility(View.VISIBLE);
            imageItemBinding.commonLookimageImg.setImageResource(0);
            imageItemBinding.delectImage.setVisibility(View.VISIBLE);
            new GlideImageLoader().loadRoundImage(item.getPath(), imageItemBinding.commonLookimageImg, 10);
        }
    }

    public void onClick(View view, int id) {
        if (id != 0) {
            return;
        }
        if (imageLookListener != null) {
            if (getData().size() > 9) {
                ToastUtils.normal("上传图片只能上传9张！", Gravity.CENTER);
            } else {
                MainThreadUtils.post(() -> {
                    boolean needDialog = KvUtils.get("homePermiss", false);
                    LogUtils.e("看下needDialog" + needDialog);
                    if (needDialog) {
                        imageLookListener.showImage();
                    } else {
                        MessageHelper.openGeneralCentDialog(mFragment.requireActivity(), "为了选择图片收集用户反馈，需要访问您设备上的照片", "", "取消",
                                "确定", false, true, new GeneralDialogListener() {
                                    @Override
                                    public void onCancel() {
                                        KvUtils.save("homePermiss", true);
                                    }

                                    @Override
                                    public void onConfim() {
                                        KvUtils.save("homePermiss", true);
                                        imageLookListener.showImage();
                                    }

                                    /**
                                     *
                                     */
                                    @Override
                                    public void onDismiss() {

                                    }
                                });
                    }
                });
            }
        }
    }

    public void delectImage(View view, int postion) {
        if (imageLookListener == null) {
            return;
        }
        imageLookListener.delectImage(postion);
    }

    public int getImageMaxSize() {
        int max = 0;
        for (ImageItem imageItem : getData()) {
            if (imageItem.getId() != 0) {
                max = max + 1;
            }
        }
        return 9 - max;
    }
}
