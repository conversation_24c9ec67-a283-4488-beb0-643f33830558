package com.zizhiguanjia.model_home.api;

import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.model_home.bean.CommonLivePreBean;
import com.zizhiguanjia.model_home.bean.CourseDetailCatalogueBean;
import com.zizhiguanjia.model_home.bean.CourseDetailGoodsBean;
import com.zizhiguanjia.model_home.bean.DefaultByAddressBean;
import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;
import com.zizhiguanjia.model_home.bean.FlutterCounponsBean;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.bean.HomeCourseBean;
import com.zizhiguanjia.model_home.bean.MainSaveOrErrorBean;
import com.zizhiguanjia.model_message.bean.ParentSubjectsBean;
import com.zizhiguanjia.model_message.bean.CityPickerBean;

import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface HomeSeriveApi {
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/Api/user/mycenter")
    Observable<BaseData<Object>> getUserInfo(@FieldMap Map<String,String> params);
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Examation/GetStatistics")
    Observable<BaseData<HomeBean>> getMainInfo(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/video/OrderLive")
    Observable<BaseData> orderLive(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Exam/GetReportCard")
    Observable<BaseData> getGradleInfo(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/common/GetUniMajorsByAreaId")
    Observable<BaseData<DefaultByAddressBean>> getDefalutAddressConfig(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/video/GetLiveVideos")
    Observable<BaseData<CommonLivePreBean>> getLivePreData(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Exam/EditEliminateSwitch")
    Observable<BaseData> clearSwitch(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Exam/DeleteWrongOrCollectionRecore")
    Observable<BaseData> clearErrorOrSaveData(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Exam/WrongOrCollectionQuestionsIndex")
    Observable<BaseData<MainSaveOrErrorBean>> getMainSaveOrErrorData(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Examation/GetTestAreas")
    Observable<BaseData> getMessageList(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/exam/OrderExam")
    Observable<BaseData> appointment(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Pay/GetUserOrders")
    Observable<BaseData> orderList(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Exam/GetExamReport")
    Observable<BaseData> getExamTimeInfo(@FieldMap Map<String,String> params);

    @POST(BaseAPI.VERSION_DES+"/API/User/UpdateUserExamDate")
    @FormUrlEncoded
    Observable<BaseData> updateExamTime(@FieldMap Map<String,String> paeams);

    @POST(BaseAPI.VERSION_DES+"/API/Exam/UpdateExamTestCount")
    @FormUrlEncoded
    Observable<BaseData> toMnksPostRecords(@FieldMap Map<String,String> paeams);

    @POST(BaseAPI.VERSION_DES+"/Api/examcoupon/couponcenter")
    @FormUrlEncoded
    Observable<BaseData<FlutterCounponsBean>> getCouponcanterDatas(@FieldMap Map<String,String> paeams);

    @POST(BaseAPI.VERSION_DES+"/Api/User/CloseGiveTip")
    @FormUrlEncoded
    Observable<BaseData> closeTip(@FieldMap Map<String,String> paeams);

    @POST(BaseAPI.VERSION_DES+"/Api/video/GetLiveIndex")
    @FormUrlEncoded
    Observable<BaseData<HomeCourseBean>> getCourseHttpData(@FieldMap Map<String,String> paeams);

    @POST(BaseAPI.VERSION_DES+"/Api/video/GetLiveIndex")
    @FormUrlEncoded
    Observable<BaseData<HomeCourseBean>> getBackVideoHttpData(@FieldMap Map<String,String> paeams);

    /**
     * 课程详情-目录信息
     */
    @POST(BaseAPI.VERSION_DES+"/Api/exam/GetCourseCatalog")
    @FormUrlEncoded
    Observable<BaseData<CourseDetailCatalogueBean>> getCourseDetailCatalogue(@FieldMap Map<String,String> params);
    /**
     * 课程详情-商品信息
     */
    @POST(BaseAPI.VERSION_DES+"/Api/Pay/GetVipGoods")
    @FormUrlEncoded
    Observable<BaseData<CourseDetailGoodsBean>> getCourseDetailGoods(@FieldMap Map<String,String> params);
    /**
     * 课程详情-上传进度
     */
    @POST(BaseAPI.VERSION_DES+"/Api/Video/SubmitVideoDuration")
    @FormUrlEncoded
    Observable<BaseData> submitVideoDuration(@FieldMap Map<String,String> params);
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Common/AddFeedback")
    Observable<BaseData> uploadImageOrTxt(@FieldMap Map<String,String> params);

    /**
     * 升级提醒信息获取
     */
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Examation/UpgradeReminder")
    Observable<BaseData<DyVipUpdateHintBean>> getUpgradeReminder(@FieldMap Map<String,String> params);

    /**
     * 获取省份
     */
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/common/GetAreas")
    Observable<BaseData<CityPickerBean>> getCityList(@FieldMap Map<String,String> params);

    /**
     * 获取科目
     */
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/common/GetMajorsByAreaId")
    Observable<BaseData<CityPickerBean>> getSecondConstruction(@FieldMap Map<String,String> params);

    /**
     * 获取考试类型
     */
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/common/GetParentSubjects")
    Observable<BaseData<ParentSubjectsBean>> getParentSubjects(@FieldMap Map<String,String> params);

    /**
     * 开通会员
     */
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/pay/ActivateVipOtherPlatform")
    Observable<BaseData> activateVipOtherPlatform(@FieldMap Map<String,String> params);
    /**
     * 关闭立即测评
     */
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Exam/CloseEvaluation")
    Observable<BaseData> closeEvaluation(@FieldMap Map<String, String> params);
}
