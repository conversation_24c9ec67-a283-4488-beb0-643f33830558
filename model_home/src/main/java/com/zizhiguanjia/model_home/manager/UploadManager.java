package com.zizhiguanjia.model_home.manager;

import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.task.RxAsyncTask;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.lib_base.listeners.CompressImageListener;
import com.zizhiguanjia.lib_base.listeners.UploadImageToServiceListener;
import com.zizhiguanjia.model_common.help.UploadImageHelp;
import com.zizhiguanjia.model_common.model.UploadImageBean;
import com.zizhiguanjia.model_common.model.UploadResultBean;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.UnsupportedCharsetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;
import top.zibin.luban.Luban;

public class UploadManager {
    private static UploadManager uploadManager;
    public static UploadManager getInstance(){
        if(uploadManager==null){
            synchronized (UploadManager.class){
                return uploadManager=new UploadManager();
            }
        }
        return uploadManager;
    }

    /**
     * 上传并压缩多张图片  带参数
     * @param url
     * @param files
     * @param params
     * @param uploadImageToServiceListener
     */
    public void uploadMulteImage(String url,List<File> files, Map<String,String> params, UploadImageToServiceListener uploadImageToServiceListener){
            compressImage(files, new CompressImageListener() {
                @Override
                public void afterCompressImage(List<File> fileList) {
                    postDataToService(url,files,params,uploadImageToServiceListener);
                }
            });
    }

    /**
     * 上传多张并压缩图片 不带参数
     * @param url
     * @param files
     * @param uploadImageToServiceListener
     */
    public void uploadMulteImage(String url,List<File> files, UploadImageToServiceListener uploadImageToServiceListener){
        compressImage(files, new CompressImageListener() {
            @Override
            public void afterCompressImage(List<File> fileList) {
                postDataToService(url,files,null,uploadImageToServiceListener);
            }
        });
    }

    /**
     * 上传 并压缩 单张图片 带参数
     * @param url
     * @param file
     * @param params
     * @param uploadImageToServiceListener
     */
    public void uploadSingleImage(String url,File file, Map<String,String> params, UploadImageToServiceListener uploadImageToServiceListener){
        List<File>files=new ArrayList<>();
        files.add(file);
        compressImage(files, new CompressImageListener() {
            @Override
            public void afterCompressImage(List<File> fileList) {
                postDataToService(url,files,params,uploadImageToServiceListener);
            }
        });
    }

    /**
     * 上传单张图片并压缩 不带参数
     * @param url
     * @param file
     * @param uploadImageToServiceListener
     */
    public void uploadSingleImage(String url,File file, UploadImageToServiceListener uploadImageToServiceListener){
        List<File>files=new ArrayList<>();
        files.add(file);
        compressImage(files, new CompressImageListener() {
            @Override
            public void afterCompressImage(List<File> fileList) {
                postDataToService(url,files,null,uploadImageToServiceListener);
            }
        });
    }

    /**
     * 上传单张图片 带参数
     * @param url
     * @param file
     * @param params
     * @param uploadImageToServiceListener
     */
    public void postDataToService(String url,File file, Map<String,String> params, UploadImageToServiceListener uploadImageToServiceListener){
        List<File> files=new ArrayList<>();
        files.add(file);
        postDataToService(url,files.get(0),params,uploadImageToServiceListener);
    }

    /**
     * 上传单张图片 不带参数
     * @param url
     * @param file
     * @param uploadImageToServiceListener
     */
    public void postDataToService(String url,File file,UploadImageToServiceListener uploadImageToServiceListener){
        List<File> files=new ArrayList<>();
        files.add(file);
        postDataToService(url,files.get(0),null,uploadImageToServiceListener);
    }

    /**
     * 上传多张图片 不带参数
     * @param url
     * @param files
     * @param uploadImageToServiceListener
     */
    public void postDataToService(String url,List<File> files, UploadImageToServiceListener uploadImageToServiceListener){
        postDataToService(url,files,null,uploadImageToServiceListener);
    }

    /**
     * 上传多张图片 带参数
     * @param url
     * @param files
     * @param params
     * @param uploadImageToServiceListener
     */
    public void postDataToService(String url,List<File> files, Map<String,String> params, UploadImageToServiceListener uploadImageToServiceListener){
        try {
            MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM);//表单类型
            for(File file:files){
                RequestBody body=RequestBody.create(MediaType.parse("multipart/form-data"),file);
                multiBuilder.addFormDataPart("file",file.getName(),body);
            }
            if(params==null||params.size()==0){
            }else {
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    multiBuilder.addFormDataPart(entry.getKey(), entry.getValue());
                }
            }
            RequestBody multiBody=multiBuilder.build();
            UploadImageHelp.getInstance().upload(url, multiBody, new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    uploadImageToServiceListener.uploadToService(false,e.getLocalizedMessage());
                }
                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if(response.code()==200){
                        UploadImageBean uploadImageBean=GsonUtils.gsonToBean(response.body().string(),UploadImageBean.class);
                        if(uploadImageBean.getCode()==200){
                            try {
                                List<String> urls=new ArrayList<>();
                                for(UploadResultBean uploadResultBean:uploadImageBean.getData().getResults()){
                                    urls.add(uploadResultBean.getFilePath());
                                }
                                uploadImageToServiceListener.uploadToService(true, StringUtils.ListToStr(urls));
                            }catch (Exception e){
                                uploadImageToServiceListener.uploadToService(false,e.getMessage());
                            }

                        }else {
                            uploadImageToServiceListener.uploadToService(false,uploadImageBean.getMessage());
                        }
                    }

                }
            });
        } catch (IOException e) {
            uploadImageToServiceListener.uploadToService(false,e.getMessage());
        }
    }
    public static String getResponseBody(Response response) {

        Charset UTF8 = Charset.forName("UTF-8");
        ResponseBody responseBody = response.body();
        BufferedSource source = responseBody.source();
        try {
            source.request(Long.MAX_VALUE); // Buffer the entire body.
        } catch (IOException e) {
            e.printStackTrace();
        }
        Buffer buffer = source.buffer();

        Charset charset = UTF8;
        MediaType contentType = responseBody.contentType();
        if (contentType != null) {
            try {
                charset = contentType.charset(UTF8);
            } catch (UnsupportedCharsetException e) {
                e.printStackTrace();
            }
        }
        return buffer.clone().readString(charset);
    }
    /**
     * 压缩图片
     * @param files
     * @param compressImageListener
     */
    public void compressImage(List<File> files, CompressImageListener compressImageListener){
        RxJavaUtils.executeAsyncTask(new RxAsyncTask<List<File>,List<File>>(files) {

            @Override
            public void doInUIThread(List<File> files) {
                compressImageListener.afterCompressImage(files);
            }
            @Override
            public List<File> doInIOThread(List<File> imageItemLists) {
                try {
                    List<File> files1= Luban.with(AppUtils.getApp()).load(files).get();
                    return files1;
                } catch (IOException e) {
                    e.printStackTrace();
                    return null;
                }
            }
        });
    }
}
