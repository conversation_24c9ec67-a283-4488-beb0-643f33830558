package com.zizhiguanjia.model_home.ui;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.Gravity;
import android.view.KeyEvent;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.ContainerActivity;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.AddressHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.listeners.MessageSuccessPayListener;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.CertificateMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig;
import com.zizhiguanjia.model_home.BuildConfig;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.config.RouteConfig;
import com.zizhiguanjia.model_home.databinding.HomeActivityBinding;
import com.zizhiguanjia.model_home.fragment.HomeFragment;
import com.zizhiguanjia.model_home.fragment.NativeStudyFragment;
import com.zizhiguanjia.model_home.listener.IFlutterStudyFragment;
import com.zizhiguanjia.model_home.listener.IHomeFragment;
import com.zizhiguanjia.model_home.listener.INativeStudyFragment;
import com.zizhiguanjia.model_home.navigator.HomeActivityNavigator;
import com.zizhiguanjia.model_home.utils.Watermark;
import com.zizhiguanjia.model_home.viewmodel.HomeViewModel;

import io.reactivex.functions.Consumer;

@Route(path = HomeRoutherPath.MAIN_ACTIVITY)
@BindRes(isMain = true)
public class HomeActivity extends ContainerActivity implements HomeActivityNavigator, IHomeFragment, IFlutterStudyFragment, INativeStudyFragment {
    private HomeActivityBinding binding;
    private long firstTime = 0;
    @BindViewModel
    HomeViewModel homeViewModel;

    // 两个Fragment实例
    private HomeFragment flutterFragment;
    //    private NativeStudyFragment nativeFragment;
    private HomeFragment homeFragment; // 保留旧的HomeFragment用于兼容

    @Autowired(name = "routePath")
    public String routePath;

    // 是否显示原生页面
    private boolean showNativePage = false;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
//        if (routePath != null) {
//            //如果是首页的话就跳转到原生首页
//            if (routePath.equals("/main")) {
//                startActivity(new Intent(this, HomeNativeActivity.class));
//                routePath = getAcvivityRoute();
//                finish();
//            }
//        }
        // 获取是否显示原生页面的参数
        if (getIntent() != null) {
            showNativePage = getIntent().getBooleanExtra("showNativePage", false); // 默认显示Flutter页面
        }
        LogUtils.e("HomeActivity onCreate - showNativePage: " + showNativePage);

        super.onCreate(savedInstanceState);
        ARouter.getInstance().inject(this);

        init();
        initFragment();
        initListener();
        String phone = AccountHelper.getCurrentLoginAccount();
        LogUtils.e("账号====>>>" + phone);
    }

    /**
     * 测试原生页面
     * 可以在应用启动时调用此方法来直接测试原生页面
     */
    public static void testNativePage(ContainerActivity activity) {
        startWithNativePage(activity);
    }

    @Override
    public void init() {
        String pid = KvUtils.get("mjPid", "1100");
        BaseConfig.MAJOR_PID = Integer.parseInt(pid);

        // 设置布局
        binding = DataBindingUtil.setContentView(this, R.layout.home_activity);
        binding.setModel(homeViewModel);
        // 修改初始化调用，传入正确的类型
        homeViewModel.init(this, null);

        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if (msgEvent.getCode() == 666666) {
                    clearAllActivity();
                } else if (msgEvent.getCode() == PayMsgTypeConfig.PAY_MSG_TS) {
                    MessageHelper.openGotoSubject(new MessageSuccessPayListener() {
                        @Override
                        public void GotoSelectSubject() {
                            startFragment(CertificateHelper.startChoseCertificateZzByAddress());
                        }
                    }, getActivity());
                } else if (msgEvent.getCode() == CertificateMsgTypeConfig.CERTIFICATE_MSG_BIND_SUCCESS) {
                    restart(false);
                } else if (msgEvent.getCode() == AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS) {
//                    homeViewModel.initCretificate();
                } else if (msgEvent.getCode() == 10106
                        || msgEvent.getCode() == AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT) {
                    homeViewModel.initCretificate();
                }
            }
        });
        if (BaseConfig.deBug) {
            Watermark.getInstance().setText("Api版本：" + BaseAPI.VERSION_DES + "编号版本：" + BuildConfig.releaseTime).setRotation(-30).show(this);
        }

        initTopTitle();
    }

    @Override
    public void initListener() {
    }

    @Override
    public void clientOffline() {
        LogUtils.e("账号过期------提示打开");
        RxJavaUtils.delay(1, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                MessageHelper.openGeneralCentDialog(getActivity(),
                        "温馨提示",
                        "身份信息过期，请重新登录！",
                        "继续做题",
                        "重新登录",
                        true,
                        false,
                        new GeneralDialogListener() {
                            @Override
                            public void onCancel() {

                            }

                            @Override
                            public void onConfim() {
                                startFragment(AccountHelper.showAccountLogin());
                            }

                            @Override
                            public void onDismiss() {

                            }
                        });
            }
        });
    }

    @Override
    public String getAcvivityRoute() {
        Uri data = getIntent().getData();
        String routh = null;
        if (data != null) {
            routh = data.getQueryParameter("action");
        } else {
            if (StringUtils.isEmpty(routePath)) {
                routh = RouteConfig.ROUTE_HOME;
            } else {
                routh = routePath;
            }
        }
        if (StringUtils.isEmpty(routh)) {
            return RouteConfig.ROUTE_HOME;
        }
        return routh;
    }

    @Override
    public void reshTxtSize(String size) {
//        binding.icnTop.titleTopic.setTextSize(Float.parseFloat(size));
    }

    @Override
    public void toGoUserInfo() {
        startFragment(UserHelper.mainPage(this));
    }

    @Override
    public void toGoChoiceCretificate(String addressName, String addressId, boolean tgAddress) {
        CertificateHelper.startCertificateActivity(addressName, addressId, tgAddress);
    }

    @Override
    public void toGoChoiceAddress() {
        AddressHelper.start(this);
    }

    @Override
    public Fragment initBaseFragment() {
        // 创建Fragment但不添加到任何容器
        // 在ContainerActivity中，这个方法的返回值会被添加到android.R.id.content容器
        // 但我们希望在initFragment中自己管理Fragment
        // 所以这里返回null，让ContainerActivity跳过添加Fragment的步骤
        return null;
    }

    @Override
    public void initFragment() {
        // 初始化Fragment
        String route = getAcvivityRoute();
        //如果是首页的话就跳转到原生首页
        if (route.equals("/main")) {
            startActivity(new Intent(this, HomeNativeActivity.class));
            finish();
        }
        if (flutterFragment == null) {
            flutterFragment = HomeFragment.getInstance(route);
            flutterFragment.setiHomeFragment(this); // 设置iHomeFragment接口实现
        }

//        if (nativeFragment == null) {
//            nativeFragment = NativeStudyFragment.newInstance();
//            nativeFragment.setListener(this);
//        }

        // 根据showNativePage参数决定显示哪个Fragment
        FragmentManager fragmentManager = getSupportFragmentManager();
        FragmentTransaction transaction = fragmentManager.beginTransaction();

        if (showNativePage) {
//            transaction.add(R.id.homeMainFf, nativeFragment, "native_fragment");
//            LogUtils.e("添加原生页面Fragment");
        } else {
            transaction.add(R.id.homeMainFf, flutterFragment, "flutter_fragment");
            LogUtils.e("添加Flutter页面Fragment");
        }

        transaction.commit();
    }

    @Override
    protected void onResume() {
        super.onResume();
        postAtTime(new Runnable() {
            @Override
            public void run() {
                restart(false);
            }
        }, 1500);
    }

    private void initTopTitle() {
        LogUtils.e("判断下路由的状态---->>>>" + routePath);
//        binding.icnTop.relMain.setVisibility(StringUtils.isEmpty(routePath) || routePath == RouteConfig.ROUTE_HOME ? View.VISIBLE : View.GONE);
    }

    @Override
    public void restart(boolean passCertificate) {
        if (StringUtils.isEmpty(routePath) || routePath == RouteConfig.ROUTE_HOME) {
            if (!passCertificate) {
                homeViewModel.initCretificate();
            }
        }
        initTopTitle();

        // 刷新当前显示的Fragment数据
//        if (showNativePage && nativeFragment != null) {
//            nativeFragment.refreshData();
//        } else if
//        (!showNativePage && flutterFragment != null) {
//            flutterFragment.reshHomeData(passCertificate);
//        }

        // 用户登录状态不需要初始化，直接返回
        // homeViewModel.initUserLoginState();
    }

    @Override
    public void loginSuccess() {
        restart(true);
    }

    @Override
    public void originalTitle(String titleTopic) {
        // 直接设置标题，避免调用homeViewModel.refreshTitle造成无限循环
//        try {
//            if (binding != null && binding.icnTop != null && binding.icnTop.titleTopic != null) {
//                binding.icnTop.titleTopic.setText(titleTopic);
//            }
//        } catch (Exception e) {
//            com.wb.lib_utils.utils.log.LogUtils.e("HomeActivity - 设置标题失败: " + e.getMessage());
//        }
    }

    @Override
    public void accountOffline() {
        clearAllActivity();
        clientOffline();
        restart(false);
    }

    /**
     * 启动带有原生页面的HomeActivity
     */
    public static void startWithNativePage(ContainerActivity activity) {
        Intent intent = new Intent(activity, HomeActivity.class);
        intent.putExtra("showNativePage", true);
        LogUtils.e("启动原生页面Activity");
        activity.startActivity(intent);
    }

    /**
     * 切换到原生学习页面
     */
    public void switchToNativePage() {
        FragmentManager fragmentManager = getSupportFragmentManager();

        // 先检查是否已经存在Native Fragment
        NativeStudyFragment existingFragment = (NativeStudyFragment) fragmentManager.findFragmentByTag("native_fragment");

//        if (existingFragment != null) {
//            // 如果已经存在，使用现有的Fragment
//            nativeFragment = existingFragment;
//            nativeFragment.setListener(this);
//        } else if (nativeFragment == null) {
//            // 如果不存在且nativeFragment为null，创建新的Fragment
//            nativeFragment = NativeStudyFragment.newInstance();
//            nativeFragment.setListener(this);
//        }
//
        FragmentTransaction transaction = fragmentManager.beginTransaction();

        // 隐藏Flutter页面Fragment
        if (flutterFragment != null && flutterFragment.isAdded()) {
            transaction.hide(flutterFragment);
        }

        // 添加或显示原生页面Fragment
//        if (nativeFragment.isAdded()) {
//            transaction.show(nativeFragment);
//        } else {
//            transaction.add(R.id.homeMainFf, nativeFragment, "native_fragment");
//        }

        transaction.commit();
        showNativePage = true;
        LogUtils.e("切换到原生页面");
    }

    /**
     * 切换到Flutter页面
     */
//    public void switchToFlutterPage() {
//        FragmentManager fragmentManager = getSupportFragmentManager();
//
//        // 先检查是否已经存在Flutter Fragment
//        FlutterStudyFragment existingFragment = (FlutterStudyFragment) fragmentManager.findFragmentByTag("flutter_fragment");
//
//        if (existingFragment != null) {
//            // 如果已经存在，使用现有的Fragment
//            flutterFragment = existingFragment;
//            flutterFragment.setListener(this);
//        } else if (flutterFragment == null) {
//            // 如果不存在且flutterFragment为null，创建新的Fragment
//            flutterFragment = FlutterStudyFragment.newInstance(getAcvivityRoute());
//            flutterFragment.setListener(this);
//        }
//
//        FragmentTransaction transaction = fragmentManager.beginTransaction();
//
//        // 隐藏原生页面Fragment
//        if (nativeFragment != null && nativeFragment.isAdded()) {
//            transaction.hide(nativeFragment);
//        }
//
//        // 添加或显示Flutter页面Fragment
//        if (flutterFragment.isAdded()) {
//            transaction.show(flutterFragment);
//        } else {
//            transaction.add(R.id.homeMainFf, flutterFragment, "flutter_fragment");
//        }
//
//        transaction.commit();
//        showNativePage = false;
//        LogUtils.e("切换到Flutter页面");
//    }
    @Override
    public void clearAllActivity() {
        cleanAllActivity(true);
    }

    @Override
    public void showGuide2() {
        // 使用 vChapter2 视图进行引导
//        if (binding.icnTop.vChapter2 != null) {
//            binding.icnTop.vChapter2.post(new Runnable() {
//                @Override
//                public void run() {
//                    final GuideBuilder builder1 = new GuideBuilder();
//                    builder1.setTargetView(binding.icnTop.vChapter2)
//                            .setAlpha(150)
//                            .setHighTargetCorner(DpUtils.dp2px(getActivity(), 0))
//                            .setHighTargetPaddingBottom(DpUtils.dp2px(getActivity(), 0))
//                            .setHighTargetGraphStyle(Component.ROUNDRECT);
//                    builder1.setOnVisibilityChangedListener(new GuideBuilder.OnVisibilityChangedListener() {
//                        @Override
//                        public void onShown() {
//                        }
//
//                        @Override
//                        public void onDismiss() {
//                        }
//                    });
//
//                    builder1.addComponent(new CustomGuideView2());
//                    Guide guide = builder1.createGuide();
//                    guide.show(getActivity());
//                }
//            });
//        }
    }

    @Override
    public void closeActivity() {
        finish();
    }

    @Override
    public void onHomeDataRefresh(BaseData<HomeBean> data) {
        if (data != null && data.Data != null) {
            homeViewModel.refreshTitle(data.Data.getMajorName());
        }
    }

    @Override
    public boolean isMain() {
        return true;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            if (System.currentTimeMillis() - firstTime > 2000) {
                ToastUtils.normal("再按一次退出程序", Gravity.CENTER);
                firstTime = System.currentTimeMillis();
            } else {
                finish();
                System.exit(0);
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    // IFlutterStudyFragment接口实现
    @Override
    public void showLoading(boolean visition, String msg) {
        if (homeFragment != null) {
            homeFragment.showLoading(visition, msg);
        }
    }

    @Override
    public void showTsDialog(int type) {
        if (homeFragment != null) {
            homeFragment.showTsDialog(type);
        }
    }

    @Override
    public void showGuide() {
        if (homeFragment != null) {
            homeFragment.showGuide();
        }
    }

    @Override
    public void closeView() {
        finish();
    }

    @Override
    public void toMainPage(String pid) {
        BaseConfig.MAJOR_PID = Integer.parseInt(pid);
        KvUtils.save("newUserCertificateGuide", true);

        LogUtils.e("跳转了");
        startActivity(new Intent(this, HomeActivity.class));
    }

    @Override
    public void showDyVipUpdateHintDialog(DyVipUpdateHintBean data) {
        // 显示抖音VIP更新提示对话框
        LogUtils.e("HomeActivity - 显示抖音VIP更新提示对话框: " + (data != null ? data.toString() : "null"));
        // 如果有相应的Fragment处理此对话框的方法
        if (flutterFragment != null) {
            // 可以委托给Fragment处理
            // flutterFragment.showDyVipUpdateHintDialog(data);
            ToastUtils.normal("VIP更新提示");
        }
    }

    // 实现IFlutterStudyFragment接口的方法
    @Override
    public void showDyVipUpdateHintDialog(Object data) {
        // 将Object类型转换为DyVipUpdateHintBean类型并调用HomeActivityNavigator的方法
        if (data instanceof DyVipUpdateHintBean) {
            showDyVipUpdateHintDialog((DyVipUpdateHintBean) data);
        } else {
            LogUtils.e("HomeActivity - showDyVipUpdateHintDialog(Object) - 数据类型不匹配");
            ToastUtils.normal("数据类型不匹配，无法显示VIP更新提示");
        }
    }

    @Override
    public void reshHomeData(boolean passCertificate) {
        homeViewModel.reshHomeData(passCertificate);
    }

    @Override
    public void setTopTitleInfo(boolean show, String title) {
        homeViewModel.setTopTitleInfo(show, title);
    }

    @Override
    public void onRefreshComplete() {
        // 刷新完成回调
        LogUtils.e("HomeActivity - 刷新完成");
    }

    @Override
    public void onActionClick(int actionType, String data) {
        // 点击事件回调
        LogUtils.e("HomeActivity - 点击事件: " + actionType + ", 数据: " + data);
        switch (actionType) {
            case 1: // 例如1表示章节练习
                // 处理章节练习点击
                break;
            case 2: // 例如2表示模拟考试
                // 处理模拟考试点击
                break;
            // 其他点击类型处理
            default:
                break;
        }
    }

    @Override
    public void onDataStatusChanged(int dataType, String newValue) {
        // 数据状态变化回调
        LogUtils.e("HomeActivity - 数据状态变化: " + dataType + ", 新值: " + newValue);
        switch (dataType) {
            case 1: // 例如1表示学习进度变化
                // 处理学习进度变化
                break;
            case 2: // 例如2表示正确率变化
                // 处理正确率变化
                break;
            // 其他数据类型处理
            default:
                break;
        }
    }

    @Override
    public void showToast(String msg) {
        // 显示Toast消息
        ToastUtils.normal(msg);
    }
}
