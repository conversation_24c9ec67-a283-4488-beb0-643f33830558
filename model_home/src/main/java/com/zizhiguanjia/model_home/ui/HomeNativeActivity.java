package com.zizhiguanjia.model_home.ui;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.binioter.guideview.Component;
import com.binioter.guideview.Guide;
import com.binioter.guideview.GuideBuilder;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.ContainerActivity;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.lib_base.constants.MessageRouterPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.AddressHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.listeners.MessageSuccessPayListener;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.CertificateMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig;
import com.zizhiguanjia.model_home.BuildConfig;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.DyVipUpdateHintBean;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.config.RouteConfig;
import com.zizhiguanjia.model_home.databinding.HomeMainActivityBinding;
import com.zizhiguanjia.model_home.fragment.NativeStudyAIFragment;
import com.zizhiguanjia.model_home.fragment.NativeStudyFragment;
import com.zizhiguanjia.model_home.listener.IFlutterStudyFragment;
import com.zizhiguanjia.model_home.listener.IHomeFragment;
import com.zizhiguanjia.model_home.listener.INativeStudyFragment;
import com.zizhiguanjia.model_home.navigator.HomeActivityNavigator;
import com.zizhiguanjia.model_home.utils.Watermark;
import com.zizhiguanjia.model_home.viewmodel.HomeFlutterViewModel;
import com.zizhiguanjia.model_home.viewmodel.HomeViewModel;

import io.reactivex.functions.Consumer;

@Route(path = HomeRoutherPath.HOME_MAIN_ACTIVITY)
@BindRes(isMain = true)
public class HomeNativeActivity extends ContainerActivity implements HomeActivityNavigator, IHomeFragment, IFlutterStudyFragment, INativeStudyFragment {
    private HomeMainActivityBinding binding;
    private long firstTime = 0;
    @BindViewModel
    HomeViewModel homeViewModel;
    @BindViewModel
    HomeFlutterViewModel homeFlutterViewModel;
    // 加载对话框
    private LoadingPopupView loadingPopupView;
    private IHomeFragment iHomeFragment;
    // 三个Fragment实例
//    private HomeFragment homeFragment; // 保留旧的HomeFragment用于兼容
    private NativeStudyFragment nativeFragment;
    private NativeStudyAIFragment nativeAIFragment; // 新增Flutter风格的原生Fragment

    @Autowired(name = "routePath")
    public String routePath;

    // 显示模式：0-旧版HomeFragment，1-原生NativeStudyFragment，2-Flutter风格的NativeStudyAIFragment
    private int displayMode = 2;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        // 获取显示模式参数
//        if (getIntent() != null) {
//            displayMode = getIntent().getIntExtra("displayMode", 0);
//        }
        LogUtils.e("HomeActivity onCreate - displayMode: " + displayMode);
        
        // 检查引导状态
        boolean hasShownGuide = KvUtils.get("main_guilder", false);
        LogUtils.e("HomeActivity onCreate - 是否已显示过引导页: " + hasShownGuide);

        super.onCreate(savedInstanceState);
        ARouter.getInstance().inject(this);
        
        LogUtils.e("HomeNativeActivity - onCreate - 开始初始化");
        init(); // Bus观察者注册在init方法中
        initFragment();
        initListener();
        String phone = AccountHelper.getCurrentLoginAccount();
        LogUtils.e("账号====>>>" + phone);

        // 初始化加载对话框
        loadingPopupView = new PopupManager.Builder(this).asLoading("", R.layout.popup_center_impl_loading);
        //更新App
//        CommonHelper.upDataApp(this);
    }

    /**
     * 测试原生页面
     * 可以在应用启动时调用此方法来直接测试原生页面
     */
    public static void testNativePage(ContainerActivity activity) {
        Intent intent = new Intent(activity, HomeNativeActivity.class);
        intent.putExtra("displayMode", 1); // 显示原生页面
        activity.startActivity(intent);
    }

    /**
     * 测试Flutter风格的原生页面
     * 可以在应用启动时调用此方法来直接测试Flutter风格的原生页面
     */
    public static void testNativeAIPage(ContainerActivity activity) {
        Intent intent = new Intent(activity, HomeNativeActivity.class);
        intent.putExtra("displayMode", 2); // 显示Flutter风格的原生页面
        activity.startActivity(intent);
    }

    @Override
    public void init() {
        String pid = KvUtils.get("mjPid", "1100");
        BaseConfig.MAJOR_PID = Integer.parseInt(pid);

        // 设置布局
        binding = DataBindingUtil.setContentView(this, R.layout.home_main_activity);
//        binding.setModel(homeViewModel);
        LogUtils.e("HomeNativeActivity - 注册Bus观察者");
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                LogUtils.e("HomeNativeActivity - 接收到Bus事件: " + msgEvent.getCode());
                if (msgEvent.getCode() == 666666) {
                    LogUtils.e("HomeNativeActivity - 接收到清除所有Activity事件");
                    clearAllActivity();
                } else if (msgEvent.getCode() == PayMsgTypeConfig.PAY_MSG_TS) {
                    LogUtils.e("HomeNativeActivity - 接收到支付提示事件");
                    MessageHelper.openGotoSubject(new MessageSuccessPayListener() {
                        @Override
                        public void GotoSelectSubject() {
                            startFragment(CertificateHelper.startChoseCertificateZzByAddress());
                        }
                    }, getActivity());
                } else if (msgEvent.getCode() == CertificateMsgTypeConfig.CERTIFICATE_MSG_BIND_SUCCESS) {
                    LogUtils.e("HomeNativeActivity - 接收到证书绑定成功事件");
                    restart(false);
                } else if (msgEvent.getCode() == AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS) {
                    LogUtils.e("HomeNativeActivity - 接收到账号登录成功事件");
                    homeViewModel.initCretificate();
                } else if (msgEvent.getCode() == 10106
                        || msgEvent.getCode() == AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT) {
                    LogUtils.e("HomeNativeActivity - 接收到账号登出事件");
                    homeViewModel.initCretificate();
                }
            }
        });
        if (BaseConfig.deBug) {
            Watermark.getInstance().setText("Api版本：" + BaseAPI.VERSION_DES + "编号版本：" + BuildConfig.releaseTime).setRotation(-30).show(this);
        }

        initTopTitle();
    }

    @Override
    public void initListener() {
    }

    @Override
    public void clientOffline() {
        LogUtils.e("账号过期------提示打开");
        RxJavaUtils.delay(1, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                MessageHelper.openGeneralCentDialog(getActivity(),
                        "温馨提示",
                        "身份信息过期，请重新登录！",
                        "继续做题",
                        "重新登录",
                        true,
                        false,
                        new GeneralDialogListener() {
                            @Override
                            public void onCancel() {

                            }

                            @Override
                            public void onConfim() {
                                startFragment(AccountHelper.showAccountLogin());
                            }

                            @Override
                            public void onDismiss() {

                            }
                        });
            }
        });
    }

    @Override
    public String getAcvivityRoute() {
        Uri data = getIntent().getData();
        String routh = null;
        if (data != null) {
            routh = data.getQueryParameter("action");
        } else {
            if (StringUtils.isEmpty(routePath)) {
                routh = RouteConfig.ROUTE_HOME;
            } else {
                routh = routePath;
            }
        }
        if (StringUtils.isEmpty(routh)) {
            return RouteConfig.ROUTE_HOME;
        }
        return routh;
    }

    @Override
    public void reshTxtSize(String size) {
//        binding.icnTop.titleTopic.setTextSize(Float.parseFloat(size));
    }

    @Override
    public void toGoUserInfo() {
        startFragment(UserHelper.mainPage(this));
    }

    @Override
    public void toGoChoiceCretificate(String addressName, String addressId, boolean tgAddress) {
        CertificateHelper.startCertificateActivity(addressName, addressId, tgAddress);
    }

    @Override
    public void toGoChoiceAddress() {
        AddressHelper.start(this);
    }

    @Override
    public Fragment initBaseFragment() {
        // 创建Fragment但不添加到任何容器
        // 在ContainerActivity中，这个方法的返回值会被添加到android.R.id.content容器
        // 但我们希望在initFragment中自己管理Fragment
        // 所以这里返回null，让ContainerActivity跳过添加Fragment的步骤
        return null;
    }

    @Override
    public void initFragment() {
        // 初始化Fragment
        String route = getAcvivityRoute();

        // 初始化所有Fragment
//        if (homeFragment == null) {
//            homeFragment = HomeFragment.getInstance(route);
//            homeFragment.setiHomeFragment(this);
//        }

        if (nativeFragment == null) {
            nativeFragment = NativeStudyFragment.newInstance();
            nativeFragment.setListener(this);
        }

        if (nativeAIFragment == null) {
            nativeAIFragment = NativeStudyAIFragment.newInstance();
            nativeAIFragment.setListener(this);
            nativeAIFragment.setiHomeFragment(this);
        }

        // 默认先显示一个Fragment，后续会根据HasAiKnowledgetree字段决定显示哪个
        switchToMode(displayMode);

        // 获取首页数据，在获取到数据后会根据HasAiKnowledgetree字段切换到相应的Fragment
        LogUtils.e("HomeNativeActivity - 开始获取首页数据");
        if (homeViewModel != null) {
            homeViewModel.reshHomeData(false);
        }
    }

    /**
     * 根据模式切换显示的Fragment
     *
     * @param mode 0-旧版HomeFragment，1-原生NativeStudyFragment，2-Flutter风格的NativeStudyAIFragment
     */
    private void switchToMode(int mode) {
        this.displayMode = mode;

        FragmentManager fragmentManager = getSupportFragmentManager();
        FragmentTransaction transaction = fragmentManager.beginTransaction();

        // 先隐藏所有Fragment
//        if (homeFragment != null && homeFragment.isAdded()) {
//            transaction.hide(homeFragment);
//        }
        if (nativeFragment != null && nativeFragment.isAdded()) {
            transaction.hide(nativeFragment);
        }
        if (nativeAIFragment != null && nativeAIFragment.isAdded()) {
            transaction.hide(nativeAIFragment);
        }

        // 根据模式显示对应Fragment
        switch (mode) {
//            case 0: // 显示旧版HomeFragment
//                if (homeFragment != null) {
//                    if (homeFragment.isAdded()) {
//                        transaction.show(homeFragment);
//                    } else {
//                        transaction.add(R.id.fl_container, homeFragment);
//                        transaction.show(homeFragment);
//                    }
//                }
//                break;
            case 1: // 显示原生NativeStudyFragment
                if (nativeFragment != null) {
                    if (nativeFragment.isAdded()) {
                        transaction.show(nativeFragment);
                    } else {
                        transaction.add(R.id.fl_container, nativeFragment);
                        transaction.show(nativeFragment);
                    }
                }
                break;
            case 2: // 显示Flutter风格的NativeStudyAIFragment
                if (nativeAIFragment != null) {
                    if (nativeAIFragment.isAdded()) {
                        transaction.show(nativeAIFragment);
                    } else {
                        transaction.add(R.id.fl_container, nativeAIFragment);
                        transaction.show(nativeAIFragment);
                    }
                }
                break;
        }

        transaction.commitAllowingStateLoss();
    }

    /**
     * 切换到旧版HomeFragment
     */
    public void switchToHomeFragment() {
        switchToMode(0);
    }

    /**
     * 切换到原生NativeStudyFragment
     */
    public void switchToNativePage() {
        switchToMode(1);
    }

    /**
     * 切换到Flutter风格的NativeStudyAIFragment
     */
    public void switchToNativeAIPage() {
        switchToMode(2);
    }

    /**
     * 循环切换显示模式
     * 每次调用会切换到下一个模式：旧版HomeFragment -> 原生NativeStudyFragment -> Flutter风格NativeStudyAIFragment -> 旧版HomeFragment
     */
    public void switchToNextMode() {
        int nextMode = (displayMode + 1) % 3;
        switchToMode(nextMode);
        String modeName = nextMode == 0 ? "旧版HomeFragment" : (nextMode == 1 ? "原生NativeStudyFragment" : "Flutter风格NativeStudyAIFragment");
        ToastUtils.normal("已切换到" + modeName);
    }

    @Override
    protected void onResume() {
        super.onResume();
        new Thread() {
            @Override
            public void run() {
                homeViewModel.initCretificate();
            }
        }.start();
    }

    private void initTopTitle() {
        homeViewModel.initCretificate();
    }

    @Override
    public void restart(boolean passCertificate) {
        if (passCertificate) {
            homeViewModel.initCretificate();
        } else {
//            if (homeFragment != null) {
//                // 使用reshHomeData方法代替restart方法
//                homeFragment.reshHomeData(passCertificate);
//            }
            if (nativeFragment != null) {
                nativeFragment.refreshData();
            }
            if (nativeAIFragment != null) {
                nativeAIFragment.refreshData();
            }
        }
    }

    @Override
    public void loginSuccess() {
        homeViewModel.initCretificate();
    }

    @Override
    public void originalTitle(String titleTopic) {
//        binding.icnTop.titleTopic.setText(titleTopic);
    }

    @Override
    public void accountOffline() {
        clientOffline();
    }

    /**
     * 以原生页面模式启动
     */
    public static void startWithNativePage(ContainerActivity activity) {
        Intent intent = new Intent(activity, HomeNativeActivity.class);
        intent.putExtra("displayMode", 1); // 显示原生页面
        activity.startActivity(intent);
    }

    /**
     * 以Flutter风格的原生页面模式启动
     */
    public static void startWithNativeAIPage(ContainerActivity activity) {
        Intent intent = new Intent(activity, HomeNativeActivity.class);
        intent.putExtra("displayMode", 2); // 显示Flutter风格的原生页面
        activity.startActivity(intent);
    }

    @Override
    public void clearAllActivity() {
        finish();
    }

    @Override
    public void showGuide2() {
        // 简化引导视图实现
        GuideBuilder builder = new GuideBuilder();
        builder.setTargetView(binding.vChapter)
                .setAlpha(150)
                .setHighTargetCorner(20)
                .setHighTargetPadding(10)
                .setOverlayTarget(false)
                .setOutsideTouchable(false);

        // 使用默认的Component实现
        builder.addComponent(new Component() {
            @Override
            public View getView(LayoutInflater inflater) {
                return inflater.inflate(R.layout.home_guide_two_layout, null);
            }

            @Override
            public int getAnchor() {
                return Component.ANCHOR_BOTTOM;
            }

            @Override
            public int getFitPosition() {
                return Component.FIT_END;
            }

            @Override
            public int getXOffset() {
                return 10;
            }

            @Override
            public int getYOffset() {
                return 10;
            }
        });

        // 添加监听器，在引导页关闭时保存状态
        builder.setOnVisibilityChangedListener(new GuideBuilder.OnVisibilityChangedListener() {
            @Override
            public void onShown() {
                // 引导页显示时的操作
            }

            @Override
            public void onDismiss() {
                // 引导页关闭时，保存状态，下次不再显示
                KvUtils.save("main_guilder", true);
                LogUtils.e("HomeNativeActivity - showGuide2 - 引导页关闭，已保存状态");
            }
        });

        Guide guide = builder.createGuide();
        guide.show(getActivity());
    }

    @Override
    public void closeActivity() {
        finish();
    }

    @Override
    public void onHomeDataRefresh(BaseData<HomeBean> data) {
        // 不需要将数据传递给homeFragment，因为homeFragment已经有自己的数据刷新机制
        if (data != null && data.Data != null) {
            homeViewModel.refreshTitle(data.Data.getMajorName());

            // 根据HasAiKnowledgetree字段决定显示哪个Fragment
            boolean hasAiKnowledgetree = data.Data.isHasAiKnowledgetree();
            LogUtils.e("HomeNativeActivity - hasAiKnowledgetree: " + hasAiKnowledgetree);

            // 如果有AI知识树，显示NativeStudyAIFragment，否则显示NativeStudyFragment
            int targetDisplayMode = hasAiKnowledgetree ? 1 : 2; // 2-NativeStudyAIFragment, 1-NativeStudyFragment

            // 将数据传递给NativeStudyFragment
            if (nativeFragment != null) {
                LogUtils.e("HomeNativeActivity - 将HomeBean数据传递给NativeStudyFragment");
                nativeFragment.updateHomeData(data.Data);
            }
            if (nativeAIFragment != null) {
                LogUtils.e("HomeNativeActivity - 将HomeBean数据传递给NativeStudyFragment");
                nativeAIFragment.updateHomeData(data.Data);
            }

            if (displayMode != targetDisplayMode) {
                // 仅当当前显示模式与目标模式不同时才切换
                LogUtils.e("HomeNativeActivity - 切换显示模式：" +
                        (targetDisplayMode == 2 ? "NativeStudyAIFragment" : "NativeStudyFragment"));
                switchToMode(targetDisplayMode);
            }
            //初始化更新题库，更新App
            initData(data.Data);
        }

    }

    private void initData(HomeBean data) {
        //        CommonManager.getInstance().upDate(true, this);
        //更新App  暂时屏蔽

        //初始化更新题库
//        ARouter.getInstance().build(MessageRouterPath.MAIN_ACTIVITY)
////                .withInt("type",1)
////                .withString("addressName",addressName)
////                .withString("addressId",addressId)
////                .withBoolean("tgAddress",tgAddress)
//                .navigation();
    }

    @Override
    public boolean isMain() {
        return true;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            long secondTime = System.currentTimeMillis();
            if (secondTime - firstTime > 2000) {
                ToastUtils.normal("再按一次退出程序");
                firstTime = secondTime;
                return true;
            } else {
                finish();
                System.exit(0);
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void showLoading(boolean visition, String msg) {
        if (visition) {
            if (loadingPopupView == null) {
                loadingPopupView = new PopupManager.Builder(this).asLoading(msg, R.layout.popup_center_impl_loading);
            } else {
                loadingPopupView.setTitle(msg);
            }
            if (!loadingPopupView.isShow()) loadingPopupView.show();
        } else {
            if (loadingPopupView != null && loadingPopupView.isShow()) {
                loadingPopupView.dismiss();
            }
        }
    }

    @Override
    public void showTsDialog(int type) {
//        if (homeFragment != null) {
//            homeFragment.showTsDialog(type);
//        }
    }

    @Override
    public void showGuide() {
//        if (homeFragment != null) {
//            homeFragment.showGuide();
//        }
    }

    @Override
    public void closeView() {
        finish();
    }

    @Override
    public void toMainPage(String pid) {
        startActivity(new Intent(this, HomeNativeActivity.class));
    }

    @Override
    public void showDyVipUpdateHintDialog(DyVipUpdateHintBean data) {
        // 显示抖音VIP更新提示对话框
        LogUtils.e("显示抖音VIP更新提示对话框: " + (data != null ? data.toString() : "null"));
        // 这里可以实现对话框显示逻辑，或者委托给Fragment处理
        // 如果有相应的Fragment处理此逻辑，可以调用Fragment的方法
        if (nativeAIFragment != null) {
            // 假设NativeStudyAIFragment有处理此对话框的方法
            // nativeAIFragment.showDyVipUpdateHintDialog(data);
            ToastUtils.normal("VIP更新提示");
        }
    }

    // 实现IFlutterStudyFragment接口的方法
    @Override
    public void showDyVipUpdateHintDialog(Object data) {
        // 将Object类型转换为DyVipUpdateHintBean类型并调用HomeActivityNavigator的方法
        if (data instanceof DyVipUpdateHintBean) {
            showDyVipUpdateHintDialog((DyVipUpdateHintBean) data);
        } else {
            LogUtils.e("HomeNativeActivity - showDyVipUpdateHintDialog(Object) - 数据类型不匹配");
            ToastUtils.normal("数据类型不匹配，无法显示VIP更新提示");
        }
    }

    @Override
    public void reshHomeData(boolean passCertificate) {
        restart(passCertificate);
    }

    @Override
    public void setTopTitleInfo(boolean show, String title) {
        // 设置顶部标题信息
        if (binding != null) {
            // 检查是否有顶部标题栏组件
            View homeHead = binding.getRoot().findViewById(R.id.home_head);
            if (homeHead != null) {
                TextView titleTopic = homeHead.findViewById(R.id.titleTopic);
                View llHomeSwitch = homeHead.findViewById(R.id.llHomeSwitch);

                if (titleTopic != null && llHomeSwitch != null) {
                    if (show) {
                        llHomeSwitch.setVisibility(View.VISIBLE);
                        titleTopic.setText(title);
                    } else {
                        llHomeSwitch.setVisibility(View.GONE);
                    }
                } else {
                    LogUtils.e("HomeNativeActivity - 找不到顶部标题栏子组件");
                }
            } else {
                LogUtils.e("HomeNativeActivity - 找不到顶部标题栏组件");
            }
        }
    }

    @Override
    public void onRefreshComplete() {
        // 刷新完成回调
        LogUtils.e("HomeNativeActivity - 刷新完成");
        // 如果是原生Flutter风格页面
        if (displayMode == 2 && nativeAIFragment != null) {
            // 可以在这里更新UI元素
        }
    }

    @Override
    public void onActionClick(int actionType, String data) {
        // 点击事件回调
        LogUtils.e("HomeNativeActivity - 点击事件: " + actionType + ", 数据: " + data);
        switch (actionType) {
            case 1: // 例如1表示章节练习
                // 处理章节练习点击
                break;
            case 2: // 例如2表示模拟考试
                // 处理模拟考试点击
                break;
            // 其他点击类型处理
            default:
                break;
        }
    }

    @Override
    public void onDataStatusChanged(int dataType, String newValue) {
        // 数据状态变化回调
        LogUtils.e("HomeNativeActivity - 数据状态变化: " + dataType + ", 新值: " + newValue);
        switch (dataType) {
            case 1: // 例如1表示学习进度变化
                // 处理学习进度变化
                break;
            case 2: // 例如2表示正确率变化
                // 处理正确率变化
                break;
            // 其他数据类型处理
            default:
                break;
        }
    }

    @Override
    public void showToast(String msg) {
        // 显示Toast消息
        ToastUtils.normal(msg);
    }

    /**
     * 处理Banner导航
     *
     * @param url 导航URL
     */
    public void handleBannerNavigation(String url) {
        LogUtils.e("处理Banner导航: " + url);

        if (url == null || url.isEmpty()) {
            ToastUtils.normal("无效的导航地址");
            return;
        }

        // 解析URL路径
        Uri uri = Uri.parse(url);
        String path = uri.getPath();

        // 根据路径进行不同的处理
        if (path != null) {
            if (path.contains("/exam")) {
                // 跳转到考试页面
                ToastUtils.normal("正在前往考试页面");
                // 这里可以添加具体的考试页面跳转逻辑
                // startFragment(ExamHelper.toExamPage());
            } else if (path.contains("/pay")) {
                // 跳转到支付页面
                showPayFragment(com.zizhiguanjia.lib_base.config.PayRouthConfig.PAY_BANNER);
            } else {
                // 默认使用WebView打开
                openWebView(url, "");
            }
        } else {
            // 默认使用WebView打开
            openWebView(url, "");
        }
    }

    /**
     * 打开WebView
     *
     * @param url   网页URL
     * @param title 网页标题，如果为空则使用URL作为标题
     */
    public void openWebView(String url, String title) {
        LogUtils.e("打开WebView: " + url);

        if (url == null || url.isEmpty()) {
            ToastUtils.normal("无效的网页地址");
            return;
        }

        // 使用通用WebView打开网页
        Bundle args = new Bundle();
        args.putString("url", url);
        args.putString("title", StringUtils.isEmpty(title) ? url : title);

        try {
            // 直接使用ARouter导航到WebView页面，不尝试获取Fragment实例
//            ARouter.getInstance()
//                    .build("/common/webview")
//                    .withString("url", url)
//                    .withString("title", StringUtils.isEmpty(title) ? url : title)
//                    .navigation(this);
            IFragment iFragment = CommonHelper.showCommonWeb();
            iFragment.initArguments().putString("routh", "home");
            iFragment.initArguments().putString("url", url);
            iFragment.initArguments().putString("payType", "1");
            iFragment.initArguments().putString("payRouthParams", PayRouthConfig.PAY_UPDATE);
            startFragment(iFragment);
        } catch (Exception e) {
            // 如果ARouter导航失败，尝试使用系统浏览器打开
            try {
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                startActivity(intent);
            } catch (Exception ex) {
                ToastUtils.normal("无法打开网页");
                LogUtils.e("打开网页失败: " + ex.getMessage());
            }
        }
    }

    /**
     * 显示支付Fragment
     *
     * @param payType 支付类型
     */
    public void showPayFragment(String payType) {
        LogUtils.e("显示支付Fragment: " + payType);

        try {
            // 创建支付页面的Fragment并启动
            IFragment payFragment =
                    (IFragment) ARouter.getInstance()
                            .build("/pay/main")
                            .withString("payRouthParams", payType)
                            .navigation();

            if (payFragment != null) {
                startFragment(payFragment);
            } else {
                ToastUtils.normal("无法打开支付页面");
            }
        } catch (Exception e) {
            ToastUtils.normal("无法打开支付页面: " + e.getMessage());
            LogUtils.e("打开支付页面失败: " + e.getMessage());
        }
    }
}
