package com.zizhiguanjia.model_home.viedeo;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;

import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_home.R;

import cn.jzvd.JzvdStd;

public class JzvdStdVolumeAfterFullscreen extends JzvdStd {
    private ImageView ivVolume;
    private ImageView fullscreen;

    public JzvdStdVolumeAfterFullscreen(Context context) {
        super(context);
    }

    public JzvdStdVolumeAfterFullscreen(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public void init(Context context) {
        super.init(context);
        ivVolume = findViewById(R.id.volume);
        ivVolume.setTag(0);
        ivVolume.setOnClickListener(this);
        fullscreen = findViewById(R.id.fullscreen);
        //
    }

    @Override
    public void startVideo() {
        super.startVideo();
        LogUtils.e("开始播放1");
        mediaInterface.setVolume(0f, 0f);
    }

    @Override
    public void onStatePlaying() {
        super.onStatePlaying();
        LogUtils.e("开始播放2");
        int tag = (int) ivVolume.getTag();
        mediaInterface.setVolume(tag == 0 ? 0f : 1F, tag == 0 ? 0f : 1F);
    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        if (v.getId() == R.id.volume) {
            int tag = (int) ivVolume.getTag();
            if (tag == 0) {
                setVolumeOpen();
            } else {
                setVolumeClose();
            }
        }
    }

    /**
     * 静音
     */
    public void setVolumeClose() {
        mediaInterface.setVolume(0f, 0f);
        ivVolume.setImageResource(R.drawable.home_video_jy);
        ivVolume.setTag(0);
    }

    /**
     * 开启声音
     */
    public void setVolumeOpen() {
        mediaInterface.setVolume(1f, 1f);
        ivVolume.setImageResource(R.drawable.home_video_qy);
        ivVolume.setTag(1);
    }

    @Override
    public void onPrepared() {
        super.onPrepared();

    }

    @Override
    public void setScreenNormal() {
        super.setScreenNormal();
        fullscreenButton.setImageResource(R.drawable.home_video_qp);
        batteryTimeLayout.setVisibility(View.INVISIBLE);
    }

    @Override
    public int getLayoutId() {
        return R.layout.home_std_with_volume_button;
    }
}