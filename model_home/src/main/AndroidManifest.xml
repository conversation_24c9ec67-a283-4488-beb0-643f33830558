<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.zizhiguanjia.model_home">

    <application>
        <activity
            android:name=".ui.HomeActivity"
            android:screenOrientation="portrait">
            <meta-data
                android:name="io.flutter.app.android.SplashScreenUntilFirstFrame"
                android:value="true" />
            <!--            <intent-filter>-->
            <!--                <action android:name="android.intent.action.VIEW"/>-->
            <!--                <category android:name="android.intent.category.DEFAULT"/>-->
            <!--                <category android:name="android.intent.category.BROWSABLE"/>-->
            <!--                <data android:scheme="anquanyuan" android:host="kst.com" android:path="/liveper"/>-->
            <!--            </intent-filter>-->
        </activity>
        <activity
            android:name=".ui.HomeNativeActivity"
            android:screenOrientation="portrait" />
    </application>
</manifest>