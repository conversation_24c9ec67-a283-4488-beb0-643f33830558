apply plugin: 'com.alibaba.arouter'
buildscript {
    ext.kotlin_version = '1.5.30-RC'
    apply from: 'versions.gradle'
    repositories {
        google()
        maven { url 'https://maven.aliyun.com/repository/public/'}
        maven{url "https://jitpack.io"}
        maven { url 'https://repo1.maven.org/maven2/' }
        mavenLocal()
        mavenCentral()
        // hms
        maven {
            url 'http://developer.huawei.com/repo/'
            allowInsecureProtocol = true
        }
    }
    dependencies {
        classpath build_plugins.android_gradle
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        classpath "com.alibaba:arouter-register:1.0.2"
        classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.4'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.github.centerzx:UploadApkPlugin:1.0.1'
        classpath 'com.huawei.agconnect:agcp:1.4.1.300'
    }
}

allprojects {
    repositories {
        google()
        maven { url 'https://maven.aliyun.com/repository/public/'}
        maven{url "https://jitpack.io"}
        mavenLocal()
        mavenCentral()
        //hms
        maven {
            url 'http://developer.huawei.com/repo/'
            allowInsecureProtocol = true
        }
    }
    tasks.withType(Javadoc) {
        options.addStringOption('Xdoclint:none', '-quiet')
        options.addStringOption('encoding', 'UTF-8')
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}