ext.mainApp = false
apply from: rootProject.file('module.gradle')
android {
    defaultConfig {
        //仅在以application方式编译时才添加applicationId属性
        if (runAsApp) {
            applicationId build_version.applicationId + '.model_sdk'
        }
        ndk {
            abiFilters 'armeabi', 'armeabi-v7a', 'arm64-v8a'
        }

    }
}
dependencies {
    implementation 'com.bytedance.applog:RangersAppLog-Lite-cn:5.3.0'
    implementation  'com.umeng.umsdk:common:9.5.0'// (必选)
    implementation  'com.umeng.umsdk:asms:1.6.3'// 必选
    implementation 'com.umeng.umsdk:link:1.2.0'//集成智能超链SDK，必选
    implementation project(':lib_jpush')
}