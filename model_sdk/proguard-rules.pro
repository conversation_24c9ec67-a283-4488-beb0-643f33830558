# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
 -dontoptimize
 -dontpreverify
 -dontwarn cn.jpush.**
 -keep class cn.jpush.** { *; }
 -dontwarn cn.jiguang.**
 -keep class cn.jiguang.** { *; }

 -dontwarn cn.com.chinatelecom.**
 -keep class cn.com.chinatelecom.** { *; }
 -dontwarn com.ct.**
 -keep class com.ct.** { *; }
 -dontwarn a.a.**
 -keep class a.a.** { *; }
 -dontwarn com.cmic.**
 -keep class com.cmic.** { *; }
 -dontwarn com.unicom.**
 -keep class com.unicom.** { *; }
 -dontwarn com.sdk.**
 -keep class com.sdk.** { *; }

 -dontwarn com.sdk.**
 -keep class com.sdk.** { *; }
 -dontwarn cn.jiguang.**
 -keep class cn.jiguang.** { *; }
 -dontwarn cn.jpush.**
 -keep class cn.jpush.** { *; }
 -keep public class com.sina.** {
    *;
 }
-dontwarn cn.com.chinatelecom.**
-keep class cn.com.chinatelecom.** { *; }
-dontwarn com.ct.**
-keep class com.ct.** { *; }
-dontwarn a.a.**
-keep class a.a.** { *; }
-dontwarn com.cmic.**
-keep class com.cmic.** { *; }
-dontwarn com.unicom.**
-keep class com.unicom.** { *; }
-dontwarn com.sdk.**
-keep class com.sdk.** { *; }
-dontwarn com.sdk.**
-keep class com.sdk.** { *; }
-dontwarn cn.magicwindow.**
-keep class cn.magicwindow.** {*;}

-dontwarn cn.jiguang.**
-keep class cn.jiguang.** { *; }