package com.zizhiguanjia.model_sdk.viewmodel;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_sdk.api.SdkApi;

import java.util.HashMap;
import java.util.Map;

public class SdkViewModel extends CommonViewModel {
    private SdkApi api=new Http().create(SdkApi.class);
    public void postJpushData(String messageId){
        LogUtils.e("消息统计----->>>"+messageId);
        if(messageId==null||messageId.isEmpty())return;
        Map<String,String> params=new HashMap<>();
        params.put("messageId",messageId);
        launchOnlyResult(api.openJpushCount(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                BaseConfig.messageId="";
            }

            @Override
            public void error(String msg) {

            }
        });
    }
}
