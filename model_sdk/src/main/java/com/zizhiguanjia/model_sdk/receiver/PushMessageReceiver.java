package com.zizhiguanjia.model_sdk.receiver;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.Gravity;

import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.model_sdk.helper.TagAliasOperatorHelper;
import com.zizhiguanjia.model_sdk.model.JpushModel;
import com.zizhiguanjia.model_sdk.ui.SdkActivity;

import java.util.Map;

import cn.jpush.android.api.CmdMessage;
import cn.jpush.android.api.CustomMessage;
import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.api.JPushMessage;
import cn.jpush.android.api.NotificationMessage;
import cn.jpush.android.service.JPushMessageService;

public class PushMessageReceiver extends JPushMessageService {
    private static final String TAG = "PushMessageReceiver";
    @Override
    public void onMessage(Context context, CustomMessage customMessage) {
        Intent intent = new Intent("com.jiguang.demo.message");
        intent.putExtra("msg", customMessage.message);
        context.sendBroadcast(intent);

    }

    @Override
    public void onNotifyMessageOpened(Context context, NotificationMessage message) {
        LogUtils.e("用户点击888----->>>>");
        try {
            JpushModel jpushModel= GsonUtils.newInstance().getBean( message.notificationExtras, JpushModel.class);
            LogUtils.e("用户点击999----->>>>"+jpushModel.toString());
            if(jpushModel.getUrlType().equals("4")){
                Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse("ykstbd://kst.com/liveper?action=live_pre"));
                it.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP );
                context.startActivity(it);
            }else{
                if(jpushModel.getUrl()==null||jpushModel.getUrl().isEmpty())return;
                if(message==null||message.msgId==null||message.msgId.isEmpty()) return;
                if(BaseConfig.APP_RESUM){

                    if(jpushModel.getAreaId()==null||jpushModel.getAreaId().isEmpty()||jpushModel.getMajorId()==null||jpushModel.getMajorId().isEmpty()){
                        ToastUtils.normal("直播信息无效", Gravity.CENTER);
                        return;
                    }
                    Map<String,String> paramsMap=CertificateHelper.getCurrentCertificate();
                    LogUtils.e("用户点击1----->>>>"+paramsMap.toString());
                    if(paramsMap==null||paramsMap.isEmpty()){
                        ToastUtils.normal("直播信息无效", Gravity.CENTER);
                        return;
                    }
                    String  addressId=paramsMap.get("addressId");
                    String  citificateId=paramsMap.get("citificateId");
                    if(addressId.equals(jpushModel.getAreaId())||citificateId.equals(jpushModel.getMajorId())){
                        Intent i = new Intent(context, SdkActivity.class);
                        Bundle bundle = new Bundle();
                        bundle.putString(JPushInterface.EXTRA_EXTRA,message.notificationExtras);
                        bundle.putString("messId", message.msgId);
                        i.putExtras(bundle);
                        i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP );
                        context.startActivity(i);
                    }else{
                        ToastUtils.normal("直播信息无效", Gravity.CENTER);
                        return;
                    }
                }else {
                    String uri="ykstbd://kst.com/commonweb?url="+jpushModel.getUrl()+"&msgId="+message.msgId+"&cityId="+jpushModel.getAreaId()+"&majId="+jpushModel.getAreaId();
                    LogUtils.e("用户点击2----->>>>"+uri);
                    Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(uri));
                    it.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP );
                    context.startActivity(it);
                }
            }
        }catch (Exception e){
        }
    }
    @Override
    public void onMultiActionClicked(Context context, Intent intent) {
        String nActionExtra = intent.getExtras().getString(JPushInterface.EXTRA_NOTIFICATION_ACTION_EXTRA);

        //开发者根据不同 Action 携带的 extra 字段来分配不同的动作。
        if (nActionExtra == null) {
            return;
        }
        if (nActionExtra.equals("my_extra1")) {
        } else if (nActionExtra.equals("my_extra2")) {
        } else if (nActionExtra.equals("my_extra3")) {
        } else {
        }
    }

    @Override
    public void onNotifyMessageArrived(Context context, NotificationMessage message) {
        //接受到了通知
        BaseConfig.messageId=message.msgId;
    }

    @Override
    public void onNotifyMessageDismiss(Context context, NotificationMessage message) {
    }

    @Override
    public void onRegister(Context context, String registrationId) {
        Intent intent = new Intent("com.jiguang.demo.register");
        context.sendBroadcast(intent);
    }

    @Override
    public void onConnected(Context context, boolean isConnected) {
    }

    @Override
    public void onCommandResult(Context context, CmdMessage cmdMessage) {
    }

    @Override
    public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
        TagAliasOperatorHelper.getInstance().onTagOperatorResult(context,jPushMessage);
        super.onTagOperatorResult(context, jPushMessage);
    }

    @Override
    public void onCheckTagOperatorResult(Context context, JPushMessage jPushMessage) {
        TagAliasOperatorHelper.getInstance().onCheckTagOperatorResult(context,jPushMessage);
        super.onCheckTagOperatorResult(context, jPushMessage);
    }

    @Override
    public void onAliasOperatorResult(Context context, JPushMessage jPushMessage) {
        TagAliasOperatorHelper.getInstance().onAliasOperatorResult(context,jPushMessage);
        super.onAliasOperatorResult(context, jPushMessage);
    }

    @Override
    public void onMobileNumberOperatorResult(Context context, JPushMessage jPushMessage) {
        TagAliasOperatorHelper.getInstance().onMobileNumberOperatorResult(context,jPushMessage);
        super.onMobileNumberOperatorResult(context, jPushMessage);
    }

    @Override
    public void onNotificationSettingsCheck(Context context, boolean isOn, int source) {
        super.onNotificationSettingsCheck(context, isOn, source);
    }

    @Override
    public void onInAppMessageClick(Context context, NotificationMessage notificationMessage) {
    }


    @Override
    public void onPullInAppResult(Context context, JPushMessage jPushMessage) {
    }

    @Override
    public void onGeofenceRegion(Context context, String s, double v, double v1) {
    }

    @Override
    public void onGeofenceReceived(Context context, String s) {
    }
}
