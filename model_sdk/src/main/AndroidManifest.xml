<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.zizhiguanjia.model_sdk">
    <!--android 6.0 以后需要添加 READ_PHONE_STATE WRITE_EXTERNAL_STORAGE 动态权限申请-->
    <!-- Required  -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <!-- Optional -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- 用于开启 debug 版本的应用在6.0 系统上 层叠窗口权限 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application>
        <activity
            android:name="cn.jiguang.verifysdk.CtLoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="unspecified"
            android:theme="@style/ActivityDialogStyle"></activity>
        <activity
            android:name=".ui.SdkActivity"
            android:exported="false"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="jpush.testAction" />
                <category android:name="jpush.testCategory" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="JPUSH_APPKEY"
            android:value="${JPUSH_APPKEY}"
            tools:replace="android:value" />
        <meta-data
            android:name="JPUSH_CHANNEL"
            android:value="${JPUSH_CHANNEL}"
            tools:replace="android:value" />
        <meta-data
            android:name="APP_SEND_PLARFORM_CODE"
            android:value="${APP_SEND_PLARFORM_CODE}"
            tools:replace="android:value" />
        <meta-data
            android:name="com.openinstall.APP_KEY"
            android:value="nincmm" />

        <service
            android:name="com.zizhiguanjia.model_sdk.receiver.PushMessageReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.RECEIVE_MESSAGE" />
                <category android:name="${applicationId}"></category>
            </intent-filter>
        </service>

    </application>
    <queries>
        <!-- 微信 -->
        <package android:name="com.tencent.mm" />
    </queries>
</manifest>