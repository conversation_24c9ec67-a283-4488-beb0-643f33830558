<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.zizhiguanjia.model_common">
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <application android:requestLegacyExternalStorage="true">
        <activity
            android:name=".ui.ServiceActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustResize"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
            />
        <activity
            android:screenOrientation="portrait"
            android:name=".ui.SetActivity"
            android:launchMode="singleTop">
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
        </activity>
    </application>
</manifest>