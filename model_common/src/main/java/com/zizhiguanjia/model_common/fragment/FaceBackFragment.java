package com.zizhiguanjia.model_common.fragment;

import android.Manifest;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.ViewTreeObserver;

import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.caimuhao.rxpicker.RxPicker;
import com.caimuhao.rxpicker.bean.ImageItem;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_utils.permission.PermissionListener;
import com.wb.lib_utils.permission.PermissionUtils;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.constants.CommonRouterPath;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.adapter.FaceBackTypeAdapter;
import com.zizhiguanjia.model_common.adapter.ImageLookAdapter;
import com.zizhiguanjia.model_common.databinding.CommonFacebackLayoutBinding;
import com.zizhiguanjia.model_common.imageLoader.GlideSelectImageLoader;
import com.zizhiguanjia.model_common.listener.FaceBackTypeListener;
import com.zizhiguanjia.model_common.listener.ImageLookListener;
import com.zizhiguanjia.model_common.model.FaceBackTypeBean;
import com.zizhiguanjia.model_common.navigator.FaceBackNavigator;
import com.zizhiguanjia.model_common.viewmodel.FaceBackViewmode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import io.reactivex.functions.Consumer;
@Route(path = CommonRouterPath.COMMON_FACEBACK)
@BindRes( statusBarStyle= BarStyle.LIGHT_CONTENT,swipeBack = SwipeStyle.NONE)
public class FaceBackFragment extends BaseFragment implements FaceBackNavigator , ImageLookListener, TextWatcher, ViewTreeObserver.OnGlobalLayoutListener, FaceBackTypeListener, TitleBar.OnTitleBarListener {
    private FaceBackTypeAdapter mFaceBackTypeAdapter;
    private CommonFacebackLayoutBinding binding;
    private ImageLookAdapter imageLookAdapter;
    @BindViewModel
    FaceBackViewmode faceBackViewmode;
    private BasePopupView loadingPopu;
    @Override
    public int initLayoutResId() {
        return R.layout.common_faceback_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding=getBinding();
        String ids=initArguments().getString("appointmentIds","");
        faceBackViewmode.initParam(this,this,ids);
        binding.setModel(faceBackViewmode);
        binding.commonFacebackMsgEd.addTextChangedListener(this);
        binding.viewContent.getViewTreeObserver().addOnGlobalLayoutListener(this);
        binding.ttbTitle.setClickListener(this);
        binding.ttbTitle.getCenterTextView().setText(StringUtils.isEmpty(ids)?"意见反馈":"信息纠错");
        binding.desTv.setText(StringUtils.isEmpty(ids)?"(必选) 请选择您反馈的问题类型":"请选择纠错的问题类型");
        binding.commonFacebackMsgEd.setHint(StringUtils.isEmpty(ids)?"请描述您遇到的具体问题（不少于10个字）":"请留下发现的问题以及对应的正确信息，我们回尽快核实处理。");
        binding.imageUploadTv.setText(StringUtils.isEmpty(ids)?"若能提供相应问题的截图就更好了":"    上传截图");
    }

    @Override
    public void initViewData() {
        RxPicker.init(new GlideSelectImageLoader());
        mFaceBackTypeAdapter=new FaceBackTypeAdapter(this);
        binding.commonBackfaceTypeRel.setLayoutManager(new GridLayoutManager(getContext(),3));
        binding.commonBackfaceTypeRel.addItemDecoration(new GridSpaceItemDecoration(3, DpUtils.dp2px(getContext(), 16), DpUtils.dp2px(getContext(), 12)));
        binding.commonBackfaceTypeRel.setAdapter(mFaceBackTypeAdapter);

        imageLookAdapter=new ImageLookAdapter(this);
        binding.grideView.setLayoutManager(new LinearLayoutManager(getContext(), RecyclerView.HORIZONTAL,false));
        binding.grideView.addItemDecoration(new GridSpaceItemDecoration(9, DpUtils.dp2px(getContext(), 0), DpUtils.dp2px(getContext(), 10)));
        binding.grideView.setAdapter(imageLookAdapter);

        loadingPopu=new PopupManager.Builder(getContext()).asLoading("上传中...",R.layout.popup_center_impl_loading);


    }

    @Override
    public void initObservable() {
        faceBackViewmode.postSuccess.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if(true){
                    finish();
                }
            }
        });
    }

    @Override
    public void getFaceBackTypeListData(List<FaceBackTypeBean> faceBackTypeBeans) {
        mFaceBackTypeAdapter.setDataItems(faceBackTypeBeans);
    }

    @Override
    public void updateFaceImage(List<ImageItem> imageItems) {
        if(imageLookAdapter.getData().size()>=9){
            for(ImageItem imageItem:imageLookAdapter.getData()){
                if(imageItem.getId()==0){
                    imageLookAdapter.getData().remove(imageItem);
                }
            }
            imageLookAdapter.notifyDataSetChanged();
        }else {
            imageLookAdapter.addDataItem(imageLookAdapter.getData().size()-1,imageItems);
        }
    }

    @Override
    public void showLoading(boolean isVisition) {
        if(isVisition){
            if(loadingPopu.isShow()){
            }else {
                loadingPopu.show();
            }
        }else {
            if(loadingPopu.isShow()){
                loadingPopu.dismiss();
            }
        }
    }

    @Override
    public void showImage() {
        PermissionUtils.with(getActivity()).addPermissions(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                .addPermissions(Manifest.permission.READ_EXTERNAL_STORAGE)
                .setPermissionsCheckListener(new PermissionListener() {
                    @Override
                    public void permissionRequestSuccess() {
                        faceBackViewmode.openImageView(imageLookAdapter.getImageMaxSize());
                        faceBackViewmode.checkTextVa(imageLookAdapter.getData().size());
                    }

                    @Override
                    public void permissionRequestFail(String[] grantedPermissions, String[] deniedPermissions, String[] forceDeniedPermissions) {
                        MessageHelper.permissionsTips(getActivity(),"温馨提示",
                                "请前往设置->应用->【" + PermissionUtils.getAppName(getActivity()) + "】->权限中打开文件读写权限，否则功能无法正常运行！","确定");
                    }
                })
                .createConfig()
                .setForceAllPermissionsGranted(false)
                .setForceDeniedPermissionTips("请前往设置->应用->【" + PermissionUtils.getAppName(getActivity()) + "】->权限中打开相关权限，否则功能无法正常运行！")
                .buildConfig()
                .startCheckPermission();
    }

    @Override
    public void delectImage(int ids) {
        List<ImageItem> imageItemLists= imageLookAdapter.getData();
        Iterator<ImageItem> iterator = imageItemLists.iterator();
        while (iterator.hasNext()){
            ImageItem imageItem=iterator.next();
            if(imageItem.getId()==ids){
                iterator.remove();
            }
        }
        imageLookAdapter.notifyDataSetChanged();
        faceBackViewmode.clearImages(ids);
        faceBackViewmode.checkTextVa(imageLookAdapter.getData().size());
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        faceBackViewmode.faceBackMsg.set(s.toString());
        faceBackViewmode.checkTextVa(0);
    }

    @Override
    public void onGlobalLayout() {
        Rect r = new Rect();
        binding.viewContent.getWindowVisibleDisplayFrame(r);
        int screenHeight = binding.viewContent.getRootView().getHeight();
        int keypadHeight = screenHeight - r.bottom;
        if (keypadHeight > screenHeight * 0.15) { // 0.15 ratio is perhaps enough to determine keypad height.
            // 当键盘显示的时候走这里，最底部空出键盘占用的高度
            binding.getRoot().setPadding(0, 0, 0, keypadHeight);
            //延迟滚动到底部，为了防止焦点出现跳动
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    //将ScrollView滚动到最底部
                    binding.svContent.fullScroll(View.FOCUS_DOWN);
                    binding.commonFacebackMsgEd.requestFocus();
                }
            }, 100);
        } else {
            // 当键盘隐藏的时候走这里，还原默认的显示
            binding.getRoot().setPadding(0, 0, 0, 0);
        }
    }

    @Override
    public void onUserSelectFaceType(String ids) {
        faceBackViewmode.faceBackTypeIds.set(ids);
        faceBackViewmode.checkTextVa(0);
    }

    @Override
    public void onClicked(View v, int action) {
        if(action==TitleBar.ACTION_LEFT_BUTTON){
            finish();
        }
    }
}
