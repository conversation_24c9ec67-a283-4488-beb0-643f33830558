package com.zizhiguanjia.model_common.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.ValueCallback;
import android.webkit.WebView;
import android.widget.LinearLayout;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.constants.CommonRouterPath;
import com.zizhiguanjia.lib_base.emums.PayTypeEnum;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PayHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.databinding.CommonWebviewBinding;
import com.zizhiguanjia.model_common.listener.CommonWebListener;
import com.zizhiguanjia.model_common.model.MainCertificateBean;
import com.zizhiguanjia.model_common.model.NativePayBean;
import com.zizhiguanjia.model_common.navigator.CommonWebViewNavigator;
import com.zizhiguanjia.model_common.viewmodel.CommonWebViewModel;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import me.jingbin.web.ByWebView;

@BindRes(statusBarStyle = BarStyle.DARK_CONTENT, swipeBack = SwipeStyle.NONE)
@Route(path = CommonRouterPath.COMMON_WEB)
public class CommonWebviewFragment extends BaseFragment implements CommonWebViewNavigator, TitleBar.OnLeftBarListener, CommonWebListener {
    private WebView webView;
    private ByWebView byWebView;
    private CommonWebviewBinding binding;
    private String payJson;
    @BindViewModel
    CommonWebViewModel commonWebViewModel;
    String routh,payRouthParams;
    private boolean paySuccessFinsh=true;
    private boolean startWachat=false;
    /**
     * 调用nativePay传递的配置数据
     */
    private NativePayBean goToPayConfigDataBean;
    @Override
    public int initLayoutResId() {
        return R.layout.common_webview;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();
        BaseConfig.finshPage=true;
        routh = initArguments().getString("routh", "");
        payRouthParams=initArguments().getString("payRouthParams", PayRouthConfig.PAY_EXTWEB_PAY);
        BaseConfig.paySuccessAutoClose=initArguments().getBoolean("paySuccessAutoClose",true);
        commonWebViewModel.initParams(this, this);
        commonWebViewModel.initWebView(1, this, routh,payRouthParams);
        binding.commonwebTitleTb.setBackListener(this);
        binding.commonwebTitleTb.setStatusBarColor(Color.parseColor("#ffcc22"));
        binding.commonwebTitleTb.getCenterTextView().setTextColor(Color.parseColor("#ffcc22"));
    }
    private void reshThemStyle(int colorInt){
        int gray = (int) (Color.red(colorInt) * 0.299 + Color.green(colorInt) * 0.587 + Color.blue(colorInt) * 0.114);
        if (gray >= 192) {
            // 深色系
            binding.commonwebTitleTb.getCenterTextView().setTextColor(Color.parseColor("#000000"));
            binding.commonwebTitleTb.getLeftImageButton().setImageResource(R.drawable.common_left_back_black_img);
        } else {
            // 浅色系
            binding.commonwebTitleTb.getCenterTextView().setTextColor(Color.parseColor("#ffffff"));
            binding.commonwebTitleTb.getLeftImageButton().setImageResource(R.drawable.common_left_back_write_img);
        }
    }
    @Override
    public void onResume() {
        super.onResume();
        byWebView.onResume();
        initListenter();
    }
    private void initListenter(){
        binding.llContainer.setFocusableInTouchMode(true);
        binding.llContainer.requestFocus();
        binding.llContainer.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
//                if (keyCode == KeyEvent.KEYCODE_BACK) {
//                    LogUtils.e("看看页面");
//                }
                return byWebView.handleKeyEvent(keyCode,event);
            }
        });
    }

    @Override
    public void onPause() {
        super.onPause();
        cleanPage();
        byWebView.onPause();
    }
    private Disposable disposable;
    private void cleanPage(){
        if(startWachat){
            if(disposable==null){
            }else{
                disposable.dispose();
            }
            disposable= RxJavaUtils.delay(3, new Consumer<Long>() {
                @Override
                public void accept(Long aLong) throws Exception {
                    finish();
                }
            });
        }
    }

    @SuppressLint("Range")
    @Override
    public void initViewData() {
        String url =initArguments().getString("url");
        LogUtils.e("传递的url"+url);
        Map<String, String> params = getParameter(url);
        String bgTitle="";
        if (params.containsKey("payType")) {
            String payType = params.get("payType");
            if (!payType.equals("-1")) {
                binding.commonwebTitleTb.setVisibility(View.VISIBLE);
                String titleBg = params.get("titleBg");
                if(titleBg==null||titleBg.isEmpty()){
                    binding.commonwebTitleTb.setStatusBarColor(Color.parseColor(initArguments().getString("titleBg") == null ? "#3163F6" : initArguments().getString("titleBg")));
                    binding.commonwebTitleTb.setBackgroundColor(Color.parseColor(initArguments().getString("titleBg") == null ? "#3163F6" : initArguments().getString("titleBg")));
                    bgTitle=initArguments().getString("titleBg") == null ? "#3163F6" : initArguments().getString("titleBg");
                }else {
                    bgTitle="#"+titleBg;
                    binding.commonwebTitleTb.setStatusBarColor(Color.parseColor("#"+titleBg));
                    binding.commonwebTitleTb.setBackgroundColor(Color.parseColor("#"+titleBg));
                }
            }else {
                binding.commonwebTitleTb.setVisibility(View.GONE);
            }
        }else {
            binding.commonwebTitleTb.setVisibility(View.VISIBLE);
            if(params.containsKey("titleBg")){
                String titleBg = params.get("titleBg");
                if(titleBg==null||titleBg.isEmpty())
                    return;
                bgTitle="#"+titleBg;
                binding.commonwebTitleTb.setStatusBarColor(Color.parseColor("#"+titleBg));
                binding.commonwebTitleTb.setBackgroundColor(Color.parseColor("#"+titleBg));
            }else {
                bgTitle=initArguments().getString("titleBg") == null ? "#3163F6" : initArguments().getString("titleBg");
                binding.commonwebTitleTb.setStatusBarColor(Color.parseColor(initArguments().getString("titleBg") == null ? "#3163F6" : initArguments().getString("titleBg")));
                binding.commonwebTitleTb.setBackgroundColor(Color.parseColor(initArguments().getString("titleBg") == null ? "#3163F6" : initArguments().getString("titleBg")));
            }
        }
        if(bgTitle==null||bgTitle.isEmpty())return;
        reshThemStyle(Color.parseColor(bgTitle));
    }

    @Override
    public void onVisible() {
        startWachat=false;
    }
    public Map<String, String> getParameter(String url) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            final String charset = "utf-8";
            url = URLDecoder.decode(url, charset);
            if (url.indexOf('?') != -1) {
                final String contents = url.substring(url.indexOf('?') + 1);
                String[] keyValues = contents.split("&");
                for (int i = 0; i < keyValues.length; i++) {
                    String key = keyValues[i].substring(0, keyValues[i].indexOf("="));
                    String value = keyValues[i].substring(keyValues[i].indexOf("=") + 1);
                    map.put(key, value);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    @Override
    public void onPause(@NonNull LifecycleOwner owner) {

    }

    @Override
    public void onStop() {
        try {
            if (webView == null) return;
            webView.evaluateJavascript("javascript:finshPay()", new ValueCallback<String>() {
                @Override
                public void onReceiveValue(String value) {
                    if (value == null || value.isEmpty()) return;
                    if (value.equals("1")) {
                        PointHelper.joinPointData(PointerMsgType.POINTER_A_PAIDLANDINGPAGE, false);
                    }
                }
            });
        } catch (Exception e) {
        }
        super.onStop();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        BaseConfig.finshPage=true;
        if(byWebView==null){
        }else {
            byWebView.onDestroy();
        }
        if (payJson == null || payJson.isEmpty()) return;
        CertificateHelper.initCertificate(false);
    }

    @Override
    public void initObservable() {
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if (msgEvent.getCode() == PayMsgTypeConfig.PAY_MSG_SUCCESS) {
                    //学习卡购买成功调用h5成功逻辑
                    if(goToPayConfigDataBean != null && goToPayConfigDataBean.getExtData() != null && Objects.equals(
                            PayTypeEnum.LEARNING_CARD.getCardType(), goToPayConfigDataBean.getExtData().getRefferPage())){
                        webView.loadUrl("javascript:successPay('" + goToPayConfigDataBean.getBillCode() + "')");
                        return;
                    }

                    if (payJson == null || payJson.isEmpty()) {
                    } else {
                        reshPaySuccess(payJson);
                    }
                    if(paySuccessFinsh){
                        finish();
                    }else {
//                        MessageHelper.openPaySuccessDialog(getActivity(), new PaySuccessListen() {
//                            @Override
//                            public void paySunncess(int type, String url) {
//                                if(type==1){
//                                    initArguments().putString("routh", "home");
//                                    initArguments().putString("url", url);
//                                    initArguments().putInt("payType", 1);
//                                    initArguments().putString("payRouthParams", PayRouthConfig.PAY_BANNER);
//                                    startFragment(CommonHelper.showCommonWeb());
//                                }
//                            }
//                        });
                        byWebView.getWebView().loadUrl(initArguments().getString("url"));
                    }
                } else if (msgEvent.getCode() == PayMsgTypeConfig.PAY_MSG_FAIL) {
                    //学习卡购买失败不跳转页面
                    if (goToPayConfigDataBean != null && goToPayConfigDataBean.getExtData() != null && Objects.equals(
                            PayTypeEnum.LEARNING_CARD.getCardType(), goToPayConfigDataBean.getExtData().getRefferPage())) {
                        MessageHelper.openGeneralCentDialog(getActivity(), "有什么疑惑吗？\n点击“联系客服”详细咨询哦", "", "暂时不需要", "联系客服",
                                false, true, new GeneralDialogListener() {
                                    @Override
                                    public void onCancel() {
                                    }

                                    @Override
                                    public void onConfim() {
                                        permissionsPrompt();
                                    }

                                    @Override
                                    public void onDismiss() {

                                    }
                                });
                        return;
                    }
                    //支付失败
                    showPayFailView();
                } else if (msgEvent.getCode() == 10106) {
                    //{"cityCode":"10","certificateName":"江苏·C1证","addressName":"null","majId":"1103"}
                    String address = msgEvent.getMsg();
                    commonWebViewModel.updataInfo(address);
                }else if(msgEvent.getCode()==10107){
                    //开始清除.
                    startWachat=true;
                }
            }
        });
    }

    private void reshPaySuccess(String strJson) {
        MainCertificateBean mainCertificateBean = new MainCertificateBean();
        Map<String, String> map = GsonUtils.gsonToMaps(strJson);
        String cityCode = map.get("cityCode");
        String certificateName = map.get("certificateName");
        String majId = map.get("majId");
        CertificateHelper.updateCertificate(cityCode, "", majId, certificateName,3,"");
    }

    @Override
    public void initByWebView(ByWebView.Builder wb) {
        byWebView = wb.setWebParent(binding.llContainer, new LinearLayout.LayoutParams(-1, -1)).loadUrl(initArguments().getString("url"));
        byWebView.setTextZoom(100);
        webView = byWebView.getWebView();
        webView.getSettings().setBuiltInZoomControls(false);
        webView.getSettings().setSupportZoom(false);
        webView.getSettings().setUseWideViewPort(true);
        webView.getSettings().setLoadWithOverviewMode(true);
    }
    @Override
    public void onReceivedTitle(String title) {
        if(title.isEmpty())
            return;
        binding.commonwebTitleTb.getCenterTextView().setText(title);
        binding.commonwebTitleTb.getCenterTextView().setMaxEms(8);
        binding.commonwebTitleTb.getCenterTextView().setEllipsize(TextUtils.TruncateAt.END);
    }

    @Override
    public void showPayFailView() {





//        openPayFailServer(Activity activity, BaseFragment baseFragment)
                MessageHelper.openPayFailServer(getActivity(),this);
//        if(CertificateHelper.isAqy()){
//            MessageHelper.openBottomListDialog(getActivity(), "您取消了支付，是因为", "", "我再想想", "科目选错了，我要换科目", false, false, new GeneralDialogListener() {
//                @Override
//                public void onCancel() {
//                    PointHelper.joinPointData(PointerMsgType.POINTER_A_PAYFAILURE_WZXXBT, false);
//                    openYwView();
//                }
//
//                @Override
//                public void onConfim() {
//                    PointHelper.joinPointData(PointerMsgType.POINTER_A_PAYFAILURE_XCLBT, false);
//                    initArguments().putBoolean("isSave", false);
//                    startFragment(AddressHelper.mainPage(getContext()));
//                }
//            });
//        }else {
//            MessageHelper.openGeneralCentDialog(getActivity(), "您取消了支付，是有什么疑惑吗？可以主动咨询客服哦", "",
//                    "暂时不需要", "好的", false, true, new GeneralDialogListener() {
//                        @Override
//                        public void onCancel() {
//                        }
//                        @Override
//                        public void onConfim() {
//                            permissionsPrompt();
//                        }
//                    });
//        }

    }

    @Override
    public void openYwView() {
        MessageHelper.openGeneralCentDialog(getActivity(),
                "是有什么疑惑吗？ 可以主动拨打给我们的客服",
                "",
                "暂时不需要",
                "好的",
                false,
                false,
                new GeneralDialogListener() {
                    @Override
                    public void onCancel() {
                    }
                    @Override
                    public void onConfim() {
                        permissionsPrompt();
                    }

                    /**
                     *
                     */
                    @Override
                    public void onDismiss() {

                    }
                });
    }

        @Override
    public void reshJsCall(String json) {
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                payJson = json;
                webView.loadUrl("javascript:refreshSubjectInfo('" + json + "')");
            }
        });
    }

    @Override
    public void permissionsPrompt() {
        if(goToPayConfigDataBean != null && goToPayConfigDataBean.getKeFu() != null){
            Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(goToPayConfigDataBean.getKeFu()));
            it.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP );
            startActivity(it);
        }else {
            DeviceUtils.dial(getContext(), ConfigHelper.getKfUrl());
        }

        //        DeviceUtils.dial(getContext(), ConfigHelper.getKfUrl());
//        PermissionUtils.with(getActivity()).addPermissions(Manifest.permission.CALL_PHONE)
//                .setPermissionsCheckListener(new PermissionListener() {
//                    @Override
//                    public void permissionRequestSuccess() {
//                        DeviceUtils.callPhone(getContext(), ConfigHelper.getKfTel());
//
//                    }
//                    @Override
//                    public void permissionRequestFail(String[] grantedPermissions, String[] deniedPermissions, String[] forceDeniedPermissions) {
//                        MessageHelper.permissionsTips(getActivity(),"温馨提示",
//                                "请前往设置->应用->【" + PermissionUtils.getAppName(getActivity()) + "】->权限中打开打电话权限，否则功能无法正常运行！","确定");
//                    }
//                })
//                .createConfig()
//                .setForceAllPermissionsGranted(false)
//                .setForceDeniedPermissionTips("请前往设置->应用->【" + PermissionUtils.getAppName(getContext()) + "】->权限中打开相关权限，否则功能无法正常运行！")
//                .buildConfig()
//                .startCheckPermission();
    }

    @Override
    public void onClicked(View v) {
        if(byWebView.isBack()){
            byWebView.getWebView().goBack();
        }else {
            finish();
        }
    }

    @Override
    public void toPay() {
        startFragment(PayHelper.toPageMain(getContext()));
    }

    @Override
    public void toCallPhone(String phone) {
//        PermissionUtils.with(getActivity()).addPermissions(Manifest.permission.CALL_PHONE)
//                .setPermissionsCheckListener(new PermissionListener() {
//                    @Override
//                    public void permissionRequestSuccess() {
//                        DeviceUtils.callPhone(getContext(), phone);
//                    }
//                    @Override
//                    public void permissionRequestFail(String[] grantedPermissions, String[] deniedPermissions, String[] forceDeniedPermissions) {
//                        MessageHelper.permissionsTips(getActivity(),"温馨提示",
//                                "请前往设置->应用->【" + PermissionUtils.getAppName(getActivity()) + "】->权限中打开打电话权限，否则功能无法正常运行！","确定");
//                    }
//                })
//                .createConfig()
//                .setForceAllPermissionsGranted(false)
//                .setForceDeniedPermissionTips("请前往设置->应用->【" + PermissionUtils.getAppName(getContext()) + "】->权限中打开相关权限，否则功能无法正常运行！")
//                .buildConfig()
//                .startCheckPermission();
        DeviceUtils.dial(getContext(), ConfigHelper.getKfTel());

    }

    @Override
    public void toLxkf(String url) {
        CommonHelper.start(getContext());
    }

    @Override
    public void nativePayByJson(String json) {
        LogUtils.e("服务端下行数据----->>>"+json);
        try {
            NativePayBean nativePayBean = com.wb.lib_utils.utils.GsonUtils.newInstance().getBean(json, NativePayBean.class);
            if (nativePayBean == null) {
                ToastUtils.normal("支付失败！", Gravity.CENTER);
            } else {
                goToPayConfigDataBean = nativePayBean;
                BaseConfig.WX_WAIT_ORDER = nativePayBean.getBillCode();
                BaseConfig.isPaySuccessDialog=nativePayBean.isShowPopup();
                LogUtils.e("弹窗----->>>"+ BaseConfig.isPaySuccessDialog);
                PayHelper.nativeToPay(getActivity()
                        , nativePayBean.getAppid(),
                        nativePayBean.getNoncestr(), nativePayBean.getPackagex(),
                        nativePayBean.getPartnerid(),
                        nativePayBean.getSign(),
                        nativePayBean.getPrepayid(),
                        nativePayBean.getTimestamp());
            }
        } catch (Exception e) {
            ToastUtils.normal("支付失败！", Gravity.CENTER);
        }
    }
    @Override
    public void onKey() {
        initListenter();
    }

    @Override
    public void paySuccessFinsh(boolean type) {
        if(type){
            BaseConfig.finshPage=true;
        }else{
            BaseConfig.finshPage=false;
        }
        this.paySuccessFinsh=type;
    }

    @Override
    public void gotoErrorPage(String ids) {
        if(AccountHelper.loginSuperintendent()){
            initArguments().putString("appointmentIds",ids);
            startFragment(new FaceBackFragment());
        }

    }
}
