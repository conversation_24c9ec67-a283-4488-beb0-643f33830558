package com.zizhiguanjia.model_common.fragment;

import android.graphics.Bitmap;
import android.net.http.SslError;
import android.os.Bundle;
import android.view.View;
import android.webkit.SslErrorHandler;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.LinearLayout;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.constants.CommonRouterPath;
import com.zizhiguanjia.lib_base.emums.WebViewLoadTypeEnum;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.databinding.CommonHtmlTextLoadLayoutBinding;
import com.zizhiguanjia.model_common.viewmodel.HtmlTextLoadWebViewViewModel;

import androidx.core.content.ContextCompat;
import me.jingbin.web.ByWebView;
import me.jingbin.web.OnByWebClientCallback;
import me.jingbin.web.OnTitleProgressCallback;

/**
 * 功能作用：网页文本加载页面
 * 初始注释时间： 2023/12/18 20:39
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
@Route(path = CommonRouterPath.COMMON_WEB_HTML_TEXT_LOAD)
public class HtmlTextLoadWebViewFragment extends BaseFragment implements TitleBar.OnLeftBarListener {
    /**
     * 页面
     */
    private CommonHtmlTextLoadLayoutBinding mBinding;

    /**
     * 数据处理
     */
    @BindViewModel
    HtmlTextLoadWebViewViewModel mViewModel;

    /**
     * 网页页面容器
     */
    private ByWebView mByWebView;

    /**
     * 网页页面
     */
    private WebView mWebView;

    /**
     * 初始化布局
     */
    @Override
    public int initLayoutResId() {
        return R.layout.common_html_text_load_layout;
    }

    /**
     * 初始化控制、监听等轻量级操作
     */
    @Override
    public void initView(Bundle savedInstanceState) {
        mBinding = getBinding();
        mBinding.commonwebTitleTb.setBackListener(this);
        initWebView();
    }

    @Override
    public void initViewData() {
        super.initViewData();
        mViewModel.setLoadType((WebViewLoadTypeEnum) initArguments().getSerializable("loadType"));
        mBinding.commonwebTitleTb.getCenterTextView().setText(initArguments().getString("title", ""));
        mViewModel.loadData(initArguments().get("params"));
    }

    @Override
    public void initObservable() {
        super.initObservable();
        mViewModel.getShowTitleText().observe(this,
                s -> mBinding.commonwebTitleTb.getCenterTextView().setText(s != null ? s : initArguments().getString("title", "")));
        mViewModel.getShowHtmlTextData().observe(this, data -> mWebView.loadDataWithBaseURL(null, data, "text/html", "utf-8", null));
    }

    /**
     * 初始化网页
     */
    private void initWebView() {
        ByWebView.Builder builder = ByWebView.with(requireActivity()).useWebProgress(ContextCompat.getColor(requireContext(), R.color.orchid))
                .setOnTitleProgressCallback(new OnTitleProgressCallback() {
                    @Override
                    public void onReceivedTitle(String title) {
                        super.onReceivedTitle(title);
                    }

                    @Override
                    public void onProgressChanged(int newProgress) {
                        super.onProgressChanged(newProgress);
                    }
                }).setOnByWebClientCallback(new OnByWebClientCallback() {
                    @Override
                    public void onPageStarted(WebView view, String url, Bitmap favicon) {
                        super.onPageStarted(view, url, favicon);
                    }

                    @Override
                    public void onPageFinished(WebView view, String url) {
                        super.onPageFinished(view, url);
                    }

                    @Override
                    public boolean isOpenThirdApp(String url) {
                        return super.isOpenThirdApp(url);
                    }

                    @Override
                    public boolean onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                        return super.onReceivedSslError(view, handler, error);
                    }
                });
        mByWebView = builder.setWebParent(mBinding.llContainer, new LinearLayout.LayoutParams(-1, -1)).loadUrl(initArguments().getString("url"));
        mByWebView.setTextZoom(100);
        mWebView = mByWebView.getWebView();
        mWebView.getSettings().setJavaScriptEnabled(true);
        mWebView.getSettings().setBuiltInZoomControls(true); //Enable Multitouch if supported by ROM
        mWebView.getSettings().setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
        mWebView.getSettings().setAppCacheEnabled(true);
        mWebView.getSettings().setAllowFileAccess(true);
        mWebView.getSettings().setDatabaseEnabled(true);
        mWebView.getSettings().setDomStorageEnabled(true);
        mWebView.getSettings().setGeolocationEnabled(true);
        mWebView.getSettings().setNeedInitialFocus(true);
        mWebView.getSettings().setBuiltInZoomControls(false);
        mWebView.getSettings().setSupportZoom(false);
        mWebView.getSettings().setUseWideViewPort(true);
        mWebView.getSettings().setLoadWithOverviewMode(true);
        mWebView.getSettings().setPluginState(WebSettings.PluginState.ON);
        mWebView.getSettings().setJavaScriptCanOpenWindowsAutomatically(true);
        mWebView.getSettings().setUserAgentString(mWebView.getSettings().getUserAgentString() + " HDM-PBS/1.0");
        mWebView.setInitialScale(25);//为25%，最小缩放等级
        //        mWebView.setDownloadListener(new ServiceWebViewModel.MyWebViewDownLoadListener());
        //        mWebView.setWebViewClient(new ServiceWebViewModel.SampleWebViewClient());
        //        mWebView.setWebChromeClient(myWebChromeClient);
    }

    @Override
    public void onPause() {
        super.onPause();
        mByWebView.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        BaseConfig.finshPage = true;
        if (mByWebView != null) {
            mByWebView.onDestroy();
        }
    }

    @Override
    public void onClicked(View v) {
        if (mByWebView.isBack()) {
            mByWebView.getWebView().goBack();
        } else {
            finish();
        }
    }
}
