package com.zizhiguanjia.model_common.fragment;

import android.os.Bundle;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.wb.lib_arch.annotations.BindViewModel;
import com.zizhiguanjia.lib_base.constants.CommonRouterPath;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.databinding.CommonPermissLayoutBinding;
import com.zizhiguanjia.model_common.viewmodel.PermissViewModel;
@Route(path = CommonRouterPath.COMMON_PERMISS)
public class PermissFragment extends BaseFragment {
     @BindViewModel
     PermissViewModel permissViewModel;
     private CommonPermissLayoutBinding commonPermissLayoutBinding;
    @Override
    public int initLayoutResId() {
        return R.layout.common_permiss_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        commonPermissLayoutBinding=getBinding();
        permissViewModel.initParams(this);
        commonPermissLayoutBinding.setModel(permissViewModel);
    }

    @Override
    public void initViewData() {

    }

    @Override
    public void initObservable() {

    }
}
