package com.zizhiguanjia.model_common.fragment;

import android.content.ComponentName;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.Gravity;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.constants.CommonRouterPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.lib_base.helper.FixBugHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.adapter.SetGroupListAdapter;
import com.zizhiguanjia.model_common.config.SetMsgConfig;
import com.zizhiguanjia.model_common.databinding.CommonSetLayoutBinding;
import com.zizhiguanjia.model_common.manager.CommonManager;
import com.zizhiguanjia.model_common.model.SetListGroupBean;
import com.zizhiguanjia.model_common.navigator.SetNavigator;
import com.zizhiguanjia.model_common.viewmodel.SetViewModel;

import java.util.List;

import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

@Route(path = CommonRouterPath.COMMON_SET)
@BindRes(statusBarStyle = BarStyle.LIGHT_CONTENT, swipeBack = SwipeStyle.NONE)
public class SetFragment extends BaseFragment implements SetNavigator, TitleBar.OnTitleBarListener {
    @BindViewModel
    SetViewModel setViewModel;
    private CommonSetLayoutBinding binding;
    private SetGroupListAdapter setGroupListAdapter;

    @Override
    public int initLayoutResId() {
        return R.layout.common_set_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();
        setViewModel.setSetNavigator(this);
        setViewModel.initSetConfig();
        CommonManager.getInstance().initUpDateApp();
        binding.tbTopTitle.setClickListener(this);
        binding.tvBottomDes.setOnClickListener(this);
        binding.setModel(setViewModel);
    }

    @Override
    public void initViewData() {
        binding.tvBottomDes.setText(ConfigHelper.getCopyRight());
        binding.commonSetRcy.setLayoutManager(new LinearLayoutManager(getContext()));
        setGroupListAdapter = new SetGroupListAdapter();
        binding.commonSetRcy.setAdapter(setGroupListAdapter);
    }

    @Override
    public void initObservable() {
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if (msgEvent.getCode() == SetMsgConfig.SET_MSG_SET) {
                    String id = msgEvent.getMsg();
                    setViewModel.afterSetToPageById(id);
                }
            }
        });
    }

    @Override
    public void getInitSetConfig(List<SetListGroupBean> setListGroupBeans) {
        setGroupListAdapter.setDataItems(setListGroupBeans);
    }

    @Override
    public void formPageToBindPhone() {
        startFragment(AccountHelper.showUpdataPhone());
    }

    @Override
    public void formPageToWeb(String type) {
        initArguments().putString("titleBg", "#3163F6");
        initArguments().putString("url", type);
        startFragment(new CommonWebviewFragment());
    }

    @Override
    public void formPageToUpdateApp() {
        CommonManager.getInstance().upDate(true, getActivity());
    }

    @Override
    public void formPageToZhuXiao() {
        if(BaseConfig.IsExamine==0){
            startFragment(AccountHelper.goToLogOffAccount());
        }else {
            MessageHelper.openServiceTips(getActivity());
        }
    }

    @Override
    public void userOnclick() {
        if (onclick()) {
            if (!NoDoubleClickUtils.isDoubleClick()) {
                startFragment(FixBugHelper.showFixBugMain());
            }
        }
    }

    @Override
    public void clearCache() {
        MessageHelper.openGeneralCentDialog(getActivity(), "温馨提示", "当前缓存：" + setViewModel.getCacheSize() + ",是否清除？",
                "取消", "清除", false, true, new GeneralDialogListener() {
                    @Override
                    public void onCancel() {

                    }

                    @Override
                    public void onConfim() {
                        setViewModel.clearImageCache();
                    }

                    /**
                     *
                     */
                    @Override
                    public void onDismiss() {

                    }
                });
    }

    /**
     *
     */
    @Override
    public void openBackInfo() {
        final Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        intent.setData(Uri.parse("https://beian.miit.gov.cn/"));
        if (intent.resolveActivity(getContext().getPackageManager()) != null) {
            final ComponentName componentName = intent.resolveActivity(getContext().getPackageManager());
            getContext().startActivity(Intent.createChooser(intent, "请选择浏览器"));
        } else {
            ToastUtils.normal("链接错误或无浏览器", Gravity.CENTER);
        }
    }

    @Override
    public void onClicked(View v, int action) {
        if (action == TitleBar.ACTION_LEFT_BUTTON) {
            finish();
        }
    }

    private final int COUNTS = 5;//点击次数
    private final long DURATION = 3 * 1000;//规定有效时间
    private long[] mHits = new long[COUNTS];

    public boolean onclick() {
        System.arraycopy(mHits, 1, mHits, 0, mHits.length - 1);
        mHits[mHits.length - 1] = SystemClock.uptimeMillis();
        if (mHits[0] >= (SystemClock.uptimeMillis() - DURATION)) {
            String tips = "您已在[" + DURATION + "]ms内连续点击【" + mHits.length + "】次了！！！";
            return true;
        }
        return false;
    }

}
