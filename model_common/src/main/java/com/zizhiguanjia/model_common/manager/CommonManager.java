package com.zizhiguanjia.model_common.manager;

import android.content.Context;
import android.graphics.Color;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.xuexiang.xupdate.XUpdate;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.http.CustomUpdateParser;
import com.zizhiguanjia.model_common.http.CustomUpdatePrompter;
import com.zizhiguanjia.model_common.http.OKHttpUpdateHttpService;
import java.util.HashMap;
import java.util.Map;
public class CommonManager {
    private static CommonManager commonManager;
    public static CommonManager getInstance(){
        if(commonManager==null){
            synchronized (CommonManager.class){
                return commonManager=new CommonManager();
            }
        }
        return commonManager;
    }
    public void initUpDateApp(){
        XUpdate.get()
                .debug(BaseConfig.deBug)
                .isWifiOnly(false)                                               //默认设置只在wifi下检查版本更新
                .isGet(false)                                                    //默认设置使用get请求检查版本
                .isAutoMode(false)
                .supportSilentInstall(false)
                .setIUpdateHttpService(new OKHttpUpdateHttpService())           //这个必须设置！实现网络请求功能。
                .init(AppUtils.getApp());
    }
    public void upDate(boolean isToast, Context mContext){
        LogUtils.e("开始更新版本----->>>>");
        Map<String,Object >params=new HashMap<>();
        params.put("source", BaseConfig.source);
        params.put("client", "2");
//        params.put("appMarket", "2");
        params.put("appMarket", com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getAppChannel(AppUtils.getApp()));
        params.put("version", DeviceUtils.getAppVersionName(AppUtils.getApp()));
        params.put("versionCode", DeviceUtils.getAppVersionNo(AppUtils.getApp())+"");
        XUpdate.newBuild(AppUtils.getApp())
                .params(params)
                .updateUrl(BaseAPI.Base_Url_Api+BaseAPI.VERSION_DES+"/API/Common/GetAppUpgradeInfo")
                .supportBackgroundUpdate(true)
                .promptThemeColor(Color.parseColor("#5081F7"))
                .promptButtonTextColor(Color.WHITE)
                .promptTopResId(R.drawable.common_updateapp_img)
                .promptWidthRatio(0.69F)
                .promptHeightRatio(0.52F)
                .updatePrompter(new CustomUpdatePrompter(mContext))
                .updateParser(new CustomUpdateParser(isToast))//设置自定义的版本更新解析器
                .update();
    }
    private BasePopupView againPayPopu;
}
