package com.zizhiguanjia.model_common.manager;

import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.model_common.navigator.SetNavigator;

public class SetManager {
    private  static  SetManager setManager;
    public static  SetManager getInstance(){
        if(setManager==null){
            synchronized (SetManager.class){
                return setManager=new SetManager();
            }
        }
        return setManager;
    }
    public void getSetPageToById(String id, SetNavigator setNavigator){
        if(id.equals("1001")){
            //绑定手机
            if(setNavigator!=null){
                setNavigator.formPageToBindPhone();
            }
        }else if(id.equals("1002")){
            //隐私协议
            if(setNavigator!=null){
                setNavigator.formPageToWeb(BaseAPI.BASE_XSYT_URL);
            }
        }else if(id.equals("1003")){
            //用户协议
            if(setNavigator!=null){
                setNavigator.formPageToWeb(BaseAPI.BASE_YHXY_URL);
            }
        }else if(id.equals("2001")){
            //检测更新
            if(setNavigator!=null){
                setNavigator.formPageToUpdateApp();
            }
        }else if(id.equals("2002")){
            //注销
            if(setNavigator!=null){
                setNavigator.clearCache();
            }
        }else if(id.equals("2003")){
            //注销
            if(setNavigator!=null){
                setNavigator.formPageToZhuXiao();
            }
        }
    }
}
