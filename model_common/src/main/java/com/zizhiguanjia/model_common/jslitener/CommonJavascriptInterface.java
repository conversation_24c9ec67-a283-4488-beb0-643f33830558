package com.zizhiguanjia.model_common.jslitener;

import android.content.ComponentName;
import android.content.Intent;
import android.net.Uri;
import android.view.Gravity;
import android.webkit.JavascriptInterface;

import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.ClipboardUtils;
import com.wb.lib_utils.utils.DataUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.emums.MetaDataKeyEnum;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.AddressHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.HomeHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.helper.VideoHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.CommonMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.zizhiguanjia.model_common.fragment.CommonWebviewFragment;
import com.zizhiguanjia.model_common.listener.CommonWebListener;
import com.zizhiguanjia.model_common.listener.ICommonWebJsLive;
import com.zizhiguanjia.model_common.model.H5Bean;
import com.zizhiguanjia.model_common.model.JsPointerBean;
import com.zizhiguanjia.model_common.viewmodel.CommonWebViewModel;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;

public class CommonJavascriptInterface {
    private CommonWebviewFragment context;
    private int payType;
    private CommonWebListener commonWebListener;
    private String routh;
    private String payRouthParams;

    public CommonJavascriptInterface(CommonWebviewFragment context, int payType, CommonWebListener commonWebListener, String routh,String payRouthParams) {
        this.context = context;
        this.routh=routh;
        this.payRouthParams=payRouthParams;
        this.payType = payType;
        this.commonWebListener=commonWebListener;
    }
    public CommonJavascriptInterface(CommonWebviewFragment context) {
        this.context = context;
    }
    @JavascriptInterface
    public String alertTest(String jjj){
        return "客户端传递了";
    }
    @JavascriptInterface
    public void toastNative(String msg){
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                ToastUtils.normal(msg,Gravity.CENTER);
            }
        });
    }
    @JavascriptInterface
    public void userGoPay() {
        //去支付
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                commonWebListener.toPay();
            }
        });
    }
    @JavascriptInterface
    public String getAccount(){
        return AccountHelper.getCurrentLoginAccount();
    }
    @JavascriptInterface
    public void userRingUp(String phone) {
        //打电话
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                if(phone==null||phone.isEmpty())return;
                commonWebListener.toCallPhone(phone);
            }
        });
    }
    @JavascriptInterface
    public void userContactMy(String kf) {
        //联系付款
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                if(kf==null||kf.isEmpty())return;
                commonWebListener.toLxkf(kf);
            }
        });
    }
    @JavascriptInterface
    public void feedBack(String ids){
        commonWebListener.gotoErrorPage(ids);
    }
    @JavascriptInterface
    public String vipInfo(){
        H5Bean h5Bean=new H5Bean();
        h5Bean.setCityCode(BaseConfig.cityCode);
        h5Bean.setSubjectId(BaseConfig.majId);
        h5Bean.setToken(BaseConfig.refreahToken);
        h5Bean.setRefferPage(payRouthParams);
        h5Bean.setSource(BaseConfig.source);
        h5Bean.setMajorPid(BaseConfig.MAJOR_PID+"");
        h5Bean.setPackNames(DeviceUtils.getAppPackageName());
        h5Bean.setChannel(com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getAppMetaData(MetaDataKeyEnum.APP_SEND_PLARFORM_CODE));
        h5Bean.setDecivesId("dsada"+DeviceUtils.getAndroidId(AppUtils.getApp()));
        h5Bean.setVersionCode(DeviceUtils.getAppVersionName(AppUtils.getApp()));
        String json= GsonUtils.newInstance().GsonToString(h5Bean);
        LogUtils.e("服务端上行数据----->>>"+json);
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                if(routh==null||routh.isEmpty()){
                }else {
                    if(routh.equals("home")){
                        upgradePage(PointerMsgType.POINTER_HOMEPAGE_PAIDLANDINGPAGE);
                    }else if(routh.equals("mypage")){
                        upgradePage(PointerMsgType.POINTER_MYPAGE_PAIDLANDINGPAGE);
                    }
                }
                upgradePage(PointerMsgType.POINTER_A_PAIDLANDINGPAGE);
            }
        });
        onKey();
        return json;
    }
    @JavascriptInterface
    public void onKey(){
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                commonWebListener.onKey();
            }
        });
    }
    @JavascriptInterface
    public void onClick(String type){
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                try {
                    if(type==null||type.isEmpty())return;
                    JsPointerBean jsPointerBean=GsonUtils.newInstance().getBean(type,JsPointerBean.class);
                    if(jsPointerBean.getRegisteredByWho().equals("1")){
                        if(jsPointerBean.getEventId().equals("a_paidLandingPage_button1")){
                            PointHelper.joinPointData("a_paidLandingPage_button1",false);
                        }else if(jsPointerBean.getEventId().equals("a_paidLandingPage_button2")){
                            PointHelper.joinPointData("a_paidLandingPage_button2",false);
                        }else {
                            PointHelper.joinPointData(jsPointerBean.getEventId(),false);
                        }
                    }else if(jsPointerBean.getRegisteredByWho().equals("2")){
                        PointHelper.joinPointData(jsPointerBean.getEventId(),getObjectToMap(jsPointerBean.getData()),false);
                    }
                }catch (Exception e){
                }
            }
        });
    }
    @JavascriptInterface
    public void openMinWxChat(String url){
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                try {
                    Bus.post(new MsgEvent(10107));
                    Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    context.startActivity(it);
                }catch (Exception e){
                }

            }
        });

    }
    @JavascriptInterface
    public void nativeToPay(String json){
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                if(json==null||json.isEmpty())return;
                if(commonWebListener==null)return;
                commonWebListener.nativePayByJson(json);
            }
        });
    }
    public  Map<String, String> getObjectToMap(Object obj) throws IllegalAccessException {
        Map<String, String> map = new LinkedHashMap<String, String>();
        Class<?> clazz = obj.getClass();
        System.out.println(clazz);
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object value = field.get(obj);
            if (value == null){
                value = "";
            }
            map.put(fieldName, value.toString());
        }
        return map;
    }
    @JavascriptInterface
    public void toCertificateByAddress(){
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                context. startFragment( AddressHelper.mainPage(AppUtils.getApp()));
                userGoBack();
            }
        });
    }
    @JavascriptInterface
    public void userGoBack() {
        //关闭
       MainThreadUtils.post(new Runnable() {
           @Override
           public void run() {
               context.finish();
           }
       });
    }
    @JavascriptInterface
    public void nativeToLogin(){
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                Bus.post(new MsgEvent(AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT));
            }
        });
    }
    @JavascriptInterface
    public void addCustomerService(String state){
       MainThreadUtils.post(new Runnable() {
           @Override
           public void run() {
               if(state==null||state.isEmpty())return;
               Map<String,String> map= com.wb.lib_network.utils.GsonUtils.gsonToMaps(state);
               openKfUrl(map.get("url"),map.get("state"),map.get("title"));
           }
       });
    }
    @JavascriptInterface
    public void upgradePage(String eventID){
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                if(eventID==null||eventID.isEmpty())return;
                PointHelper.joinPointData(eventID,false);
            }
        });
    }

    public void openKfUrl(String url,String state,String title){
        if(state==null||state.isEmpty())return;
        if(!DataUtils.isInteger(state))return;
        int states=Integer.parseInt(state);
        if(states==1){
            //开启url跳转
            MainThreadUtils.post(new Runnable() {
                @Override
                public void run() {
                    context.initArguments().putString("url", url);
                    context.initArguments().putInt("payType",-1);
                    context.startFragment(CommonHelper.showCommonWeb());
                }
            });
        }else {
            //不开启
            MainThreadUtils.post(new Runnable() {
                @Override
                public void run() {
                    ClipboardUtils.copyText(AppUtils.getApp(),title);
                    ToastUtils.normal("微信号码复制成功，请前往微信添加客服", Gravity.CENTER);
                    Intent intent = new Intent();
                    ComponentName cmp = new ComponentName("com.tencent.mm", "com.tencent.mm.ui.LauncherUI");
                    intent.setAction(Intent.ACTION_MAIN);
                    intent.addCategory(Intent.CATEGORY_LAUNCHER);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.setComponent(cmp);
                    context.startActivity(intent);
                }
            });
        }
    }
    @JavascriptInterface
    public void noPremissBuyTags(){
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                MessageHelper.openNoPremissBuyDialog(context.getActivity(),true, PayRouthConfig.PAY_BACKPLAY_PAY);
            }
        });
    }
    @JavascriptInterface
    public void orderLive(String json){
        Map<String,String> maps= com.wb.lib_network.utils.GsonUtils.gsonToMaps(json);
        String liveId=maps.get("liveId");
        String wxChatUrl=maps.get("weixinUrl");
        CommonWebViewModel commonWebViewModel=new CommonWebViewModel();
        commonWebViewModel.liveActive(liveId, new ICommonWebJsLive() {
            @Override
            public void livePreSuccess() {
                Bus.post(new MsgEvent(CommonMsgTypeConfig.COMMON_LIVE_PRE_SUCCESS));
                openMinWxChat(wxChatUrl);
            }
        },context.getContext());
    }
    @JavascriptInterface
    public void goliveEndPage(){
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                context.initArguments().putString("routh","playblack");
                context.startFragment(HomeHelper.mainPage());
            }
        });
    }
    @JavascriptInterface
    public void backPlay(String str){
        // type liveId
        try {

            //{"type":3,"liveId":598}
            MainThreadUtils.post(new Runnable() {
                @Override
                public void run() {
                    Map<String,Object> map=com.wb.lib_network.utils.GsonUtils.gsonToMaps(str);
                    String liveId=map.get("liveId").toString();
                    String type=map.get("type").toString();
                    if(liveId==null||liveId.isEmpty()){
                        VideoHelper.showVideoActivity(type);
                    }else {
                        VideoHelper.showVideoActivity(type,liveId);
                    }
                }
            });
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void showTip(String msg){
        toastNative(msg);
    }
    @JavascriptInterface
    public void paySuccessType(String payType){
        commonWebListener.paySuccessFinsh(payType.equals("1")?true:false);
    }
    @JavascriptInterface
    public void againClose(){
        if(!NoDoubleClickUtils.isDoubleClick()){
            MessageHelper.openGeneralCentDialog(context.getActivity(), "您取消了支付，是有什么疑惑吗？可以主动咨询客服哦", "",
                    "暂时不需要", "好的", false, true, new GeneralDialogListener() {
                        @Override
                        public void onCancel() {
                            userGoBack();
                        }
                        @Override
                        public void onConfim() {
                        }

                        /**
                         *
                         */
                        @Override
                        public void onDismiss() {

                        }
                    });
        }
    }
    @JavascriptInterface
    public void freePayReminder(String json){
            if(StringUtils.isEmpty(json)){
                ToastUtils.normal("无效参数~");
                return;
            }
            MessageHelper.openOtherPayTs(context.getActivity(),json);
    }
}
