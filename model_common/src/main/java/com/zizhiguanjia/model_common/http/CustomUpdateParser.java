package com.zizhiguanjia.model_common.http;

import android.view.Gravity;

import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.xuexiang.xupdate.entity.UpdateEntity;
import com.xuexiang.xupdate.listener.IUpdateParseCallback;
import com.xuexiang.xupdate.proxy.IUpdateParser;
import com.zizhiguanjia.model_common.model.UpDateAppBean;

public class CustomUpdateParser implements IUpdateParser {
    private boolean isToast;

    public CustomUpdateParser(boolean isToast) {
        this.isToast = isToast;
    }

    @Override
    public UpdateEntity parseJson(String json) throws Exception {
        try {
            UpDateAppBean result = GsonUtils.gsonToBean(json, UpDateAppBean.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        UpDateAppBean result = GsonUtils.gsonToBean(json, UpDateAppBean.class);
        if (result != null) {
            if (isToast) {
                //设置
                String minVersionStr;
                if (result.getData().getMinCompatibleVersionNum().contains("V")) {
                    minVersionStr = result.getData().getMinCompatibleVersionNum().replace("V", "");
                } else {
                    minVersionStr = result.getData().getMinCompatibleVersionNum();
                }
                int minVersion = compareVersion(DeviceUtils.getAppVersionName(AppUtils.getApp()), minVersionStr);
                if (minVersion < 0) {
                    return new UpdateEntity()
                            .setHasUpdate(true)
                            .setIsIgnorable(false)
                            .setForce(true)
                            .setVersionCode(result.getData().getVersionCode())
                            .setVersionName(result.getData().getVersionNum())
                            .setUpdateContent(result.getData().getMsg())
                            .setDownloadUrl(result.getData().getDownLoadUrl());
                } else {
                    if (DeviceUtils.getAppVersionNo(AppUtils.getApp()) - result.getData().getVersionCode() >= 0) {
                        if (isToast) {
                            ToastUtils.normal("已是最新版本，无需更新！", Gravity.CENTER);
                        } else {
//                            MainHelper.checkExamUpdata();
                            return null;
                        }
                    } else {
                        if (DeviceUtils.getAppVersionNo(AppUtils.getApp()) - result.getData().getVersionCode() >= 0 || result.getData().getUpdataType() == 3) {//处理==3不显示弹窗
                            return null;
                        }
                        if (DeviceUtils.getAppVersionNo(AppUtils.getApp()) - result.getData().getVersionCode() >= 0) {
//                            MainHelper.checkExamUpdata();
                            return new UpdateEntity()
                                    .setHasUpdate(false)
                                    .setIsIgnorable(result.getData().getUpdataType() == 2 || result.getData().getUpdataType() == 3 ? true : false)
                                    .setForce(result.getData().getUpdataType() == 2 || result.getData().getUpdataType() == 3 ? false : true)
                                    .setVersionCode(result.getData().getVersionCode())
                                    .setVersionName(result.getData().getVersionNum())
                                    .setUpdateContent(result.getData().getMsg())
                                    .setDownloadUrl(result.getData().getDownLoadUrl());
                        } else {
                            return new UpdateEntity()
                                    .setHasUpdate(true)
                                    .setIsIgnorable(result.getData().getUpdataType() == 2 || result.getData().getUpdataType() == 3 ? true : false)
                                    .setForce(result.getData().getUpdataType() == 2 || result.getData().getUpdataType() == 3 ? false : true)
                                    .setVersionCode(result.getData().getVersionCode())
                                    .setVersionName(result.getData().getVersionNum())
                                    .setUpdateContent(result.getData().getMsg())
                                    .setDownloadUrl(result.getData().getDownLoadUrl());
                        }
                    }
                }
            } else {
                //首页
                String minVersionStr;
                if (result.getData().getMinCompatibleVersionNum().contains("V")) {
                    minVersionStr = result.getData().getMinCompatibleVersionNum().replace("V", "");
                } else {
                    minVersionStr = result.getData().getMinCompatibleVersionNum();
                }
                int minVersion = compareVersion(DeviceUtils.getAppVersionName(AppUtils.getApp()), minVersionStr);
                if (minVersion < 0) {
                    return new UpdateEntity()
                            .setHasUpdate(true)
                            .setIsIgnorable(false)
                            .setForce(true)
                            .setVersionCode(result.getData().getVersionCode())
                            .setVersionName(result.getData().getVersionNum())
                            .setUpdateContent(result.getData().getMsg())
                            .setDownloadUrl(result.getData().getDownLoadUrl());
                } else {
                    if (DeviceUtils.getAppVersionNo(AppUtils.getApp()) - result.getData().getVersionCode() >= 0) {
                        if (isToast) {
                            ToastUtils.normal("已是最新版本，无需更新！", Gravity.CENTER);
                        } else {
//                            MainHelper.checkExamUpdata();
                        }
                        return null;
                    } else {
                        if (result.getData().getUpdataType() == 3) {
                            if (isToast) {
                                ToastUtils.normal("已是最新版本，无需更新！", Gravity.CENTER);
                            } else {
//                                MainHelper.checkExamUpdata();
                            }
                            return null;
                        } else {
                            if (DeviceUtils.getAppVersionNo(AppUtils.getApp()) - result.getData().getVersionCode() >= 0) {
//                                MainHelper.checkExamUpdata();
                                return new UpdateEntity()
                                        .setHasUpdate(false)
                                        .setIsIgnorable(result.getData().getUpdataType() == 2 ? true : false)
                                        .setForce(result.getData().getUpdataType() == 2 ? false : true)
                                        .setVersionCode(result.getData().getVersionCode())
                                        .setVersionName(result.getData().getVersionNum())
                                        .setUpdateContent(result.getData().getMsg())
                                        .setDownloadUrl(result.getData().getDownLoadUrl());
                            } else {
                                return new UpdateEntity()
                                        .setHasUpdate(true)
                                        .setIsIgnorable(result.getData().getUpdataType() == 2 ? true : false)
                                        .setForce(result.getData().getUpdataType() == 2 ? false : true)
                                        .setVersionCode(result.getData().getVersionCode())
                                        .setVersionName(result.getData().getVersionNum())
                                        .setUpdateContent(result.getData().getMsg())
                                        .setDownloadUrl(result.getData().getDownLoadUrl());
                            }

                        }
                    }
                }
            }


        } else {
            if (isToast) {
                ToastUtils.normal("已是最新版本，无需更新！", Gravity.CENTER);
            } else {
//                MainHelper.checkExamUpdata();
            }
        }
        return null;
    }

    public static int compareVersion(String version1, String version2) {
        if (version1.equals(version2)) {
            return 0;
        }
        String[] version1Array = version1.split("\\.");
        String[] version2Array = version2.split("\\.");
        int index = 0;
        // 获取最小长度值
        int minLen = Math.min(version1Array.length, version2Array.length);
        int diff = 0;
        // 循环判断每位的大小
        while (index < minLen
                && (diff = Integer.parseInt(version1Array[index])
                - Integer.parseInt(version2Array[index])) == 0) {
            index++;
        }
        if (diff == 0) {
            // 如果位数不一致，比较多余位数
            for (int i = index; i < version1Array.length; i++) {
                if (Integer.parseInt(version1Array[i]) > 0) {
                    return 1;
                }
            }

            for (int i = index; i < version2Array.length; i++) {
                if (Integer.parseInt(version2Array[i]) > 0) {
                    return -1;
                }
            }
            return 0;
        } else {
            return diff > 0 ? 1 : -1;
        }
    }

    @Override
    public void parseJson(String json, IUpdateParseCallback callback) throws Exception {

    }

    @Override
    public boolean isAsyncParser() {
        return false;
    }
}
