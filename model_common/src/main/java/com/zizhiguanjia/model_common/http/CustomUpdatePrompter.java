package com.zizhiguanjia.model_common.http;

import android.app.Activity;
import android.content.Context;
import android.os.Message;

import androidx.annotation.NonNull;

import com.xuexiang.xupdate.entity.PromptEntity;
import com.xuexiang.xupdate.entity.UpdateEntity;
import com.xuexiang.xupdate.proxy.IUpdatePrompter;
import com.xuexiang.xupdate.proxy.IUpdateProxy;
import com.zizhiguanjia.lib_base.bean.OrderDialogBean;
import com.zizhiguanjia.lib_base.helper.MessageHelper;

public class CustomUpdatePrompter implements IUpdatePrompter {
    private Context mContext;
    public CustomUpdatePrompter(Context context) {
        this.mContext=context;
    }
    @Override
    public void showPrompt(@NonNull UpdateEntity updateEntity, @NonNull IUpdateProxy updateProxy, @NonNull PromptEntity promptEntity) {

        OrderDialogBean orderDialogBean=new OrderDialogBean();
        orderDialogBean.setIndex(1);
        orderDialogBean.setActivity((Activity) mContext);
        orderDialogBean.setUpdateProxy(updateProxy);
        orderDialogBean.setPromptEntity(updateEntity);
        MessageHelper.orderOpenDialog(orderDialogBean);
    }
}
