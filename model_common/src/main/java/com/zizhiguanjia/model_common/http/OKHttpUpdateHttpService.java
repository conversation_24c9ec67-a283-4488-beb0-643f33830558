package com.zizhiguanjia.model_common.http;

import androidx.annotation.NonNull;

import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.xuexiang.xupdate.proxy.IUpdateHttpService;
import com.zhy.http.okhttp.OkHttpUtils;
import com.zhy.http.okhttp.builder.PostFormBuilder;
import com.zhy.http.okhttp.builder.PostStringBuilder;
import com.zhy.http.okhttp.callback.FileCallBack;
import com.zhy.http.okhttp.callback.StringCallback;
import com.zhy.http.okhttp.request.RequestCall;
import com.zizhiguanjia.lib_base.config.BaseConfig;

import java.io.File;
import java.util.Map;
import java.util.TreeMap;
import okhttp3.Call;
import okhttp3.MediaType;
import okhttp3.Request;

public class OKHttpUpdateHttpService implements IUpdateHttpService {

    private boolean mIsPostJson;

    public OKHttpUpdateHttpService() {
        this(false);
    }

    public OKHttpUpdateHttpService(boolean isPostJson) {
        mIsPostJson = isPostJson;
    }


    @Override
    public void asyncGet(@NonNull String url, @NonNull Map<String, Object> params, final @NonNull Callback callBack) {
        OkHttpUtils.get()
                .url(url)
                .params(transform(params))
                .build()
                .execute(new StringCallback() {
                    @Override
                    public void onError(Call call, Exception e, int id) {
                        callBack.onError(e);
                    }

                    @Override
                    public void onResponse(String response, int id) {
                        callBack.onSuccess(response);
                    }
                });
    }

    @Override
    public void asyncPost(@NonNull String url, @NonNull Map<String, Object> params, final @NonNull Callback callBack) {
        //这里默认post的是Form格式，使用json格式的请修改 post -> postString
        RequestCall requestCall;
        if (mIsPostJson) {
            PostStringBuilder requestBuilder = OkHttpUtils.postString()
                    .url(url)
                    .addHeader("source","1")
                    .addHeader("deviceId", "dsada"+DeviceUtils.getAndroidId(AppUtils.getApp()))
                    .addHeader("version", DeviceUtils.getAppVersionName(AppUtils.getApp()))
//                    .content(JsonUtil.toJson(params))
                    .mediaType(MediaType.parse("application/json; charset=utf-8"));
            String token = BaseConfig.getRequestToken();
            if(StringUtils.isEmpty(token)){
                requestBuilder.addHeader("tokenErrorType", "3333");
            }else{
                requestBuilder.addHeader("token",token);
            }
            String oaId = com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getOaIdInfo();
            if(StringUtils.isEmpty(oaId)){
                com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getImeiInfo(info -> requestBuilder.addHeader("oaid",info));
            }else {
                requestBuilder.addHeader("oaid",oaId);
            }
            requestCall=requestBuilder.build();
        } else {
            PostFormBuilder requestBuilder = OkHttpUtils.post()
                    .url(url)
                    .addHeader("source","1")
                    .addHeader("deviceId", "dsada"+DeviceUtils.getAndroidId(AppUtils.getApp()))
                    .addHeader("version", DeviceUtils.getAppVersionName(AppUtils.getApp()))
                    .params(transform(params));
            String token = BaseConfig.getRequestToken();
            if(StringUtils.isEmpty(token)){
                requestBuilder.addHeader("tokenErrorType", "4444");
            }else{
                requestBuilder.addHeader("token",token);
            }
            String oaId = com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getOaIdInfo();
            if(StringUtils.isEmpty(oaId)){
                com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getImeiInfo(info -> requestBuilder.addHeader("oaid",info));
            }else {
                requestBuilder.addHeader("oaid",oaId);
            }
            requestCall=requestBuilder.build();
        }
        requestCall
                .execute(new StringCallback() {
                    @Override
                    public void onError(Call call, Exception e, int id) {
                        callBack.onError(e);
                    }

                    @Override
                    public void onResponse(String response, int id) {
                        callBack.onSuccess(response);
                    }
                });
    }

    @Override
    public void download(@NonNull String url, @NonNull String path, @NonNull String fileName, final @NonNull DownloadCallback callback) {
        OkHttpUtils.get()
                .url(url)
                .tag(url)
                .addHeader("source","1")
                .addHeader("deviceId", "dsada"+DeviceUtils.getAndroidId(AppUtils.getApp()))
                .addHeader("version", DeviceUtils.getAppVersionName(AppUtils.getApp()))
                .build()
                .execute(new FileCallBack(path, fileName) {
                    @Override
                    public void inProgress(float progress, long total, int id) {
                        callback.onProgress(progress, total);
                    }

                    @Override
                    public void onError(Call call, Exception e, int id) {
                        callback.onError(e);
                    }

                    @Override
                    public void onResponse(File response, int id) {
                        callback.onSuccess(response);
                    }

                    @Override
                    public void onBefore(Request request, int id) {
                        super.onBefore(request, id);
                        callback.onStart();
                    }
                });
    }

    @Override
    public void cancelDownload(@NonNull String url) {
        OkHttpUtils.getInstance().cancelTag(url);
    }

    private Map<String, String> transform(Map<String, Object> params) {
        Map<String, String> map = new TreeMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            map.put(entry.getKey(), entry.getValue().toString());
        }
        return map;
    }


}