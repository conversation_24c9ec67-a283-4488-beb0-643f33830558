package com.zizhiguanjia.model_common.ui;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.zizhiguanjia.lib_base.base.ContainerActivity;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.constants.CommonRouterPath;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.databinding.CommonServiceLayoutBinding;
import com.zizhiguanjia.model_common.viewmodel.ServiceWebViewModel;
@Route(path = CommonRouterPath.COMMON_WEB_ACTIVITY)
public class ServiceActivity extends ContainerActivity implements TitleBar.OnTitleBarListener {
    private CommonServiceLayoutBinding commonServiceLayoutBinding;
    @BindViewModel
    ServiceWebViewModel serviceWebViewModel;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        commonServiceLayoutBinding=DataBindingUtil.setContentView(this, R.layout.common_service_layout);
        serviceWebViewModel.initParams(this);
        serviceWebViewModel.initWebViewParams(commonServiceLayoutBinding.wbService);
        commonServiceLayoutBinding.wbService.loadUrl(ConfigHelper.getKfUrl());
        commonServiceLayoutBinding.tbService.setClickListener(this);
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if(msgEvent.getCode()==999999999){
                    finish();
                }
            }
        });
    }
    @Override
    public void onBackPressed() {
        finish();
    }
    @Override
    protected void onActivityResult(int requestCode, int resultCode,  Intent intent) {
        super.onActivityResult(requestCode, resultCode, intent);
        //
        if (requestCode == serviceWebViewModel.FILECHOOSER_RESULTCODE) {
            if (null == serviceWebViewModel.mUploadMessage)
                return;
            Uri result = intent == null || resultCode != RESULT_OK ? null: intent.getData();
            serviceWebViewModel.mUploadMessage.onReceiveValue(result);
            serviceWebViewModel.mUploadMessage = null;
        } else if (requestCode == serviceWebViewModel.FILECHOOSER_RESULTCODE_FOR_ANDROID_5){
            if (null == serviceWebViewModel.mUploadMessageForAndroid5)
                return;
            Uri result = (intent == null || resultCode != RESULT_OK) ? null: intent.getData();
            if (result != null) {
                serviceWebViewModel.mUploadMessageForAndroid5.onReceiveValue(new Uri[]{result});
            } else {
                serviceWebViewModel.mUploadMessageForAndroid5.onReceiveValue(new Uri[]{});
            }
            serviceWebViewModel.mUploadMessageForAndroid5 = null;
        }
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        //主动回收
        Runtime.getRuntime().gc();
    }

    @Override
    public void onClicked(View v, int action) {
        if(action==TitleBar.ACTION_LEFT_BUTTON){
            finish();
        }
    }
}
