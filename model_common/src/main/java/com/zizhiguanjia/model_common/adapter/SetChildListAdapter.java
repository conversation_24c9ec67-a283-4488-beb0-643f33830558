package com.zizhiguanjia.model_common.adapter;

import android.view.Gravity;
import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.config.SetMsgConfig;
import com.zizhiguanjia.model_common.databinding.CommonSetchildItemBinding;
import com.zizhiguanjia.model_common.databinding.CommonSetgroupItemBinding;
import com.zizhiguanjia.model_common.model.SetListChildBean;

import vn.luongvo.widget.iosswitchview.SwitchView;

public class SetChildListAdapter extends BaseAdapter<SetListChildBean> {
    private CommonSetchildItemBinding binding;
    public SetChildListAdapter( ) {
        super(R.layout.common_setchild_item);
    }

    @Override
    protected void bind(BaseViewHolder holder, SetListChildBean item, int position) {
        binding=holder.getBinding();
        binding.setData(item);
        binding.setMaxsize(getItemCount());
        binding.setPostion(position+1);
        binding.setModel(this);
        if(item.getType()==3){
            binding.tvDes.setVisibility(View.GONE);
            binding.tvMessDes.setVisibility(View.VISIBLE);
        }else {
            binding.tvDes.setVisibility(View.VISIBLE);
            binding.tvMessDes.setVisibility(View.GONE);
        }
        boolean state=KvUtils.get("push_state",true);
        binding.switchview.setChecked(state);
        binding.switchview.setOnCheckedChangeListener(new SwitchView.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(SwitchView switchView, boolean isChecked) {
                ToastUtils.normal(isChecked?"开启推送":"关闭推送", Gravity.CENTER);
                KvUtils.save("push_state",isChecked);
            }
        });
    }
    public void onClick(View view, String id){
        Bus.post(new MsgEvent(SetMsgConfig.SET_MSG_SET,id));
    }
}
