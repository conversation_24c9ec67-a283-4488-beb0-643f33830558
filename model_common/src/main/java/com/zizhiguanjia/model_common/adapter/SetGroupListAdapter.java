package com.zizhiguanjia.model_common.adapter;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.databinding.CommonSetgroupItemBinding;
import com.zizhiguanjia.model_common.model.SetListGroupBean;

public class SetGroupListAdapter extends BaseAdapter<SetListGroupBean> {
    private CommonSetgroupItemBinding binding;
    public SetGroupListAdapter() {
        super(R.layout.common_setgroup_item);
    }

    @Override
    protected void bind(BaseViewHolder holder, SetListGroupBean item, int position) {
        binding=holder.getBinding();
        SetChildListAdapter childListAdapter=null;
        if(binding.commonSetChildRcy.getAdapter()==null){
            binding.commonSetChildRcy.setLayoutManager(new LinearLayoutManager(holder.itemView.getContext()));
            childListAdapter=new SetChildListAdapter();
            binding.commonSetChildRcy.setAdapter(childListAdapter);
        }else {
            childListAdapter= (SetChildListAdapter) binding.commonSetChildRcy.getAdapter();
        }
        childListAdapter.setDataItems(item.getSetListBeans());
    }
}
