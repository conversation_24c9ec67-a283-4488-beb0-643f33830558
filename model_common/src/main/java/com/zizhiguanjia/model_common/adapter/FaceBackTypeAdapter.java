package com.zizhiguanjia.model_common.adapter;

import android.graphics.Color;
import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.databinding.CommonFackbacktypeItemBinding;
import com.zizhiguanjia.model_common.listener.FaceBackTypeListener;
import com.zizhiguanjia.model_common.model.FaceBackTypeBean;

public class FaceBackTypeAdapter extends BaseAdapter<FaceBackTypeBean> {
    private CommonFackbacktypeItemBinding binding;
    private String selectId;
    public String getSelectId() {
        return selectId;
    }
    private FaceBackTypeListener faceBackTypeListener;
    public FaceBackTypeAdapter(FaceBackTypeListener listener) {
        super(R.layout.common_fackbacktype_item);
        this.faceBackTypeListener=listener;
    }

    @Override
    protected void bind(BaseViewHolder holder, FaceBackTypeBean item, int position) {
        binding=holder.getBinding();
        binding.setData(item);
        binding.setModel(this);
        binding.commonBackfaceTypeImg.setImageResource(getImageRes(item.getId()));
    }
    public void onClick(View view,String ids){
        selectId=ids;
        notifyDataSetChanged();
        faceBackTypeListener.onUserSelectFaceType(ids);
    }
    public int getImageRes(String id){
        binding.commonBackfaceTypeImg.setSelected(id.equals(selectId)?true:false);
        binding.commonBackfaceTypeMainLl.setSelected(id.equals(selectId)?true:false);
        binding.commonFacebackTypeTitileTv.setTextColor(id.equals(selectId)?Color.parseColor("#FFFFFF"):Color.parseColor("#6C7596"));
        if(id.equals("10")){
            //账号
            return R.drawable.common_face_type_account;
        }else if(id.equals("11")){
            //功能故障
            return R.drawable.common_face_type_action;
        }else if(id.equals("12")){
            //题库
            return R.drawable.common_face_type_tk;
        }else if(id.equals("13")){
            //其他
            return R.drawable.common_face_type_other;
        }else if(id.equals("14")){
            //建议
            return R.drawable.common_face_type_jy;
        }else if(id.equals("15")){
            //建议
            return R.drawable.common_face_type_ksjs;
        }else if(id.equals("16")){
            //建议
            return R.drawable.common_face_type_bkzb;
        }else if(id.equals("17")){
            //建议
            return R.drawable.common_face_type_kslc;
        }else if(id.equals("18")){
            //建议
            return R.drawable.common_face_type_ksap;
        }else if(id.equals("19")){
            //建议
            return R.drawable.common_face_type_qtap;
        }
        return R.drawable.common_face_type_jy;
    }
}
