package com.zizhiguanjia.model_common.adapter;

import android.view.Gravity;
import android.view.View;

import com.caimuhao.rxpicker.bean.ImageItem;
import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_image_loadcal.GlideImageLoader;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.databinding.CommonFacebackImageItemBinding;
import com.zizhiguanjia.model_common.fragment.FaceBackFragment;

import java.util.ArrayList;
import java.util.List;

public class ImageLookAdapter extends BaseAdapter<ImageItem> {
    private CommonFacebackImageItemBinding imageItemBinding;
    private  FaceBackFragment mFaceBackFragment;
    public ImageLookAdapter(FaceBackFragment mFaceBackFragment ) {
        super(R.layout.common_faceback_image_item);
        this.mFaceBackFragment=mFaceBackFragment;
        List<ImageItem> strings=new ArrayList<>();
        ImageItem imageItem=new ImageItem();
        imageItem.setId(0);
        strings.add(imageItem);
        setDataItems(strings);
    }

    @Override
    protected void bind(BaseViewHolder holder, ImageItem item, int position) {
        imageItemBinding=holder.getBinding();
        imageItemBinding.setModel(this);
        imageItemBinding.setIds(item.getId());
        if(item.getId()==0){
            imageItemBinding.commonLookimageImg.setImageResource(R.drawable.add_img);
            imageItemBinding.delectImage.setVisibility(View.GONE);
        }else {
            imageItemBinding.commonLookimageImg.setImageResource(0);
            imageItemBinding.delectImage.setVisibility(View.VISIBLE);
            new GlideImageLoader().loadRoundImage(item.getPath(),imageItemBinding.commonLookimageImg,10);
        }
    }
    public void onClick(View view,int id){
        if(id!=0)return;
        if (mFaceBackFragment != null) {
            if (getData().size() > 9) {
                ToastUtils.normal("上传图片只能上传9张！", Gravity.CENTER);
            } else {
                MainThreadUtils.post(() -> {
                    boolean needDialog = KvUtils.get("homePermiss", false);
                    LogUtils.e("看下needDialog" + needDialog);
                    if (needDialog) {
                        mFaceBackFragment.showImage();
                    } else {
                        MessageHelper.openGeneralCentDialog(mFaceBackFragment.requireActivity(), "为了选择图片收集用户反馈，需要访问您设备上的照片", "",
                                "取消", "确定", false, true, new GeneralDialogListener() {
                                    @Override
                                    public void onCancel() {
                                        KvUtils.save("homePermiss", true);
                                    }

                                    @Override
                                    public void onConfim() {
                                        KvUtils.save("homePermiss", true);
                                        mFaceBackFragment.showImage();
                                    }

                                    /**
                                     *
                                     */
                                    @Override
                                    public void onDismiss() {

                                    }
                                });
                    }
                });
            }
        }
    }
    public void delectImage(View view,int postion){
        if(mFaceBackFragment==null)return;
        mFaceBackFragment.delectImage(postion);
    }
    public int getImageMaxSize(){
        int max=0;
        for(ImageItem imageItem:getData()){
            if(imageItem.getId()!=0){
                max=max+1;
            }
        }
        return 9-max;
    }
}
