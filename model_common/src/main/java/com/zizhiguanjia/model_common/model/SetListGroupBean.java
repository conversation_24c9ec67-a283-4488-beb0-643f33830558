package com.zizhiguanjia.model_common.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class SetListGroupBean implements Parcelable {
    private String id;
    private List<SetListChildBean> setListBeans;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<SetListChildBean> getSetListBeans() {
        return setListBeans;
    }

    public void setSetListBeans(List<SetListChildBean> setListBeans) {
        this.setListBeans = setListBeans;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.id);
        dest.writeTypedList(this.setListBeans);
    }

    public SetListGroupBean() {
    }

    protected SetListGroupBean(Parcel in) {
        this.id = in.readString();
        this.setListBeans = in.createTypedArrayList(SetListChildBean.CREATOR);
    }

    public static final Parcelable.Creator<SetListGroupBean> CREATOR = new Parcelable.Creator<SetListGroupBean>() {
        @Override
        public SetListGroupBean createFromParcel(Parcel source) {
            return new SetListGroupBean(source);
        }

        @Override
        public SetListGroupBean[] newArray(int size) {
            return new SetListGroupBean[size];
        }
    };
}
