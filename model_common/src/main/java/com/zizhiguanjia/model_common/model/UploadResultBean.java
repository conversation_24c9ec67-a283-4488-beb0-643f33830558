package com.zizhiguanjia.model_common.model;

import android.os.Parcel;
import android.os.Parcelable;

public class UploadResultBean implements Parcelable {
    private String FileName;
    private String FilePath;
    private boolean IsUpload;

    public String getFileName() {
        return FileName;
    }

    public void setFileName(String fileName) {
        FileName = fileName;
    }

    public String getFilePath() {
        return FilePath;
    }

    public void setFilePath(String filePath) {
        FilePath = filePath;
    }

    public boolean isUpload() {
        return IsUpload;
    }

    public void setUpload(boolean upload) {
        IsUpload = upload;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.FileName);
        dest.writeString(this.FilePath);
        dest.writeByte(this.IsUpload ? (byte) 1 : (byte) 0);
    }

    public UploadResultBean() {
    }

    protected UploadResultBean(Parcel in) {
        this.FileName = in.readString();
        this.FilePath = in.readString();
        this.IsUpload = in.readByte() != 0;
    }

    public static final Parcelable.Creator<UploadResultBean> CREATOR = new Parcelable.Creator<UploadResultBean>() {
        @Override
        public UploadResultBean createFromParcel(Parcel source) {
            return new UploadResultBean(source);
        }

        @Override
        public UploadResultBean[] newArray(int size) {
            return new UploadResultBean[size];
        }
    };
}
