package com.zizhiguanjia.model_common.model;

import android.os.Parcel;
import android.os.Parcelable;

public class FaceBackTypeBean implements Parcelable {
    private String title;
    private String id;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.title);
        dest.writeString(this.id);
    }

    public FaceBackTypeBean() {
    }

    protected FaceBackTypeBean(Parcel in) {
        this.title = in.readString();
        this.id = in.readString();
    }

    public static final Parcelable.Creator<FaceBackTypeBean> CREATOR = new Parcelable.Creator<FaceBackTypeBean>() {
        @Override
        public FaceBackTypeBean createFromParcel(Parcel source) {
            return new FaceBackTypeBean(source);
        }

        @Override
        public FaceBackTypeBean[] newArray(int size) {
            return new FaceBackTypeBean[size];
        }
    };
}
