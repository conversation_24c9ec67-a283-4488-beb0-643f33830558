package com.zizhiguanjia.model_common.model;

import com.google.gson.annotations.SerializedName;

public class UpDateAppBean {

    private DataBean Data;
    private int Code;
    private boolean IsOk;
    private String Message;

    public DataBean getData() {
        return Data;
    }

    public void setData(DataBean Data) {
        this.Data = Data;
    }

    public int getCode() {
        return Code;
    }

    public void setCode(int Code) {
        this.Code = Code;
    }

    public boolean isIsOk() {
        return IsOk;
    }

    public void setIsOk(boolean IsOk) {
        this.IsOk = IsOk;
    }

    public String getMessage() {
        return Message;
    }

    public void setMessage(String Message) {
        this.Message = Message;
    }

    public static class DataBean {
        /**
         * Id : 1
         * AppTypeId : 1
         * AppName : App_安全员考试宝典_android
         * VersionCode : 1
         * VersionNum : 1.0
         * Msg : 1.0
         * UpdataType : 1
         * DownLoadUrl :
         * ApiUrl :
         * IsExamine : 0
         */

        private int Id;
        private int AppTypeId;
        private String AppName;
        private int VersionCode;
        @SerializedName("VersionNumber")
        private String VersionNum;
        @SerializedName("UpgradePrompt")
        private String Msg;
        @SerializedName("UpgradeType")
        private int UpdataType;
        @SerializedName("DownLoadAddress")
        private String DownLoadUrl;
        private String ApiUrl;
        private int IsExamine;
        private String MinCompatibleVersionNum;

        public String getMinCompatibleVersionNum() {
            return MinCompatibleVersionNum;
        }

        public void setMinCompatibleVersionNum(String minCompatibleVersionNum) {
            MinCompatibleVersionNum = minCompatibleVersionNum;
        }

        public int getId() {
            return Id;
        }
        public void setId(int Id) {
            this.Id = Id;
        }

        public int getAppTypeId() {
            return AppTypeId;
        }

        public void setAppTypeId(int AppTypeId) {
            this.AppTypeId = AppTypeId;
        }

        public String getAppName() {
            return AppName;
        }

        public void setAppName(String AppName) {
            this.AppName = AppName;
        }

        public int getVersionCode() {
            return VersionCode;
        }

        public void setVersionCode(int VersionCode) {
            this.VersionCode = VersionCode;
        }

        public String getVersionNum() {
            return VersionNum;
        }

        public void setVersionNum(String VersionNum) {
            this.VersionNum = VersionNum;
        }

        public String getMsg() {
            return Msg;
        }

        public void setMsg(String Msg) {
            this.Msg = Msg;
        }

        public int getUpdataType() {
            return UpdataType;
        }

        public void setUpdataType(int UpdataType) {
            this.UpdataType = UpdataType;
        }

        public String getDownLoadUrl() {
            return DownLoadUrl;
        }

        public void setDownLoadUrl(String DownLoadUrl) {
            this.DownLoadUrl = DownLoadUrl;
        }

        public String getApiUrl() {
            return ApiUrl;
        }

        public void setApiUrl(String ApiUrl) {
            this.ApiUrl = ApiUrl;
        }

        public int getIsExamine() {
            return IsExamine;
        }

        public void setIsExamine(int IsExamine) {
            this.IsExamine = IsExamine;
        }
    }
}
