package com.zizhiguanjia.model_common.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class UploadBean implements Parcelable {
    private List<UploadResultBean> Results;

    public List<UploadResultBean> getResults() {
        return Results;
    }

    public void setResults(List<UploadResultBean> results) {
        Results = results;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(this.Results);
    }

    public UploadBean() {
    }

    protected UploadBean(Parcel in) {
        this.Results = in.createTypedArrayList(UploadResultBean.CREATOR);
    }

    public static final Parcelable.Creator<UploadBean> CREATOR = new Parcelable.Creator<UploadBean>() {
        @Override
        public UploadBean createFromParcel(Parcel source) {
            return new UploadBean(source);
        }

        @Override
        public UploadBean[] newArray(int size) {
            return new UploadBean[size];
        }
    };
}
