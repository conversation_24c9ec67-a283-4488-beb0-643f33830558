package com.zizhiguanjia.model_common.model;

import com.google.gson.annotations.SerializedName;

public class NativePayBean {
    private String  appid;
    private String  partnerid;
    private String   prepayid;
    @SerializedName("package")
    private String  packagex;
    private String  noncestr;
    private String  timestamp;
    private String  sign;
    //订单号
    private String  billCode;
    private String WxCodeUrl;
    private String WxLink;
    private boolean isShowPopup;
    private ExtData extData;
    private String keFu;

    public void setKeFu(String keFu) {
        this.keFu = keFu;
    }

    public String getKeFu() {
        return keFu;
    }

    public void setExtData(NativePayBean.ExtData extData) {
        this.extData = extData;
    }

    public NativePayBean.ExtData getExtData() {
        return extData;
    }

    public boolean isShowPopup() {
        return isShowPopup;
    }

    public void setShowPopup(boolean showPopup) {
        isShowPopup = showPopup;
    }

    public String getWxCodeUrl() {
        return WxCodeUrl;
    }

    public void setWxCodeUrl(String wxCodeUrl) {
        WxCodeUrl = wxCodeUrl;
    }

    public String getWxLink() {
        return WxLink;
    }

    public void setWxLink(String wxLink) {
        WxLink = wxLink;
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPartnerid() {
        return partnerid;
    }

    public void setPartnerid(String partnerid) {
        this.partnerid = partnerid;
    }

    public String getPrepayid() {
        return prepayid;
    }

    public void setPrepayid(String prepayid) {
        this.prepayid = prepayid;
    }

    public String getPackagex() {
        return packagex;
    }

    public void setPackagex(String packagex) {
        this.packagex = packagex;
    }

    public String getNoncestr() {
        return noncestr;
    }

    public void setNoncestr(String noncestr) {
        this.noncestr = noncestr;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    /**
     * 扩展信息
     */
    public static class ExtData{
        /**
         * 刷新页面类型
         */
        private String RefferPage;

        public void setRefferPage(String refferPage) {
            RefferPage = refferPage;
        }

        public String getRefferPage() {
            return RefferPage;
        }
    }
}
