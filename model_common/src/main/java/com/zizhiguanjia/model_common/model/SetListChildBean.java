package com.zizhiguanjia.model_common.model;

import android.os.Parcel;
import android.os.Parcelable;

public class SetListChildBean implements Parcelable {

    private String id;
    private String title;
    private int type;
    private String des;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.id);
        dest.writeString(this.title);
        dest.writeInt(this.type);
        dest.writeString(this.des);
    }

    public SetListChildBean() {
    }

    protected SetListChildBean(Parcel in) {
        this.id = in.readString();
        this.title = in.readString();
        this.type = in.readInt();
        this.des = in.readString();
    }

    public static final Parcelable.Creator<SetListChildBean> CREATOR = new Parcelable.Creator<SetListChildBean>() {
        @Override
        public SetListChildBean createFromParcel(Parcel source) {
            return new SetListChildBean(source);
        }

        @Override
        public SetListChildBean[] newArray(int size) {
            return new SetListChildBean[size];
        }
    };
}
