package com.zizhiguanjia.model_common.model;

public class JsPointerBean {

    /**
     * data : {"state":"0","isReachBottom":"0","hangTime":"200"}
     * registeredByWho : 0
     * eventId : dasda
     */

    private JsDataBean data;
    private String registeredByWho;
    private String eventId;

    public JsDataBean getData() {
        return data;
    }

    public void setData(JsDataBean data) {
        this.data = data;
    }

    public String getRegisteredByWho() {
        return registeredByWho;
    }

    public void setRegisteredByWho(String registeredByWho) {
        this.registeredByWho = registeredByWho;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public static class JsDataBean {
        /**
         * state : 0
         * isReachBottom : 0
         * hangTime : 200
         */

        private String state;
        private String isReachBottom;
        private String hangTime;

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getIsReachBottom() {
            return isReachBottom;
        }

        public void setIsReachBottom(String isReachBottom) {
            this.isReachBottom = isReachBottom;
        }

        public String getHangTime() {
            return hangTime;
        }

        public void setHangTime(String hangTime) {
            this.hangTime = hangTime;
        }
    }
}
