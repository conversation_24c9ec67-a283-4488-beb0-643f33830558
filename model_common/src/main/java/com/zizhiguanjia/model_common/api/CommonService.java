package com.zizhiguanjia.model_common.api;

import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.model_common.model.UploadBean;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;

public interface CommonService {
    @Multipart
    @POST(BaseAPI.VERSION_DES+"/API/Common/UploadImages")
    Observable<BaseData<UploadBean>> uploadImage(@Part List<MultipartBody.Part> parts, @Part("type") RequestBody Content);
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Common/AddFeedback")
    Observable<BaseData> uploadImageOrTxt(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/video/OrderLive")
    Observable<BaseData> postLive(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Common/IgnoreAPPVersionUpgrade")
    Observable<BaseData> ignoreUpdata(@FieldMap Map<String,String> params);
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/ExamDoc/GetExamDocContent")
    Observable<BaseData<Map<String,String>>> getExamDocContent(@FieldMap Map<String,String> params);

}
