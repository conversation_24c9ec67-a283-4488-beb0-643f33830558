package com.zizhiguanjia.model_common.help;

import com.wb.lib_network.AbstractHttp;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseUrlInterceptor;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.config.ResponInterceptor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;

public class UploadImageHelp extends AbstractHttp {
    private volatile static UploadImageHelp http;
    public static UploadImageHelp getInstance() {
        if (http == null) {
            synchronized (Http.class) {
                if (http == null) {
                    http = new UploadImageHelp();
                }
            }
        }
        return http;
    }
    @Override
    protected String baseUrl() {
        return BaseAPI.Base_Url_Api;
    }
    @Override
    protected Iterable<Interceptor> interceptors() {
        final List<Interceptor> interceptorList = new ArrayList<>();
        interceptorList.add(new BaseUrlInterceptor());
        interceptorList.add(new ResponInterceptor());
        return interceptorList;

    }
    public void upload(String url, RequestBody requestBody, Callback callback) throws IOException {
        Request request = new Request.Builder()
                .url(BaseAPI.Base_Url_Api+url)
                .post(requestBody)
                .build();
        doAsync(request, callback);
    }
    /**
     * 异步请求
     */
    private void doAsync(Request request, Callback callback) throws IOException {
        //创建请求会话
        Call call = okHttpClient().newCall(request);
        //异步执行会话请求
        call.enqueue(callback);
    }
}
