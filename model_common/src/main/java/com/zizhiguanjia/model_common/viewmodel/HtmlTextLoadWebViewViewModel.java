package com.zizhiguanjia.model_common.viewmodel;

import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.emums.WebViewLoadTypeEnum;
import com.zizhiguanjia.model_common.api.CommonService;

import java.util.HashMap;
import java.util.Map;

import androidx.lifecycle.MutableLiveData;

/**
 * 功能作用：html文本本地加载model
 * 初始注释时间： 2023/12/18 20:44
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class HtmlTextLoadWebViewViewModel extends CommonWebViewModel {
    private final CommonService mApi = Http.getInstance().create(CommonService.class);
    /**
     * 显示的文本数据
     */
    private final MutableLiveData<String> mShowHtmlTextData = new MutableLiveData<>();
    /**
     * 显示的标题文本
     */
    private final MutableLiveData<String> mShowTitleText = new MutableLiveData<>();

    /**
     * 网页加载类型
     */
    private WebViewLoadTypeEnum mLoadType;

    public MutableLiveData<String> getShowHtmlTextData() {
        return mShowHtmlTextData;
    }

    public MutableLiveData<String> getShowTitleText() {
        return mShowTitleText;
    }

    /**
     * 设置网页加载类型
     */
    public void setLoadType(WebViewLoadTypeEnum loadType) {
        mLoadType = loadType;
    }


    /**
     * 加载数据
     *
     * @param params 请求参数
     */
    public void loadData(Object params) {
        Map<String, String> map = new HashMap<>();
        switch (mLoadType) {
            case DOC_MAJORS:
                map.put("docId", (String) params);
                launchOnlyResult(mApi.getExamDocContent(map), new OnHandleException<BaseData<Map<String, String>>>() {
                    @Override
                    public void success(BaseData<Map<String, String>> data) {
                        mShowTitleText.postValue(data.Data.get("Title"));
                        mShowHtmlTextData.postValue(data.Data.get("Content"));
                    }

                    @Override
                    public void error(String msg) {

                    }
                });
                break;
            default:
                break;
        }
    }
}
