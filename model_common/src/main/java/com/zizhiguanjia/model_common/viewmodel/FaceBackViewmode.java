package com.zizhiguanjia.model_common.viewmodel;

import android.view.Gravity;
import android.view.View;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.caimuhao.rxpicker.RxPicker;
import com.caimuhao.rxpicker.bean.ImageItem;
import com.example.lib_common.base.CommonViewModel;
import com.example.lib_common.bean.MsgData;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.task.RxAsyncTask;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.FileUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.listeners.UploadImageToServiceListener;
import com.zizhiguanjia.model_common.api.CommonService;
import com.zizhiguanjia.model_common.config.ImageTypeConfig;
import com.zizhiguanjia.model_common.fragment.FaceBackFragment;
import com.zizhiguanjia.model_common.manager.UploadManager;
import com.zizhiguanjia.model_common.model.FaceBackTypeBean;
import com.zizhiguanjia.model_common.model.UploadBean;
import com.zizhiguanjia.model_common.model.UploadResultBean;
import com.zizhiguanjia.model_common.navigator.FaceBackNavigator;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import io.reactivex.functions.Consumer;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.http.Multipart;
import retrofit2.http.PUT;
import top.zibin.luban.Luban;

public class FaceBackViewmode extends CommonViewModel {
    private FaceBackNavigator faceBackNavigator;
    private FaceBackFragment faceBackFragment;
    private List<ImageItem> imageItems;
    private CommonService mApi= Http.getInstance().create(CommonService.class);
    public ObservableField<String> faceBackMsg=new ObservableField<>();
    public ObservableField<String> faceBackTypeIds=new ObservableField<>();
    public MutableLiveData<Boolean> postSuccess=new MutableLiveData<>();
    public ObservableField<Boolean>accountLoginVer=new ObservableField<>();
    private String ids;
    public void initParam(FaceBackFragment faceBackFragments,FaceBackNavigator faceBackNavigator,String ids){
        this.faceBackFragment=faceBackFragments;
        this.faceBackNavigator = faceBackNavigator;
        this.ids=ids;
        accountLoginVer.set(false);
        imageItems=new ArrayList<>();
        initFaceBackTypeConfig();
    }
    public void clearImages(int ids){
        if(imageItems==null||imageItems.size()==0)return;
        Iterator<ImageItem> iterator = imageItems.iterator();
        while (iterator.hasNext()){
            ImageItem imageItem=iterator.next();
            if(imageItem.getId()==ids){
                iterator.remove();
            }
        }
    }
    public void initFaceBackTypeConfig(){
        RxJavaUtils.executeAsyncTask(new RxAsyncTask<String, List<FaceBackTypeBean>>(StringUtils.isEmpty(ids)?"facebacktypeconfig.json":"errorcorrection.json") {
            @Override
            public void doInUIThread(List<FaceBackTypeBean> faceBackTypeBeans) {
                if(faceBackNavigator!=null){
                    faceBackNavigator.getFaceBackTypeListData(faceBackTypeBeans);
                }
            }

            @Override
            public List<FaceBackTypeBean> doInIOThread(String s) {
                LogUtils.e("看看读取----->>>>"+s);
                try {
                    String jsonStr=FileUtils.getFromAssets(AppUtils.getApp(),s);
                    List<FaceBackTypeBean> faceBackTypeBeans=GsonUtils.jsonToList(jsonStr,FaceBackTypeBean.class);
                    return faceBackTypeBeans;
                }catch (Exception e){
                    return null;
                }
            }
        });
    }
    public void onclick(View view){
        lunbanToImage();
    }

    public void openImageView(int currentMaxSize){
        RxPicker.of()
                .single(false)
                .camera(false)
                .limit(1,currentMaxSize)
                .start(faceBackFragment.getActivity())
                .subscribe(new Consumer<List<ImageItem>>() {
                    @Override
                    public void accept(List<ImageItem> images) throws Exception {
                        imageItems.addAll(images);
                        faceBackNavigator.updateFaceImage(images);
                    }
                });
    }

    public void lunbanToImage(){
        if(StringUtils.isEmpty(faceBackTypeIds.get())){
            ToastUtils.normal("请选择问题类型！", Gravity.CENTER);
            return;
        }
        if(StringUtils.isEmpty(faceBackMsg.get())&&(imageItems==null||imageItems.size()==0)){
            //没有文本
            ToastUtils.normal("图片或者描述必须有一个！", Gravity.CENTER);
        }else if(!StringUtils.isEmpty(faceBackMsg.get())&&(imageItems==null||imageItems.size()==0)){
            //只有文字
            postMsgOrImage(faceBackMsg.get(),faceBackTypeIds.get(),null);
        }else {
            postImage(faceBackTypeIds.get(),faceBackMsg.get());
        }
    }
    public void postImage(String questionType,String context){
        List<File>files=new ArrayList<>();
        for(ImageItem imageItem:imageItems){
            if(imageItem.getId()!=0){
                files.add(new File(imageItem.getPath()));
            }
        }
        faceBackNavigator.showLoading(true);
        Map<String,String> params=new HashMap<>();
        params.put("type", StringUtils.isEmpty(ids)?ImageTypeConfig.IMAGE_TYPE_FACEBACK+"":ImageTypeConfig.IMAGE_TYPE_ErrorCorrection+"");
        if(!StringUtils.isEmpty(ids)){
            params.put("questionId",ids);
        }
        UploadManager.getInstance().uploadMulteImage(BaseAPI.VERSION_DES+"/API/Common/UploadImages", files, params, new UploadImageToServiceListener() {
            @Override
            public void uploadToService(boolean uploadState, String urls) {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        if(uploadState){
                            postMsgOrImage(context,questionType,urls);
                        }else {
                            ToastUtils.normal(urls, Gravity.CENTER);
                            faceBackNavigator.showLoading(false);
                        }
                    }
                });
            }
        });
    }
    public void postMsgOrImage(String context,String type,String images){
        faceBackNavigator.showLoading(true);
        Map<String,String> params=new HashMap<>();
        params.put("type",type);
        if(!StringUtils.isEmpty(context)){
            params.put("content",context);
        }
        if(!StringUtils.isEmpty(images)){
            params.put("images",images);
        }
        params.put("refferPage",StringUtils.isEmpty(ids)?"4":"5");
        launchOnlyResult(mApi.uploadImageOrTxt(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                ToastUtils.normal("反馈成功！", Gravity.CENTER);
                faceBackNavigator.showLoading(false);
                postSuccess.postValue(true);
            }

            @Override
            public void error(String msg) {
                ToastUtils.normal(msg, Gravity.CENTER);
                faceBackNavigator.showLoading(false);
            }
        });
    }
    public void checkTextVa(int image){
        if(StringUtils.isEmpty(faceBackTypeIds.get())&&StringUtils.isEmpty(faceBackMsg.get())&&image==0){
            accountLoginVer.set(false);
        }else {
            accountLoginVer.set(true);
        }

    }
}
