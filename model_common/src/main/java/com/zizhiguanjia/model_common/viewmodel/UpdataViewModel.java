package com.zizhiguanjia.model_common.viewmodel;

import android.view.Gravity;

import com.wb.lib_network.BaseViewModel;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_common.api.CommonService;

import java.util.HashMap;
import java.util.Map;

public class UpdataViewModel extends BaseViewModel {
    private CommonService mApi=new Http().create(CommonService.class);
    public void ignoreUpdata(String version){
        Map<String,String> params=new HashMap<>();
        params.put("version",version);
        launchOnlyResult(mApi.ignoreUpdata(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
            }
            @Override
            public void error(String msg) {
            }
        });
    }
}
