package com.zizhiguanjia.model_common.viewmodel;

import android.content.Intent;
import android.net.Uri;
import android.provider.Settings;
import android.view.View;

import androidx.databinding.ObservableField;

import com.wb.lib_network.BaseViewModel;
import com.wb.lib_utils.AppUtils;
import com.zizhiguanjia.model_common.fragment.PermissFragment;

import static com.wb.lib_utils.permission.PermissionFragment.REQUEST_PERMISSION_SETTING;

public class PermissViewModel extends BaseViewModel {
    public ObservableField<String> permissTxt=new ObservableField<>();
    private PermissFragment permissFragment;
    public void initParams(PermissFragment permissFragments){
        this.permissFragment=permissFragments;
    }
    public void onClick(View view){
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", AppUtils.getApp().getPackageName(), null);
        intent.setData(uri);
        permissFragment.startActivityForResult(intent, REQUEST_PERMISSION_SETTING);
        permissFragment.finish();
    }
}
