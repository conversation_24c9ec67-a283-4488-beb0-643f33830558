package com.zizhiguanjia.model_common.viewmodel;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.http.SslError;
import android.util.Log;
import android.view.Gravity;
import android.webkit.SslErrorHandler;
import android.webkit.WebView;
import android.widget.LinearLayout;

import androidx.core.content.ContextCompat;
import androidx.databinding.ObservableField;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_network.BaseViewModel;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.ClipboardUtils;
import com.wb.lib_utils.utils.DataUtils;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.UmengHelper;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.api.CommonService;
import com.zizhiguanjia.model_common.fragment.CommonWebviewFragment;
import com.zizhiguanjia.model_common.jslitener.CommonJavascriptInterface;
import com.zizhiguanjia.model_common.listener.CommonWebListener;
import com.zizhiguanjia.model_common.listener.ICommonWebJsLive;
import com.zizhiguanjia.model_common.navigator.CommonWebViewNavigator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.jingbin.web.ByWebView;
import me.jingbin.web.OnByWebClientCallback;
import me.jingbin.web.OnTitleProgressCallback;

public class CommonWebViewModel extends CommonViewModel {
    private CommonWebviewFragment commonWebviewFragment;
    private CommonWebViewNavigator navigator;
    public ObservableField<String> titleObs=new ObservableField<>();
    private CommonJavascriptInterface commonJavascriptInterface;
    private CommonService mApi=new Http().create(CommonService.class);
    public void initParams(CommonWebviewFragment commonWebviewFragments,CommonWebViewNavigator navigators){
        this.commonWebviewFragment=commonWebviewFragments;
        this.navigator=navigators;
    }
    public void initWebView(int payType, CommonWebListener commonWebListener,String routh,String payRouthParams){
        commonJavascriptInterface=new CommonJavascriptInterface(commonWebviewFragment,payType,commonWebListener,routh,payRouthParams);
       ByWebView.Builder byWebView =ByWebView
                .with(commonWebviewFragment.getActivity())
                .useWebProgress(ContextCompat.getColor(commonWebviewFragment.getContext(), R.color.orchid))
                .setOnTitleProgressCallback(onTitleProgressCallback)
                .setOnByWebClientCallback(new OnByWebClientCallback() {
                    @Override
                    public void onPageStarted(WebView view, String url, Bitmap favicon) {
                        super.onPageStarted(view, url, favicon);
                    }

                    @Override
                    public void onPageFinished(WebView view, String url) {
                        super.onPageFinished(view, url);
                    }

                    @Override
                    public boolean isOpenThirdApp(String url) {
                        return super.isOpenThirdApp(url);
                    }

                    @Override
                    public boolean onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                        return super.onReceivedSslError(view, handler, error);
                    }
                })
                .addJavascriptInterface("injectedObject", commonJavascriptInterface);
        navigator.initByWebView(byWebView);
    }
    public void updataInfo(String json){
        navigator.reshJsCall(json);

    }
    private OnTitleProgressCallback onTitleProgressCallback = new OnTitleProgressCallback() {
        @Override
        public void onReceivedTitle(String title) {
            navigator.onReceivedTitle(title);
        }
    };
    private LoadingPopupView loadingPopupView;
    public void liveActive(String h5Url, ICommonWebJsLive iCommonWebJsLive, Context mContext){
        if(loadingPopupView==null){
            loadingPopupView=new PopupManager.Builder(mContext).asLoading("请稍等....");
        }
        loadingPopupView.show();
        Map<String,String> params=new HashMap<>();
        params.put("liveIds",h5Url);
        launchOnlyResult(mApi.postLive(params), new BaseViewModel.OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                ToastUtils.normal(data.getMsg(), Gravity.CENTER);
                loadingPopupView.dismiss();
                iCommonWebJsLive.livePreSuccess();
            }

            @Override
            public void error(String msg) {
                ToastUtils.normal(msg, Gravity.CENTER);
                loadingPopupView.dismiss();
            }
        });
    }

}
