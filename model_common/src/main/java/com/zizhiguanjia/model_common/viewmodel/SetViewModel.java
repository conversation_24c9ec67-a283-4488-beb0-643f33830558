package com.zizhiguanjia.model_common.viewmodel;

import android.view.View;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_image_loadcal.GlideApp;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.task.RxAsyncTask;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.FileUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.model_common.R;
import com.zizhiguanjia.model_common.manager.SetManager;
import com.zizhiguanjia.model_common.model.SetListChildBean;
import com.zizhiguanjia.model_common.model.SetListGroupBean;
import com.zizhiguanjia.model_common.navigator.SetNavigator;

import java.io.File;
import java.util.List;

import static com.wb.lib_utils.utils.CleanDataUtils.getFolderSize;
import static com.wb.lib_utils.utils.CleanDataUtils.getFormatSize;

public class SetViewModel extends CommonViewModel {
    private SetNavigator setNavigator;

    public void setSetNavigator(SetNavigator setNavigator) {
        this.setNavigator = setNavigator;
    }
    public void onclick(View mView){
        if(mView.getId()== R.id.tvBottomDes){
            setNavigator.userOnclick();
        }else if(mView.getId()== R.id.tvBottomBack){
            setNavigator.openBackInfo();
        }
    }
    public void initSetConfig(){
        RxJavaUtils.executeAsyncTask(new RxAsyncTask<String, List<SetListGroupBean>>("setlistconfig.json") {
            @Override
            public void doInUIThread(List<SetListGroupBean> setListGroupBeans) {
                if(setNavigator!=null){
                    if(setListGroupBeans==null||setListGroupBeans.size()==0)return;
                    setNavigator.getInitSetConfig(setListGroupBeans);
                }
            }
            @Override
            public List<SetListGroupBean> doInIOThread(String s) {
                try {
                    String json=FileUtils.getFromAssets(AppUtils.getApp(),s);
                    List<SetListGroupBean> setListGroupBeans= GsonUtils.jsonToList(json,SetListGroupBean.class);
                    for(SetListGroupBean setListGroupBean:setListGroupBeans){
                        for(SetListChildBean setListChildBean:setListGroupBean.getSetListBeans()){
                            if(setListChildBean.getId().equals("2001")){
                                setListChildBean.setDes("安全员考试宝典"+"("+DeviceUtils.getAppVersionName(AppUtils.getApp())+")");
                            }else if(setListChildBean.getId().equals("2002")){
                                setListChildBean.setDes(getCacheSize());
                            }
                        }
                    }
                    return setListGroupBeans;
                }catch (Exception e){
                    return null;
                }
            }
        });
    }
    public String getCacheSize() {
        try {
            File file= GlideApp.getPhotoCacheDir(AppUtils.getApp());
            return getFormatSize(getFolderSize(file));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "0kb";
    }
    public void afterSetToPageById(String id){
        if(StringUtils.isEmpty(id))return;
        SetManager.getInstance().getSetPageToById(id,setNavigator);
    }
    public void clearImageCache(){
        RxJavaUtils.executeAsyncTask(new RxAsyncTask<Boolean, Boolean>(true) {
            @Override
            public void doInUIThread(Boolean o) {
                GlideApp.get(AppUtils.getApp()).clearMemory();
                initSetConfig();
            }

            @Override
            public Boolean doInIOThread(Boolean o) {
                GlideApp.get(AppUtils.getApp()).clearDiskCache();
                return true;
            }
        });
    }
}
