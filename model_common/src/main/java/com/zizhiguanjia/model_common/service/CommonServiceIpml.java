package com.zizhiguanjia.model_common.service;

import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.CommonRouterPath;
import com.zizhiguanjia.lib_base.service.CommonService;
import com.zizhiguanjia.model_common.manager.CommonManager;

@Route(path = CommonRouterPath.SERVICE)
public class CommonServiceIpml implements CommonService {
    @Override
    public void start(Context context) {
        ARouterUtils.navActivity(CommonRouterPath.COMMON_WEB_ACTIVITY);
    }

    @Override
    public IFragment mainPage(Context context) {
        return null;
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {

    }

    @Override
    public IFragment showCommonWebView() {
        return ARouterUtils.navFragment(CommonRouterPath.COMMON_WEB);
    }
    @Override
    public IFragment showCommonWebViewHtmlTextLoad() {
        return ARouterUtils.navFragment(CommonRouterPath.COMMON_WEB_HTML_TEXT_LOAD);
    }

    @Override
    public IFragment showSetFragmentView() {
        return ARouterUtils.navFragment(CommonRouterPath.COMMON_SET);
    }

    @Override
    public IFragment showFaceBackFragmentView() {
        return ARouterUtils.navFragment(CommonRouterPath.COMMON_FACEBACK);
    }

    @Override
    public IFragment showPermissView() {
        return ARouterUtils.navFragment(CommonRouterPath.COMMON_PERMISS);
    }

    @Override
    public void upDataApp(Context mContext) {
        CommonManager.getInstance().initUpDateApp();
        CommonManager.getInstance().upDate(true,mContext);
    }

    @Override
    public void showCommonWebView(String routh, int payType, String url) {
        ARouterUtils.navActivity(CommonRouterPath.COMMON_PAY_ACTIVITY);
    }
}
