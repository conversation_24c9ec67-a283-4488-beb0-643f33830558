package com.zizhiguanjia.model_common.imageLoader;

import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.caimuhao.rxpicker.utils.RxPickerImageLoader;
import com.wb.lib_image_loadcal.ImageManager;

public class GlideSelectImageLoader implements RxPickerImageLoader {

  @Override public void display(ImageView imageView, String path, int width, int height) {
    ImageManager.getInstance().displayImage(path,imageView);
  }
}