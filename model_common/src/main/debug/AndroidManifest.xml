<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.zizhiguanjia.model_common">

    <uses-permission android:name="android.permission.INTERNET" />
    <application
        android:name="com.zizhiguanjia.lib_base.DebugApplication"
        android:allowBackup="true"
        android:label="${APP_NAME}"
        android:supportsRtl="true"
        android:theme="@style/BaseAppTheme">
        <activity
            android:screenOrientation="portrait"
            android:name=".ui.SetActivity"
            >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>