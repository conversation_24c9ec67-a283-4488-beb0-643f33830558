<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data></data>
    <LinearLayout
        android:orientation="vertical"
        android:background="#ffffff"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.wb.lib_weiget.titlebar.TitleBar xmlns:app="http://schemas.android.com/apk/res-auto"
            app:tb_statusBarColor="#3163F6"
            android:id="@+id/tbService"
            app:tb_titleBarColor="#3163F6"
            app:tb_centerTextColor="@color/white"
            app:tb_centerTextSize="18sp"
            app:tb_centerType="textView"
            app:tb_centerText="客服"
            app:tb_leftType="imageButton"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <WebView
            android:id="@+id/wbService"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>
</layout>