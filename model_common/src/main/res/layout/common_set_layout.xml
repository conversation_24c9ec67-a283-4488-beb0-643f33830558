<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_common.viewmodel.SetViewModel" />
    </data>
   <LinearLayout xmlns:app="http://schemas.android.com/apk/res-auto"
       android:orientation="vertical"
       android:layout_width="match_parent"
       android:layout_height="match_parent">
       <com.wb.lib_weiget.titlebar.TitleBar
           android:id="@+id/tbTopTitle"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           app:tb_leftType="imageButton"
           app:tb_leftImageResource="@drawable/common_left_back_write_img"
           app:tb_centerType="textView"
           app:tb_centerText="设置"
           app:tb_centerTextSize="18.5sp"
           app:tb_centerTextColor="@color/white"
           app:tb_fillStatusBar="true"
           app:tb_titleBarColor="#3163F6"
           app:tb_statusBarColor="#3163F6"
           />
       <RelativeLayout
           android:background="#F4F7F9"
           xmlns:app="http://schemas.android.com/apk/res-auto"
           android:layout_width="match_parent"
           android:orientation="vertical"
           android:layout_height="match_parent">
           <androidx.recyclerview.widget.RecyclerView
               android:background="@color/white"
               android:id="@+id/common_set_rcy"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"/>
           <TextView
               android:onClick="@{model::onclick}"
               android:id="@+id/tvBottomDes"
               android:layout_marginBottom="2dp"
               android:textSize="11sp"
               android:textColor="#999999"
               android:gravity="center"
               android:layout_centerHorizontal="true"
               android:layout_above="@+id/tvBottomBack"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"/>
           <TextView
               android:onClick="@{model::onclick}"
               android:id="@+id/tvBottomBack"
               android:layout_marginBottom="26dp"
               android:textSize="11sp"
               android:textColor="#999999"
               android:gravity="center"
               android:text="京ICP备19053962号-3A"
               android:layout_centerHorizontal="true"
               android:layout_alignParentBottom="true"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"/>
       </RelativeLayout>
   </LinearLayout>
</layout>