<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data></data>
    <LinearLayout
        android:background="@drawable/common_service_tel_bg"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_marginTop="13dp"
            android:gravity="center"
            android:text="电话客服工作时间"
            android:textSize="13sp"
            android:textColor="#838384"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <TextView
            android:layout_marginTop="1dp"
            android:gravity="center"
            android:text="8:00-12:00，13:00-18:00"
            android:textSize="13sp"
            android:textColor="#333333"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <View
            android:layout_marginTop="11dp"
            android:background="#EAEBEC"
            android:layout_width="match_parent"
            android:layout_height="0.25dp"/>
        <TextView
            android:id="@+id/tvCommonCallTel"
            android:layout_marginTop="25dp"
            android:gravity="center"
            android:text="拨打：133-9150-9550"
            android:textSize="16sp"
            android:textColor="#0079FF"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <View
            android:layout_marginTop="14dp"
            android:layout_marginBottom="14dp"
            android:background="#F6F7F8"
            android:layout_width="match_parent"
            android:layout_height="10dp"/>
        <TextView
            android:id="@+id/tvCommonCancel"
            android:layout_marginBottom="17.5dp"
            android:gravity="center"
            android:text="取消"
            android:textSize="15.5sp"
            android:textColor="#000000"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</layout>