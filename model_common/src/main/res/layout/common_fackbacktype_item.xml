<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="data"
            type="com.zizhiguanjia.model_common.model.FaceBackTypeBean" />
        <variable
            name="model"
            type="com.zizhiguanjia.model_common.adapter.FaceBackTypeAdapter" />
        <variable
            name="select"
            type="java.lang.Boolean" />
    </data>
    <LinearLayout
        android:id="@+id/common_backface_type_main_ll"
        android:onClick="@{(view)->model.onClick(view,data.id)}"
        android:background="@drawable/common_faceback_type_bg"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="71dp">
        <ImageView
            android:id="@+id/common_backface_type_img"
            android:layout_width="24dp"
            android:layout_height="24dp"/>
        <TextView
            android:id="@+id/common_faceback_type_titile_tv"
            android:text="@{data.title}"
            android:textSize="13sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</layout>