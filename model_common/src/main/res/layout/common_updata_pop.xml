<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:gravity="center"
    android:minHeight="330dp"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="118dp"
            android:scaleType="fitXY"
            android:src="@drawable/common_updateapp_img" />

       <LinearLayout
           android:layout_marginLeft="14.5dp"
           android:layout_marginTop="36dp"
           android:id="@+id/imgClose"
           android:layout_width="30dp"
           android:layout_height="30dp">
           <ImageView
               android:layout_width="15dp"
               android:layout_height="15dp"
               android:src="@drawable/common_icon_close" />
       </LinearLayout>
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/common_updatat_app_bg"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:text="是否升级到1.0.7版本？"
            android:textColor="#FF000000"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tvDes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="9dp"
            android:layout_marginRight="20dp"
            android:text="是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？是否升级到1.0.7版本？"
            android:textColor="#FF7E7E7E"
            android:textSize="14sp" />

        <com.xuexiang.xupdate.widget.NumberProgressBar xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/nbpbProgress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="24dp"
            android:layout_marginRight="25dp"
            app:xnpb_max="100"
            app:xnpb_reached_bar_height="5.5dp"
            app:xnpb_text_color="#007AFF"
            app:xnpb_text_size="14sp"
            app:xnpb_unreached_bar_height="3dp" />

        <TextView
            android:id="@+id/tvUpdataApp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="55dp"
            android:layout_marginRight="25dp"
            android:layout_marginBottom="25dp"
            android:background="@drawable/common_updata_app_bg"
            android:gravity="center"
            android:paddingTop="8.5dp"
            android:paddingBottom="8dp"
            android:text="升级"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tvUpdataDesApp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="25dp"
            android:layout_marginTop="55dp"
            android:layout_marginRight="25dp"
            android:layout_marginBottom="25dp"
            android:text="正在下载中，请稍后…"
            android:textColor="#007AFF"
            android:textSize="14sp"
            android:visibility="gone" />
    </LinearLayout>
</LinearLayout>