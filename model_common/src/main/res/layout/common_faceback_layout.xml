<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="com.wb.lib_utils.utils.StringUtils" />

        <variable
            name="model"
            type="com.zizhiguanjia.model_common.viewmodel.FaceBackViewmode" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.wb.lib_weiget.titlebar.TitleBar xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/ttbTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tb_centerText="意见反馈"
            app:tb_centerTextColor="@color/white"
            app:tb_centerTextSize="18.5sp"
            app:tb_centerType="textView"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            app:tb_leftType="imageButton"
            app:tb_statusBarColor="#3163F6"
            app:tb_titleBarColor="#3163F6" />

        <com.zizhiguanjia.model_common.view.NonFocusingScrollView
            android:id="@+id/sv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/view_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingTop="14dp"
                android:paddingRight="12dp"
                android:paddingBottom="27.5dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/desTv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="12dp"
                        android:layout_marginBottom="10.5dp"
                        android:text="(必选) 请选择您反馈的问题类型"
                        android:textColor="#333333"
                        android:textSize="16sp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/common_backface_type_rel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="28.5dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="12dp"
                        android:layout_marginBottom="10.5dp"
                        android:text="请描述问题"
                        android:textColor="#333333"
                        android:textSize="16sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="12dp"
                        android:background="@drawable/common_faceback_type_no_bg"
                        android:orientation="vertical"
                        android:paddingTop="13.5dp"
                        android:paddingRight="20dp"
                        android:paddingBottom="7.5dp">

                        <EditText
                            android:id="@+id/common_faceback_msg_ed"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="19dp"
                            android:background="@null"
                            android:gravity="top|left"
                            android:hint="请描述您遇到的具体问题（不少于10个字）"
                            android:minHeight="80dp"
                            android:textColor="#333333"
                            android:textColorHint="#CDCDCF"
                            android:textCursorDrawable="@drawable/common_cursor"
                            android:textSize="14sp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:gravity="right|center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{StringUtils.isEmpty(model.faceBackMsg)?String.valueOf(0):String.valueOf(model.faceBackMsg.length())}"
                                android:textColor="#476BFF"
                                android:textSize="14sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="/200"
                                android:textColor="#CDCDCF"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/gride_view"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10dp" />

                        <TextView
                            android:id="@+id/imageUploadTv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="5.5dp"
                            android:text="上传截图"
                            android:textColor="#6C7596"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="28dp"
                    android:background="@{model.accountLoginVer?@drawable/common_login_yes_bg:@drawable/common_login_nor_bg}"
                    android:gravity="center"
                    android:onClick="@{model::onclick}"
                    android:paddingTop="12.5dp"
                    android:paddingBottom="10dp"
                    android:text="提交"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>
        </com.zizhiguanjia.model_common.view.NonFocusingScrollView>
    </LinearLayout>
</layout>