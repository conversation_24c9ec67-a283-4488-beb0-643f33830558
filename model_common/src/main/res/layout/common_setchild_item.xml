<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.zizhiguanjia.model_common.model.SetListChildBean" />

        <variable
            name="postion"
            type="java.lang.Integer" />

        <variable
            name="maxsize"
            type="java.lang.Integer" />

        <variable
            name="model"
            type="com.zizhiguanjia.model_common.adapter.SetChildListAdapter" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:onClick="@{(view)->model.onClick(view,data.id)}"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:layout_marginBottom="13dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="12dp"
                android:text="@{data.title}"
                android:textColor="#333333"
                android:textSize="15sp" />

            <LinearLayout
                android:id="@+id/tvDes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17.5dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{data.des}"
                    android:textColor="#999999"
                    android:textSize="12sp" />
                <ImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/common_right_img" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/tvMessDes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17.5dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <vn.luongvo.widget.iosswitchview.SwitchView
                    android:id="@+id/switchview"
                    android:layout_width="50dp"
                    android:layout_height="wrap_content"
                    app:checked="true"
                    app:color_off="#eee"
                    app:color_on="#3163F6" />
            </LinearLayout>
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:background="#EDEEEF"
            android:visibility="@{postion==maxsize?View.GONE:View.VISIBLE}" />
    </LinearLayout>
</layout>