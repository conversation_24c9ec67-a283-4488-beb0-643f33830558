<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data></data>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/ll_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.wb.lib_weiget.titlebar.TitleBar
            app:tb_showBottomLine="false"
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/commonweb_title_tb"
            app:tb_centerType="textView"
            app:tb_centerTextMarquee="false"
            app:tb_centerText="安全员考试宝典"
            app:tb_centerTextSize="18sp"
            app:tb_centerTextColor="@color/white"
            app:tb_leftType="imageButton"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tb_statusBarColor="#00000000"
            app:tb_titleBarColor="#00000000"
            />
    </LinearLayout>
</layout>