<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_common.adapter.ImageLookAdapter" />
        <variable
            name="ids"
            type="java.lang.Integer" />
    </data>
    <RelativeLayout
        android:layout_width="78dp"
        android:layout_height="78dp">
        <ImageView
            android:layout_centerInParent="true"
            android:scaleType="fitXY"
            android:onClick="@{(view)->model.onClick(view,ids)}"
            android:id="@+id/common_lookimage_img"
            android:src="@drawable/add_img"
            android:layout_width="68dp"
            android:layout_height="68dp"/>
        <ImageView
            android:onClick="@{(view)->model.delectImage(view,ids)}"
            android:id="@+id/delectImage"
            android:layout_alignParentRight="true"
            android:src="@drawable/common_delect_img"
            android:layout_width="16dp"
            android:layout_height="16dp"/>
    </RelativeLayout>
</layout>