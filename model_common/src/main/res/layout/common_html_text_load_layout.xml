<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <data></data>

    <LinearLayout
        android:id="@+id/ll_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.wb.lib_weiget.titlebar.TitleBar xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/commonweb_title_tb"
            app:tb_leftType="imageButton"
            app:tb_centerText="本节习题"
            app:tb_centerTextColor="@color/white"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            app:tb_fillStatusBar="true"
            app:tb_statusBarColor="#3163F6"
            app:tb_titleBarColor="#3163F6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"/>
    </LinearLayout>
</layout>