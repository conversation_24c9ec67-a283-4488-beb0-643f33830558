<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_common.viewmodel.PermissViewModel" />
    </data>
    <LinearLayout
        android:background="@color/white"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">
        <com.wb.lib_weiget.titlebar.TitleBar xmlns:app="http://schemas.android.com/apk/res-auto"
            app:tb_showBottomLine="false"
            app:tb_titleBarColor="@color/white"
            app:tb_statusBarColor="@color/white"
            app:tb_centerText="申请权限"
            app:tb_centerTextColor="#000000"
            app:tb_centerTextSize="18sp"
            android:id="@+id/titleTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_marginTop="50dp"
                    android:textColor="#9899AC"
                    android:textSize="15sp"
                    android:text="安全员考试宝典没有权限使用您的相册权限"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:layout_marginLeft="27dp"
                    android:layout_marginTop="6.5dp"
                    android:textSize="22.5sp"
                    android:textColor="#0E152B"
                    android:text="您可以通过如下操作开启。"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
                    <LinearLayout
                        android:orientation="vertical"
                        android:gravity="left"
                        android:layout_marginLeft="56dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:layout_marginTop="62dp"
                            android:text="1、打开软件应用信息"
                            android:textColor="#333333"
                            android:textSize="15sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:layout_marginTop="22dp"
                            android:text="2、点击“权限管理”"
                            android:textColor="#333333"
                            android:textSize="15sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:layout_marginTop="22dp"
                            android:text="3、选择“手机读写存储”"
                            android:textColor="#333333"
                            android:textSize="15sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:layout_marginTop="22dp"
                            android:text="4、设置“安全员考试宝典”允许"
                            android:textColor="#333333"
                            android:textSize="15sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </LinearLayout>
                    <TextView
                        android:onClick="@{model::onClick}"
                        android:layout_marginTop="130dp"
                        android:textSize="16sp"
                        android:textColor="@color/white"
                        android:paddingTop="10dp"
                        android:paddingBottom="9dp"
                        android:paddingRight="53dp"
                        android:paddingLeft="53dp"
                        android:text="设置"
                        android:background="@drawable/common_face_postimg_bg"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</layout>