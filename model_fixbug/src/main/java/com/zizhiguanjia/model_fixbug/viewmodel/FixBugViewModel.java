package com.zizhiguanjia.model_fixbug.viewmodel;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.tb.TableCertificate;
import com.zizhiguanjia.lib_base.tb.TableUserInfo;
import com.zizhiguanjia.model_fixbug.listener.IFixBug;
import com.zizhiguanjia.model_fixbug.navigator.FixBugNavigator;

public class FixBugViewModel extends CommonViewModel implements IFixBug {
    private FixBugNavigator fixBugNavigator;
    public void initParams(FixBugNavigator fixBugNavigator){
        this.fixBugNavigator=fixBugNavigator;
        initData();
    }
    @Override
    public void initData() {
        String account= AccountHelper.getCurrentLoginAccount();
        TableUserInfo tableUserInfo=UserHelper.getUserInfo();
        TableCertificate tableCertificate= CertificateHelper.getUserCertificate(account);
        fixBugNavigator.showUserInfo(tableUserInfo);
        fixBugNavigator.showCurrentAccount(account);
        fixBugNavigator.showCurrentCertificate(tableCertificate);
    }
}
