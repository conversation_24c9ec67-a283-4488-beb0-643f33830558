package com.zizhiguanjia.model_utils.service;

import android.app.Activity;
import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.ListRouterPath;
import com.zizhiguanjia.lib_base.constants.UtilsRouthPath;
import com.zizhiguanjia.lib_base.service.UtilsService;
import com.zizhiguanjia.model_utils.manager.MarketScoreManager;

@Route(path = UtilsRouthPath.SERVICE)
public class UtilsServiceImpl implements UtilsService {
    @Override
    public void start(Context context) {

    }

    @Override
    public IFragment mainPage(Context context) {
        return null;
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {

    }

    @Override
    public void openMarkets(Activity activity) {
        MarketScoreManager.getInstance().openMarkets(activity);
    }
}
