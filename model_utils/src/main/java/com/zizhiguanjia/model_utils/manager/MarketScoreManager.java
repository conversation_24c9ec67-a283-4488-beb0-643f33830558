package com.zizhiguanjia.model_utils.manager;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.text.TextUtils;
import android.view.Gravity;

import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.emums.MetaDataKeyEnum;
import com.zizhiguanjia.lib_base.utils.AppUtils;

import java.util.ArrayList;
import java.util.List;

import static com.xuexiang.xupdate.utils.UpdateUtils.startActivity;

public class MarketScoreManager {
    private static MarketScoreManager marketScoreManager;
    public static MarketScoreManager getInstance(){
        if(marketScoreManager==null){
            synchronized (MarketScoreManager.class){
                return marketScoreManager=new MarketScoreManager();
            }
        }
        return marketScoreManager;
    }
    public void openMarkets(Context context){
        if(checkChannel(context)){
            ArrayList<String> markets=getInstallAppMarkets(context);
            LogUtils.e("排查1---->>>"+markets.toString());
            if(markets==null||markets.size()==0){
                ToastUtils.normal("没有有效应用商店~", Gravity.CENTER);
                return;
            }
            launchAppDetail(markets.get(0));
        }
    }
    private boolean checkChannel(Context context){
        String appChanner=AppUtils.getInstance().getAppMetaData(MetaDataKeyEnum.APP_SEND_PLARFORM_CODE);
        if(appChanner.equals("1")){
            return true;
        }else if(appChanner.equals("2")){
            if(checkQQInstalled(context,"com.huawei.appmarket")){
                launchAppDetail("com.huawei.appmarket");
                return false;
            }
           return true;
        }else if(appChanner.equals("4")){
            if(checkQQInstalled(context,"com.tencent.android.qqdownloader")){
                launchAppDetail("com.tencent.android.qqdownloader");
                return false;
            }
            return true;
        }else if(appChanner.equals("5")){
            if(checkQQInstalled(context,"com.xiaomi.market")){
                launchAppDetail("com.xiaomi.market");
                return false;
            }
            return true;
        }else if(appChanner.equals("6")){
            if(checkQQInstalled(context,"com.baidu.appsearch")){
                launchAppDetail("com.baidu.appsearch");
                return false;
            }
            return true;
        }else if(appChanner.equals("7")){
            if(checkQQInstalled(context,"com.qihoo.appstore")){
                launchAppDetail("com.qihoo.appstore");
                return false;
            }
            return true;
        }else if(appChanner.equals("8")){
            if(checkQQInstalled(context,"com.oppo.market")){
                launchAppDetail("com.oppo.market");
                return false;
            }
            return true;
        }else if(appChanner.equals("9")){
            if(checkQQInstalled(context,"com.vivo.market")){
                launchAppDetail("com.vivo.market");
                return false;
            }
            return true;
        }
        return true;
    }
    private  boolean checkQQInstalled(Context context, String pkgName) {
        if (TextUtils.isEmpty(pkgName)) {
            return false;
        }
        try {
            context.getPackageManager().getPackageInfo(pkgName, 0);
        } catch (Exception x) {
            return false;
        }
        return true;
    }
    /**
     * 获取手机上的安装包
     * @param context
     * @return
     */
    public ArrayList<String> getInstallAppMarkets(Context context) {
        //默认的应用市场列表，有些应用市场没有设置APP_MARKET通过隐式搜索不到
        ArrayList<String> pkgList = new ArrayList<>();
        pkgList.add("com.tencent.android.qqdownloader");
        pkgList.add("com.qihoo.appstore");
        pkgList.add("com.baidu.appsearch");
        pkgList.add("com.xiaomi.market");
        pkgList.add("com.huawei.appmarket");
        pkgList.add("com.oppo.market");
        pkgList.add("com.vivo.market");
        ArrayList<String> pkgs = new ArrayList<String>();
        if (context == null)
            return pkgs;
//        Intent intent = new Intent();
//        intent.setAction("android.intent.action.MAIN");
//        intent.addCategory("android.intent.category.APP_MARKET");
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        intent.addCategory(Intent.CATEGORY_DEFAULT);
        intent.setData(Uri.parse("market://details?id="));
        PackageManager pm = context.getPackageManager();
        List<ResolveInfo> infos = pm.queryIntentActivities(intent, 0);
        if (infos == null || infos.size() == 0)
            return pkgs;
        int size = infos.size();
        for (int i = 0; i < size; i++) {
            String pkgName = "";
            try {
                ActivityInfo activityInfo = infos.get(i).activityInfo;
                pkgName = activityInfo.packageName;


            } catch (Exception e) {
                e.printStackTrace();
            }
            if (!TextUtils.isEmpty(pkgName))
                pkgs.add(pkgName);

        }
        //取两个list并集,去除重复
        pkgList.removeAll(pkgs);
        pkgs.addAll(pkgList);
        return pkgs;
    }

    /**
     * 过滤出已经安装的包名集合
     * @param context
     * @param pkgs 待过滤包名集合
     * @return 已安装的包名集合
     */
    public ArrayList<String> getFilterInstallMarkets(Context context, ArrayList<String> pkgs) {
        ArrayList<String> appList = new ArrayList<String>();
        if (context == null || pkgs == null || pkgs.size() == 0)
            return appList;
        PackageManager pm = context.getPackageManager();
        List<PackageInfo> installedPkgs = pm.getInstalledPackages(0);
        int li = installedPkgs.size();
        int lj = pkgs.size();
        for (int j = 0; j < lj; j++) {
            for (int i = 0; i < li; i++) {
                String installPkg = "";
                String checkPkg = pkgs.get(j);
                PackageInfo packageInfo = installedPkgs.get(i);
                try {
                    installPkg = packageInfo.packageName;

                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (TextUtils.isEmpty(installPkg))
                    continue;
                if (installPkg.equals(checkPkg)) {
                    // 如果非系统应用，则添加至appList,这个会过滤掉系统的应用商店，如果不需要过滤就不用这个判断
                    if ((packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
                        //将应用相关信息缓存起来，用于自定义弹出应用列表信息相关用
                        appList.add(installPkg);
                    }
                    break;
                }

            }
        }
        return appList;
    }
    public void launchAppDetail(String marketPkg) {
        String appPkg="com.zizhiguanjia.flutter_exam";
        try {
            if (TextUtils.isEmpty(appPkg))
                return;
            String uriDes;
//            if(marketPkg.equals("com.xiaomi.market")){
//                uriDes="http://app.xiaomi.com/detail/1407683";
//            }else {
                uriDes="market://details?id="+appPkg;
//            }
            LogUtils.e("应用商店--sss--->>>>"+uriDes);
            Uri uri = Uri.parse(uriDes);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            if (!TextUtils.isEmpty(marketPkg))
                intent.setPackage(marketPkg);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
