package com.zizhiguanjia.model_pay.adapter;

import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.utils.DataUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_pay.R;
import com.zizhiguanjia.model_pay.databinding.PayPackitemLayoutBinding;
import com.zizhiguanjia.model_pay.listener.PayPackListener;
import com.zizhiguanjia.model_pay.model.GoodsInfoBean;

public class PayPackAdapter extends BaseAdapter<GoodsInfoBean.DetailsBean> {
    private PayPackitemLayoutBinding payPackitemLayoutBinding;
    private int goodsId=-1;
    private PayPackListener payPackListener;
    public PayPackAdapter(PayPackListener payPackListener) {
        super(R.layout.pay_packitem_layout);
        this.payPackListener=payPackListener;
    }

    @Override
    protected void bind(BaseViewHolder holder, GoodsInfoBean.DetailsBean item, int position) {
        payPackitemLayoutBinding=holder.getBinding();
        if(goodsId==-1){
            payPackitemLayoutBinding.relPayMain.setSelected(item.isSelect());
        }else {
            payPackitemLayoutBinding.relPayMain.setSelected(goodsId==item.getGoodsId()?true:false);
        }
        payPackitemLayoutBinding.setBean(item);
        payPackitemLayoutBinding.tvPayItemPrice.setText(DataUtils.format2Decimals(String.valueOf( DataUtils.stringToDouble(item.getPrice())/100)));
        payPackitemLayoutBinding.setModel(this);
    }
    public void onclikGoods(View view,int goolds,String price){
        this.goodsId=goolds;
        payPackListener.userSelectPack(goodsId+"",price);
        notifyDataSetChanged();
    }
}
