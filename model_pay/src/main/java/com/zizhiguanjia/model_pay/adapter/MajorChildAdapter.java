package com.zizhiguanjia.model_pay.adapter;

import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_pay.R;
import com.zizhiguanjia.model_pay.config.MsgTypeConfig;
import com.zizhiguanjia.model_pay.databinding.PayMajorsChildItemBinding;
import com.zizhiguanjia.model_pay.model.MainCertificateBean;
import com.zizhiguanjia.model_pay.model.MajorChildBean;

public class MajorChildAdapter extends BaseAdapter<MajorChildBean> {
    private PayMajorsChildItemBinding binding;
    private int type;
    private int majorId;
    public void setMajorId(int majorId) {
        this.majorId = majorId;
    }
    public void setType(int type) {
        this.type = type;
    }

    public MajorChildAdapter() {
        super(R.layout.pay_majors_child_item);
    }

    @Override
    protected void bind(BaseViewHolder holder, MajorChildBean item, int position) {
        binding=holder.getBinding();
        binding.setData(item);
        binding.setType(type);
        binding.setModel(this);
        binding.mainMajorItemLl.setBackgroundResource(majorId==item.getMagId()?R.drawable.pay_major_child_yes_bg :R.drawable.pay_major_child_no_bg);
    }
    public void onClick(View view,MajorChildBean majorChildBean){
        MainCertificateBean mainCertificateBean=new MainCertificateBean();
        mainCertificateBean.setClassFlyId(type);
        mainCertificateBean.setSubjuectId(majorChildBean.getMagId());
        mainCertificateBean.setName(majorChildBean.getName());
        mainCertificateBean.setDes(StringUtils.isEmpty(majorChildBean.getSubTxt())?"":"("+majorChildBean.getSubTxt()+")");
        Bus.post(new MsgEvent(MsgTypeConfig.MSG_TYPE_CLOSE_MAJOR,mainCertificateBean));
    }
}
