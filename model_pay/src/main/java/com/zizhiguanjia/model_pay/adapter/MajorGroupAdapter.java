package com.zizhiguanjia.model_pay.adapter;

import androidx.recyclerview.widget.GridLayoutManager;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.model_pay.R;
import com.zizhiguanjia.model_pay.config.ClassFLyTypeConfig;
import com.zizhiguanjia.model_pay.config.SubjectClassTypeConfig;
import com.zizhiguanjia.model_pay.databinding.PayGroupLayoutBinding;
import com.zizhiguanjia.model_pay.model.MajorGroupBean;

public class MajorGroupAdapter extends BaseAdapter<MajorGroupBean> {
    private PayGroupLayoutBinding binding;
    private int majorId;

    public void setMajorId(int majorId) {
        this.majorId = majorId;
    }

    public MajorGroupAdapter() {
        super(R.layout.pay_group_layout);
    }

    @Override
    protected void bind(BaseViewHolder holder, MajorGroupBean item, int position) {
        binding=holder.getBinding();
        binding.setBean(item);
        binding.mainMajorChildRev.setLayoutManager(new GridLayoutManager(holder.itemView.getContext(),3));
        MajorChildAdapter childAdapter= (MajorChildAdapter) binding.mainMajorChildRev.getAdapter();
        if(childAdapter==null){
            binding.mainMajorChildRev.addItemDecoration(new GridSpaceItemDecoration(3, DpUtils.dp2px(holder.itemView.getContext(), 12), DpUtils.dp2px(holder.itemView.getContext(), 6)));
            childAdapter=new MajorChildAdapter();
            binding.mainMajorChildRev.setAdapter(childAdapter);
        }
        childAdapter.setDataItems(item.getChildBeans());
        childAdapter.setType(item.getClassFlyType());
        childAdapter.setMajorId(majorId);
        if(item.getClassFlyType()== ClassFLyTypeConfig.CLASS_FLY_RJ){
            binding.mainEjImg.setImageResource(item.getSubjrctType()== SubjectClassTypeConfig.SUBJECT_PUB?R.drawable.pay_er_ggkm :R.drawable.pay_er_zykm);
        }
    }
}
