package com.zizhiguanjia.model_pay.viewmodel;

import android.view.View;

import androidx.databinding.ObservableField;

import com.example.lib_common.base.CommonViewModel;
import com.wangbo.www.paylibs.wxpay.WxPayConfig;
import com.wangbo.www.paylibs.wxpay.WxPayManager;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DataUtils;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_pay.R;
import com.zizhiguanjia.model_pay.api.PayApi;
import com.zizhiguanjia.model_pay.fragment.PayFragment;
import com.zizhiguanjia.model_pay.listener.PayAfterMajorDataListener;
import com.zizhiguanjia.model_pay.manager.PayDataManager;
import com.zizhiguanjia.model_pay.model.GoodsInfoBean;
import com.zizhiguanjia.model_pay.model.MainCertificateBean;
import com.zizhiguanjia.model_pay.model.MajorBean;
import com.zizhiguanjia.model_pay.model.MajorGroupBean;
import com.zizhiguanjia.model_pay.model.PayParamsBean;
import com.zizhiguanjia.model_pay.navigator.PayNavigator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;

import static com.zizhiguanjia.lib_base.config.BaseConfig.WX_APP_ID;

public class PayViewModel extends CommonViewModel {
    private PayFragment payFragment;
    private PayNavigator navigator;
    private PayApi mApi = Http.getInstance().create(PayApi.class);
    public ObservableField<String> payAcccountObs = new ObservableField<>();
    public ObservableField<String> durationObs = new ObservableField<>();
    public ObservableField<String> timeOutObs = new ObservableField<>();
    public ObservableField<String> majorNameObs = new ObservableField<>();
    public ObservableField<String> tkPrice = new ObservableField<>();
    public ObservableField<String> tkPriceStr = new ObservableField<>();

    public ObservableField<Integer> subjectObs = new ObservableField<>();
    public ObservableField<Integer> classFlyObs = new ObservableField<>();
    private String goodsId;
    private List<List<MajorGroupBean>> majorGroupBeans;

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public void initParams(PayFragment payFragment, PayNavigator navigator) {
        this.navigator = navigator;
        this.payFragment = payFragment;
        majorGroupBeans = new ArrayList<>();
        navigator.showLoadingView();
//        initMajors();
        getLastUserSaveCertificate();
        getLastUserAccount();
    }

    private void getLastUserAccount() {
    }

    private void getLastUserSaveCertificate() {
    }

    public void getCurrentPackage(String majorId, boolean isVisitionDialog) {
        Map<String, String> params = new HashMap<>();
        params.put("majorId", majorId);

        if (isVisitionDialog) {
            navigator.showLoading(true);
        }
        launchOnlyResult(mApi.getCurrentPackage(params), new OnHandleException<BaseData<GoodsInfoBean>>() {
            @Override
            public void success(BaseData<GoodsInfoBean> data) {
                if (isVisitionDialog) {
                    navigator.showLoading(false);
                }
                navigator.goodInfo(data.Data.getGoods());
                setGoodsId(data.Data.getGoods().getGoodsId() + "");
                navigator.packList(data.Data.getDetails());
            }

            @Override
            public void error(String msg) {
                if (isVisitionDialog) {
                    navigator.showLoading(false);
                }
            }
        });
    }

    public void onclick(View view) {
        if (view.getId() == R.id.tvPayMain) {
            postPay();
        } else if (view.getId() == R.id.relPayRight) {
            navigator.showMajorsDiglog(majorGroupBeans);
        }
    }

    private void postPay() {
        if (StringUtils.isEmpty(goodsId)) {
            navigator.showToast("商品ID的错误!");
            return;
        }
        navigator.showLoading(true);
        Map<String, String> params = new HashMap<>();
        params.put("platform", "2");
        params.put("payType", "1");
        params.put("goodId", goodsId);
        launchOnlyResult(mApi.postPay(params), new OnHandleException<BaseData<PayParamsBean>>() {
            @Override
            public void success(BaseData<PayParamsBean> data) {
                navigator.showLoading(false);
                WX_APP_ID = data.Data.getTenPayAppParameters().getAppid();
                WxPayConfig wxPayConfig = new WxPayConfig.Builder().with(payFragment.getActivity())
                        .setAppId(WX_APP_ID)
                        .setNoncestr(data.Data.getTenPayAppParameters().getNoncestr())
                        .setPackagex(data.Data.getTenPayAppParameters().getPackageX())
                        .setPartnerid(data.Data.getTenPayAppParameters().getPartnerid())
                        .setSign(data.Data.getTenPayAppParameters().getSign())
                        .setPrepayid(data.Data.getTenPayAppParameters().getPrepayid())
                        .setTimestamp(data.Data.getTenPayAppParameters().getTimestamp())
                        .builder();
                navigator.WxChatPay(wxPayConfig);
            }

            @Override
            public void error(String msg) {

            }
        });
    }
//    public void initMajors() {
//        launchOnlyResult(mApi.getMajors(new HashMap<>()), new OnHandleException<BaseData<MajorBean>>() {
//            @Override
//            public void success(BaseData<MajorBean> data) {
//                PayDataManager.getInstance().afterMainMajorData(data.Data, new PayAfterMajorDataListener() {
//                    @Override
//                    public void afterMajorListData(List<List<MajorGroupBean>> majorGroups) {
//                        majorGroupBeans.clear();
//                        if(majorGroupBeans!=null)
//                        majorGroupBeans.addAll(majorGroups);
//                    }
//                });
//            }
//            @Override
//            public void error(String msg) {
//                ToastUtils.normal("获取证书列表失败，请重新刷新！");
//            }
//        });
//    }
}
