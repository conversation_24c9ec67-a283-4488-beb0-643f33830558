package com.zizhiguanjia.model_pay.manager;

import android.app.Activity;
import android.view.Gravity;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wangbo.www.paylibs.wxpay.WxPayConfig;
import com.wangbo.www.paylibs.wxpay.WxPayManager;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.task.RxAsyncTask;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.listeners.PayInfoListener;
import com.zizhiguanjia.model_pay.config.ClassFLyTypeConfig;
import com.zizhiguanjia.model_pay.config.SubjectClassTypeConfig;
import com.zizhiguanjia.model_pay.helper.PayHelper;
import com.zizhiguanjia.model_pay.listener.PayAfterMajorDataListener;
import com.zizhiguanjia.model_pay.model.GoodsInfoBean;
import com.zizhiguanjia.model_pay.model.MajorBean;
import com.zizhiguanjia.model_pay.model.MajorChildBean;
import com.zizhiguanjia.model_pay.model.MajorGroupBean;
import com.zizhiguanjia.model_pay.model.PayParamsBean;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

import static com.zizhiguanjia.lib_base.config.BaseConfig.WX_APP_ID;
import static com.zizhiguanjia.lib_base.config.BaseConfig.WX_WAIT_ORDER;

public class PayDataManager {
    private static PayDataManager mainDataManager;

    public static PayDataManager getInstance() {
        if (mainDataManager == null) {
            synchronized (PayDataManager.class) {
                return mainDataManager = new PayDataManager();
            }
        }
        return mainDataManager;
    }

    public void afterMainMajorData(MajorBean majorBean, PayAfterMajorDataListener mainAfterMajorDataListener) {
        RxJavaUtils.executeAsyncTask(new RxAsyncTask<MajorBean, List<List<MajorGroupBean>>>(majorBean) {
            @Override
            public void doInUIThread(List<List<MajorGroupBean>> majorGroupBeans) {

                mainAfterMajorDataListener.afterMajorListData(majorGroupBeans);
            }

            @Override
            public List<List<MajorGroupBean>> doInIOThread(MajorBean majorBeans) {
                if (majorBeans == null) return null;
                List<MajorGroupBean> aqy = getAqyListData(majorBean.getAnquanyuan());
                List<MajorGroupBean> rj = getEjListData(majorBean.getErjian());
                List<List<MajorGroupBean>> datas = new ArrayList<>();
                datas.add(aqy);
                datas.add(rj);
                return datas;
            }
        });

    }

    /**
     * 封装安全员数据
     *
     * @param majorBean
     */
    private List<MajorGroupBean> getAqyListData(MajorBean.AnquanyuanBean majorBean) {
        List<MajorGroupBean> majorGroupBeans = new ArrayList<>();
        MajorGroupBean majorGroupBean = new MajorGroupBean();
        majorGroupBean.setName(majorBean.getTxt());
        majorGroupBean.setSubjrctType(SubjectClassTypeConfig.SUBJECT_PUB);
        majorGroupBean.setClassFlyType(ClassFLyTypeConfig.CLASS_FLY_AQY);
        List<MajorChildBean> majorChildBeans = new ArrayList<>();
        if (majorBean.getSubjects() == null || majorBean.getSubjects().size() == 0) {
            majorGroupBean.setChildBeans(majorChildBeans);
        } else {
            for (MajorBean.AnquanyuanBean.SubjectsBean subjectsBean : majorBean.getSubjects()) {
                MajorChildBean majorChildBean = new MajorChildBean();
                majorChildBean.setMagId(subjectsBean.getId());
                majorChildBean.setName(subjectsBean.getTxt());
                majorChildBean.setSubTxt(subjectsBean.getSub_txt());
                majorChildBeans.add(majorChildBean);
            }
            majorGroupBean.setChildBeans(majorChildBeans);
        }
        majorGroupBeans.add(majorGroupBean);
        return majorGroupBeans;
    }

    /**
     * 封装二建数据
     *
     * @param erjianBean
     * @return
     */
    private List<MajorGroupBean> getEjListData(MajorBean.ErjianBean erjianBean) {
        List<MajorGroupBean> majorGroupBeans = new ArrayList<>();


        if (erjianBean.getPub_subjects() == null && erjianBean.getProfess_subjects() == null) {
            //没有分类
        } else if (erjianBean.getPub_subjects() == null) {
            //没有公共科目
            MajorGroupBean majorGroupBean1 = new MajorGroupBean();
            majorGroupBean1.setName(erjianBean.getProfess_subjects().getTxt());
            majorGroupBean1.setClassFlyType(ClassFLyTypeConfig.CLASS_FLY_RJ);
            majorGroupBean1.setSubjrctType(SubjectClassTypeConfig.SUBJECT_ZY);

            List<MajorChildBean> majorChildBeans = new ArrayList<>();
            if (erjianBean.getProfess_subjects().getArr() == null || erjianBean.getProfess_subjects().getArr().size() == 0) {
                //没有科目
                majorGroupBean1.setChildBeans(majorChildBeans);
            } else {
                for (MajorBean.ErjianBean.ProfessSubjectsBean.ArrBeanX arrBeanX : erjianBean.getProfess_subjects().getArr()) {
                    MajorChildBean majorChildBean = new MajorChildBean();
                    majorChildBean.setMagId(arrBeanX.getId());
                    majorChildBean.setName(arrBeanX.getTxt());
                    majorChildBeans.add(majorChildBean);
                }
                majorGroupBean1.setChildBeans(majorChildBeans);
            }
            majorGroupBeans.add(majorGroupBean1);
        } else if (erjianBean.getProfess_subjects() == null) {
            //没有专业科目
            MajorGroupBean majorGroupBean2 = new MajorGroupBean();
            majorGroupBean2.setName(erjianBean.getProfess_subjects().getTxt());
            majorGroupBean2.setClassFlyType(ClassFLyTypeConfig.CLASS_FLY_RJ);
            majorGroupBean2.setSubjrctType(SubjectClassTypeConfig.SUBJECT_PUB);
            List<MajorChildBean> majorChildBeans = new ArrayList<>();
            if (erjianBean.getProfess_subjects().getArr() == null || erjianBean.getProfess_subjects().getArr().size() == 0) {
                //没有科目
                majorGroupBean2.setChildBeans(majorChildBeans);
            } else {
                for (MajorBean.ErjianBean.ProfessSubjectsBean.ArrBeanX arrBeanX : erjianBean.getProfess_subjects().getArr()) {
                    MajorChildBean majorChildBean = new MajorChildBean();
                    majorChildBean.setMagId(arrBeanX.getId());
                    majorChildBean.setName(arrBeanX.getTxt());
                    majorChildBeans.add(majorChildBean);
                }
                majorGroupBean2.setChildBeans(majorChildBeans);
            }

            majorGroupBeans.add(majorGroupBean2);
        } else {
            MajorGroupBean majorGroupBean1 = new MajorGroupBean();
            majorGroupBean1.setName(erjianBean.getProfess_subjects().getTxt());
            majorGroupBean1.setClassFlyType(ClassFLyTypeConfig.CLASS_FLY_RJ);
            majorGroupBean1.setSubjrctType(SubjectClassTypeConfig.SUBJECT_ZY);

            List<MajorChildBean> majorChildBeans = new ArrayList<>();
            if (erjianBean.getProfess_subjects().getArr() == null || erjianBean.getProfess_subjects().getArr().size() == 0) {
                //没有科目
                majorGroupBean1.setChildBeans(majorChildBeans);
            } else {
                for (MajorBean.ErjianBean.ProfessSubjectsBean.ArrBeanX arrBeanX : erjianBean.getProfess_subjects().getArr()) {
                    MajorChildBean majorChildBean = new MajorChildBean();
                    majorChildBean.setMagId(arrBeanX.getId());
                    majorChildBean.setName(arrBeanX.getTxt());
                    majorChildBeans.add(majorChildBean);
                }
                majorGroupBean1.setChildBeans(majorChildBeans);
            }

            MajorGroupBean majorGroupBean2 = new MajorGroupBean();
            majorGroupBean2.setName(erjianBean.getPub_subjects().getTxt());
            majorGroupBean2.setClassFlyType(ClassFLyTypeConfig.CLASS_FLY_RJ);
            majorGroupBean2.setSubjrctType(SubjectClassTypeConfig.SUBJECT_PUB);
            List<MajorChildBean> majorChildBeans1 = new ArrayList<>();
            if (erjianBean.getProfess_subjects().getArr() == null || erjianBean.getProfess_subjects().getArr().size() == 0) {
                //没有科目
                majorGroupBean2.setChildBeans(majorChildBeans1);
            } else {
                for (MajorBean.ErjianBean.PubSubjectsBean.ArrBean arrBeanX : erjianBean.getPub_subjects().getArr()) {
                    MajorChildBean majorChildBean = new MajorChildBean();
                    majorChildBean.setMagId(arrBeanX.getId());
                    majorChildBean.setName(arrBeanX.getTxt());
                    majorChildBeans1.add(majorChildBean);
                }
                majorGroupBean2.setChildBeans(majorChildBeans1);
            }

            majorGroupBeans.add(majorGroupBean2);
            majorGroupBeans.add(majorGroupBean1);
        }
        return majorGroupBeans;
    }

    public void payOrder(String goodId, Activity activity, String payRouthParams) {
        LoadingPopupView loading = new PopupManager.Builder(activity).asLoading("请稍等...");
        loading.show();
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("platform", BaseConfig.clent);
        multiBuilder.addFormDataPart("payType", BaseConfig.source);
        multiBuilder.addFormDataPart("goodId", goodId);
        multiBuilder.addFormDataPart("refferPage", payRouthParams);
        RequestBody multiBody = multiBuilder.build();
        PayHelper.getInstance().getPayData(BaseAPI.VERSION_DES + "/API/Pay/BuyVipGoods", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        loading.dismiss();
                    }
                });
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtils.normal(e.getMessage(), Gravity.CENTER);
                    }
                });

            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //暂时不处理上传失败的数据
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        loading.dismiss();
                    }
                });
                if (response.code() == 200) {
                    try {
                        String jsonStr = response.body().string();
                        Type jsonType = new TypeToken<BaseData<PayParamsBean>>() {
                        }.getType();
                        BaseData<PayParamsBean> commonExamBeanBaseData = new Gson().fromJson(jsonStr, jsonType);
                        if (commonExamBeanBaseData.Code.equals("200")) {
                            WX_APP_ID = commonExamBeanBaseData.Data.getTenPayAppParameters().getAppid();
                            try {
                                BaseConfig.isPaySuccessDialog = commonExamBeanBaseData.getResult().getTenPayAppParameters().isShowPopup();
                            } catch (Exception e) {
                            }
                            LogUtils.e("----->>>>开始这个支付------>>>>" + commonExamBeanBaseData.getResult().getTenPayAppParameters().isShowPopup());
                            WX_WAIT_ORDER = commonExamBeanBaseData.Data.getTenPayAppParameters().getBillCode();
                            WxPayConfig wxPayConfig =
                                    new WxPayConfig.Builder().with(activity)
                                            .setAppId(WX_APP_ID)
                                            .setNoncestr(commonExamBeanBaseData.Data.getTenPayAppParameters().getNoncestr())
                                            .setPackagex(commonExamBeanBaseData.Data.getTenPayAppParameters().getPackageX())
                                            .setPartnerid(commonExamBeanBaseData.Data.getTenPayAppParameters().getPartnerid())
                                            .setSign(commonExamBeanBaseData.Data.getTenPayAppParameters().getSign())
                                            .setPrepayid(commonExamBeanBaseData.Data.getTenPayAppParameters().getPrepayid())
                                            .setTimestamp(commonExamBeanBaseData.Data.getTenPayAppParameters().getTimestamp())
                                            .builder();
                            MainThreadUtils.post(new Runnable() {
                                @Override
                                public void run() {
                                    WxPayManager.getInstance().sendPay(wxPayConfig);
                                }
                            });
                        } else {
                            MainThreadUtils.post(new Runnable() {
                                @Override
                                public void run() {
                                    ToastUtils.normal(commonExamBeanBaseData.Message, Gravity.CENTER);
                                }
                            });

                        }
                    } catch (Exception e) {
                        MainThreadUtils.post(new Runnable() {
                            @Override
                            public void run() {
                                ToastUtils.normal(e.getMessage(), Gravity.CENTER);
                            }
                        });

                    }
                }

            }
        });
    }


    public void getOrderInfoByMajId(String majId,PayInfoListener payInfoListener) {
        if (StringUtils.isEmpty(majId)) {
            return;
        }
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("majorId", majId);
        RequestBody multiBody = multiBuilder.build();
        PayHelper.getInstance().getPayData(BaseAPI.VERSION_DES + "/API/Pay/GetVipGoods", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                payInfoListener.onPayInfo(false, e.getMessage(), null, null, null,0, 1);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //暂时不处理上传失败的数据
                if (response.code() == 200) {
                    try {
                        String jsonStr = response.body().string();
                        Type jsonType = new TypeToken<BaseData<GoodsInfoBean>>() {
                        }.getType();
                        BaseData<GoodsInfoBean> commonExamBeanBaseData = new Gson().fromJson(jsonStr, jsonType);
                        if (commonExamBeanBaseData.Code.equals("200")) {

                            if (commonExamBeanBaseData.Data.getFreePay() == null) {
                                String jsonList = GsonUtils.newInstance().listToJson(commonExamBeanBaseData.getResult().getDetails());
                                payInfoListener.onPayInfo(true,
                                        commonExamBeanBaseData.Data.getGoods().getPrice(),
                                        commonExamBeanBaseData.Data.getGoods().getDuration(),
                                        String.valueOf(commonExamBeanBaseData.Data.getGoods().getGoodsId()),
                                        jsonList,commonExamBeanBaseData.Data.getGoods().getGoodsType(),
                                        commonExamBeanBaseData.Data.getMajorType());
                            } else if (commonExamBeanBaseData.Data.isCanPay()) {
                                String jsonList = GsonUtils.newInstance().listToJson(commonExamBeanBaseData.getResult().getDetails());
                                payInfoListener.onPayInfo(true,
                                        commonExamBeanBaseData.Data.getGoods().getPrice(),
                                        commonExamBeanBaseData.Data.getGoods().getDuration(),
                                        String.valueOf(commonExamBeanBaseData.Data.getGoods().getGoodsId()),
                                        jsonList,commonExamBeanBaseData.Data.getGoods().getGoodsType(), commonExamBeanBaseData.Data.getMajorType());
                            } else {
                                String des = GsonUtils.newInstance().GsonToString(commonExamBeanBaseData.Data.getFreePay());
                                payInfoListener.onPayInfo(true,
                                        null,
                                        null,
                                        null,
                                        des,0, commonExamBeanBaseData.Data.getMajorType());
                            }

                        } else {
                            payInfoListener.onPayInfo(false, commonExamBeanBaseData.Message, null, null, null,0,
                                    commonExamBeanBaseData.Data.getMajorType());
                        }
                    } catch (Exception e) {
                        payInfoListener.onPayInfo(false, e.getMessage(), null, null, null,0, 1);
                    }
                }


            }
        });
    }

    public void natieToPay(Activity mActivity, String appId, String Noncestr, String PackageX, String Partnerid, String Sign, String Prepayid, String Timestamp) {
        WX_APP_ID = appId;
        WxPayConfig wxPayConfig = new WxPayConfig.Builder().with(mActivity)
                .setAppId(WX_APP_ID)
                .setNoncestr(Noncestr)
                .setPackagex(PackageX)
                .setPartnerid(Partnerid)
                .setSign(Sign)
                .setPrepayid(Prepayid)
                .setTimestamp(Timestamp)
                .builder();
        MainThreadUtils.post(new Runnable() {
            @Override
            public void run() {
                WxPayManager.getInstance().sendPay(wxPayConfig);
            }
        });
    }
}
