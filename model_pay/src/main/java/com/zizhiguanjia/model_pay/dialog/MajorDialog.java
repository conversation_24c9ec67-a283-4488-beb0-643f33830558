package com.zizhiguanjia.model_pay.dialog;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wb.lib_arch.common.Bus;
import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_utils.utils.GsonUtils;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_pay.R;
import com.zizhiguanjia.model_pay.adapter.MajorGroupAdapter;
import com.zizhiguanjia.model_pay.config.ClassFLyTypeConfig;
import com.zizhiguanjia.model_pay.model.MainCertificateBean;
import com.zizhiguanjia.model_pay.model.MajorGroupBean;
import com.zizhiguanjia.model_pay.model.SegmentItem;
import com.zizhiguanjia.model_pay.views.SegmentedBarView;

import java.util.ArrayList;
import java.util.List;

public class MajorDialog extends BottomPopupView implements SegmentedBarView.OnSegItemClickListener, View.OnClickListener {
    private SegmentedBarView mSegmentedBarView;
    private RecyclerView mRecyclerView;
    private MajorGroupAdapter mMajorGroupAdapter;
    private List<List<MajorGroupBean>> majorGroupBeans;
    private int currentClassFlyId;
    private int currentMajoreId;
    private ImageView mClosePayImg;
    public MajorDialog(@NonNull Context context,List<List<MajorGroupBean>> majorGroupBean,int currentClassFlyId,int currentMajoreId) {
        super(context);
        this.majorGroupBeans=majorGroupBean;
        this.currentClassFlyId=currentClassFlyId;
        this.currentMajoreId=currentMajoreId;
        addInnerContent();
    }

    @Override
    protected int initLayoutResId() {
        return R.layout.pay_majors_layout;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        mSegmentedBarView=findViewById(R.id.segmentedBarView);
        mRecyclerView=findViewById(R.id.main_qh_rev);
        mClosePayImg=findViewById(R.id.imgClosePay);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mMajorGroupAdapter=new MajorGroupAdapter();
        mRecyclerView.setAdapter(mMajorGroupAdapter);
        mSegmentedBarView.setOnSegItemClickListener(this);
        mClosePayImg.setOnClickListener(this);
        initData();
    }
    private void initData(){
        List<SegmentItem> mList = new ArrayList<>();
        mMajorGroupAdapter.setMajorId(currentMajoreId);
        mMajorGroupAdapter.setDataItems(getMajorBeanByItem(currentClassFlyId== ClassFLyTypeConfig.CLASS_FLY_AQY?ClassFLyTypeConfig.CLASS_FLY_AQY:ClassFLyTypeConfig.CLASS_FLY_RJ));
        mList.add(new SegmentItem(currentClassFlyId==ClassFLyTypeConfig.CLASS_FLY_AQY?"安全员":"二建"));
        mList.add(new SegmentItem(currentClassFlyId==ClassFLyTypeConfig.CLASS_FLY_AQY?"二建":"安全员"));
        mSegmentedBarView.addSegmentedBars(mList);
    }
    private List<MajorGroupBean>  getMajorBeanByItem(int type){
        for(List<MajorGroupBean> list:majorGroupBeans){
            if(list.get(0).getClassFlyType()==type){
                return list;
            }
        }
        return null;
    }
    @Override
    public void onSegItemClick(SegmentItem item, int position) {
        mMajorGroupAdapter.setDataItems(getMajorBeanByItem(currentClassFlyId==ClassFLyTypeConfig.CLASS_FLY_AQY?
                (position==0?ClassFLyTypeConfig.CLASS_FLY_AQY:ClassFLyTypeConfig.CLASS_FLY_RJ):
                (position==0?ClassFLyTypeConfig.CLASS_FLY_RJ:ClassFLyTypeConfig.CLASS_FLY_AQY)));
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.imgClosePay){
            dismiss();
        }
    }
}
