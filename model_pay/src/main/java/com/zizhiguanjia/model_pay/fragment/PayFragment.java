package com.zizhiguanjia.model_pay.fragment;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;

import androidx.databinding.ObservableField;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wangbo.www.paylibs.wxpay.WxPayConfig;
import com.wangbo.www.paylibs.wxpay.WxPayManager;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.constants.PayRouterPath;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig;
import com.zizhiguanjia.model_pay.R;
import com.zizhiguanjia.model_pay.adapter.PayPackAdapter;
import com.zizhiguanjia.model_pay.config.MsgTypeConfig;
import com.zizhiguanjia.model_pay.databinding.PayContextLayoutBinding;
import com.zizhiguanjia.model_pay.databinding.PayMainpayLayoutBinding;
import com.zizhiguanjia.model_pay.databinding.PayMainpayLayoutBindingImpl;
import com.zizhiguanjia.model_pay.dialog.MajorDialog;
import com.zizhiguanjia.model_pay.listener.PayPackListener;
import com.zizhiguanjia.model_pay.model.GoodsInfoBean;
import com.zizhiguanjia.model_pay.model.MainCertificateBean;
import com.zizhiguanjia.model_pay.model.MajorGroupBean;
import com.zizhiguanjia.model_pay.navigator.PayNavigator;
import com.zizhiguanjia.model_pay.viewmodel.PayViewModel;

import java.util.List;

import io.reactivex.internal.operators.observable.ObservableUsing;

@Route(path = PayRouterPath.MAIN_FRAGMENT)
@BindRes(statusBarStyle = BarStyle.LIGHT_CONTENT)
public class PayFragment extends BaseFragment implements PayNavigator, PayPackListener, TitleBar.OnTitleBarListener {
    private PayMainpayLayoutBinding binding;
    @BindViewModel
    PayViewModel model;
    private PayPackAdapter payPackAdapter;
    private LoadingPopupView loadingPopupView;

    @Override
    public int initLayoutResId() {
        return R.layout.pay_mainpay_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();
        model.initParams(this, this);
        binding.setModel(model);
        binding.tbExamPay.setClickListener(this);
    }

    @Override
    public void initViewData() {
        payPackAdapter = new PayPackAdapter(this);
        binding.icePayContext.rcvPayPack.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.icePayContext.rcvPayPack.addItemDecoration(new GridSpaceItemDecoration(1, DpUtils.dp2px(getContext(), 5), 0));
        binding.icePayContext.rcvPayPack.setAdapter(payPackAdapter);
        loadingPopupView = new PopupManager.Builder(getContext()).asLoading("请稍后...",R.layout.popup_center_impl_loading);
    }

    @Override
    public void initObservable() {
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if (msgEvent.getCode() == MsgTypeConfig.MSG_TYPE_CLOSE_MAJOR) {
                    //更新了选择
                    String json = msgEvent.getMsg();
                    MainCertificateBean mainCertificateBean = GsonUtils.newInstance().getBean(json, MainCertificateBean.class);
                    BaseConfig.cityCode=mainCertificateBean.getCityCode();
                    BaseConfig.majId=String.valueOf(mainCertificateBean.getSubjuectId());
                    model.majorNameObs.set(mainCertificateBean.getName());
                    model.subjectObs.set(mainCertificateBean.getSubjuectId());
                    model.getCurrentPackage(String.valueOf(mainCertificateBean.getSubjuectId()), true);
                } else if (msgEvent.getCode() == PayMsgTypeConfig.PAY_MSG_SUCCESS) {

                    finish();
                }
            }
        });
    }

    @Override
    public void goodInfo(GoodsInfoBean.GoodsBean goodsBean) {
        binding.mtlsvPayState.showContent();
        model.durationObs.set(goodsBean.getDuration());
        model.timeOutObs.set(goodsBean.getTimeOutDate());
        if (Integer.parseInt(goodsBean.getPrice()) > 99) {
            model.tkPrice.set(String.valueOf(Integer.parseInt(goodsBean.getPrice()) / 100) + ".00");
            model.tkPriceStr.set(String.valueOf(Integer.parseInt(goodsBean.getPrice()) / 100));
        } else {
            model.tkPrice.set(String.valueOf(Double.parseDouble(goodsBean.getPrice()) / 100));
            model.tkPriceStr.set(String.valueOf(Double.parseDouble(goodsBean.getPrice()) / 100));
        }
        model.setGoodsId(goodsBean.getGoodsId() + "");
    }

    @Override
    public void packList(List<GoodsInfoBean.DetailsBean> packs) {
        payPackAdapter.setDataItems(packs);
    }

    @Override
    public void showToast(String msg) {
        ToastUtils.normal(msg, Gravity.CENTER);
    }

    @Override
    public void showLoading(boolean visition) {
        if (visition) {
            if (loadingPopupView.isShow()) return;
            loadingPopupView.show();
        } else {
            if (loadingPopupView == null) return;
            if (loadingPopupView.isShow()) loadingPopupView.dismiss();
        }
    }

    @Override
    public void WxChatPay(WxPayConfig wxPayConfig) {
        WxPayManager.getInstance().sendPay(wxPayConfig);
    }

    @Override
    public void showMajorsDiglog(List<List<MajorGroupBean>> lists) {
        initArguments().putBoolean("canSave", false);
        startFragment(CertificateHelper.mainPage(getContext()));
    }

    @Override
    public void showErrorView() {
        binding.mtlsvPayState.showError();
    }

    @Override
    public void showLoadingView() {
        binding.mtlsvPayState.showLoading();
    }

    @Override
    public void showEmptyView() {
        binding.mtlsvPayState.showEmpty();
    }

    @Override
    public void showNetWorkView() {
        binding.mtlsvPayState.showNoNetwork();
    }

    @Override
    public void userSelectPack(String goodsId, String price) {
        if (Integer.parseInt(price) > 99) {
            model.tkPrice.set(String.valueOf(Integer.parseInt(price) / 100) + ".00");
            model.tkPriceStr.set(String.valueOf(Integer.parseInt(price) / 100));
        } else {
            model.tkPrice.set(String.valueOf(Double.parseDouble(price) / 100));
            model.tkPriceStr.set(String.valueOf(Double.parseDouble(price) / 100));
        }

        model.setGoodsId(goodsId);
    }

    @Override
    public void onClicked(View v, int action) {
        if (action == TitleBar.ACTION_LEFT_BUTTON) {
            finish();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
