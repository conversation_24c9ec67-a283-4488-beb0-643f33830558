package com.zizhiguanjia.model_pay.api;

import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.model_pay.model.GoodsInfoBean;
import com.zizhiguanjia.model_pay.model.MajorBean;
import com.zizhiguanjia.model_pay.model.PayParamsBean;

import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface PayApi {
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Pay/GetVipGoods")
    Observable<BaseData<GoodsInfoBean>> getCurrentPackage(@FieldMap Map<String,String> params);
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Pay/BuyVipGoods")
    Observable<BaseData<PayParamsBean>> postPay(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Examation/GetMajors")
    Observable<BaseData<MajorBean>> getMajors(@FieldMap Map<String,String> params);
}
