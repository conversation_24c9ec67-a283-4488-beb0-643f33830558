package com.zizhiguanjia.model_pay.model;

import android.os.Parcel;
import android.os.Parcelable;

public class MainCertificateBean implements Parcelable {
    private int classFlyId;
    private int subjuectId;
    private String name;
    private String des;
    private String cityCode;

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public int getClassFlyId() {
        return classFlyId;
    }

    public void setClassFlyId(int classFlyId) {
        this.classFlyId = classFlyId;
    }

    public int getSubjuectId() {
        return subjuectId;
    }

    public void setSubjuectId(int subjuectId) {
        this.subjuectId = subjuectId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public MainCertificateBean() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.classFlyId);
        dest.writeInt(this.subjuectId);
        dest.writeString(this.name);
        dest.writeString(this.des);
    }

    protected MainCertificateBean(Parcel in) {
        this.classFlyId = in.readInt();
        this.subjuectId = in.readInt();
        this.name = in.readString();
        this.des = in.readString();
    }

    public static final Creator<MainCertificateBean> CREATOR = new Creator<MainCertificateBean>() {
        @Override
        public MainCertificateBean createFromParcel(Parcel source) {
            return new MainCertificateBean(source);
        }

        @Override
        public MainCertificateBean[] newArray(int size) {
            return new MainCertificateBean[size];
        }
    };

    @Override
    public String toString() {
        return "MainCertificateBean{" +
                "classFlyId=" + classFlyId +
                ", subjuectId=" + subjuectId +
                ", name='" + name + '\'' +
                ", des='" + des + '\'' +
                ", cityCode='" + cityCode + '\'' +
                '}';
    }
}
