package com.zizhiguanjia.model_pay.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class MajorGroupBean implements Parcelable {
    private String name;
    private int subjrctType;
    private int classFlyType;
    private List<MajorChildBean> childBeans;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getSubjrctType() {
        return subjrctType;
    }

    public void setSubjrctType(int subjrctType) {
        this.subjrctType = subjrctType;
    }

    public int getClassFlyType() {
        return classFlyType;
    }

    public void setClassFlyType(int classFlyType) {
        this.classFlyType = classFlyType;
    }

    public List<MajorChildBean> getChildBeans() {
        return childBeans;
    }

    public void setChildBeans(List<MajorChildBean> childBeans) {
        this.childBeans = childBeans;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.name);
        dest.writeInt(this.subjrctType);
        dest.writeInt(this.classFlyType);
        dest.writeTypedList(this.childBeans);
    }

    public MajorGroupBean() {
    }

    protected MajorGroupBean(Parcel in) {
        this.name = in.readString();
        this.subjrctType = in.readInt();
        this.classFlyType = in.readInt();
        this.childBeans = in.createTypedArrayList(MajorChildBean.CREATOR);
    }

    public static final Creator<MajorGroupBean> CREATOR = new Creator<MajorGroupBean>() {
        @Override
        public MajorGroupBean createFromParcel(Parcel source) {
            return new MajorGroupBean(source);
        }

        @Override
        public MajorGroupBean[] newArray(int size) {
            return new MajorGroupBean[size];
        }
    };
}
