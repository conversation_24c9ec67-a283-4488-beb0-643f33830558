package com.zizhiguanjia.model_pay.model;

import java.util.List;

public class MajorBean {

    private ErjianBean erjian;
    private AnquanyuanBean anquanyuan;

    public ErjianBean getErjian() {
        return erjian;
    }

    public void setErjian(ErjianBean erjian) {
        this.erjian = erjian;
    }

    public AnquanyuanBean getAnquanyuan() {
        return anquanyuan;
    }

    public void setAnquanyuan(AnquanyuanBean anquanyuan) {
        this.anquanyuan = anquanyuan;
    }

    public static class ErjianBean {
        private int id;
        private String txt;
        private PubSubjectsBean pub_subjects;
        private ProfessSubjectsBean profess_subjects;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getTxt() {
            return txt;
        }

        public void setTxt(String txt) {
            this.txt = txt;
        }

        public PubSubjectsBean getPub_subjects() {
            return pub_subjects;
        }

        public void setPub_subjects(PubSubjectsBean pub_subjects) {
            this.pub_subjects = pub_subjects;
        }

        public ProfessSubjectsBean getProfess_subjects() {
            return profess_subjects;
        }

        public void setProfess_subjects(ProfessSubjectsBean profess_subjects) {
            this.profess_subjects = profess_subjects;
        }

        public static class PubSubjectsBean {
            private String txt;
            private List<ArrBean> arr;

            public String getTxt() {
                return txt;
            }

            public void setTxt(String txt) {
                this.txt = txt;
            }

            public List<ArrBean> getArr() {
                return arr;
            }

            public void setArr(List<ArrBean> arr) {
                this.arr = arr;
            }

            public static class ArrBean {
                /**
                 * txt : 施工管理
                 * id : 2035
                 */

                private String txt;
                private int id;

                public String getTxt() {
                    return txt;
                }

                public void setTxt(String txt) {
                    this.txt = txt;
                }

                public int getId() {
                    return id;
                }

                public void setId(int id) {
                    this.id = id;
                }
            }
        }

        public static class ProfessSubjectsBean {
            /**
             * txt : 专业科目
             * arr : [{"txt":"建筑工程","id":2037},{"txt":"市政工程","id":2038},{"txt":"机电工程","id":2039}]
             */

            private String txt;
            private List<ArrBeanX> arr;

            public String getTxt() {
                return txt;
            }

            public void setTxt(String txt) {
                this.txt = txt;
            }

            public List<ArrBeanX> getArr() {
                return arr;
            }

            public void setArr(List<ArrBeanX> arr) {
                this.arr = arr;
            }

            public static class ArrBeanX {
                /**
                 * txt : 建筑工程
                 * id : 2037
                 */

                private String txt;
                private int id;

                public String getTxt() {
                    return txt;
                }

                public void setTxt(String txt) {
                    this.txt = txt;
                }

                public int getId() {
                    return id;
                }

                public void setId(int id) {
                    this.id = id;
                }
            }
        }
    }

    public static class AnquanyuanBean {
        /**
         * id : 1100
         * txt : 安全员
         * subjects : [{"txt":"A本","sub_txt":"主要负责人","id":1101},{"txt":"B本","sub_txt":"项目负责人","id":1102},{"txt":"C1本","sub_txt":"机械类","id":1103},{"txt":"C2本","sub_txt":"土建类","id":1104},{"txt":"C3本","sub_txt":"综合类","id":1105}]
         */

        private int id;
        private String txt;
        private List<SubjectsBean> subjects;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getTxt() {
            return txt;
        }

        public void setTxt(String txt) {
            this.txt = txt;
        }

        public List<SubjectsBean> getSubjects() {
            return subjects;
        }

        public void setSubjects(List<SubjectsBean> subjects) {
            this.subjects = subjects;
        }

        public static class SubjectsBean {
            /**
             * txt : A本
             * sub_txt : 主要负责人
             * id : 1101
             */

            private String txt;
            private String sub_txt;
            private int id;

            public String getTxt() {
                return txt;
            }

            public void setTxt(String txt) {
                this.txt = txt;
            }

            public String getSub_txt() {
                return sub_txt;
            }

            public void setSub_txt(String sub_txt) {
                this.sub_txt = sub_txt;
            }

            public int getId() {
                return id;
            }

            public void setId(int id) {
                this.id = id;
            }
        }
    }
}
