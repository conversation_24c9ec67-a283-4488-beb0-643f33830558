package com.zizhiguanjia.model_pay.model;

import android.os.Parcel;
import android.os.Parcelable;

public class FreePayBean implements Parcelable {
    private String MajorName;
    private String Content;
    private String SubContent;
    private String WXLink;

    public String getMajorName() {
        return MajorName;
    }

    public void setMajorName(String majorName) {
        MajorName = majorName;
    }

    public String getContent() {
        return Content;
    }

    public void setContent(String content) {
        Content = content;
    }

    public String getSubContent() {
        return SubContent;
    }

    public void setSubContent(String subContent) {
        SubContent = subContent;
    }

    public String getWXLink() {
        return WXLink;
    }

    public void setWXLink(String WXLink) {
        this.WXLink = WXLink;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.MajorName);
        dest.writeString(this.Content);
        dest.writeString(this.SubContent);
        dest.writeString(this.WXLink);
    }

    public void readFromParcel(Parcel source) {
        this.MajorName = source.readString();
        this.Content = source.readString();
        this.SubContent = source.readString();
        this.WXLink = source.readString();
    }

    public FreePayBean() {
    }

    protected FreePayBean(Parcel in) {
        this.MajorName = in.readString();
        this.Content = in.readString();
        this.SubContent = in.readString();
        this.WXLink = in.readString();
    }

    public static final Parcelable.Creator<FreePayBean> CREATOR = new Parcelable.Creator<FreePayBean>() {
        @Override
        public FreePayBean createFromParcel(Parcel source) {
            return new FreePayBean(source);
        }

        @Override
        public FreePayBean[] newArray(int size) {
            return new FreePayBean[size];
        }
    };
}
