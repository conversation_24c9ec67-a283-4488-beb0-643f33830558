package com.zizhiguanjia.model_pay.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class GoodsInfoBean {
    private GoodsBean Goods;
    private List<DetailsBean> Details;
    private FreePayBean FreePay;
    private boolean IsCanPay;

    private int MajorType;

    public void setMajorType(int majorType) {
        MajorType = majorType;
    }

    public int getMajorType() {
        return MajorType;
    }

    public boolean isCanPay() {
        return IsCanPay;
    }

    public void setCanPay(boolean canPay) {
        IsCanPay = canPay;
    }

    public FreePayBean getFreePay() {
        return FreePay;
    }

    public void setFreePay(FreePayBean freePay) {
        FreePay = freePay;
    }

    public GoodsBean getGoods() {
        return Goods;
    }
    public void setGoods(GoodsBean Goods) {
        this.Goods = Goods;
    }
    public List<DetailsBean> getDetails() {
        return Details;
    }

    public void setDetails(List<DetailsBean> Details) {
        this.Details = Details;
    }
    public static class GoodsBean implements Parcelable {
        private String MajorName;
        private Object Details;
        private String Duration;
        private String TimeOutDate;
        private String ServiceContent;
        private String Price;
        private int GoodsId;
        private int GoodsType;

        public int getGoodsType() {
            return GoodsType;
        }

        public void setGoodsType(int goodsType) {
            GoodsType = goodsType;
        }

        public int getGoodsId() {
            return GoodsId;
        }

        public void setGoodsId(int goodsId) {
            GoodsId = goodsId;
        }

        public String getMajorName() {
            return MajorName;
        }

        public void setMajorName(String MajorName) {
            this.MajorName = MajorName;
        }

        public Object getDetails() {
            return Details;
        }

        public void setDetails(Object Details) {
            this.Details = Details;
        }

        public String getDuration() {
            return Duration;
        }

        public void setDuration(String Duration) {
            this.Duration = Duration;
        }

        public String getTimeOutDate() {
            return TimeOutDate;
        }

        public void setTimeOutDate(String TimeOutDate) {
            this.TimeOutDate = TimeOutDate;
        }

        public String getServiceContent() {
            return ServiceContent;
        }

        public void setServiceContent(String ServiceContent) {
            this.ServiceContent = ServiceContent;
        }

        public String getPrice() {
            return Price;
        }

        public void setPrice(String Price) {
            this.Price = Price;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.MajorName);
            dest.writeString(this.Duration);
            dest.writeString(this.TimeOutDate);
            dest.writeString(this.ServiceContent);
            dest.writeString(this.Price);
            dest.writeInt(this.GoodsId);
        }

        public GoodsBean() {
        }

        protected GoodsBean(Parcel in) {
            this.MajorName = in.readString();
            this.Details = in.readParcelable(Object.class.getClassLoader());
            this.Duration = in.readString();
            this.TimeOutDate = in.readString();
            this.ServiceContent = in.readString();
            this.Price = in.readString();
            this.GoodsId = in.readInt();
        }

        public static final Parcelable.Creator<GoodsBean> CREATOR = new Parcelable.Creator<GoodsBean>() {
            @Override
            public GoodsBean createFromParcel(Parcel source) {
                return new GoodsBean(source);
            }

            @Override
            public GoodsBean[] newArray(int size) {
                return new GoodsBean[size];
            }
        };
    }

    public static class DetailsBean {
        /**
         * GoodsName : 单科畅学
         * Desc : 施工管理
         * Info : 单独购买
         * Price : 1
         * Select : true
         * GoodsId : 8
         * ShowInfo : 2
         */

        private String GoodsName;
        private String Desc;
        private String Info;
        private String Price;
        private boolean Select;
        private int GoodsId;
        private int ShowInfo;

        public String getGoodsName() {
            return GoodsName;
        }

        public void setGoodsName(String GoodsName) {
            this.GoodsName = GoodsName;
        }

        public String getDesc() {
            return Desc;
        }

        public void setDesc(String Desc) {
            this.Desc = Desc;
        }

        public String getInfo() {
            return Info;
        }

        public void setInfo(String Info) {
            this.Info = Info;
        }

        public String getPrice() {
            return Price;
        }

        public void setPrice(String Price) {
            this.Price = Price;
        }

        public boolean isSelect() {
            return Select;
        }

        public void setSelect(boolean Select) {
            this.Select = Select;
        }

        public int getGoodsId() {
            return GoodsId;
        }

        public void setGoodsId(int GoodsId) {
            this.GoodsId = GoodsId;
        }

        public int getShowInfo() {
            return ShowInfo;
        }

        public void setShowInfo(int ShowInfo) {
            this.ShowInfo = ShowInfo;
        }
    }
}
