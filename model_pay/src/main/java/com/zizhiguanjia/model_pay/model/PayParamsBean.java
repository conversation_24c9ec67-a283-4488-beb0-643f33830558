package com.zizhiguanjia.model_pay.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class PayParamsBean implements Parcelable {
    private TenPayAppParametersBean TenPayAppParameters;
    private WxKeFu WxKeFu;
    public com.zizhiguanjia.model_pay.model.WxKeFu getWxKeFu() {
        return WxKeFu;
    }
    public void setWxKeFu(com.zizhiguanjia.model_pay.model.WxKeFu wxKeFu) {
        WxKeFu = wxKeFu;
    }

    public TenPayAppParametersBean getTenPayAppParameters() {
        return TenPayAppParameters;
    }
    public void setTenPayAppParameters(TenPayAppParametersBean TenPayAppParameters) {
        this.TenPayAppParameters = TenPayAppParameters;
    }
    public static class TenPayAppParametersBean implements Parcelable {
        private boolean isShowPopup;
        private String appid;
        private String partnerid;
        private String prepayid;
        @SerializedName("package")
        private String packageX;
        private String noncestr;
        private String timestamp;
        private String sign;
        private String billCode;

        public boolean isShowPopup() {
            return isShowPopup;
        }

        public void setShowPopup(boolean showPopup) {
            isShowPopup = showPopup;
        }

        public String getBillCode() {
            return billCode;
        }

        public void setBillCode(String billCode) {
            this.billCode = billCode;
        }

        public String getAppid() {
            return appid;
        }

        public void setAppid(String appid) {
            this.appid = appid;
        }

        public String getPartnerid() {
            return partnerid;
        }

        public void setPartnerid(String partnerid) {
            this.partnerid = partnerid;
        }

        public String getPrepayid() {
            return prepayid;
        }

        public void setPrepayid(String prepayid) {
            this.prepayid = prepayid;
        }

        public String getPackageX() {
            return packageX;
        }

        public void setPackageX(String packageX) {
            this.packageX = packageX;
        }

        public String getNoncestr() {
            return noncestr;
        }

        public void setNoncestr(String noncestr) {
            this.noncestr = noncestr;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

        public String getSign() {
            return sign;
        }

        public void setSign(String sign) {
            this.sign = sign;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.appid);
            dest.writeString(this.partnerid);
            dest.writeString(this.prepayid);
            dest.writeString(this.packageX);
            dest.writeString(this.noncestr);
            dest.writeString(this.timestamp);
            dest.writeString(this.sign);
        }

        public TenPayAppParametersBean() {
        }

        protected TenPayAppParametersBean(Parcel in) {
            this.appid = in.readString();
            this.partnerid = in.readString();
            this.prepayid = in.readString();
            this.packageX = in.readString();
            this.noncestr = in.readString();
            this.timestamp = in.readString();
            this.sign = in.readString();
        }

        public static final Creator<TenPayAppParametersBean> CREATOR = new Creator<TenPayAppParametersBean>() {
            @Override
            public TenPayAppParametersBean createFromParcel(Parcel source) {
                return new TenPayAppParametersBean(source);
            }

            @Override
            public TenPayAppParametersBean[] newArray(int size) {
                return new TenPayAppParametersBean[size];
            }
        };
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeParcelable(this.TenPayAppParameters, flags);
    }

    public PayParamsBean() {
    }

    protected PayParamsBean(Parcel in) {
        this.TenPayAppParameters = in.readParcelable(TenPayAppParametersBean.class.getClassLoader());
    }

    public static final Parcelable.Creator<PayParamsBean> CREATOR = new Parcelable.Creator<PayParamsBean>() {
        @Override
        public PayParamsBean createFromParcel(Parcel source) {
            return new PayParamsBean(source);
        }

        @Override
        public PayParamsBean[] newArray(int size) {
            return new PayParamsBean[size];
        }
    };
}
