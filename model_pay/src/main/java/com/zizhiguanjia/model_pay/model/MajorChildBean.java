package com.zizhiguanjia.model_pay.model;

import android.os.Parcel;
import android.os.Parcelable;

public class MajorChildBean implements Parcelable {
    private String name;
    private int magId;
    private String subTxt;

    public String getSubTxt() {
        return subTxt;
    }

    public void setSubTxt(String subTxt) {
        this.subTxt = subTxt;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getMagId() {
        return magId;
    }

    public void setMagId(int magId) {
        this.magId = magId;
    }

    public MajorChildBean() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.name);
        dest.writeInt(this.magId);
        dest.writeString(this.subTxt);
    }

    protected MajorChildBean(Parcel in) {
        this.name = in.readString();
        this.magId = in.readInt();
        this.subTxt = in.readString();
    }

    public static final Creator<MajorChildBean> CREATOR = new Creator<MajorChildBean>() {
        @Override
        public MajorChildBean createFromParcel(Parcel source) {
            return new MajorChildBean(source);
        }

        @Override
        public MajorChildBean[] newArray(int size) {
            return new MajorChildBean[size];
        }
    };
}
