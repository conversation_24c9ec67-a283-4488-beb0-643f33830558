package com.zizhiguanjia.model_pay.navigator;

import com.wangbo.www.paylibs.wxpay.WxPayConfig;
import com.zizhiguanjia.model_pay.model.GoodsInfoBean;
import com.zizhiguanjia.model_pay.model.MajorGroupBean;

import java.util.List;

public interface PayNavigator {
    void goodInfo(GoodsInfoBean.GoodsBean goodsBean);
    void packList(List<GoodsInfoBean.DetailsBean> packs);
    void showToast(String msg);
    void showLoading(boolean visition);
    void WxChatPay(WxPayConfig wxPayConfig);
    void showMajorsDiglog(List<List<MajorGroupBean>> lists);
    void showErrorView();
    void showLoadingView();
    void showEmptyView();
    void showNetWorkView();
}
