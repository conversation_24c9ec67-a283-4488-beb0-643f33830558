package com.zizhiguanjia.model_pay.service;

import android.app.Activity;
import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.MainRouterPath;
import com.zizhiguanjia.lib_base.constants.PayRouterPath;
import com.zizhiguanjia.lib_base.listeners.PayInfoListener;
import com.zizhiguanjia.lib_base.service.PayService;
import com.zizhiguanjia.model_pay.manager.PayDataManager;

@Route(path = PayRouterPath.SERVICE)
public class PayServiceImpl implements PayService {
    @Override
    public void start(Context context) {

    }

    @Override
    public IFragment mainPage(Context context) {
        return ARouterUtils.navFragment(PayRouterPath.MAIN_FRAGMENT);
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {

    }

    @Override
    public void getPayInfoByMajIds(String majId, PayInfoListener payInfoListener) {
        PayDataManager.getInstance().getOrderInfoByMajId(majId,payInfoListener);
    }

    @Override
    public void payOrder(String goodId, Activity activity,String payRouthParams) {
        PayDataManager.getInstance().payOrder(goodId,activity,payRouthParams);
    }

    @Override
    public void nativeToPay(Activity mActivity,String appId,String Noncestr,String PackageX,String Partnerid,String Sign,String Prepayid,String Timestamp) {
        PayDataManager.getInstance().natieToPay(mActivity,appId,Noncestr,PackageX,Partnerid,Sign,Prepayid,Timestamp);
    }
}
