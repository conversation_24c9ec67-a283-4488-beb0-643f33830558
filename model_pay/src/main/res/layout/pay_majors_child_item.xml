<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="data"
            type="com.zizhiguanjia.model_pay.model.MajorChildBean" />
        <variable
            name="type"
            type="java.lang.Integer" />
        <import type="com.zizhiguanjia.model_pay.config.ClassFLyTypeConfig"/>
        <import type="android.view.View"/>
        <variable
            name="model"
            type="com.zizhiguanjia.model_pay.adapter.MajorChildAdapter" />
    </data>
    <LinearLayout
        android:id="@+id/main_major_item_ll"
        android:onClick="@{(view)->model.onClick(view,data)}"
        android:gravity="center"
        android:background="@drawable/pay_major_child_no_bg"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="50dp">
        <TextView
            android:text="@{data.name}"
            android:textColor="#2A2F38"
            android:textSize="14.5sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:visibility="@{type==ClassFLyTypeConfig.CLASS_FLY_RJ?View.GONE:View.VISIBLE}"
            android:text="@{data.subTxt}"
            android:textColor="#999999"
            android:textSize="13sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</layout>