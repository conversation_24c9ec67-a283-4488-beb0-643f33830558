<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/pay_major_bg"
    android:layout_width="match_parent"
    android:layout_height="305dp">
    <RelativeLayout
        android:layout_marginTop="17dp"
        android:layout_marginLeft="15dp"
        android:layout_marginBottom="34dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/imgClosePay"
            android:layout_centerVertical="true"
            android:src="@drawable/pay_icon_close"
            android:layout_width="17dp"
            android:layout_height="17dp"/>
        <com.zizhiguanjia.model_pay.views.SegmentedBarView
            android:layout_centerHorizontal="true"
            android:layout_width="109dp"
            android:layout_height="27dp"
            android:id="@+id/segmentedBarView"
            app:pay_seg_selectedTextColor="#0079FF"
            app:pay_seg_unSelectedTextColor="#999999"
            app:pay_seg_selectedColor="#E3F0FF"
            app:pay_seg_unSelectedColor="@color/white"
            app:pay_seg_pading_selectedColor="#3A6AF7"
            app:pay_seg_pading_unSelectedColor="#EFEEF0"
            app:pay_seg_textSize="13sp"
            app:pay_seg_outerRadii="13dp"
            app:pay_seg_lineColor="#3A6AF7"
            app:pay_seg_outerMarginColor="#EFEEF0"
            app:pay_seg_isCircleFrame="true"
            app:pay_seg_isShowLine="true"
            app:pay_seg_outerMarginWidth="0dp"/>
    </RelativeLayout>
    <androidx.recyclerview.widget.RecyclerView
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:id="@+id/main_qh_rev"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</LinearLayout>