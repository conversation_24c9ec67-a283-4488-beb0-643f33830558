<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <import type="android.view.View"/>
        <import type="com.zizhiguanjia.model_pay.config.ClassFLyTypeConfig"/>
        <import type="com.zizhiguanjia.model_pay.config.SubjectClassTypeConfig"/>
        <variable
            name="bean"
            type="com.zizhiguanjia.model_pay.model.MajorGroupBean" />
    </data>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:id="@+id/main_major_rel"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp"
            android:visibility="@{bean.classFlyType==ClassFLyTypeConfig.CLASS_FLY_AQY?View.GONE:View.VISIBLE}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/main_ej_img"
                android:layout_width="19dp"
                android:layout_height="19dp"/>
            <TextView
                android:textSize="19sp"
                android:textColor="#141517"
                android:layout_marginLeft="10.5dp"
                android:text="@{bean.name}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:layout_marginBottom="37dp"
            android:layout_below="@+id/main_major_rel"
            android:id="@+id/main_major_child_rev"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </RelativeLayout>
</layout>