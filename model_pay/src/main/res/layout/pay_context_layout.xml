<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="model"
            type="com.zizhiguanjia.model_pay.viewmodel.PayViewModel" />

        <import type="com.wb.lib_utils.utils.DataUtils" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#3163F6"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15.5dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="15dp"
                android:layout_toLeftOf="@+id/relPayRight"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="当前选择科目："
                    android:textColor="#ccffffff"
                    android:textSize="13sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3.5dp"
                    android:text="@{model.majorNameObs}"
                    android:textColor="@color/white"
                    android:textSize="19sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/relPayRight"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="16dp"
                android:gravity="center_vertical"
                android:onClick="@{model::onclick}">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="切换专业"
                    android:textColor="#ccffffff"
                    android:textSize="13sp" />

                <ImageView
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:layout_marginLeft="7.5dp"
                    android:src="@drawable/pay_right_write" />
            </LinearLayout>
        </RelativeLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginRight="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="账号信息"
                        android:textColor="#99000000"
                        android:textSize="13sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:text="@{model.payAcccountObs}"
                        android:textColor="#cc000000"
                        android:textSize="12sp" />
                </RelativeLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="17dp"
                    android:layout_marginRight="16dp"
                    android:layout_marginBottom="10dp"
                    android:text="选择套餐"
                    android:textColor="#99000000"
                    android:textSize="13sp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rcvPayPack"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginRight="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="开通时长"
                        android:textColor="#99000000"
                        android:textSize="13sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:gravity="right"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/pay_passtime_bg"
                            android:paddingLeft="8.5dp"
                            android:paddingTop="3dp"
                            android:paddingRight="8dp"
                            android:paddingBottom="3dp"
                            android:text="@{model.durationObs}"
                            android:textColor="#3163F6"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:text='@{model.timeOutObs+"到期"}'
                            android:textColor="#cc000000"
                            android:textSize="12sp" />
                    </LinearLayout>
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="23.5dp"
                    android:layout_marginRight="16dp"
                    android:background="#ECECEC" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginRight="16dp">

                    <TextView

                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="支付价格"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:text='@{"￥"+model.tkPrice}'
                        android:textColor="#F12211"
                        android:textSize="12sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="23dp"
                    android:layout_marginRight="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="支付方式"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:src="@drawable/pay_wx" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_marginLeft="5dp"
                            android:text="微信支付"
                            android:textColor="#ff000000"
                            android:textSize="12sp" />
                    </LinearLayout>
                </RelativeLayout>

                <TextView
                    android:id="@+id/tvPayMain"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="65.5dp"
                    android:layout_marginRight="16dp"
                    android:layout_marginBottom="34dp"
                    android:background="@drawable/pay_start_bg"
                    android:gravity="center"
                    android:onClick="@{model::onclick}"
                    android:paddingTop="12.5dp"
                    android:paddingBottom="10dp"
                    android:text='@{"￥"+model.tkPriceStr+"开通"}'
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</layout>