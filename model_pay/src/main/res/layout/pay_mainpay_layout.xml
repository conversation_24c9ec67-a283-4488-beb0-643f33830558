<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="model"
            type="com.zizhiguanjia.model_pay.viewmodel.PayViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.wb.lib_weiget.titlebar.TitleBar xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/tbExamPay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tb_centerText="确认信息"
            app:tb_centerTextColor="@color/white"
            app:tb_centerTextSize="18sp"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            app:tb_leftType="imageButton"
            app:tb_showBottomLine="false"
            app:tb_statusBarColor="#3163F6"
            app:tb_titleBarColor="#3163F6" />

        <com.wb.lib_weiget.views.MultipleStatusView xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/mtlsvPayState"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:emptyView="@layout/layout_common_empty"
            app:errorView="@layout/layout_common_error"
            app:loadingView="@layout/public_loding_view"
            app:noNetworkView="@layout/layout_common_network">

            <include
                android:id="@+id/icePayContext"
                layout="@layout/pay_context_layout"
                app:model="@{model}" />
        </com.wb.lib_weiget.views.MultipleStatusView>
    </LinearLayout>
</layout>