<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_pay.adapter.PayPackAdapter" />
        <variable
            name="bean"
            type="com.zizhiguanjia.model_pay.model.GoodsInfoBean.DetailsBean" />
    </data>
    <RelativeLayout
        android:onClick="@{(view)->model.onclikGoods(view,bean.goodsId,bean.price)}"
        android:id="@+id/relPayMain"
        android:paddingBottom="11dp"
        android:paddingLeft="15dp"
        android:paddingTop="10dp"
        android:paddingRight="18dp"
        android:background="@drawable/pay_pack_bg"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <LinearLayout
                android:gravity="center"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:text="@{bean.goodsName}"
                    android:textSize="14sp"
                    android:textColor="#000000"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <ImageView
                    android:layout_marginLeft="2dp"
                    android:src="@{bean.showInfo==1?@drawable/pay_hot_img:@drawable/pay_dd_buy}"
                    android:layout_width="53dp"
                    android:layout_height="15dp"/>
            </LinearLayout>
            <TextView
                android:layout_marginTop="7dp"
                android:text="@{bean.desc}"
                android:textSize="12sp"
                android:textColor="#D9A869"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <LinearLayout
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:text="￥"
                android:textSize="13sp"
                android:textColor="#D9A869"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tvPayItemPrice"
                android:textStyle="bold"
                android:textColor="#D9A869"
                android:textSize="21sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </RelativeLayout>
</layout>