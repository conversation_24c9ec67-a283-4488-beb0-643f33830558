<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="pay_SegmentedBarView">
        <attr name="pay_seg_outerRadii" format="dimension|reference"/>
        <attr name="pay_seg_outerMarginWidth" format="dimension|reference"/>
        <attr name="pay_seg_selectedColor" format="color|reference"/>
        <attr name="pay_seg_unSelectedColor" format="color|reference"/>
        <attr name="pay_seg_outerMarginColor" format="color|reference"/>
        <attr name="pay_seg_selectedTextColor" format="color|reference"/>
        <attr name="pay_seg_unSelectedTextColor" format="color|reference"/>
        <attr name="pay_seg_isShowLine" format="boolean"/>
        <attr name="pay_seg_lineColor" format="color|reference"/>
        <attr name="pay_seg_textSize" format="dimension|reference"/>
        <attr name="pay_seg_isCircleFrame" format="boolean|reference"/>
        <attr name="pay_seg_pading_selectedColor" format="color|reference"/>
        <attr name="pay_seg_pading_unSelectedColor" format="color|reference"/>
    </declare-styleable>
</resources>