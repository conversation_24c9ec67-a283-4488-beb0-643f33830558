# This file is deprecated. Tools should instead consume 
# `.dart_tool/package_config.json`.
# 
# For more info see: https://dart.dev/go/dot-packages-deprecation
# 
# Generated by pub on 2025-07-17 09:38:34.678030.
async:file:///D:/pub_cache/hosted/pub.flutter-io.cn/async-2.8.2/lib/
auto_size_text:file:///D:/pub_cache/hosted/pub.flutter-io.cn/auto_size_text-3.0.0/lib/
boolean_selector:file:///D:/pub_cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.0/lib/
characters:file:///D:/pub_cache/hosted/pub.flutter-io.cn/characters-1.2.0/lib/
charcode:file:///D:/pub_cache/hosted/pub.flutter-io.cn/charcode-1.3.1/lib/
clock:file:///D:/pub_cache/hosted/pub.flutter-io.cn/clock-1.1.0/lib/
collection:file:///D:/pub_cache/hosted/pub.flutter-io.cn/collection-1.15.0/lib/
cupertino_icons:file:///D:/pub_cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.5/lib/
dio:file:///D:/pub_cache/hosted/pub.flutter-io.cn/dio-5.3.3/lib/
fake_async:file:///D:/pub_cache/hosted/pub.flutter-io.cn/fake_async-1.2.0/lib/
flutter:file:///D:/tools/flutterSdk/flutter_windows_2.8.0-stable/flutter/packages/flutter/lib/
flutter_page_indicator:file:///D:/pub_cache/hosted/pub.flutter-io.cn/flutter_page_indicator-0.0.3/lib/
flutter_screenutil:file:///D:/pub_cache/hosted/pub.flutter-io.cn/flutter_screenutil-5.7.0/lib/
flutter_swiper:file:///D:/pub_cache/hosted/pub.flutter-io.cn/flutter_swiper-1.1.6/lib/
flutter_test:file:///D:/tools/flutterSdk/flutter_windows_2.8.0-stable/flutter/packages/flutter_test/lib/
get:file:///D:/pub_cache/hosted/pub.flutter-io.cn/get-4.6.5/lib/
http_parser:file:///D:/pub_cache/hosted/pub.flutter-io.cn/http_parser-4.0.2/lib/
matcher:file:///D:/pub_cache/hosted/pub.flutter-io.cn/matcher-0.12.11/lib/
meta:file:///D:/pub_cache/hosted/pub.flutter-io.cn/meta-1.7.0/lib/
path:file:///D:/pub_cache/hosted/pub.flutter-io.cn/path-1.8.0/lib/
pull_to_refresh:file:///D:/pub_cache/hosted/pub.flutter-io.cn/pull_to_refresh-2.0.0/lib/
sky_engine:file:///D:/tools/flutterSdk/flutter_windows_2.8.0-stable/flutter/bin/cache/pkg/sky_engine/lib/
sleek_circular_slider:file:///D:/pub_cache/hosted/pub.flutter-io.cn/sleek_circular_slider-2.0.1/lib/
source_span:file:///D:/pub_cache/hosted/pub.flutter-io.cn/source_span-1.8.1/lib/
stack_trace:file:///D:/pub_cache/hosted/pub.flutter-io.cn/stack_trace-1.10.0/lib/
stream_channel:file:///D:/pub_cache/hosted/pub.flutter-io.cn/stream_channel-2.1.0/lib/
string_scanner:file:///D:/pub_cache/hosted/pub.flutter-io.cn/string_scanner-1.1.0/lib/
term_glyph:file:///D:/pub_cache/hosted/pub.flutter-io.cn/term_glyph-1.2.0/lib/
test_api:file:///D:/pub_cache/hosted/pub.flutter-io.cn/test_api-0.4.3/lib/
text_scroll:file:///D:/pub_cache/hosted/pub.flutter-io.cn/text_scroll-0.1.2/lib/
transformer_page_view:file:///D:/pub_cache/hosted/pub.flutter-io.cn/transformer_page_view-0.1.6/lib/
typed_data:file:///D:/pub_cache/hosted/pub.flutter-io.cn/typed_data-1.3.0/lib/
vector_math:file:///D:/pub_cache/hosted/pub.flutter-io.cn/vector_math-2.1.1/lib/
flutter_module:lib/
