name: flutter_module
description: A new Flutter module.

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
#
# This version is used _only_ for the Runner app, which is used if you just do
# a `flutter run` or a `flutter make-host-app-editable`. It has no impact
# on any other native host app that you embed your Flutter project into.
version: 1.0.0+1

environment:
  sdk: ">=2.2.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  dio: ^5.3.3
  get: ^4.3.8
  flutter_screenutil: ^5.0.1+2
  flutter_swiper: ^1.1.6
  pull_to_refresh: ^2.0.0
  sleek_circular_slider: ^2.0.1
  text_scroll: ^0.1.0
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  auto_size_text: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - images/qst_img.png #图标
    - images/main_start_bg.png #图标
    - images/main_left_line.png #图标
    - images/main_right_line.png #图标
    - images/video_bg.png #图标
    - images/video_des.png #图标
    - images/bg_next.png #图标
    - images/video_dxk.png #图标
    - images/video_spk.png #图标
    - images/main_tel.png #图标
    - images/main_spkv_video.png #图标
    - images/main_mess.png
    - images/main_play.png
    - images/flutter_save_img.png
    - images/flutter_delet_img.png
    - images/flutter_save_no_img.png
    - images/flutter_save_yes_img.png
    - images/save_progress_bg_img.png
    - images/right_img.png
    - images/main_kf.png
    - images/start_tk.png
    - images/update_tk_des.png
    - images/left_lint_bottom.png
    - images/right_lint_bottom.png
    - images/home_error.png
    - images/home_mnks.png
    - images/home_save.png
    - images/home_txlx.png
    - images/gude_addreass.png
    - images/gude_bg.png
    - images/gude_right.png
    - images/gude_subhect.png
    - images/gude_tags.png
    - images/home_foot_right_img.png
    - images/home_foot_left_img.png
    - images/home_live.png
    - images/home_live_tags.png
    - images/live_yy_bg.png
    - images/live_play.png
    - images/live_back.png
    - images/back_live_tag.png
    - images/back_play_bg.png
    - images/shaper_friends.png
    - images/shaper_kefu.png
    - images/shaper_phone.png
    - images/shaper_wx.png
    - images/home_bkzn.png
    - images/home_right.png
    - images/transcript_bg.png
    - images/transcript_yuan.png
    - images/nowork_img.png
    - images/test_date_bottom_des.png
    - images/test_date_bottom_shou.png
    - images/test_date_bottom_yuan.png
    - images/test_date_top_1_bg.png
    - images/test_date_top_bg.png
    - images/test_date_xu_line.png
    - images/message_bg.png
    - images/message_right_tags.png
    - images/order_ygq.png
    - images/home_order_ytf.png
    - images/cjd_yc.png
    - images/cjd_nodata.png
    - images/cjd_jxdt.png
    - images/exam_date_line.png
    - images/coupin_pass.png
    - images/coupion_bg.png
    - images/coupions_nodata.png
    - images/coupions_sy.png
    - images/order_nodata.png
    - images/coupin_gq.png
    - images/guilde_next.png
    - images/order_zs.png
    - images/index_active_close.png
    - images/flutter_gg.png
    - images/index_jna.png
    - images/index_sg.png
    - images/home_xypj.png
    - images/home_gjjk.png
    - images/home_play.png
    - images/home_right_img.png
    - images/bg_main_study_plan.png
    - images/icon_main_study_plan_unset.png
  module:
    androidX: true
    androidPackage: com.zizhiguanjia.flutter_module
    iosBundleIdentifier: com.zizhiguanjia.flutterModule
