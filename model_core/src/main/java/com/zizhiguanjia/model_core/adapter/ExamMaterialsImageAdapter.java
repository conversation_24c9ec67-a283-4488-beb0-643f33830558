package com.zizhiguanjia.model_core.adapter;

import android.graphics.Color;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_image_loadcal.ImageManager;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.databinding.CoreExamViewExammaterialsImageItemBinding;
import com.zizhiguanjia.model_core.listener.OnRecycleItemClickListener;
import com.zizhiguanjia.model_core.model.OriginImageBean;

public class ExamMaterialsImageAdapter extends BaseAdapter<OriginImageBean> {
    private CoreExamViewExammaterialsImageItemBinding binding;
    private OnRecycleItemClickListener onItemClickListener;
    private boolean imageTags;
    private ExamCoreStyleConfigBean styleConfigBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    private ExamCoreThemeConfigThemeBean themeBean;

    public void setImageTags(boolean imageTags) {
        this.imageTags = imageTags;
    }

    public void setOnItemClickListener(OnRecycleItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public ExamMaterialsImageAdapter() {
        super(R.layout.core_exam_view_exammaterials_image_item);
    }

    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        notifyItemRangeChanged(0,getItemCount());
    }

    @Override
    protected void bind(BaseViewHolder holder, OriginImageBean item, int position) {
        binding = holder.getBinding();
        ImageManager.getInstance().displayImage(item.getThumbnailUrl(), binding.examMaterialsItemImg);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.width = (DeviceUtils.getScreenWidth(holder.itemView.getContext()) - DpUtils.dp2px(holder.itemView.getContext(), 38)) / 2;
        params.height = DpUtils.dp2px(holder.itemView.getContext(), 72) * params.width / DpUtils.dp2px(holder.itemView.getContext(), 168);
        binding.examMaterialsItemImg.setLayoutParams(params);
        binding.examMaterialsItemImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.OnItemOnclic(v, position);
                }
            }
        });
        binding.examMaterialsItemFlagsTv.setVisibility(imageTags?View.VISIBLE:View.GONE);
        binding.examMaterialsItemFlagsTv.setText("图"+"("+(position+1)+")");
        if(themeBean != null){
            binding.examMaterialsItemFlagsTv.setTextColor(Color.parseColor(themeBean.getTextColor()));
        }
    }
}
