package com.zizhiguanjia.model_core.adapter;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.view.View;

import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_adapter.ItemDelegate;
import com.wb.lib_adapter.MultiItemTypeAdapter;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreSheetTitleBinding;
import com.zizhiguanjia.model_core.databinding.CoreSheetchildLayoutBinding;
import com.zizhiguanjia.model_core.listener.ExamSheetCall;
import com.zizhiguanjia.model_core.model.CoreSheetBean;

public class ExamSheetListAdapter extends MultiItemTypeAdapter<CoreSheetBean> {
    private ExamSheetCall call;
    private int paperType;
    private int paperId;
    public void setPaperId(int paperId) {
        this.paperId = paperId;
    }
    private ExamCoreStyleConfigBean styleConfigBean;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    public ExamSheetListAdapter(ExamSheetCall call, int paperType ) {
        this.call=call;
        this.paperType=paperType;
//        initFootView();

        addItemDelegate(new ItemDelegate<CoreSheetBean>() {
            private CoreSheetchildLayoutBinding binding;
            @Override
            public int layoutId() {
                return R.layout.core_sheetchild_layout;
            }

            @Override
            public boolean isThisType(CoreSheetBean item, int position) {
                return item.getChapterType()==2?true:false;
            }

            @Override
            public void convert(BaseViewHolder holder, CoreSheetBean item, int position) {
                binding = holder.getBinding();
                binding.setData(item);
                if(themeBean == null){
                    binding.tvExamSheetQuNum.setVisibility(View.GONE);
                    binding.viewExamSheetQuNumBg.setVisibility(View.GONE);
                    binding.viewExamSheetQuNumStore.setVisibility(View.GONE);
                    return;
                }
                binding.tvExamSheetQuNum.setVisibility(View.VISIBLE);
                binding.viewExamSheetQuNumBg.setVisibility(View.VISIBLE);
                binding.viewExamSheetQuNumStore.setVisibility(View.VISIBLE);
                if (item.isSee()) {
                    binding.viewExamSheetQuNumStore.setBackgroundResource(R.drawable.hollow_max);
                    if ((paperType == ExamPaperTypeConfig.EXAM_PAPER_TKLX && paperId == 3) ||
                            paperType == ExamPaperTypeConfig.EXAM_PAPER_LNZT ||
                            paperType == ExamPaperTypeConfig.EXAM_PAPER_MNZT ||
                            paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM ||
                            paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
                        if (item.isDone()) {
                            binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorDoing())));
                            binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorDoing())));
                            binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorDoing()));
                        } else {
                            binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorNo())));
                            binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorNo())));
                            binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorNo()));
                        }
                    } else {
                        if (item.isDone()) {
                            //已做过
                            if (!getStateExam()) return;
                            if (item.isRight()) {
                                //正确
                                binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorSure())));
                                binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorSure())));
                                binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorSure()));
                            } else {
                                //错误
                                binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorError())));
                                binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorError())));
                                binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorError()));
                            }
                        } else {
                            //未做过
                            binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorNo())));
                            binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorNo())));
                            binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorNo()));
                        }
                    }

                } else {
                    //不能看
                    binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorLuck())));
                    binding.viewExamSheetQuNumStore.setBackgroundResource(R.drawable.core_exam_sheet_unlink);
                    binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemTextColorLuck())));
                }
                binding.tvExamSheetQuNum.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        call.onUserSelectSheetQuestionNum(Integer.valueOf(item.getQuestionNum()),item.isSee());
                    }
                });
            }
        });
        addItemDelegate(new ItemDelegate<CoreSheetBean>() {
            private CoreSheetTitleBinding binding;
           @Override
           public int layoutId() {
               return R.layout.core_sheet_title;
           }

           @Override
           public boolean isThisType(CoreSheetBean item, int position) {
               return item.getChapterType()==1?true:false;
           }

           @Override
           public void convert(BaseViewHolder holder, CoreSheetBean item, int position) {
               binding=holder.getBinding();
               binding.tvTitle.setText(item.getQuestionNum());
               if(themeBean != null) {
                   binding.tvTitle.setTextColor(Color.parseColor(themeBean.getAnswerResultTitleTextColor()));
               }
           }
       });
//        addItemDelegate(new ItemDelegate<CoreSheetBean>() {
//            private CoreSheetTitleBinding binding;
//            @Override
//            public int layoutId() {
//                return R.layout.core_sheet_title;
//            }
//
//            @Override
//            public boolean isThisType(CoreSheetBean item, int position) {
//                return position+1==getItemCount()?true:false;
//            }
//
//            @Override
//            public void convert(BaseViewHolder holder, CoreSheetBean item, int position) {
//                binding=holder.getBinding();
//                binding.tvTitle.setText(item.getQuestionNum());
//            }
//        });
    }



    public void onClick(View view, int questionNum, boolean isSee) {
        if (call == null) return;
        call.onUserSelectSheetQuestionNum(questionNum, isSee);
    }

    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        notifyItemRangeChanged(0,getItemCount());
    }

    private boolean getStateExam() {
        if (paperType == ExamPaperTypeConfig.EXAM_PAPER_MNZT || paperType == ExamPaperTypeConfig.EXAM_PAPER_LNZT || paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
            return false;
        } else {
            return true;
        }
    }
}