package com.zizhiguanjia.model_core.adapter;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.AppUtils;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreExamSheetchildLayoutBinding;
import com.zizhiguanjia.model_core.listener.ExamSheetCall;
import com.zizhiguanjia.model_core.model.AnswerSheetOptionBean;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

public class ExamSheetChildAdapter extends BaseAdapter<AnswerSheetOptionBean> {
    private CoreExamSheetchildLayoutBinding binding;
    private ExamSheetCall call;
    private int paperType;
    private int paperId;
    private TextView mReStartExamTv;
    private ExamCoreStyleConfigBean styleConfigBean;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;

    public void setPaperId(int paperId) {
        this.paperId = paperId;
    }

    //    public void setPaperType(int paperType) {
    //        this.paperType = paperType;
    //    }

    public ExamSheetChildAdapter(ExamSheetCall call, boolean addFootView, int paperType) {
        super(R.layout.core_exam_sheetchild_layout);
        this.call = call;
        this.paperType = paperType;
        if (addFootView) {
            initFootView();
        }

    }

    private void initFootView() {
        View mView = LayoutInflater.from(AppUtils.getApp()).inflate(R.layout.core_sheet_bt, null);
        LinearLayout llRestartExam = mView.findViewById(R.id.llRestartExam);
        LinearLayout mRestarExamtLl = mView.findViewById(R.id.llBottomView);
        mReStartExamTv = mView.findViewById(R.id.tvReStartExam);
        ImageView mRestImg = mView.findViewById(R.id.imgRest);
        if (paperType == ExamPaperTypeConfig.EXAM_PAPER_SAVE || paperType == ExamPaperTypeConfig.EXAM_PAPER_ERROR ||
                paperType == ExamPaperTypeConfig.EXAM_PAPER_ALSY || paperType == ExamPaperTypeConfig.EXAM_PAPER_ALSYERROR) {
            mRestarExamtLl.setVisibility(GONE);
        } else {
            //            addFooterView(mView);
            mRestarExamtLl.setVisibility(VISIBLE);
        }
        if (getStateExam()) {
            mReStartExamTv.setText("重新练习");
            mReStartExamTv.setTextColor(Color.parseColor("#3163F6"));
            mRestImg.setImageResource(R.drawable.core_exam_icon_cxlx);
            llRestartExam.setBackgroundResource(R.drawable.core_exam_sheet_post_bg);
        } else {
            mReStartExamTv.setText("交卷并查看结果");
            mReStartExamTv.setTextColor(Color.parseColor("#ffffff"));
            mRestImg.setImageResource(R.drawable.core_exam_icon_jj);
            llRestartExam.setBackgroundResource(R.drawable.core_exam_button_bg);
        }
        if(themeBean != null){
            mRestarExamtLl.setBackgroundColor(Color.parseColor(themeBean.getBgColor()));
        }


        llRestartExam.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                call.onUserSelectRestartExam(getStateExam());
            }
        });
    }

    /**
     * 获取重新练习控件
     *
     * @return 重新练习控件，为空就不是重新练习控件
     */
    public View getBottomOptionsView() {
        return mReStartExamTv;
    }

    @Override
    protected void bind(BaseViewHolder holder, AnswerSheetOptionBean item, int position) {
        binding = holder.getBinding();
        binding.setData(item);
        binding.setModel(this);
        if(themeBean == null){
            binding.tvExamSheetQuNum.setVisibility(View.GONE);
            binding.viewExamSheetQuNumBg.setVisibility(View.GONE);
            binding.viewExamSheetQuNumStore.setVisibility(View.GONE);
            return;
        }
        binding.tvExamSheetQuNum.setVisibility(View.VISIBLE);
        binding.viewExamSheetQuNumBg.setVisibility(View.VISIBLE);
        binding.viewExamSheetQuNumStore.setVisibility(View.VISIBLE);
        if (item.isSee()) {
            binding.viewExamSheetQuNumStore.setBackgroundResource(R.drawable.hollow_max);
            if ((paperType == ExamPaperTypeConfig.EXAM_PAPER_TKLX && paperId == 3) ||
                    //                    paperType == ExamPaperTypeConfig.EXAM_PAPER_ERROR ||
                    //                    paperType == ExamPaperTypeConfig.EXAM_PAPER_SAVE ||
                    paperType == ExamPaperTypeConfig.EXAM_PAPER_LNZT || paperType == ExamPaperTypeConfig.EXAM_PAPER_MNZT ||
                    paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM||paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
                if (item.isDone()) {
                    binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorDoing())));
                    binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorDoing())));
                    binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorDoing()));
                } else {
                    binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorNo())));
                    binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorNo())));
                    binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorNo()));
                }
            } else {
                if (item.isDone()) {
                    //已做过
                    if (!getStateExam()) {
                        return;
                    }
                    if (item.isRight()) {
                        //正确
                        binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorSure())));
                        binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorSure())));
                        binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorSure()));
                    } else {
                        //错误
                        binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorError())));
                        binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorError())));
                        binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorError()));
                    }
                } else {
                    //未做过
                    binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorNo())));
                    binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorNo())));
                    binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorNo()));
                }
            }
        } else {
            //不能看
            binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorLuck())));
            binding.viewExamSheetQuNumStore.setBackgroundResource(R.drawable.core_exam_sheet_unlink);
            binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemTextColorLuck())));
        }
    }

    public void onClick(View view, int questionNum, boolean isSee) {
        if (call == null) {
            return;
        }
        call.onUserSelectSheetQuestionNum(questionNum, isSee);
    }


    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        initFootView();
        notifyItemRangeChanged(0,getItemCount());
    }

    private boolean getStateExam() {
        if (paperType == ExamPaperTypeConfig.EXAM_PAPER_MNZT ||
                paperType == ExamPaperTypeConfig.EXAM_PAPER_LNZT ||
                paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM ||
                paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
            return false;
        } else {
            return true;
        }
    }
}