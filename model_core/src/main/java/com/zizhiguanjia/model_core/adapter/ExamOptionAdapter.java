package com.zizhiguanjia.model_core.adapter;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Build;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.config.ExamQuestionTypeConfig;
import com.zizhiguanjia.model_core.config.ExamResultStyleTypeConfig;
import com.zizhiguanjia.model_core.config.ExamTypeConfig;
import com.zizhiguanjia.model_core.config.UserAnswerTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreExamOptionItemBinding;
import com.zizhiguanjia.model_core.listener.ExamOptionListener;
import com.zizhiguanjia.model_core.listener.ExamUserSelectOptionCall;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.repository.ExamOptionRepository;
import com.zizhiguanjia.model_core.utils.ExamUtils;

import java.util.List;

import androidx.annotation.RequiresApi;

public class ExamOptionAdapter extends BaseAdapter<ExamPackAccessDataBean.RecordsBean.QuestionsBean> implements ExamOptionListener {
    private CoreExamOptionItemBinding binding;
    private List<String> mRightAnswer;
    private List<String> mUserAnswer;
    private ExamOptionRepository mExamOptionRepository;
    private int type;
    private ExamUserSelectOptionCall call;
    private boolean confim = false;
    private int questionState;
    private ExamCoreStyleConfigBean styleConfigBean;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;

    public void setQuestionState(int questionState) {
        this.questionState = questionState;
    }

    public void setConfim(boolean confim) {
        this.confim = confim;
    }

    public void setCall(ExamUserSelectOptionCall call) {
        this.call = call;
    }

    public List<String> getmUserAnswer() {
        return mUserAnswer;
    }

    private int tkType = ExamPaperTypeConfig.EXAM_PAPER_MNZT;

    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        if (getItemCount() > 0) {
            notifyItemRangeChanged(0, getItemCount());
        }
    }

    public void setTkType(int tkType) {
        this.tkType = tkType;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setmRightAnswer(List<String> mRightAnswer) {
        this.mRightAnswer = mRightAnswer;
    }

    public void setmUserAnswer(List<String> mUserAnswer) {
        this.mUserAnswer = mUserAnswer;
    }

    public ExamOptionAdapter() {
        super(R.layout.core_exam_option_item);
        mExamOptionRepository = new ExamOptionRepository(this);
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    protected void bind(BaseViewHolder holder, ExamPackAccessDataBean.RecordsBean.QuestionsBean item, int position) {
        binding = holder.getBinding();
        binding.setData(item);
        binding.setReposity(mExamOptionRepository);
        binding.setPostion(String.valueOf(position));
        int state = 0;
        if (confim || ExamUtils.getInstance().getExamQuestionResultStyle(tkType) == ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_SELECT_NO) {
            state = mExamOptionRepository.getUserAnswerState(mRightAnswer, mUserAnswer, position, tkType);
        } else {
            state = mExamOptionRepository.getUserDuoAnswerState(mUserAnswer, position, tkType);
        }
        binding.examJxEmlv.exammateriaisBinding.exmaViewTitle.setTextSize(textSizeBean.getSize());
        binding.examOptionFlagsEof.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        if (state == UserAnswerTypeConfig.USER_ANSWER_NO) {
            binding.examOptionMainRel.setBackgroundResource(R.drawable.core_exam_option_select_nom_bg);
            binding.examOptionMainRel.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectBgColorNo())));
            binding.examJxEmlv.exammateriaisBinding.exmaViewTitle.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectTextColorNo()));
        } else if (state == UserAnswerTypeConfig.USER_ANSWER_DO && !this.styleConfigBean.isShowMemoryModel()) {
            binding.examOptionMainRel.setBackgroundResource(R.drawable.core_exam_option_select_right_bg);
            binding.examOptionMainRel.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectBgColorSure())));
            binding.examJxEmlv.exammateriaisBinding.exmaViewTitle.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectTextColorSure()));
        } else if (state == UserAnswerTypeConfig.USER_ANSWER_ERROR && !this.styleConfigBean.isShowMemoryModel()) {
            binding.examOptionMainRel.setBackgroundResource(R.drawable.core_exam_option_select_error_bg);
            binding.examOptionMainRel.setBackgroundTintList(
                    ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectBgColorError())));
            binding.examJxEmlv.exammateriaisBinding.exmaViewTitle.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectTextColorError()));
        } else if (state == UserAnswerTypeConfig.USER_ANSWER_RIGHT) {
            binding.examOptionMainRel.setBackgroundResource(R.drawable.core_exam_option_select_right_bg);
            binding.examOptionMainRel.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectBgColorSure())));
            binding.examJxEmlv.exammateriaisBinding.exmaViewTitle.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectTextColorSure()));
        }else {
            binding.examOptionMainRel.setBackgroundResource(R.drawable.core_exam_option_select_nom_bg);
            binding.examOptionMainRel.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectBgColorNo())));
            binding.examJxEmlv.exammateriaisBinding.exmaViewTitle.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectTextColorNo()));
        }
        binding.examJxEmlv.setContext(item.getText(), "").setImage(item.getImages()).build();
        mExamOptionRepository.setOptionRightImage(state, binding.examOptionSelectImg, tkType,styleConfigBean);
        binding.examOptionFlagsEof.mExamPostion(position).setFlgasColor(state, type).build();
    }

    @Override
    public void OnclickItem(int postion) {
        if (questionState == 1) {
            Bus.post(new MsgEvent(ExamTypeConfig.EXAM_BUYVIP_NO_PERMISS));
            return;
        }
        if (ExamUtils.getInstance().getExamQuestionResultStyle(tkType) == ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_SELECT_NO) {
            return;
        }
        if (tkType == ExamPaperTypeConfig.EXAM_PAPER_MNZT || tkType == ExamPaperTypeConfig.EXAM_PAPER_LNZT ||
                tkType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || tkType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
            if (type != ExamQuestionTypeConfig.EXAM_QUESTION_DUO) {
                mUserAnswer.clear();
            }
        } else {
            if (confim) {
                return;
            }
        }
        String cp = String.valueOf(postion + 1);
        if (mUserAnswer.contains(cp)) {
            mUserAnswer.remove(cp);
        } else {
            mUserAnswer.add(cp);
        }
        if (call != null) {
            call.getUserSelectOption(cp);
        }
        int examResultType = ExamUtils.getInstance().getExamQuestionResultStyle(tkType);
        if (examResultType == ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_DAN_ALSY && type == ExamQuestionTypeConfig.EXAM_QUESTION_DAN) {
            confim = true;
        }
        notifyDataSetChanged();
    }

}
