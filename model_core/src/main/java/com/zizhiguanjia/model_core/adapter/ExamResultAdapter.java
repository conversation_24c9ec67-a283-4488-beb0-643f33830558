package com.zizhiguanjia.model_core.adapter;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.zizhiguanjia.model_core.R;

import java.util.Arrays;

public class ExamResultAdapter extends RecyclerView.Adapter<ExamResultAdapter.ViewHolder> {

    public static final int TYPE_SINGLE = 1;
    public static final int TYPE_MULTIPLE = 2;
    public static final int TYPE_JUDGMENT = 3;

    private int[] numbers;
    private Boolean[] states; // true: 正确, false: 错误, null: 未做
    private int type;
    
    // 点击事件监听器接口
    public interface OnItemClickListener {
        void onItemClick(int questionNumber, boolean isWrong);
    }
    
    private OnItemClickListener listener;
    
    // 设置点击监听器
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.listener = listener;
    }

    public ExamResultAdapter(int type) {
        this.type = type;
    }

    public void setDataItems(int[] numbers, Boolean[] states) {
        this.numbers = numbers;
        this.states = states;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_exam_result_question, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        int number = numbers[position];
        Boolean state = states[position];
        
        holder.tvNumber.setText(String.valueOf(number));
        
        if (state == null) {
            // 未做题
            holder.tvNumber.setBackgroundResource(R.drawable.bg_exam_result_undone);
            holder.tvNumber.setTextColor(Color.BLACK);
//            holder.itemView.setOnClickListener(null); // 未做题不响应点击
            setItemClickListener(holder, number, false);
        } else if (state) {
            // 正确
            holder.tvNumber.setBackgroundResource(R.drawable.bg_exam_result_correct);
            holder.tvNumber.setTextColor(Color.WHITE);
            // 正确题目也可以点击查看，但我们需要区分它们
            setItemClickListener(holder, number, false);
        } else {
            // 错误
            holder.tvNumber.setBackgroundResource(R.drawable.bg_exam_result_wrong);
            holder.tvNumber.setTextColor(Color.WHITE);
            setItemClickListener(holder, number, true);
        }
    }
    
    // 设置项目点击监听器
    private void setItemClickListener(ViewHolder holder, int questionNumber, boolean isWrong) {
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> {
                listener.onItemClick(questionNumber, isWrong);
            });
        }
    }

    @Override
    public int getItemCount() {
        return numbers == null ? 0 : numbers.length;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvNumber;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvNumber = itemView.findViewById(R.id.tv_question_number);
        }
    }
} 