package com.zizhiguanjia.model_core.adapter;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.view.View;
import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.databinding.CoreExamCaseQuestionTitleItemBinding;
import com.zizhiguanjia.model_core.listener.ExamCaseQuestionQhCall;

public class ExamCaseQuestionHListAdapter extends BaseAdapter<String> {
//    private ExamCaseQuestionTitleItemBinding binding;
    private CoreExamCaseQuestionTitleItemBinding binding;
    private ExamCaseQuestionQhCall caseQuestionQhCall;
    public int selectOpstion=0;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    private ExamCoreStyleConfigBean styleConfigBean;
    public ExamCaseQuestionHListAdapter(ExamCaseQuestionQhCall caseQuestionQhCall) {
        super(R.layout.core_exam_case_question_title_item);
        this.caseQuestionQhCall=caseQuestionQhCall;
    }

    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        notifyItemRangeChanged(0,getItemCount());
    }

    @Override
    protected void bind(BaseViewHolder holder, String item, int position) {
        binding = holder.getBinding();
        binding.setDes(item);
        binding.setSelect(selectOpstion);
        binding.setModel(this);
        binding.setPostion(position);
        if (themeBean != null) {
            if (position == selectOpstion) {
                binding.lnBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionTabSelectBgColor())));
                binding.tvText.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionTabSelectStoreColor())));
                binding.tvText.setTextColor(Color.parseColor(themeBean.getQuestionTabSelectTextColor()));
            } else {
                binding.lnBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionTabUnSelectBgColor())));
                binding.tvText.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionTabUnSelectStoreColor())));
                binding.tvText.setTextColor(Color.parseColor(themeBean.getQuestionTabUnSelectTextColor()));
            }
        }
    }
    public void onclick(View view,int postion){
        selectOpstion=postion;
        caseQuestionQhCall.userQhQuestion(selectOpstion);
        notifyItemRangeChanged(0,getItemCount());
    }
}
//  <TextView
//android:onClick="@{(view)->model.onclick(view,postion)}"
//android:textSize="14sp"
//android:textColor="@{select==postion?@color/white:@color/core_exam_case_title_color}"
//android:text="@{des}"
//android:paddingRight="15.5dp"
//android:paddingLeft="15.5dp"
//android:paddingBottom="8.5dp"
//android:paddingTop="8.5dp"
//android:background="@{postion==select?@drawable/core_exam_case_question_title_bg:@drawable/core_exam_case_question_title_no_bg}"
//android:layout_width="wrap_content"
//android:layout_height="wrap_content"/>
