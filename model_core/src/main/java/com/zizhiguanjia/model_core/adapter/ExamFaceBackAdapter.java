package com.zizhiguanjia.model_core.adapter;

import android.view.View;
import android.widget.TextView;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.databinding.CoreExamBottomFaceviewItemBinding;
import com.zizhiguanjia.model_core.listener.FaceBackLister;
import com.zizhiguanjia.model_core.model.ExamFaceBackModel;

import java.util.HashSet;
import java.util.Set;

public class ExamFaceBackAdapter extends BaseAdapter<ExamFaceBackModel> {
    private Set<Long>selectOption=new HashSet<>();
    private FaceBackLister faceBackLister;
    public Set<Long> getSelectOption() {
        return selectOption;
    }
    public void cleanDataList(){
        selectOption.clear();
    }
    private CoreExamBottomFaceviewItemBinding examBottomFaceviewItemBinding;
    public ExamFaceBackAdapter(FaceBackLister lister) {
        super(R.layout.core_exam_bottom_faceview_item);
        this.faceBackLister=lister;
    }
    @Override
    protected void bind(BaseViewHolder holder, ExamFaceBackModel item, int position) {
        examBottomFaceviewItemBinding=holder.getBinding();
        examBottomFaceviewItemBinding.setData(item);
        examBottomFaceviewItemBinding.ivImage.setImageResource(selectOption.contains(item.getId())?item.getIconResYes():item.getIconResNo());
        examBottomFaceviewItemBinding.examFacebackBg.setBackgroundResource(selectOption.contains(item.getId())?R.drawable.core_exam_faceback_lan_bg:R.drawable.core_exam_faceback_bg);
        examBottomFaceviewItemBinding.examFacebackMainRel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(selectOption.contains(item.getId())){
                    selectOption.remove(item.getId());
                }else {
                    selectOption.add(item.getId());
                }
                faceBackLister.onUserOption(getSelectOption().size()<=0?false:true);
                notifyDataSetChanged();
            }
        });
    }
}
