package com.zizhiguanjia.model_core.adapter;

import android.app.Activity;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.model_core.config.PageTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreExamContentItemBinding;
import com.zizhiguanjia.model_core.listener.ExamContentSelectQuestionTabCall;
import com.zizhiguanjia.model_core.listener.ExamUserAnswerListener;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;

import java.util.HashMap;
import java.util.Map;

import static com.zizhiguanjia.model_core.config.ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_SELECT_NO;

public class ExamContentAdapter extends BaseAdapter<AnswerGroupBean> {
    private CoreExamContentItemBinding examContentBinding;
    private ExamUserAnswerListener examContentUserAnswerCall;
    private Activity activity;
    private int paperType;
    private int paperTypeDefault;
    private int currentQun;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    private ExamCoreStyleConfigBean styleConfigBean;
    private Map<Integer,Integer> positionTabQuestionSelectMap = new HashMap<>();

    public void setExamContentUserAnswerCall(ExamUserAnswerListener examContentUserAnswerCall) {
        this.examContentUserAnswerCall = examContentUserAnswerCall;
    }

    public ExamContentAdapter(Activity activity, int paperType) {
        super(R.layout.core_exam_content_item);
        this.activity = activity;
        this.paperType = paperType;
        this.paperTypeDefault = paperType;
    }

    /**
     * 设置主题数据
     */
    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean, ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        this.styleConfigBean = styleConfigBean;
        if(styleConfigBean.isShowMemoryModel()){
            paperType = EXAM_RESULT_STYLE_SELECT_NO;
        }else {
            paperType = paperTypeDefault;
        }
        notifyItemRangeChanged(0,getItemCount());
    }

    @Override
    protected void bind(BaseViewHolder holder, AnswerGroupBean item, int position) {
        examContentBinding=holder.getBinding();
        examContentBinding.ecvExamContent.setThemeConfigData(this.styleConfigBean,this.themeBean,this.textSizeBean);
        LogUtils.e("开始更新------>>>"+item.getQuestionNum());
        if(item.getPageState()== PageTypeConfig.PAGE_STATE_SUCCESS){
            examContentBinding.ecvExamContent.reshData(item,activity,paperType);
        }
        examContentBinding.ecvExamContent.setExamContentActionCall(examContentUserAnswerCall);
        examContentBinding.ecvExamContent.setExamContentSelectQuestionTabCall(new ExamContentSelectQuestionTabCall() {
            @Override
            public void onTabChange(int tabPosition) {
                positionTabQuestionSelectMap.put(position,tabPosition);
            }
        });

    }
    public void setCurrentNum(int num){
        this.currentQun=num;
    }
    public String getQuestionId(int index){
        return String.valueOf(getData().get(index).getAnswerSheetOptionBean().getQuestionId());
    }

    public int getQuestionTabInfo(int position) {
        return positionTabQuestionSelectMap.containsKey(position) ? positionTabQuestionSelectMap.get(position):0;
    }
}
