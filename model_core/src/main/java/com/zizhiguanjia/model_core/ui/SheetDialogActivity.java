package com.zizhiguanjia.model_core.ui;

import android.os.Bundle;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_weiget.views.MultipleStatusView;
import com.zizhiguanjia.lib_base.base.ContainerActivity;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.adapter.ExamSheetChildAdapter;
import com.zizhiguanjia.model_core.adapter.ExamSheetListAdapter;

public class SheetDialogActivity extends ContainerActivity {
    private RecyclerView mRecyclerView;
    private ExamSheetListAdapter adapter;
    private TextView mCurrentTv, mCountTv;
    private LinearLayout llTags;
    private TextView tvTitle;
    private ExamSheetChildAdapter examSheetChildAdapter;
    private MultipleStatusView multipleStatusView;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ARouter.getInstance().inject(this);
        setContentView(R.layout.core_exam_sheetanswer_layout);

    }
}
