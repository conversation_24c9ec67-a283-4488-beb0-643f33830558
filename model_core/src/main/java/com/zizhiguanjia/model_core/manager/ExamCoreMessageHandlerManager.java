package com.zizhiguanjia.model_core.manager;

import com.zizhiguanjia.model_core.listener.IExamCoreMessage;
import com.zizhiguanjia.model_core.thread.ExamCoreFrameThread;

import java.util.concurrent.ArrayBlockingQueue;
public class ExamCoreMessageHandlerManager implements IExamCoreMessage {
    private static ExamCoreMessageHandlerManager examMessageHandler;
    private ArrayBlockingQueue<Integer> mDetectResultQueue;
    private ExamCoreFrameThread examFrameThread;
    public ExamCoreFrameThread getExamFrameThread() {
        return examFrameThread;
    }
    public static ExamCoreMessageHandlerManager getInstance(){
        if(examMessageHandler==null){
            synchronized (ExamCoreMessageHandlerManager.class){
                return examMessageHandler=new ExamCoreMessageHandlerManager();
            }
        }
        return examMessageHandler;
    }
    @Override
    public void initExamMessageHandler(){
        mDetectResultQueue = new ArrayBlockingQueue<Integer>(10);
        examFrameThread = new ExamCoreFrameThread();
        examFrameThread.start();
    }
    @Override
    public ArrayBlockingQueue<Integer> getmDetectResultQueue() {
        return mDetectResultQueue;
    }
    @Override
    public void setmDetectResultQueue(ArrayBlockingQueue<Integer> mDetectResultQueue) {
        this.mDetectResultQueue = mDetectResultQueue;
    }
    @Override
    public void clearExamCoreMessage(){
        examFrameThread.setLastIndex(-1);
    }
}
