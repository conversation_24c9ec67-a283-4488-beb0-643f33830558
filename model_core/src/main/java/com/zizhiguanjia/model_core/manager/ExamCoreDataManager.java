package com.zizhiguanjia.model_core.manager;

import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.task.RxAsyncTask;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.config.PageTypeConfig;
import com.zizhiguanjia.model_core.listener.ExamDataClearListenter;
import com.zizhiguanjia.model_core.listener.IExamCoreData;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.AnswerSheetOptionBean;
import com.zizhiguanjia.model_core.model.ExamAnswerSheetBean;
import com.zizhiguanjia.model_core.model.ExamChaptersBean;
import com.zizhiguanjia.model_core.utils.ExamUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExamCoreDataManager implements IExamCoreData {
    private static ExamCoreDataManager instance;

    public static ExamCoreDataManager getInstance() {
        if (instance == null) {
            synchronized (ExamCoreDataManager.class) {
                instance = new ExamCoreDataManager();
            }
        }
        return instance;
    }

    @Override
    public void getQuestionIdByLocalAddress(int currentAddress, List<AnswerGroupBean> createSheetQuestionBeans, ExamDataClearListenter examDataClearListenter,boolean first) {
        RxJavaUtils.executeAsyncTask(new RxAsyncTask<List<AnswerGroupBean>, Map<String,String>>(createSheetQuestionBeans) {
            @Override
            public void doInUIThread(Map<String,String> s) {
                if(s==null||s.isEmpty()){
                    examDataClearListenter.afterGetLocalData(null);
                }else {
                    examDataClearListenter.afterGetLocalData(s.get("ids")+"|"+s.get("start")+"|"+s.get("count"));
                }
            }

            @Override
            public Map<String,String> doInIOThread(List<AnswerGroupBean> createSheetQuestionBeans) {
                try {
                    List<String> address = ExamUtils.getInstance().getLeftAndRightFlags(currentAddress);
                    Map<String,String> loadress=new HashMap<>();
                    if (address == null || address.size() == 0) {
                        return null;
                    } else {
                        StringBuffer stringBuffer = new StringBuffer();
                        for (String str : address) {
                            int index = Integer.parseInt(str);
                            if (index < createSheetQuestionBeans.size()) {
                                AnswerGroupBean createSheetQuestionBean = createSheetQuestionBeans.get(index);
                                if (createSheetQuestionBean.getPageState() == PageTypeConfig.PAGE_STATE_SUCCESS) {
                                } else {
                                    stringBuffer.append(String.valueOf(createSheetQuestionBean.getQuestionNum())).append(',');
                                }
                            }
                        }
                        String questId = stringBuffer.deleteCharAt(stringBuffer.length() - 1).toString();
                        if (StringUtils.isEmpty(questId)) {
                            return null;
                        } else {
                            String [] questIds=StringUtils.convertStrToArray2(questId);
                            if(questIds.length<=1){
                                if(Integer.parseInt(questIds[0])<currentAddress){
                                    //向前滑动
                                    StringBuffer stringBuffers = new StringBuffer();
                                    stringBuffers.append(questId).append(","+(Integer.parseInt(questIds[0])-1)).append(","+(Integer.parseInt(questIds[0])-2)).append(","+(Integer.parseInt(questIds[0])-3)).append(","+(Integer.parseInt(questIds[0])-4));
                                    questId=stringBuffers.toString();
                                    String[] loas=StringUtils.convertStrToArray2(questId);
                                    loadress.put("start",loas[loas.length-1]);
                                    loadress.put("ids",questId);
                                    loadress.put("count",loas.length+"");
                                }else {
                                    //向后滑动
                                    StringBuffer stringBuffers = new StringBuffer();
                                    stringBuffers.append(questId).append(","+(Integer.parseInt(questIds[0])+1)).append(","+(Integer.parseInt(questIds[0])+2)).append(","+(Integer.parseInt(questIds[0])+3)).append(","+(Integer.parseInt(questIds[0])+4));
                                    questId=stringBuffers.toString();
                                    String[] loas=StringUtils.convertStrToArray2(questId);
                                    loadress.put("start",loas[0]);
                                    loadress.put("count",loas.length+"");
                                    loadress.put("ids",questId);
                                }
                            }else {
                                loadress.put("start",Integer.parseInt(questIds[0])<currentAddress||Integer.parseInt(questIds[0])==1?questIds[0]:questIds[questIds.length-1]);
                                loadress.put("count",questIds.length+"");
                                loadress.put("ids",questId);
                            }
                            return loadress;
                        }
                    }
                } catch (Exception e) {
                    return null;
                }
            }
        });
    }

    @Override
    public void cleanDataToSheetExamData(ExamAnswerSheetBean examAnswerSheetBean, ExamDataClearListenter clearCall) {
        RxJavaUtils.executeAsyncTask(new RxAsyncTask<ExamAnswerSheetBean, List<AnswerGroupBean>>(examAnswerSheetBean) {
            @Override
            public void doInUIThread(List<AnswerGroupBean> answerSheetOptionBeans) {
                LogUtils.e("清理了数据---->>>>"+answerSheetOptionBeans.size());
                clearCall.afterClearSheetData(answerSheetOptionBeans, examAnswerSheetBean.getLocation(),
                        answerSheetOptionBeans.size(),
                        examAnswerSheetBean.getExamType() == ExamPaperTypeConfig.EXAM_PAPER_MNZT ? examAnswerSheetBean.getDuration() : 0, examAnswerSheetBean.getRemainingQuestionsCount());
            }

            @Override
            public List<AnswerGroupBean> doInIOThread(ExamAnswerSheetBean examAnswerSheetBeans) {
                List<AnswerGroupBean> answerSheetOptionBeans = new ArrayList<>();
                int index = 0;
                for (ExamChaptersBean examAnswerSheetBean : examAnswerSheetBeans.getRecords()) {
                    if (!examAnswerSheetBean.getItems().isEmpty()) {
                        for (AnswerSheetOptionBean answerSheetOptionBean : examAnswerSheetBean.getItems()) {
                            if (answerSheetOptionBean.isSee()) {
                                AnswerGroupBean answerGroupBean = new AnswerGroupBean();
                                answerGroupBean.setPageState(PageTypeConfig.PAGE_STATE_LOADING);
                                answerGroupBean.setQuestionNum(answerSheetOptionBean.getQuestionNum());
                                answerGroupBean.setIndexs(index++);
                                answerSheetOptionBeans.add(answerGroupBean);
                            }
                        }
                    }else {
                        LogUtils.e("数据清理------>>>>66666");
                    }
                }
                return answerSheetOptionBeans;
            }
        });
    }

}
