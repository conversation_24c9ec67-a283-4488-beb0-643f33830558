package com.zizhiguanjia.model_core.listener;

import com.zizhiguanjia.model_core.model.AnswerGroupBean;

import java.util.List;

public interface IExamCore {
    void init(ExamCoreListenter examCoreListenter,String paperType,String paperId);
    ExamCoreListenter getListenter();
    void getExamSheetListData(String paperType, String paperValue,boolean newStart,int location);
    void addPostDataQueue(Integer integer, List<AnswerGroupBean> answerGroupBeans);
    void getExamListData(String qid,String paperType,String paperValue,boolean newStart,String count);
}
