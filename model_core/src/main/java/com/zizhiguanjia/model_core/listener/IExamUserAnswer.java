package com.zizhiguanjia.model_core.listener;

import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.navigator.ExamNavigator;

import java.util.List;

public interface IExamUserAnswer {
    void initData(AnswerGroupBean answerGroupBean, int paperType, ExamNavigator examNavigator);
    void showContextView(AnswerGroupBean answerGroupBean, int paperType, ExamNavigator examNavigator);
    void initAnswerGroupBean(AnswerGroupBean answerGroupBean);
    ExamPackAccessDataBean.RecordsBean getAnswerItemBean();
    void initAlChoiceData();
    void initDataCommon();
    void  getContextTitle();
    void  getOptionList();
    void getResultData(boolean result);
    List<String> getRightList();
    void userConfim(String option, ExamUserAnswerListener examToAutoPageListener);
}
