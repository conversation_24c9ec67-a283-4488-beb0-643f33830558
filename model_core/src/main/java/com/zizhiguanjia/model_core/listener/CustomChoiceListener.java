package com.zizhiguanjia.model_core.listener;


import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.model.OriginImageBean;

import java.util.List;

public interface CustomChoiceListener {
    void showResultView(List<String> mUserAnswers, List<String> mRightAnswers, String txt, List<OriginImageBean> objects, boolean jtjq);
    void showContextTitlteView(String context,int questionType,List<OriginImageBean> iamges);
    void showOptionView(List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> examOptionBeans, int questionType, List<String> userAnswer, List<String> answer, int questionState);
    void onQuestComfig(boolean comfig);
    void onQuestionTypeState(int state);
    void getQuestionsListDatas(List<String> arrs);
    void showOptionTagsList(String context,int questionType,List<OriginImageBean> iamges);
    void userConfim(String option, ExamToAutoPageListener examToAutoPageListener);
    List<String> getRightList();
    List<String> getUserAnswer();
    void userQhQuestion(int index);
}
