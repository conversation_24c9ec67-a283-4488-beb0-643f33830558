package com.zizhiguanjia.model_core.listener;

import android.view.View;

import com.zizhiguanjia.model_core.fragment.ExamCoreFragment;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamAnswerSheetBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.navigator.ExamCoreNavigator;

import java.util.List;

public interface ExamCoreListenter {
    void initData(String title, String paperType, String paperId, boolean restart, int location, ExamCoreNavigator examCoreNavigator, ExamCoreFragment examCoreFragment);
    void initParam(String title, String paperType, String paperId, boolean restart, int location, ExamCoreNavigator examCoreNavigator, ExamCoreFragment examCoreFragment);
    void getExamSheetDataListener(ExamAnswerSheetBean commonGroupExamBeanLists);
    boolean checkExamAnswerSheetBeanValues(ExamAnswerSheetBean examAnswerSheetBean);
    void getExamCount(ExamAnswerSheetBean commonGroupExamBeans);
    int getExamSheetCount();
    boolean checkAnswerGroupBeanValues(List<AnswerGroupBean> answerSheetOptionBeans);
    void initDataSheet(List<AnswerGroupBean> answerSheetOptionBeans, int localAddress, int QuestionCount, int Duration, int RemainingQuestionsCount);
    void getMaxSeeNum(List<AnswerGroupBean> answerSheetOptionBeans);
    int getIndexByQuestionNum(int index);
    int getCurrentExamQuestionNumByIndex(int index);
    void getPacketAccessData(String local);
    void getReadPostHttpExam(int index);
    void getReadStartPostHttpExam(String qid,String count );
    void getExamListData(List<ExamPackAccessDataBean.RecordsBean> lists);
    boolean checkRecordsBeanValues(List<ExamPackAccessDataBean.RecordsBean> lists);
    void findNoVipPermiss(int postion,int remainingQuestionsCount);
    boolean NeedCheckVipPermiss();
    void checkPermiss(int postion,int remainingQuestionsCount);
    int getQuestionNumByIndex(int index);
    void calculateResidueCount();
    int getResidueCount();
    void updataModelByQuestionNum(int questionNum, boolean rights);
    void destory();
    boolean canPostRecordData();
    void reshExamData(int index);
    void shaperWechat(String questionId);
    void noExamPermiss(boolean need,boolean ponter);
    void viewPageListener(int postion,boolean first);
    void checkGuilder(int postion,boolean first);
    void initFaceBackPopup();
    void initLoadingPopup();
    void checkPointerRouth();
    void restartExam();
    String getUserConfimHandPager();
    void canCelQuestion(String questionId);
    void saveQuestion(String questionId, String userAnswer,String paperType);
    void postHandPaper();
    void postFaceBackData(String opion,String questionId,String context, int currentPostion, AnswerGroupBean selectOption, int questionTabInfo);
    void dissFaceBackView();
    void onClick(View view);
    void initDownTime(long time);
    void checkPageFinsh(int index,boolean onclick);
    boolean lastOptionHaveAnswer();
    void showPageFinshView();
}
