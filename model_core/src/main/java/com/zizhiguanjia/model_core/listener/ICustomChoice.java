package com.zizhiguanjia.model_core.listener;


import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;

import org.json.JSONException;

import java.util.List;

public interface ICustomChoice {
    void initData(int examType);
    void init(CustomChoiceListener customChoiceListener, AnswerGroupBean answerGroupBean, int examType);
    CustomChoiceListener getListener();
    List<String> getRightList();
    ExamPackAccessDataBean.RecordsBean getAnswerItemBean();
    void getResultData(boolean isconfig);
    void getContextTitle();
    void  getOptionList();
    void  getOptionTagsList();
    void getQuestionsListDatas();
    void reshIndex(int index);
    void userConfim(String option, ExamToAutoPageListener examToAutoPageListener);
    List<String> getUserAnswer();
    void saveQhData(String datas);
    String getUserSaveAnswerStr() throws JSONException;
    boolean checkQuestionRight();
}
