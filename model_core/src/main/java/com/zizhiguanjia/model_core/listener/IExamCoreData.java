package com.zizhiguanjia.model_core.listener;

import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamAnswerSheetBean;

import java.util.List;

public interface IExamCoreData {
    void getQuestionIdByLocalAddress(int currentAddress,List<AnswerGroupBean> createSheetQuestionBeans,ExamDataClearListenter examDataClearListenter,boolean first);
    void cleanDataToSheetExamData(ExamAnswerSheetBean examAnswerSheetBean, ExamDataClearListenter clearCall);
}
