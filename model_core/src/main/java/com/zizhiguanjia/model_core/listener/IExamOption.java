package com.zizhiguanjia.model_core.listener;

import android.view.View;
import android.widget.ImageView;

import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;

import java.util.List;

public interface IExamOption {
    int getUserAnswerState(List<String> mRightAnswer,List<String> mUserAnswer,int currentPostion,int type);
    boolean checkDiffrent5(List<String> mRightAnswer,List<String> mUserAnswer);
    void setOptionRightImage(int state,ImageView imageView,int examType, ExamCoreStyleConfigBean styleConfigBean);
    void OnclickItem(View mView,String postion);
    int getUserDuoAnswerState(List<String> mUserAnswer,int currentPostion,int type);
}
