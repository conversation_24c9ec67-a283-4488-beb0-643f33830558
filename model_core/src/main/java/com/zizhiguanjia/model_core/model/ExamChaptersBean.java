package com.zizhiguanjia.model_core.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class ExamChaptersBean implements Parcelable {
    private int QuestionCount;
    private String QuestionName;
    private int Sort;
    private List<AnswerSheetOptionBean> Items;

    public int getQuestionCount() {
        return QuestionCount;
    }

    public void setQuestionCount(int questionCount) {
        QuestionCount = questionCount;
    }

    public String getQuestionName() {
        return QuestionName;
    }

    public void setQuestionName(String questionName) {
        QuestionName = questionName;
    }

    public int getSort() {
        return Sort;
    }

    public void setSort(int sort) {
        Sort = sort;
    }

    public List<AnswerSheetOptionBean> getItems() {
        return Items;
    }

    public void setItems(List<AnswerSheetOptionBean> items) {
        Items = items;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.QuestionCount);
        dest.writeString(this.QuestionName);
        dest.writeInt(this.Sort);
        dest.writeTypedList(this.Items);
    }

    public ExamChaptersBean() {
    }

    protected ExamChaptersBean(Parcel in) {
        this.QuestionCount = in.readInt();
        this.QuestionName = in.readString();
        this.Sort = in.readInt();
        this.Items = in.createTypedArrayList(AnswerSheetOptionBean.CREATOR);
    }

    public static final Creator<ExamChaptersBean> CREATOR = new Creator<ExamChaptersBean>() {
        @Override
        public ExamChaptersBean createFromParcel(Parcel source) {
            return new ExamChaptersBean(source);
        }

        @Override
        public ExamChaptersBean[] newArray(int size) {
            return new ExamChaptersBean[size];
        }
    };
}
