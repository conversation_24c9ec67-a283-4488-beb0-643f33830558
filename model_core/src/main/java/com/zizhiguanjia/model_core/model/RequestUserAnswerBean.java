package com.zizhiguanjia.model_core.model;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

import java.util.List;

public class RequestUserAnswerBean implements Parcelable {
    private String QuestionId;
    private boolean IsRight;
    private String QuestionNum;
    private List<String> UserAnswer;

    public String getId() {
        return QuestionId;
    }

    public void setId(String id) {
        QuestionId = id;
    }


    public String getQuestionNum() {
        return QuestionNum;
    }

    public void setQuestionNum(String questionNum) {
        QuestionNum = questionNum;
    }

    public List<String> getUserAnswer() {
        return UserAnswer;
    }

    public void setUserAnswer(List<String> userAnswer) {
        UserAnswer = userAnswer;
    }

    public boolean isRight() {
        return IsRight;
    }

    public void setRight(boolean right) {
        IsRight = right;
    }

    public RequestUserAnswerBean() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.QuestionId);
        dest.writeByte(this.IsRight ? (byte) 1 : (byte) 0);
        dest.writeString(this.QuestionNum);
        dest.writeStringList(this.UserAnswer);
    }

    protected RequestUserAnswerBean(Parcel in) {
        this.QuestionId = in.readString();
        this.IsRight = in.readByte() != 0;
        this.QuestionNum = in.readString();
        this.UserAnswer = in.createStringArrayList();
    }

    public static final Creator<RequestUserAnswerBean> CREATOR = new Creator<RequestUserAnswerBean>() {
        @Override
        public RequestUserAnswerBean createFromParcel(Parcel source) {
            return new RequestUserAnswerBean(source);
        }

        @Override
        public RequestUserAnswerBean[] newArray(int size) {
            return new RequestUserAnswerBean[size];
        }
    };

    @Override
    public String toString() {
        return "RequestUserAnswerBean{" +
                "QuestionId='" + QuestionId + '\'' +
                ", IsRight=" + IsRight +
                ", QuestionNum='" + QuestionNum + '\'' +
                ", UserAnswer=" + UserAnswer +
                '}';
    }
}
