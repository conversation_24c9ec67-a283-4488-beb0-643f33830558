package com.zizhiguanjia.model_core.model;

import android.os.Parcel;
import android.os.Parcelable;
public class AnswerSheetOptionBean implements Parcelable {
    private int QuestionNum;
    private int QuestionId;
    private boolean IsDone;
    private boolean IsRight;
    private boolean IsSee;
    private int DataState;
    private boolean IsCreate;

    public boolean isCreate() {
        return IsCreate;
    }

    public void setCreate(boolean create) {
        IsCreate = create;
    }

    public int getDataState() {
        return DataState;
    }

    public void setDataState(int dataState) {
        DataState = dataState;
    }

    public int getQuestionNum() {
        return QuestionNum;
    }

    public void setQuestionNum(int questionNum) {
        QuestionNum = questionNum;
    }

    public int getQuestionId() {
        return QuestionId;
    }

    public void setQuestionId(int questionId) {
        QuestionId = questionId;
    }

    public boolean isDone() {
        return IsDone;
    }

    public void setDone(boolean done) {
        IsDone = done;
    }

    public boolean isRight() {
        return IsRight;
    }

    public void setRight(boolean right) {
        IsRight = right;
    }

    public boolean isSee() {
        return IsSee;
    }

    public void setSee(boolean see) {
        IsSee = see;
    }

    public AnswerSheetOptionBean() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.QuestionNum);
        dest.writeInt(this.QuestionId);
        dest.writeByte(this.IsDone ? (byte) 1 : (byte) 0);
        dest.writeByte(this.IsRight ? (byte) 1 : (byte) 0);
        dest.writeByte(this.IsSee ? (byte) 1 : (byte) 0);
        dest.writeInt(this.DataState);
        dest.writeByte(this.IsCreate ? (byte) 1 : (byte) 0);
    }

    protected AnswerSheetOptionBean(Parcel in) {
        this.QuestionNum = in.readInt();
        this.QuestionId = in.readInt();
        this.IsDone = in.readByte() != 0;
        this.IsRight = in.readByte() != 0;
        this.IsSee = in.readByte() != 0;
        this.DataState = in.readInt();
        this.IsCreate = in.readByte() != 0;
    }

    public static final Creator<AnswerSheetOptionBean> CREATOR = new Creator<AnswerSheetOptionBean>() {
        @Override
        public AnswerSheetOptionBean createFromParcel(Parcel source) {
            return new AnswerSheetOptionBean(source);
        }

        @Override
        public AnswerSheetOptionBean[] newArray(int size) {
            return new AnswerSheetOptionBean[size];
        }
    };
}
