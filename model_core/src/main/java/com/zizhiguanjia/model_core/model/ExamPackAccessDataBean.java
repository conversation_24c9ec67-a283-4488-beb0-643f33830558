package com.zizhiguanjia.model_core.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class ExamPackAccessDataBean {

    private List<RecordsBean> Records;

    public List<RecordsBean> getRecords() {
        return Records;
    }

    public void setRecords(List<RecordsBean> Records) {
        this.Records = Records;
    }
    public static class RecordsBean implements Parcelable {
        private String Content;
        private String ContentImage;
        private int QuestionType;
        private boolean IsCollection;
        private int QuestionNum;
        private int QuestionId;
        private int Id;
        private boolean IsRight;
        private List<QuestionsBean> Questions;
        private List<AnswersBean> Answers;
        private List<AnswerKeyBean> AnswerKey;
        private List<String> UserAnswer;
        private boolean confim;
        private int QuestionState;
        private String Skill;
        private String ErrorTip;
        private String ExamCodeTitle;

        public String getExamCodeTitle() {
            return ExamCodeTitle;
        }

        public void setExamCodeTitle(String examCodeTitle) {
            ExamCodeTitle = examCodeTitle;
        }

        private List<OriginImageBean>ContentImages;
        private List<RecordsBean> Childs;

        public void setErrorTip(String errorTip) {
            ErrorTip = errorTip;
        }

        public String getErrorTip() {
            return ErrorTip;
        }

        public List<RecordsBean> getChilds() {
            return Childs;
        }

        public List<OriginImageBean> getImages() {
            return ContentImages;
        }

        public void setImages(List<OriginImageBean> images) {
            ContentImages = images;
        }

        public void setChilds(List<RecordsBean> childs) {
            Childs = childs;
        }

        public String getSkill() {
            return Skill;
        }

        public void setSkill(String skill) {
            Skill = skill;
        }


        public int getQuestionState() {
            return QuestionState;
        }

        public void setQuestionState(int questionState) {
            QuestionState = questionState;
        }

        public boolean isCollection() {
            return IsCollection;
        }

        public void setCollection(boolean collection) {
            IsCollection = collection;
        }

        public boolean isRight() {
            return IsRight;
        }

        public void setRight(boolean right) {
            IsRight = right;
        }

        public boolean isConfim() {
            return confim;
        }

        public void setConfim(boolean confim) {
            this.confim = confim;
        }

        public String getContent() {
            return Content;
        }

        public void setContent(String Content) {
            this.Content = Content;
        }

        public String getContentImage() {
            return ContentImage;
        }

        public void setContentImage(String ContentImage) {
            this.ContentImage = ContentImage;
        }

        public int getQuestionType() {
            return QuestionType;
        }

        public void setQuestionType(int QuestionType) {
            this.QuestionType = QuestionType;
        }

        public boolean isIsCollection() {
            return IsCollection;
        }

        public void setIsCollection(boolean IsCollection) {
            this.IsCollection = IsCollection;
        }

        public int getQuestionNum() {
            return QuestionNum;
        }

        public void setQuestionNum(int QuestionNum) {
            this.QuestionNum = QuestionNum;
        }

        public int getQuestionId() {
            return QuestionId;
        }

        public void setQuestionId(int QuestionId) {
            this.QuestionId = QuestionId;
        }

        public int getId() {
            return Id;
        }

        public void setId(int Id) {
            this.Id = Id;
        }

        public boolean isIsRight() {
            return IsRight;
        }

        public void setIsRight(boolean IsRight) {
            this.IsRight = IsRight;
        }

        public List<QuestionsBean> getQuestions() {
            return Questions;
        }

        public void setQuestions(List<QuestionsBean> Questions) {
            this.Questions = Questions;
        }

        public List<AnswersBean> getAnswers() {
            return Answers;
        }

        public void setAnswers(List<AnswersBean> Answers) {
            this.Answers = Answers;
        }

        public List<AnswerKeyBean> getAnswerKey() {
            return AnswerKey;
        }

        public void setAnswerKey(List<AnswerKeyBean> AnswerKey) {
            this.AnswerKey = AnswerKey;
        }

        public List<String> getUserAnswer() {
            return UserAnswer;
        }

        public void setUserAnswer(List<String> UserAnswer) {
            this.UserAnswer = UserAnswer;
        }

        public static class QuestionsBean implements Parcelable {
            /**
             * Text : 20万元以上50万元以下
             * Image :
             */

            private String Text;
            private String Image;
            private String userAnswer;
            private boolean confim;
            private List<OriginImageBean> Images;
            public List<OriginImageBean> getImages() {
                return Images;
            }

            public void setImages(List<OriginImageBean> images) {
                Images = images;
            }

            public String getUserAnswer() {
                return userAnswer;
            }

            public void setUserAnswer(String userAnswer) {
                this.userAnswer = userAnswer;
            }

            public boolean isConfim() {
                return confim;
            }

            public void setConfim(boolean confim) {
                this.confim = confim;
            }

            public String getText() {
                return Text;
            }

            public void setText(String Text) {
                this.Text = Text;
            }

            public String getImage() {
                return Image;
            }

            public void setImage(String Image) {
                this.Image = Image;
            }

            public QuestionsBean() {
            }

            @Override
            public int describeContents() {
                return 0;
            }

            @Override
            public void writeToParcel(Parcel dest, int flags) {
                dest.writeString(this.Text);
                dest.writeString(this.Image);
                dest.writeString(this.userAnswer);
                dest.writeByte(this.confim ? (byte) 1 : (byte) 0);
            }

            protected QuestionsBean(Parcel in) {
                this.Text = in.readString();
                this.Image = in.readString();
                this.userAnswer = in.readString();
                this.confim = in.readByte() != 0;
            }

            public static final Creator<QuestionsBean> CREATOR = new Creator<QuestionsBean>() {
                @Override
                public QuestionsBean createFromParcel(Parcel source) {
                    return new QuestionsBean(source);
                }

                @Override
                public QuestionsBean[] newArray(int size) {
                    return new QuestionsBean[size];
                }
            };
        }

        public static class AnswersBean implements Parcelable {

            /**
             * Text : 4
             * Image :
             */

            private String Text;
            private String Image;
            private List<OriginImageBean> Images;

            public List<OriginImageBean> getImages() {
                return Images;
            }

            public void setImages(List<OriginImageBean> images) {
                Images = images;
            }

            public String getText() {
                return Text;
            }

            public void setText(String Text) {
                this.Text = Text;
            }

            public String getImage() {
                return Image;
            }

            public void setImage(String Image) {
                this.Image = Image;
            }

            @Override
            public int describeContents() {
                return 0;
            }

            @Override
            public void writeToParcel(Parcel dest, int flags) {
                dest.writeString(this.Text);
                dest.writeString(this.Image);
            }

            public AnswersBean() {
            }

            protected AnswersBean(Parcel in) {
                this.Text = in.readString();
                this.Image = in.readString();
            }

            public static final Parcelable.Creator<AnswersBean> CREATOR = new Parcelable.Creator<AnswersBean>() {
                @Override
                public AnswersBean createFromParcel(Parcel source) {
                    return new AnswersBean(source);
                }

                @Override
                public AnswersBean[] newArray(int size) {
                    return new AnswersBean[size];
                }
            };
        }

        public static class AnswerKeyBean implements Parcelable {
            /**
             * Text : 事故发生单位及其有关人员有下列行为之一的，对事故发生单位处【100万元以上500万元以下】的罚款；对主要负责人、直接负责的主管人员和其他直接责任人员处上一年年收入60%至100%的罚款；（五）在事故调查中作伪证或者指使他人作伪证的；（六）事故发生后逃匿的。【通关秘籍】事故单位有以下情况，单位罚100万-500万，主要责任人者罚上一年收入的60%-100%：①欺骗（谎报瞒报；伪造现场；作伪证）②逃匿（拒绝接受调查；事故后逃匿）
             * Image :
             */

            private String Text;
            private String Image;
            private List<OriginImageBean> Images;

            public List<OriginImageBean> getImages() {
                return Images;
            }

            public void setImages(List<OriginImageBean> images) {
                Images = images;
            }

            public String getText() {
                return Text;
            }

            public void setText(String Text) {
                this.Text = Text;
            }

            public String getImage() {
                return Image;
            }

            public void setImage(String Image) {
                this.Image = Image;
            }

            @Override
            public int describeContents() {
                return 0;
            }

            @Override
            public void writeToParcel(Parcel dest, int flags) {
                dest.writeString(this.Text);
                dest.writeString(this.Image);
            }

            public AnswerKeyBean() {
            }

            protected AnswerKeyBean(Parcel in) {
                this.Text = in.readString();
                this.Image = in.readString();
            }

            public static final Parcelable.Creator<AnswerKeyBean> CREATOR = new Parcelable.Creator<AnswerKeyBean>() {
                @Override
                public AnswerKeyBean createFromParcel(Parcel source) {
                    return new AnswerKeyBean(source);
                }

                @Override
                public AnswerKeyBean[] newArray(int size) {
                    return new AnswerKeyBean[size];
                }
            };
        }

        public RecordsBean() {
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.Content);
            dest.writeString(this.ContentImage);
            dest.writeInt(this.QuestionType);
            dest.writeByte(this.IsCollection ? (byte) 1 : (byte) 0);
            dest.writeInt(this.QuestionNum);
            dest.writeInt(this.QuestionId);
            dest.writeInt(this.Id);
            dest.writeByte(this.IsRight ? (byte) 1 : (byte) 0);
            dest.writeTypedList(this.Questions);
            dest.writeTypedList(this.Answers);
            dest.writeTypedList(this.AnswerKey);
            dest.writeStringList(this.UserAnswer);
            dest.writeByte(this.confim ? (byte) 1 : (byte) 0);
        }

        protected RecordsBean(Parcel in) {
            this.Content = in.readString();
            this.ContentImage = in.readString();
            this.QuestionType = in.readInt();
            this.IsCollection = in.readByte() != 0;
            this.QuestionNum = in.readInt();
            this.QuestionId = in.readInt();
            this.Id = in.readInt();
            this.IsRight = in.readByte() != 0;
            this.Questions = in.createTypedArrayList(QuestionsBean.CREATOR);
            this.Answers = in.createTypedArrayList(AnswersBean.CREATOR);
            this.AnswerKey = in.createTypedArrayList(AnswerKeyBean.CREATOR);
            this.UserAnswer = in.createStringArrayList();
            this.confim = in.readByte() != 0;
        }

        public static final Creator<RecordsBean> CREATOR = new Creator<RecordsBean>() {
            @Override
            public RecordsBean createFromParcel(Parcel source) {
                return new RecordsBean(source);
            }

            @Override
            public RecordsBean[] newArray(int size) {
                return new RecordsBean[size];
            }
        };
    }
}
