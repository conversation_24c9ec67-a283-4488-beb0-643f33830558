package com.zizhiguanjia.model_core.model;

import android.os.Parcel;
import android.os.Parcelable;

public class OriginImageBean{
    private String SourceUrl;
    private String ThumbnailUrl;
    private String Sort;

    @Override
    public String toString() {
        return "OriginImageBean{" +
                "SourceUrl='" + SourceUrl + '\'' +
                ", ThumbnailUrl='" + ThumbnailUrl + '\'' +
                ", Sort='" + Sort + '\'' +
                '}';
    }

    public String getSourceUrl() {
        return SourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        SourceUrl = sourceUrl;
    }

    public String getThumbnailUrl() {
        return ThumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        ThumbnailUrl = thumbnailUrl;
    }

    public String getSort() {
        return Sort;
    }

    public void setSort(String sort) {
        Sort = sort;
    }
}
