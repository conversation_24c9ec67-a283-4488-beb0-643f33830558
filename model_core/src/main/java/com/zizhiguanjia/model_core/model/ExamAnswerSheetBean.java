package com.zizhiguanjia.model_core.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class ExamAnswerSheetBean implements Parcelable {
    private int Location;
    private int Duration;
    private int ExamType;
    private int RemainingQuestionsCount;
    private int maxNum;
    private int doNum;
    public int getMaxNum() {
        return maxNum;
    }
    public void setMaxNum(int maxNum) {
        this.maxNum = maxNum;
    }

    public int getDoNum() {
        return doNum;
    }

    public void setDoNum(int doNum) {
        this.doNum = doNum;
    }

    private List<ExamChaptersBean> Records;
    public int getLocation() {
        return Location;
    }

    public int getRemainingQuestionsCount() {
        return RemainingQuestionsCount;
    }

    public void setRemainingQuestionsCount(int remainingQuestionsCount) {
        RemainingQuestionsCount = remainingQuestionsCount;
    }

    public void setLocation(int Location) {
        this.Location = Location;
    }

    public int getDuration() {
        return Duration;
    }

    public void setDuration(int Duration) {
        this.Duration = Duration;
    }


    public int getExamType() {
        return ExamType;
    }

    public void setExamType(int ExamType) {
        this.ExamType = ExamType;
    }

    public List<ExamChaptersBean> getRecords() {
        return Records;
    }

    public void setRecords(List<ExamChaptersBean> records) {
        Records = records;
    }

    public ExamAnswerSheetBean() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.Location);
        dest.writeInt(this.Duration);
        dest.writeInt(this.ExamType);
        dest.writeTypedList(this.Records);
    }

    protected ExamAnswerSheetBean(Parcel in) {
        this.Location = in.readInt();
        this.Duration = in.readInt();
        this.ExamType = in.readInt();
        this.Records = in.createTypedArrayList(ExamChaptersBean.CREATOR);
    }

    public static final Creator<ExamAnswerSheetBean> CREATOR = new Creator<ExamAnswerSheetBean>() {
        @Override
        public ExamAnswerSheetBean createFromParcel(Parcel source) {
            return new ExamAnswerSheetBean(source);
        }

        @Override
        public ExamAnswerSheetBean[] newArray(int size) {
            return new ExamAnswerSheetBean[size];
        }
    };
}
