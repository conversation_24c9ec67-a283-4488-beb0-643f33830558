package com.zizhiguanjia.model_core.utils;

import androidx.annotation.NonNull;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.wb.lib_utils.utils.log.LogUtils;

/**
 * Created by march on 16/6/8.
 * 预加载列表更多
 * ps：不能嵌套NestedScrollView 否则不会工作
 */
public class LoadMoreModule {
 
    private OnLoadMoreListener mLoadMoreListener;
    private boolean mIsLoadingMore;//代表什么时候让其停止
    private int preLoadNum = 0;//表示提前多少个Item触发预加载，未到达底部时,距离底部preLoadNum个Item开始加载
    private boolean isEnding = false;
    private float mPer = 0.6f;//ScrollView滑动时的加载时机，根据百分比算
    private boolean isIdle = true;//列表是否是滚动状态，默认不滚动
 
    public LoadMoreModule(int preLoadNum, OnLoadMoreListener mLoadMoreListener) {
        this.preLoadNum = preLoadNum;
        this.mLoadMoreListener = mLoadMoreListener;
    }
 
    public LoadMoreModule(float per, OnLoadMoreListener mLoadMoreListener) {
        this.mPer = per;
        this.mLoadMoreListener = mLoadMoreListener;
    }
 
    public LoadMoreModule(OnLoadMoreListener mLoadMoreListener) {
        this.mLoadMoreListener = mLoadMoreListener;
    }
 
    public void onAttachedToNestedScrollView(final NestedScrollView scrollView, RecyclerView recyclerView) {
        scrollView.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
            @Override
            public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                if (scrollY > oldScrollY) {//向下滚动
                    int threshold = v.getChildAt(0).getMeasuredHeight() - scrollY - v.getMeasuredHeight();
                    LogUtils.e("checkPreload：" + threshold);
                    if (threshold < 1500) {
                        LogUtils.e("checkPreload：已经达到预定阀值，执行加载更多任务");
                        if (null != mLoadMoreListener && !mIsLoadingMore) {
                            mIsLoadingMore = true;
                            mLoadMoreListener.onLoadMore(LoadMoreModule.this);
                        }
                    }
                }
            }
 
        });
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {//当滑动停止，更新列表
                    isIdle = true;
                    if (null != mLoadMoreListener) {
                        mLoadMoreListener.onRefreshData();
                    }
                }else{
                    isIdle = false;
                }
            }
        });
    }
 
    public void onAttachedToRecyclerView(final RecyclerView mRecyclerView, final RecyclerView.Adapter mAttachAdapter) {
        mRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE && isEnding) {
                    if (null != mLoadMoreListener && !mIsLoadingMore) {
                        mIsLoadingMore = true;
                        mLoadMoreListener.onLoadMore(LoadMoreModule.this);
                    }
                }
            }
 
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (null != mLoadMoreListener && dy > 0) {
                    int lastVisiblePosition = getLastVisiblePosition(mRecyclerView);
                    isEnding = lastVisiblePosition + 1 + preLoadNum >= mAttachAdapter.getItemCount();
                    LogUtils.e("checkPreload-onScrolled:" + lastVisiblePosition + 1 + preLoadNum, "," + mAttachAdapter.getItemCount() + "," + preLoadNum + "," + isEnding);
                }
            }
        });
    }
 
    /**
     * 获取最后一条展示的位置
     *
     * @return pos
     */
    private int getLastVisiblePosition(RecyclerView mRecyclerView) {
        int position;
        RecyclerView.LayoutManager manager = mRecyclerView.getLayoutManager();
        if (manager instanceof GridLayoutManager) {
            position = ((GridLayoutManager) manager).findLastVisibleItemPosition();
        } else if (manager instanceof LinearLayoutManager) {
            position = ((LinearLayoutManager) manager).findLastVisibleItemPosition();
        } else if (manager instanceof StaggeredGridLayoutManager) {
            StaggeredGridLayoutManager layoutManager = (StaggeredGridLayoutManager) manager;
            int[] lastPositions = layoutManager.findLastVisibleItemPositions(new int[layoutManager.getSpanCount()]);
            position = getMaxPosition(lastPositions);
        } else {
            position = manager.getItemCount() - 1;
        }
        return position;
    }
 
    /**
     * 获得最大的位置
     *
     * @param positions 位置
     * @return pos
     */
    private int getMaxPosition(int[] positions) {
        int maxPosition = Integer.MIN_VALUE;
        for (int position : positions) {
            maxPosition = Math.max(maxPosition, position);
        }
        return maxPosition;
    }
 
 
    public void finishLoad() {
        this.mIsLoadingMore = false;
    }
 
    /**
     * 返回ScrollView的滚动状态
     * @return
     */
    public boolean isIdle(){
        return isIdle;
    }
    public interface OnLoadMoreListener {
        void onLoadMore(LoadMoreModule loadMoreModule);
        void onRefreshData();
    }
}