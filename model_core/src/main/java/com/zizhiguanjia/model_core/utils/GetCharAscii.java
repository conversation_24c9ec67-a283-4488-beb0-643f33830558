package com.zizhiguanjia.model_core.utils;

public class GetCharAscii {
    /**
     * Ascii转换为字符串
     * @param value
     * @return
     */
    public static String asciiToString(int value)
    {
        StringBuffer sbu = new StringBuffer();
        String[] chars = String.valueOf(value).split(",");
        for (int i = 0; i < chars.length; i++) {
            sbu.append((char) Integer.parseInt(chars[i]));
        }
        return sbu.toString();
    }
    /**
     * 字符串转换为Ascii
     * @param value
     * @return
     */
    public static String stringToAscii(String value)
    {
        StringBuffer sbu = new StringBuffer();
        char[] chars = value.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if(i != chars.length - 1)
            {
                sbu.append((int)chars[i]).append(",");
            }
            else {
                sbu.append((int)chars[i]);
            }
        }
        return sbu.toString();
    }
}
