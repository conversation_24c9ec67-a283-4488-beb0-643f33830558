package com.zizhiguanjia.model_core.repository;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.listener.ExamFaceBackListener;
import com.zizhiguanjia.model_core.model.ExamFaceBackModel;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
public class ExamFaceBackRepository extends CommonViewModel {
    private WeakReference<ExamFaceBackListener> recognizeListener;
    public  ExamFaceBackRepository(ExamFaceBackListener recognizeRespListener) {
        if (recognizeRespListener != null) {
            recognizeListener=new WeakReference<>(recognizeRespListener);
        }
    }
    public void init(){
        initData();
    }
    private void initData(){
        List<ExamFaceBackModel> examFaceBackModels=new ArrayList<>();
        ExamFaceBackModel examFaceBackModel1=new ExamFaceBackModel("发现错别字", R.drawable.core_exam_finderror_nor,R.drawable.core_exam_finderror_yes,1);
        ExamFaceBackModel examFaceBackModel2=new ExamFaceBackModel("答案选项错误",R.drawable.core_exam_error_option_no,R.drawable.core_exam_error_option_yes,2);
        ExamFaceBackModel examFaceBackModel3=new ExamFaceBackModel("正确答案不匹配",R.drawable.core_exam_right_answer_no,R.drawable.core_exam_right_answer_yes,3);
        ExamFaceBackModel examFaceBackModel4=new ExamFaceBackModel("题干和解析不对应",R.drawable.core_exam_alsy_no,R.drawable.core_exam_alsy_yes,4);
        ExamFaceBackModel examFaceBackModel5=new ExamFaceBackModel("其他",R.drawable.core_exam_other_no,R.drawable.core_exam_other_yes,5);
        examFaceBackModels.add(examFaceBackModel1);
        examFaceBackModels.add(examFaceBackModel2);
        examFaceBackModels.add(examFaceBackModel3);
        examFaceBackModels.add(examFaceBackModel4);
        examFaceBackModels.add(examFaceBackModel5);
        getListener().initData(examFaceBackModels);

    }
    private ExamFaceBackListener getListener() {
        return recognizeListener.get();
    }

    public void postData(){
    }
}
