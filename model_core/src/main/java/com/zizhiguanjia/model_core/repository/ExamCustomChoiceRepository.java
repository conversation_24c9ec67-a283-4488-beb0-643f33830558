package com.zizhiguanjia.model_core.repository;

import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.config.ExamQuestionTypeConfig;
import com.zizhiguanjia.model_core.config.ExamResultStyleTypeConfig;
import com.zizhiguanjia.model_core.listener.CustomChoiceListener;
import com.zizhiguanjia.model_core.listener.ExamToAutoPageListener;
import com.zizhiguanjia.model_core.listener.ICustomChoice;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.model.OriginImageBean;
import com.zizhiguanjia.model_core.model.RequestUserAnswerBean;
import com.zizhiguanjia.model_core.utils.ExamUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;

public class ExamCustomChoiceRepository implements ICustomChoice {
    private WeakReference<CustomChoiceListener> customChoiceListenerWeakReference;
    private AnswerGroupBean mAnswerGroupBean;
    private int index=0;
    private int examType;
//    public ObservableField<Integer> currentOption=new ObservableField<>();
    @Override
    public void initData(int examType) {
        this.examType=examType;
        if(ObjectUtil.hasEmpty(mAnswerGroupBean))return;
        getListener().onQuestionTypeState(1);
        getContextTitle();
        getOptionList();
        getOptionTagsList();
        if(ExamUtils.getInstance().getExamQuestionResultStyle(examType)== ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_SELECT_NO){
            getListener().onQuestComfig(true);
            getResultData(true);
        }else {
            if(examType== ExamPaperTypeConfig.EXAM_PAPER_ERROR||examType== ExamPaperTypeConfig.EXAM_PAPER_SAVE){
                try {
                    getUserAnswer().clear();
                }catch (Exception e){

                }
            }
            if(getUserAnswer()==null||getUserAnswer().size()==0){
                getListener().onQuestComfig(false);
                return;
            }else {
                getListener().onQuestComfig(ExamUtils.getInstance().getExamQuestionResultStyle(examType)== ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_NOR?false:true);
                getResultData(ExamUtils.getInstance().getExamQuestionResultStyle(examType)== ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_NOR?false:true);
            }
        }
    }

    @Override
    public void init(CustomChoiceListener customChoiceListener,AnswerGroupBean answerGroupBean,int examType) {
        if(customChoiceListener!=null){
            customChoiceListenerWeakReference=new WeakReference<>(customChoiceListener);
        }
        this.mAnswerGroupBean=answerGroupBean;
        initData(examType);
        getQuestionsListDatas();
    }

    @Override
    public CustomChoiceListener getListener() {
        return customChoiceListenerWeakReference.get();
    }

    @Override
    public List<String> getRightList() {
        List<String> right=new ArrayList<>();
        if(getAnswerItemBean()==null){
            return right;
        }
        for(ExamPackAccessDataBean.RecordsBean.AnswersBean answersBean:getAnswerItemBean().getAnswers()){
            right.add(answersBean.getText());
        }
        return right;
    }

    @Override
    public ExamPackAccessDataBean.RecordsBean getAnswerItemBean() {
        try {
            if(mAnswerGroupBean.getAnswerSheetOptionBean().getChilds()==null||mAnswerGroupBean.getAnswerSheetOptionBean().getChilds().size()==0)
                return null;
            return mAnswerGroupBean.getAnswerSheetOptionBean().getChilds().get(index);
        }catch (Exception e){
            return null;
        }

    }

    @Override
    public void getResultData(boolean isconfig) {
        List<OriginImageBean> originImage=new ArrayList<>();
        List<String> rights=new ArrayList<>();
        List<String> jxStrs=new ArrayList<>();
        for(ExamPackAccessDataBean.RecordsBean.AnswersBean answersBean:getAnswerItemBean().getAnswers()){
            rights.add(answersBean.getText());
        }
        for(ExamPackAccessDataBean.RecordsBean.AnswerKeyBean answersBean:getAnswerItemBean().getAnswerKey()){
           try {
               if(answersBean.getImages()==null||answersBean.getImages().size()==0){
               }else {
                   originImage.addAll(answersBean.getImages());
               }
           }catch (Exception e){
           }
            jxStrs.add(answersBean.getText());
        }
        getListener().showResultView(getUserAnswer(),rights,jxStrs.get(0),originImage,isconfig);
    }

    @Override
    public void getContextTitle() {
        if(mAnswerGroupBean == null || mAnswerGroupBean.getAnswerSheetOptionBean() == null){
            return;
        }
        getListener().showContextTitlteView(mAnswerGroupBean.getAnswerSheetOptionBean().getContent(),mAnswerGroupBean.getAnswerSheetOptionBean().getQuestionType(),mAnswerGroupBean.getAnswerSheetOptionBean().getImages());
    }

    @Override
    public void getOptionList() {
//        getAnswerItemBean().getChilds().get(0).getQuestions()
        //getAnswerItemBean().getQuestions()
        if (getAnswerItemBean() != null) {
            getListener().showOptionView(getAnswerItemBean().getQuestions(), getAnswerItemBean().getQuestionType(), getUserAnswer(), getRightList(),
                    getAnswerItemBean().getQuestionState());
        }
    }

    @Override
    public void getOptionTagsList() {
        if(getAnswerItemBean() == null){
            return;
        }
        getListener().showOptionTagsList(getAnswerItemBean().getContent(),getAnswerItemBean().getQuestionType(),getAnswerItemBean().getImages());
    }

    @Override
    public void getQuestionsListDatas() {
        if(mAnswerGroupBean.getAnswerSheetOptionBean().getChilds()==null||mAnswerGroupBean.getAnswerSheetOptionBean().getChilds().size()==0)return;
        List<String> strs=new ArrayList<>();
        for(int i=0;i<mAnswerGroupBean.getAnswerSheetOptionBean().getChilds().size();i++){
            strs.add("问题"+(i+1));
        }
//        if(strs.size()==0||strs.size()==1)
//            return;
        getListener().getQuestionsListDatas(strs);
    }

    @Override
    public void reshIndex(int index) {
        this.index=index;
        initData(examType);
        getQuestionsListDatas();
    }
//    private int lastOption=0;
//    public ObservableField<String> msgObs=new ObservableField<>();
    @Override
    public void userConfim(String option, ExamToAutoPageListener examToAutoPageListener) {
        if (getAnswerItemBean().getQuestionType() == ExamQuestionTypeConfig.EXAM_QUESTION_DUO
                || getAnswerItemBean().getQuestionType() == ExamQuestionTypeConfig.EXAM_QUESTION_DAN
                || getAnswerItemBean().getQuestionType() == ExamQuestionTypeConfig.EXAM_QUESTION_PAN) {
            //单选
            if(getAnswerItemBean().isConfim())return;
            saveQhData(option);
            String result = getUserSaveAnswerStr();
            List<String> userAs = Arrays.asList(StringUtils.convertStrToArray2(result));
            boolean right = checkQuestionRight();
            getAnswerItemBean().setIsRight(right);

            getResultData(true);
            RequestUserAnswerBean requestUserAnswerBean = new RequestUserAnswerBean();
            requestUserAnswerBean.setId(getAnswerItemBean().getQuestionId() + "");
            requestUserAnswerBean.setRight(right);
            requestUserAnswerBean.setQuestionNum(getAnswerItemBean().getQuestionNum() + "");
            requestUserAnswerBean.setUserAnswer(userAs);
            DataHelper.uploadRecolder(GsonUtils.gsonString(requestUserAnswerBean));
            getListener().onQuestComfig(ExamUtils.getInstance().getExamQuestionResultStyle(examType) == ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_NOR ? false : true);
             if(ExamUtils.getInstance().getExamQuestionResultStyle(examType) == ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_NOR){
                 LogUtils.e("看看选择的1---->>>>"+getAnswerItemBean().getUserAnswer().size()+"****"+getAnswerItemBean().getAnswers().size());
                 LogUtils.e("看看选择的2---->>>>"+getUserAnswer().toString()+"***"+getRightList().toString());
                 LogUtils.e("看看选择的3---->>>>"+getAnswerItemBean().isConfim());
                 LogUtils.e("看看选择的4---->>>>"+getUserSaveAnswerStr()+"******"+StringUtils.convertStrToArray2(getUserSaveAnswerStr()).length);
                 boolean rightStr=mnksRight(getUserSaveAnswerStr());
                 if(examToAutoPageListener==null)return;
                 examToAutoPageListener.toPageByAuto(rightStr);
             }else {
                 if(right){
                     if(examToAutoPageListener==null)return;
                     examToAutoPageListener.toPageByAuto(right);
                 }
             }
        }
    }

    @Override
    public List<String> getUserAnswer() {
        List<String> users=new ArrayList<>();
        if(getAnswerItemBean()==null){
            return users;
        }
        for(String str:getAnswerItemBean().getUserAnswer()){
            if(!str.equals("null")){
                users.add(str);
            }
        }
        return users;
    }

    @Override
    public void saveQhData(String datas) {
        if(getAnswerItemBean()==null){
            return;
        }
        getAnswerItemBean().setUserAnswer(Arrays.asList(StringUtils.convertStrToArray2(datas)));
        if(examType==ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM||examType==ExamPaperTypeConfig.EXAM_PAPER_LNZT||examType==ExamPaperTypeConfig.EXAM_PAPER_MNZT||examType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS){
            getAnswerItemBean().setConfim(false);
        }else {
            getAnswerItemBean().setConfim(true);
        }

    }

    @Override
    public String getUserSaveAnswerStr() {
        List<String> strings=new ArrayList<>();
        for(ExamPackAccessDataBean.RecordsBean questionsBean:mAnswerGroupBean.getAnswerSheetOptionBean().getChilds()){
            if(questionsBean.getUserAnswer()==null||questionsBean.getUserAnswer().isEmpty()){
                strings.add("null");
               // strings.
            }else {
                List<String> users=questionsBean.getUserAnswer();
                strings.add(ListToStrs(users));
            }
        }
        return StringUtils.ListToStr(strings);
    }
    private boolean mnksRight(String str){
        String[] sts=StringUtils.convertStrToArray2(str);
        if(sts==null||sts.length==0)return false;
        for(String st : sts){
            if (st.contains("null")){
                return false;
            }
        }
        return true;
    }
    @Override
    public boolean checkQuestionRight() {
        List<Boolean> rights=new ArrayList<>();
       List<ExamPackAccessDataBean.RecordsBean> recordsBean= mAnswerGroupBean.getAnswerSheetOptionBean().getChilds();
       if(recordsBean==null||recordsBean.size()==0)return false;
       for(ExamPackAccessDataBean.RecordsBean recordsBean1:mAnswerGroupBean.getAnswerSheetOptionBean().getChilds()){
           List<String> userLists=recordsBean1.getUserAnswer();
           List<String> rightList=new ArrayList<>();
           for(ExamPackAccessDataBean.RecordsBean.AnswersBean answersBean : recordsBean1.getAnswers()){
               if(!answersBean.getText().equals("null")){
                   rightList.add(answersBean.getText());
               }
           }
           boolean right = ExamUtils.getInstance().getUserRight(userLists, rightList);
           rights.add(right);
       }
        for(Boolean b:rights){
            if(!b)return false;
        }
        return true;
    }
    public boolean checkQuestionDoOver() {
//        List<ExamPackAccessDataBean.RecordsBean> recordsBean= mAnswerGroupBean.getAnswerSheetOptionBean().getChilds();
//        if(recordsBean==null||recordsBean.size()==0)return true;
//        for(ExamPackAccessDataBean.RecordsBean.QuestionsBean questionsBean:mLists){
//            LogUtils.e("看看提交的数据----->>>"+questionsBean.isConfim()+"***"+questionsBean.getUserAnswer()+"****"+questionsBean.getText());
//            if(!questionsBean.isConfim()){
//                return false;
//            }
//        }
        return true;
    }
    public   String ListToStrs(List<String> strings){
        StringBuffer stringBuffer=new StringBuffer();
        if(strings==null||strings.size()==0){
            return "";
        }
        for(String string:strings){
            stringBuffer.append(string+"_");
        }
        stringBuffer.deleteCharAt(stringBuffer.length() - 1);
        return stringBuffer.toString();
    }
}
