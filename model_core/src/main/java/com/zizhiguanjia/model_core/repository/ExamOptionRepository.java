package com.zizhiguanjia.model_core.repository;

import android.os.Build;
import android.view.View;
import android.widget.ImageView;

import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.config.ExamResultStyleTypeConfig;
import com.zizhiguanjia.model_core.config.UserAnswerTypeConfig;
import com.zizhiguanjia.model_core.listener.ExamOptionListener;
import com.zizhiguanjia.model_core.listener.IExamOption;
import com.zizhiguanjia.model_core.utils.ExamUtils;

import java.lang.ref.WeakReference;
import java.util.List;

import androidx.annotation.RequiresApi;

public class ExamOptionRepository implements IExamOption {
    private WeakReference<ExamOptionListener> examOptionListenerWeakReferences;
    public ExamOptionRepository(ExamOptionListener examOptionListenerWeakReference) {
        if (examOptionListenerWeakReference != null) {
            examOptionListenerWeakReferences=new WeakReference<>(examOptionListenerWeakReference);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    public int getUserAnswerState(List<String> mRightAnswer, List<String> mUserAnswer, int currentPostion,int type) {
        int examResultType= ExamUtils.getInstance().getExamQuestionResultStyle(type);
        String cp=String.valueOf(currentPostion+1);
        if(examResultType== ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_DAN_ALSY||examResultType==ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_SELECT_NO){
            if(mUserAnswer==null||mUserAnswer.size()==0){
                if(mRightAnswer.contains(cp)){
                    return UserAnswerTypeConfig.USER_ANSWER_RIGHT;
                }else {
                    return UserAnswerTypeConfig.USER_ANSWER_NO;
                }
            }else {
                if(mUserAnswer.contains(cp)&&mRightAnswer.contains(cp)){
                    return UserAnswerTypeConfig.USER_ANSWER_RIGHT;
                }else {
                    if(mUserAnswer.contains(cp)){
                        return UserAnswerTypeConfig.USER_ANSWER_ERROR;
                    }else {
                        if(mRightAnswer.contains(cp)){
                            return UserAnswerTypeConfig.USER_ANSWER_RIGHT;
                        }else {
                            return UserAnswerTypeConfig.USER_ANSWER_NO;
                        }

                    }
                }
            }
        }else if(examResultType==ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_NOR){
            if(mUserAnswer==null||mUserAnswer.size()==0){
                return UserAnswerTypeConfig.USER_ANSWER_NO;
            }else {
                if(mUserAnswer.contains(cp)){
                    return UserAnswerTypeConfig.USER_ANSWER_RIGHT;
                }else {
                    return UserAnswerTypeConfig.USER_ANSWER_NO;
                }
            }
        }else {
            if(!mRightAnswer.contains(cp)){
                return UserAnswerTypeConfig.USER_ANSWER_ERROR;
            }else {
                return UserAnswerTypeConfig.USER_ANSWER_RIGHT;
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    public boolean checkDiffrent5(List<String> mRightAnswer, List<String> mUserAnswer) {
        return ExamUtils.getInstance().getUserRight(mUserAnswer,mRightAnswer);
    }

    @Override
    public void setOptionRightImage(int state,ImageView imageView,int examType, ExamCoreStyleConfigBean styleConfigBean) {
        if(examType== ExamPaperTypeConfig.EXAM_PAPER_LNZT||examType== ExamPaperTypeConfig.EXAM_PAPER_MNZT||examType==ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM||examType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS){
            imageView.setVisibility(View.GONE);
            return;
        }
        if(UserAnswerTypeConfig.USER_ANSWER_ERROR==state && !styleConfigBean.isShowMemoryModel()){
            //错误
            imageView.setVisibility(View.VISIBLE);
            imageView.setImageResource(R.drawable.core_exam_error_img);
        }else if(UserAnswerTypeConfig.USER_ANSWER_RIGHT==state){
            //正确
            imageView.setVisibility(View.VISIBLE);
            imageView.setImageResource(R.drawable.core_exam_right_img);
        }else {
            //未作
            imageView.setVisibility(View.GONE);
        }
    }

    @Override
    public void OnclickItem(View mView,String postion) {
        getListener().OnclickItem(Integer.parseInt(postion));
    }

    @Override
    public int getUserDuoAnswerState(List<String> mUserAnswer, int currentPostion, int type) {
        if(mUserAnswer==null||mUserAnswer.size()==0){
            return UserAnswerTypeConfig.USER_ANSWER_NO;
        }
        String cp=String.valueOf(currentPostion+1);
        if(mUserAnswer.contains(cp)){
            return UserAnswerTypeConfig.USER_ANSWER_DO;
        }else {
            return UserAnswerTypeConfig.USER_ANSWER_NO;
        }

    }

    private ExamOptionListener getListener() {
        return examOptionListenerWeakReferences.get();
    }
}
