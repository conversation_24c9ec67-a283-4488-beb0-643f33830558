package com.zizhiguanjia.model_core.repository;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.lib_base.utils.EncryptDecryptOptions;
import com.zizhiguanjia.model_core.helper.ExamCoreHelper;
import com.zizhiguanjia.model_core.listener.ExamCoreListenter;
import com.zizhiguanjia.model_core.listener.ExamThreadListenter;
import com.zizhiguanjia.model_core.listener.IExamCore;
import com.zizhiguanjia.model_core.manager.ExamCoreMessageHandlerManager;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamAnswerSheetBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;

import java.io.IOException;
import java.lang.ref.WeakReference;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

public class ExamCoreRepository implements IExamCore ,ExamThreadListenter{
    private EncryptDecryptOptions encryDecrySkey = new EncryptDecryptOptions();

    private WeakReference<ExamCoreListenter> examCoreListenterWeakReference;
    @Override
    public void init(ExamCoreListenter examCoreListenter,String paperType,String paperId) {
        if(examCoreListenter!=null){
            examCoreListenterWeakReference=new WeakReference<>(examCoreListenter);
        }
        DataHelper.initExamConfig(paperType, paperId, String.valueOf(System.currentTimeMillis()));
        ExamCoreMessageHandlerManager.getInstance().initExamMessageHandler();
        ExamCoreMessageHandlerManager.getInstance().getExamFrameThread().setCall(this);
    }
    @Override
    public ExamCoreListenter getListenter() {
        return examCoreListenterWeakReference.get();
    }
    @Override
    public void getExamSheetListData(String paperType, String paperValue, boolean newStart, int location) {
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("paperType", paperType);
        if(!StringUtils.isEmpty(paperValue)){
            if(!paperValue.equals("-1")){
                multiBuilder.addFormDataPart("paperValue", paperValue);
            }
        }
        if(location!=-1){
            multiBuilder.addFormDataPart("location", String.valueOf(location));
        }
        multiBuilder.addFormDataPart("newStart", String.valueOf(newStart));
        RequestBody multiBody=multiBuilder.build();
        ExamCoreHelper.getInstance().getExamSheetData(BaseAPI.VERSION_DES+"/API/Exam/GetExamAnswerSheet",new HashMap<>(), multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        LogUtils.e("无效的------>>>>3333");
                        if(getListenter()==null)return;
                        getListenter().getExamSheetDataListener(null);
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if(response.code()==200){
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                String json=response.body().string();
                                String strJson=DataHelper.getExamSheetDataList(json);
                                Type jsonType = new TypeToken<BaseData<ExamAnswerSheetBean>>() {}.getType();
                                BaseData<ExamAnswerSheetBean> commonExamBeanBaseData= new Gson().fromJson(strJson,jsonType);
                                if(commonExamBeanBaseData.Code.equals(BaseConfig.SUCCESS_CODE)){
                                    if(getListenter()==null)return;
                                    getListenter().getExamSheetDataListener(commonExamBeanBaseData.Data);
                                }else {
                                    if(getListenter()==null)return;
                                    getListenter().getExamSheetDataListener(null);
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                                if(getListenter()==null)return;
                                getListenter().getExamSheetDataListener(null);
                            }
                        }
                    });
                }

            }
        });
    }
    @Override
    public void addPostDataQueue(Integer integer, List<AnswerGroupBean> answerGroupBeans) {
        ExamCoreMessageHandlerManager.getInstance().getExamFrameThread().setAnswerGroupBeans(answerGroupBeans);
        ExamCoreMessageHandlerManager.getInstance().getmDetectResultQueue().add(integer);
    }
    @Override
    public void getExamListData(String qid, String paperType, String paperValue, boolean newStart,String count) {
        if(StringUtils.isEmpty(qid)){
            //无效数据
            return;
        }
//        List<String> strList= Arrays.asList(StringUtils.convertStrToArray2(qid));
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("paperType", paperType);
        if(!StringUtils.isEmpty(paperValue)){
            multiBuilder.addFormDataPart("paperValue", paperValue);
        }
        multiBuilder.addFormDataPart("startQuestionNum", qid);
        multiBuilder.addFormDataPart("questionCount", count);
        multiBuilder.addFormDataPart("newStart", String.valueOf(newStart));
        RequestBody multiBody=multiBuilder.build();

        Map<String,String> headerMap = new HashMap<>();
        headerMap.put("enckey","xxxxxxxxxxxx");
        ExamCoreHelper.getInstance().getExamSheetData(BaseAPI.VERSION_DES+"/API/Exam/GetExamQuestionList",headerMap, multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        if(getListenter()==null)return;
                        getListenter().getExamListData(null);
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if(response.code()==200){
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                String json=response.body().string();
                                encryDecrySkey.setAesKey(response.header("akey"));
                               try {
                                   Type jsonType = new TypeToken<BaseData<ExamPackAccessDataBean>>() {}.getType();
                                   BaseData<ExamPackAccessDataBean> commonExamBeanBaseData= new Gson().fromJson(json,jsonType);
                                   if(commonExamBeanBaseData==null||commonExamBeanBaseData.Data==null||commonExamBeanBaseData.Data.getRecords()==null){
                                       if(getListenter()==null)return;
                                       getListenter().getExamListData(null);
                                   }else{
                                       if(getListenter()==null)return;
                                       for (ExamPackAccessDataBean.RecordsBean bean : commonExamBeanBaseData.Data.getRecords()) {
                                           bean.setContent(encryDecrySkey.decryptData(bean.getContent()));
                                       }
                                       getListenter().getExamListData(commonExamBeanBaseData.Data.getRecords());
                                   }
                               } catch (Exception ignore){
                                   if(getListenter()==null)return;
                                   getListenter().getExamListData(null);
                               }
//                                BaseData baseData= GsonUtils.gsonToBean(json,BaseData.class);
//                                if(baseData.Code.equals(BaseConfig.SUCCESS_CODE)){
//                                    Type jsonType = new TypeToken<BaseData<ExamPackAccessDataBean>>() {}.getType();
//                                    BaseData<ExamPackAccessDataBean> commonExamBeanBaseData= new Gson().fromJson(json,jsonType);
//                                    if(commonExamBeanBaseData==null||commonExamBeanBaseData.Code==null){
//                                        if(getListenter()==null)return;
//                                        getListenter().getExamListData(null);
//                                        return;
//                                    }
//                                    if(commonExamBeanBaseData.Code.equals("200")){
//                                        //有效
//                                        try {
//                                            if(getListenter()==null)return;
//                                            if(commonExamBeanBaseData==null||commonExamBeanBaseData.Data==null||commonExamBeanBaseData.Data.getRecords()==null){
//                                                getListenter().getExamListData(null);
//                                            }else{
//                                                encryDecrySkey.decryptData(response.header("akey"));
//
//                                                getListenter().getExamListData(commonExamBeanBaseData.Data.getRecords());
//                                            }
//                                        }catch (Exception e){
//                                            getListenter().getExamListData(null);
//                                        }
//                                    }else {
//                                        if(getListenter()==null)return;
//                                        getListenter().getExamListData(null);
//                                    }
//                                }else {
//                                    if(getListenter()==null)return;
//                                    getListenter().getExamListData(null);
//                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                                if(getListenter()==null)return;
                                getListenter().getExamListData(null);
                            }
                        }
                    });
                }

            }
        });
    }
    @Override
    public void getReadPostLoacl(List<String> index,String start,String count) {
        for(String str:index){
            if(getListenter()==null)return;
            getListenter().getReadPostHttpExam(Integer.parseInt(str));
        }
        if(getListenter()==null)return;
        getListenter().getReadStartPostHttpExam(start,count);
    }
}
