package com.zizhiguanjia.model_core.helper;

import com.wb.lib_network.AbstractHttp;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseUrlInterceptor;
import com.zizhiguanjia.lib_base.config.ResponInterceptor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;

public class ExamCoreHelper extends AbstractHttp {
    private static ExamCoreHelper helper;
    public static ExamCoreHelper getInstance(){
        if(helper==null){
            synchronized (ExamCoreHelper.class){
                return helper=new ExamCoreHelper();
            }
        }
        return helper;
    }

    @Override
    protected String baseUrl() {
        return BaseAPI.Base_Url_Api;
    }
    @Override
    protected Iterable<Interceptor> interceptors() {
        final List<Interceptor> interceptorList = new ArrayList<>();
        interceptorList.add(new BaseUrlInterceptor());
        interceptorList.add(new ResponInterceptor());
        return interceptorList;

    }
    public void getExamSheetData(String url, Map<String,String> headerMap, RequestBody requestBody, Callback callback){
        Request.Builder builder = new Request.Builder().url(BaseAPI.Base_Url_Api + url).post(requestBody);
        if(headerMap != null){
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                builder.header(entry.getKey(),entry.getValue());
            }
        }
        doAsync(builder.build(), callback);
    }
    private void doAsync(Request request, Callback callback) {
        //创建请求会话
        Call call = okHttpClient().newCall(request);
        //异步执行会话请求
        call.enqueue(callback);
    }
}
