package com.zizhiguanjia.model_core.service;

import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.CoreExamRouterPath;
import com.zizhiguanjia.lib_base.service.CoreExamService;

@Route(path = CoreExamRouterPath.SERVICE)
public class CoreExamServiceIpml implements CoreExamService {
    @Override
    public void start(Context context) {

    }

    @Override
    public IFragment mainPage(Context context) {
        return ARouterUtils.navFragment(CoreExamRouterPath.MAIN_FRAGMENT);
    }

    @Override
    public IFragment courseDetailChildFinish(Context context) {
        return ARouterUtils.navFragment(CoreExamRouterPath.COURSE_DETAIL_CHILD_FINISH);
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {

    }
}
