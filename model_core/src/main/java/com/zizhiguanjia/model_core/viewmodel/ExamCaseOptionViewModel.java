package com.zizhiguanjia.model_core.viewmodel;

import android.text.Editable;
import android.view.View;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.model_core.listener.ExamContentUserAnswerCall;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.model.RequestUserAnswerBean;
import com.zizhiguanjia.model_core.navigator.ExamCaseQuestionNavigator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import androidx.databinding.ObservableField;
public class ExamCaseOptionViewModel extends CommonViewModel {
    public ObservableField<Boolean> lookAsyObs=new ObservableField<>();
    public ObservableField<Boolean> lookEditObs=new ObservableField<>();
    public ObservableField<String> msgObs=new ObservableField<>();
    public ObservableField<Integer> currentOption=new ObservableField<>();
    private int lastOption=0;
    private ExamContentUserAnswerCall caseDataCall;
    private ExamCaseQuestionNavigator navigator;
    private List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> mLists;
    private List<ExamPackAccessDataBean.RecordsBean.AnswersBean>  rightAnswers;
    private List<ExamPackAccessDataBean.RecordsBean.AnswerKeyBean> analysisLists;
    private List<String> userAnswers;
    private int questionState;
    private String questionIds;
    private String questionNum;
    public ObservableField<String> currentLookDes=new ObservableField<>();
    public ObservableField<Boolean> lookJqjqObs=new ObservableField<>();
    public ObservableField<Boolean> lookJqjqStateObs=new ObservableField<>();
    public ObservableField<String> lookJqjqTxtObs=new ObservableField<>();
    public ExamCoreThemeConfigThemeBean themeBean;
    public ExamCoreThemeConfigTextSizeBean textSizeBean;
    private ExamCoreStyleConfigBean styleConfigBean;
    public List<ExamPackAccessDataBean.RecordsBean.AnswersBean> getRightAnswers() {
        if(rightAnswers==null)
            return rightAnswers=new ArrayList<>();
        return rightAnswers;
    }

    public List<ExamPackAccessDataBean.RecordsBean.AnswerKeyBean> getAnalysisLists() {
        if(analysisLists==null)
            return analysisLists=new ArrayList<>();
        return analysisLists;
    }

    public List<String> getUserAnswers() {
        if(userAnswers==null)
            return userAnswers=new ArrayList<>();
        return userAnswers;
    }

    public List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> getmLists() {
        if(mLists==null)
            return mLists=new ArrayList<>();
        return mLists;
    }

    public void initParams(ExamContentUserAnswerCall caseDataCall, ExamCaseQuestionNavigator navigator){
        lookAsyObs.set(false);
        lookEditObs.set(true);
        currentOption.set(0);
        this.caseDataCall=caseDataCall;
        this.navigator=navigator;
    }
    public void onclick(View view){
        if(view.getId()== R.id.tvLookAsy){
            if(questionState==1){
                Bus.post(new MsgEvent(0x111));
                return;
            }
            if(getmLists().get(currentOption.get()).isConfim())return;
            if(caseDataCall==null)return;
            getmLists().get(currentOption.get()).setConfim(true);
            qhSaveData(currentOption.get());

//            caseDataCall.getUserSelectOption(getUserSaveAnswerStr());
            postData();
        }
    }

    public void postData(){
        RequestUserAnswerBean requestUserAnswerBean=new RequestUserAnswerBean();
        requestUserAnswerBean.setId(questionIds);
        requestUserAnswerBean.setRight(false);
        requestUserAnswerBean.setQuestionNum(questionNum);
        requestUserAnswerBean.setUserAnswer(Arrays.asList(StringUtils.convertStrToArray2(getUserSaveAnswerStr())));
        DataHelper.uploadRecolder(GsonUtils.gsonString(requestUserAnswerBean));
        if(checkQuestionDoOver()){
            caseDataCall.userSelectAnswerData(Integer.valueOf(questionNum),true);
        }
    }
    public boolean checkQuestionDoOver() {
        for(ExamPackAccessDataBean.RecordsBean.QuestionsBean questionsBean:mLists){
            LogUtils.e("看看提交的数据----->>>"+questionsBean.isConfim()+"***"+questionsBean.getUserAnswer()+"****"+questionsBean.getText());
            if(!questionsBean.isConfim()){
                return false;
            }
        }
        return true;
    }
    public void onMsgEditTextChanged(Editable editable){
        msgObs.set(editable.toString());
    }
    public void qhSaveData(int posttion){
        currentOption.set(posttion);
        getmLists().get(lastOption).setUserAnswer(msgObs.get());
        msgObs.set("");
        String msg=getmLists().get(currentOption.get()).getUserAnswer();
        lastOption=currentOption.get();
        msgObs.set(msg);
        navigator.movingCursor(msg);
        lookAsyObs.set(getmLists().get(currentOption.get()).isConfim());
        lookEditObs.set(!getmLists().get(currentOption.get()).isConfim());
    }

    public void reshData(int postion){
        navigator.showCaseDnView(getRightAnswers().get(postion).getText());
        navigator.showCaseJxView(getAnalysisLists().get(postion).getText());
        navigator.showCaseWtView(getmLists().get(postion).getText());
        msgObs.set(getmLists().get(currentOption.get()).getUserAnswer());
        lookAsyObs.set(getmLists().get(currentOption.get()).isConfim());
        lookEditObs.set(!getmLists().get(currentOption.get()).isConfim());
    }
    public void initIssusListData(List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> questionsBeans,
                                  List<ExamPackAccessDataBean.RecordsBean.AnswersBean>  rightAnswers,
                                  List<ExamPackAccessDataBean.RecordsBean.AnswerKeyBean> analysisLists,
                                  List<String> userAnswers,int stateQuestion,String jtjq,String ids,String nums){
        getmLists().clear();
        getmLists().addAll(questionsBeans);
        this.questionState=stateQuestion;
        this.questionIds=ids;
        this.questionNum=nums;
        List<String> strs=new ArrayList<>();
        if(questionsBeans!=null||questionsBeans.size()!=0){
            for(int i=0;i<questionsBeans.size();i++){
                strs.add("问题"+(i+1));
                try {
                    getmLists().get(i).setUserAnswer((userAnswers==null||userAnswers.size()==0)?null:userAnswers.get(i));
                    getmLists().get(i).setConfim(StringUtils.isEmpty(getmLists().get(i).getUserAnswer())?false:true);
                }catch (Exception e){
                }
            }
            navigator.showIssusListData(strs);
        }
        this.rightAnswers=rightAnswers;
        this.analysisLists=analysisLists;
        this.userAnswers=userAnswers;
        if(jtjq==null||jtjq.isEmpty()){
            lookJqjqObs.set(false);
        }else {
            lookJqjqObs.set(true);
            if(UserHelper.isBecomeVip()){
                lookJqjqStateObs.set(true);
            }else {
                lookJqjqStateObs.set(false);
            }
            lookJqjqTxtObs.set(jtjq);

        }

        reshData(0);
    }

    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        if(styleConfigBean != null){
            setAnswerShowState(styleConfigBean.isShowMemoryModel());
        }
    }

    private String getUserSaveAnswerStr(){
        StringBuffer stringBuffer=new StringBuffer();
        for(ExamPackAccessDataBean.RecordsBean.QuestionsBean questionsBean:getmLists()){
            stringBuffer.append(StringUtils.isEmpty(questionsBean.getUserAnswer())?" ":questionsBean.getUserAnswer()).append(",");
        }
        return stringBuffer.deleteCharAt(stringBuffer.length()-1).toString();
    }

    /**
     * 设置答案显示状态
     * @param show 显示状态
     */
    private void setAnswerShowState(boolean show){
        if(currentOption.get() != null && getmLists().size() > currentOption.get()) {
            if (show) {
                getmLists().get(currentOption.get()).setConfim(true);
            } else {
                getmLists().get(currentOption.get()).setConfim(false);
            }
        }
    }
}
