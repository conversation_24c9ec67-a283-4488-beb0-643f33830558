package com.zizhiguanjia.model_core.viewmodel;

import android.graphics.Color;
import android.view.Gravity;
import android.view.View;

import androidx.databinding.ObservableField;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.subsciber.SimpleThrowableAction;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.lib_base.helper.HomeHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.listeners.ExamDataUploadListener;
import com.zizhiguanjia.lib_base.msgconfig.PointCheckType;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.adapter.ExamContentAdapter;
import com.zizhiguanjia.model_core.api.CoreExamServiceApi;
import com.zizhiguanjia.model_core.config.ExamQuestionTypeConfig;
import com.zizhiguanjia.model_core.config.PageTypeConfig;
import com.zizhiguanjia.model_core.fragment.ExamCoreFragment;
import com.zizhiguanjia.model_core.fragment.ExamResultFragment;
import com.zizhiguanjia.model_core.listener.ExamCoreListenter;
import com.zizhiguanjia.model_core.manager.ExamCoreMessageHandlerManager;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.AnswerSheetOptionBean;
import com.zizhiguanjia.model_core.model.ExamAnswerSheetBean;
import com.zizhiguanjia.model_core.model.ExamChaptersBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.navigator.ExamCoreNavigator;
import com.zizhiguanjia.model_core.repository.ExamCoreRepository;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Editor;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

public class ExamCoreViewModel extends CommonViewModel implements ExamCoreListenter {
    private String paperType, paperId, title;
    private int location;
    private ExamCoreRepository examCoreRepository;
    private ExamCoreNavigator examCoreNavigator;
    private List<ExamChaptersBean> sheetLists = new ArrayList<>();
    private int remainingQuestionsCount;
    public ObservableField<Integer> maxNumObs = new ObservableField<>();
    ;
    public ObservableField<String> countObs = new ObservableField<>();
    ;
    public ObservableField<List<AnswerGroupBean>> answerSheetOptionBeansObs = new ObservableField<>();
    ;
    public ObservableField<String> numObs = new ObservableField<>();
    ;
    public ObservableField<Integer> bgColor = new ObservableField<>(Color.WHITE);
    ;
    public ObservableField<Integer> textColor = new ObservableField<>(Color.BLACK);
    ;
    public ObservableField<Boolean> downTimeObs = new ObservableField<>();
    ;
    public ObservableField<String> resultTrueCount = new ObservableField<>("0");
    ;
    public ObservableField<String> resultFalseCount = new ObservableField<>("0");
    ;
    private List<Integer> examDataFlags = new ArrayList<>();
    ;
    private Disposable faceBackDisposable;
    private CoreExamServiceApi mApi = Http.getInstance().create(CoreExamServiceApi.class);
    private ExamCoreFragment examCoreFragment;
    public int maxSeeNum, doNum;
    public ObservableField<Boolean> firsShowViewObs = new ObservableField<>();
    public ObservableField<Long> downTime = new ObservableField<>();
    public boolean isCanBack;

    public ObservableField<ExamCoreThemeConfigThemeBean> themeBean = new ObservableField<>();
    public ExamCoreThemeConfigTextSizeBean textSizeBean = null;
    public ExamCoreStyleConfigBean styleConfigBean;

    /**
     * 初始化主题数据
     */
    public void initThemeData() {
        if (styleConfigBean == null) {
            styleConfigBean = ExamCoreStyleConfigBean.getConfigInfoByStore(paperType, title);
        }
        styleConfigBean.saveConfigInfoToStore(paperType, title);
        ExamCoreThemeConfigThemeBean themeBeanInfo = styleConfigBean.getThemeBeanInfo();
        if (themeBeanInfo != null) {
            themeBean.set(themeBeanInfo);
        }
        ExamCoreThemeConfigTextSizeBean textSizeBeanInfo = styleConfigBean.getTextSizeBeanInfo();
        if (textSizeBeanInfo != null) {
            this.textSizeBean = textSizeBeanInfo;
        }
        if (themeBean.get() != null) {
            bgColor.set(Color.parseColor(Objects.requireNonNull(themeBean.get()).getBgColor()));
            textColor.set(Color.parseColor(Objects.requireNonNull(themeBean.get()).getTextColor()));
            examCoreFragment.changeShowData(styleConfigBean, themeBean.get(), textSizeBean);
        }
    }

    public ExamCoreStyleConfigBean getStyleConfigBean() {
        return styleConfigBean;
    }

    @Override
    public void initData(String title, String paperType, String paperId, boolean restart, int location, ExamCoreNavigator examCoreNavigator,
                         ExamCoreFragment examCoreFragment) {
        this.title = title;
        this.examCoreNavigator = examCoreNavigator;
        this.examCoreFragment = examCoreFragment;
        this.paperType = paperType;
        this.paperId = paperId;
        this.location = location;
        countObs.set("0");
        maxNumObs.set(0);
        numObs.set("0");
        firsShowViewObs.set(true);
        downTimeObs.set(false);
        downTime.set(0L);
    }

    public void userInitiativeExit(String paperType) {
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM) {
            Map<String, String> params = new HashMap<>();
            params.put("paperType", paperType);
            params.put("reason", "1");
            launchOnlyResult(mApi.userExit(params), new OnHandleException<BaseData>() {
                @Override
                public void success(BaseData data) {
                }

                @Override
                public void error(String msg) {

                }
            });
        }
    }

    @Override
    public void initParam(String title, String paperType, String paperId, boolean restart, int location, ExamCoreNavigator examCoreNavigator,
                          ExamCoreFragment examCoreFragment) {
        initData(title, paperType, paperId, restart, location, examCoreNavigator, examCoreFragment);
        examCoreRepository = new ExamCoreRepository();
        examCoreRepository.init(this, paperType, paperId);
        examCoreRepository.getExamSheetListData(paperType, paperId, restart, location);
    }

    @Override
    public void getExamSheetDataListener(ExamAnswerSheetBean commonGroupExamBeanLists) {
        if (examCoreNavigator == null) {
            return;
        }
        if (checkExamAnswerSheetBeanValues(commonGroupExamBeanLists)) {
            LogUtils.e("测试---->>>>1");
            examCoreNavigator.showEmptyView();
        } else {
            LogUtils.e("测试---->>>>2");
            maxSeeNum = commonGroupExamBeanLists.getMaxNum();
            doNum = commonGroupExamBeanLists.getDoNum();
            sheetLists.clear();
            sheetLists.addAll(commonGroupExamBeanLists.getRecords());
            resetResultCount();
            examCoreNavigator.showExamView(commonGroupExamBeanLists);
            downTime.set(Long.parseLong(commonGroupExamBeanLists.getDuration() + ""));
            checkNullSheet();
        }
    }

    private void checkNullSheet() {
        if (userDo) {
            return;
        }
        if (sheetLists == null || sheetLists.isEmpty()) {
            userDo = false;
            return;
        }
        for (ExamChaptersBean examChaptersBean : sheetLists) {
            for (AnswerSheetOptionBean answerSheetOptionBean : examChaptersBean.getItems()) {
                if (answerSheetOptionBean.isDone()) {
                    userDo = true;
                    return;
                }
            }
        }
        userDo = false;
    }

    @Override
    public boolean checkExamAnswerSheetBeanValues(ExamAnswerSheetBean examAnswerSheetBean) {
        if (examAnswerSheetBean == null || examAnswerSheetBean.getRecords() == null || examAnswerSheetBean.getRecords().size() == 0) {
            return true;
        }
        return false;
    }

    @Override
    public void getExamCount(ExamAnswerSheetBean commonGroupExamBeans) {
        int count = 0;
        for (ExamChaptersBean examChaptersBean : commonGroupExamBeans.getRecords()) {
            count = count + examChaptersBean.getQuestionCount();
        }
        countObs.set(String.valueOf(count));
    }

    @Override
    public int getExamSheetCount() {
        try {
            return Integer.parseInt(countObs.get());
        } catch (Exception e) {
            return 0;
        }

    }

    @Override
    public boolean checkAnswerGroupBeanValues(List<AnswerGroupBean> answerSheetOptionBeans) {
        if (answerSheetOptionBeans == null || answerSheetOptionBeans.size() == 0) {
            return true;
        }
        return false;
    }

    @Override
    public void initDataSheet(List<AnswerGroupBean> answerSheetOptionBeans, int localAddress, int QuestionCount, int Duration,
                              int RemainingQuestionsCount) {
        this.remainingQuestionsCount = RemainingQuestionsCount;
        LogUtils.e("清理-----》》》3333333");
        getMaxSeeNum(answerSheetOptionBeans);
        answerSheetOptionBeansObs.set(answerSheetOptionBeans);
        examCoreNavigator.showContextView();
        int index = getIndexByQuestionNum(localAddress);
        LogUtils.e("清理-----》》》444444");
        try {
            numObs.set(String.valueOf(localAddress));
        } catch (Exception e) {
            LogUtils.e("清理-----》》》666666");
        }
        examCoreNavigator.setGoToPage(index, answerSheetOptionBeans);
        LogUtils.e("清理-----》》》555555");
    }

    @Override
    public void getMaxSeeNum(List<AnswerGroupBean> answerSheetOptionBeans) {
        int maxNum = answerSheetOptionBeans.get(answerSheetOptionBeans.size() - 1).getQuestionNum();
        maxNumObs.set(maxNum);
    }

    @Override
    public int getIndexByQuestionNum(int questionNum) {
        List<AnswerGroupBean> answerGroupBeans = answerSheetOptionBeansObs.get();
        if (answerGroupBeans.isEmpty()) {
            return 0;
        }
        for (AnswerGroupBean answerGroupBean : answerGroupBeans) {
            if (answerGroupBean.getQuestionNum() == questionNum) {
                return answerGroupBean.getIndexs();
            }
        }
        return 0;
    }

    @Override
    public int getCurrentExamQuestionNumByIndex(int index) {
        try {
            return answerSheetOptionBeansObs.get().get(index).getQuestionNum();
        } catch (Exception e) {
            return 1;
        }
    }

    @Override
    public void getPacketAccessData(String currentPostion) {
        if (!StringUtils.isEmpty(currentPostion)) {
            int currentPostionInt = Integer.parseInt(currentPostion);
            if (examDataFlags.contains(currentPostion)) {
                int startIndex = examDataFlags.get(0);
                int endIndex = examDataFlags.get(examDataFlags.size() - 1);
                if (currentPostionInt - startIndex < 2 || endIndex - currentPostionInt < 3) {
                    examCoreRepository.addPostDataQueue(currentPostionInt, answerSheetOptionBeansObs.get());
                }
            } else {
                examCoreRepository.addPostDataQueue(currentPostionInt, answerSheetOptionBeansObs.get());
            }
        }
    }

    @Override
    public void getReadPostHttpExam(int index) {
        answerSheetOptionBeansObs.get().get(getIndexByQuestionNum(index)).setPageState(PageTypeConfig.PAGE_STATE_LOADING);
    }

    @Override
    public void getReadStartPostHttpExam(String qid, String count) {

        examCoreRepository.getExamListData(qid, paperType, paperId, false, count);
    }

    private boolean pageExam = false;

    @Override
    public void getExamListData(List<ExamPackAccessDataBean.RecordsBean> lists) {
        if (lists == null || lists.isEmpty() || lists.size() == 0) {
            examCoreNavigator.showEmptyView();
            return;
        }
        if (!pageExam) {
            if (downTime.get() > 0) {
                initDownTime(downTime.get());
            }
        }
        pageExam = true;
        if (checkRecordsBeanValues(lists)) {
            examCoreNavigator.showEmptyView();
            return;
        }
        for (ExamPackAccessDataBean.RecordsBean recordsBean : lists) {
            if (Integer.parseInt(paperType) == (ExamPaperTypeConfig.EXAM_PAPER_ERROR) || Integer.parseInt(paperType) ==
                    (ExamPaperTypeConfig.EXAM_PAPER_SAVE)) {
                if (recordsBean.getUserAnswer() == null || recordsBean.getUserAnswer().size() == 0) {
                } else {
                    recordsBean.getUserAnswer().clear();
                }
            }
            int index = getIndexByQuestionNum(recordsBean.getQuestionNum());
            if (examDataFlags.contains(index)) {
            } else {
                examDataFlags.add(index);
                ListUtil.sort(examDataFlags, new Comparator<Integer>() {
                    @Override
                    public int compare(Integer o1, Integer o2) {
                        return o1 > o2 ? 1 : 0;
                    }
                });

                AnswerGroupBean answerGroupBean = new AnswerGroupBean();
                answerGroupBean.setPageState(PageTypeConfig.PAGE_STATE_SUCCESS);
                answerGroupBean.setQuestionNum(recordsBean.getQuestionNum());
                answerGroupBean.setAnswerSheetOptionBean(recordsBean);
                if (examCoreNavigator == null) {
                    return;
                }
                examCoreNavigator.updataExamPage(getIndexByQuestionNum(recordsBean.getQuestionNum()), answerGroupBean);
            }
        }
        examCoreNavigator.showSaveView();
    }

    @Override
    public boolean checkRecordsBeanValues(List<ExamPackAccessDataBean.RecordsBean> lists) {
        if (lists == null || lists.size() == 0) {
            return true;
        }
        return false;
    }

    @Override
    public void findNoVipPermiss(int postion, int remainingQuestionsCount) {
        if (NeedCheckVipPermiss()) {
            if (!UserHelper.isBecomeVip()) {
                if (remainingQuestionsCount <= 0) {
                    checkPermiss(postion, remainingQuestionsCount);
                }
            }
        }
    }

    @Override
    public boolean NeedCheckVipPermiss() {
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_SAVE || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_ERROR) {
            return false;
        }
        return true;
    }

    @Override
    public void checkPermiss(int postion, int remainingQuestionsCount) {
        if (maxNumObs.get() == 0) {
            return;
        }
        int currentNum = getQuestionNumByIndex(postion);
        if (currentNum == -1) {
            return;
        }
        if (Integer.parseInt(countObs.get()) == currentNum) {
            return;
        }
        if (currentNum == maxNumObs.get()) {
            //表示最后一条了
            examCoreNavigator.noExamPermiss(remainingQuestionsCount == 0 ? false : true);
        }
    }

    @Override
    public int getQuestionNumByIndex(int index) {
        List<AnswerGroupBean> answerGroupBeans = answerSheetOptionBeansObs.get();
        if (answerGroupBeans.isEmpty()) {
            return -1;
        }
        List<AnswerGroupBean> answerGroupBeans1 = ListUtil.filter(answerGroupBeans, new Editor<AnswerGroupBean>() {
            @Override
            public AnswerGroupBean edit(AnswerGroupBean answerGroupBean) {
                if (answerGroupBean.getIndexs() == index) {
                    return answerGroupBean;
                }
                return null;
            }
        });
        if (answerGroupBeans1.isEmpty()) {
            return -1;
        } else {
            return answerGroupBeans1.get(0).getQuestionNum();
        }
    }

    @Override
    public void calculateResidueCount() {
        if (remainingQuestionsCount != 0) {
            remainingQuestionsCount--;
        }
    }

    @Override
    public int getResidueCount() {
        return remainingQuestionsCount;
    }

    public boolean userDo = false;

    @Override
    public void updataModelByQuestionNum(int questionNum, boolean rights) {
        ++doNum;
        userDo = true;
        for (ExamChaptersBean examChaptersBean : sheetLists) {
            for (AnswerSheetOptionBean answerSheetOptionBean : examChaptersBean.getItems()) {
                if (answerSheetOptionBean.getQuestionNum() == questionNum) {
                    answerSheetOptionBean.setRight(rights);
                    answerSheetOptionBean.setDone(true);
                }
            }
        }
        resetResultCount();
    }

    /**
     * 重置结果数量
     */
    public void resetResultCount() {
        int trueCount = 0;
        int falseCount = 0;
        for (ExamChaptersBean examChaptersBean : sheetLists) {
            for (AnswerSheetOptionBean answerSheetOptionBean : examChaptersBean.getItems()) {
                if (answerSheetOptionBean.isDone()) {
                    if (answerSheetOptionBean.isRight()) {
                        trueCount++;
                    } else {
                        falseCount++;
                    }
                }
            }
        }
        resultTrueCount.set(String.valueOf(trueCount));
        resultFalseCount.set(String.valueOf(falseCount));
    }


    @Override
    public void destory() {
        PointHelper.clernPointRouth(PointCheckType.CHECK_EXAM_CLEAL);
        ExamCoreMessageHandlerManager.getInstance().clearExamCoreMessage();
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ZJLX || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_TKLX || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ERROR || Integer.parseInt(
                paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_INTELLIGENT_PRACTICE
                || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_EvaluationReport
                || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_HIGH_FREQUENCY) {
            examCoreNavigator.showErrorToast("正在为您保存做题记录~");
        }
        DataHelper.postAllQuestion(2);
        LogUtils.e("结束关闭了------->>>>");
        RxJavaUtils.delay(500, TimeUnit.MILLISECONDS, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                examCoreFragment.finish();
                LogUtils.e("倒计时结束------->>>>");
                checkToResult();
            }
        }, new SimpleThrowableAction("RxJavaUtils"));
    }

    public void checkToResult() {
        LogUtils.e("----->>>>" + isCanBack + "****" + paperType);
        if (isCanBack) {
            return;
        }
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
            examCoreFragment.initArguments().putString("flutterRoute", "/results" + "/" + (UserHelper.isBecomeVip() ? "1" : "0") + "/" + paperType);
            examCoreFragment.initArguments().putBoolean("autoDialog", false);
            examCoreFragment.startFragment(HomeHelper.startMessageFragment());

        }
    }

    @Override
    public boolean canPostRecordData() {
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ERROR || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_ZJLX || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_TKLX || Integer.parseInt(
                paperType) == ExamPaperTypeConfig.EXAM_PAPER_LNZT || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_MNZT ||
                Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_INTELLIGENT_PRACTICE
                || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_EvaluationReport
                || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_HIGH_FREQUENCY) {
            return true;
        }
        return false;
    }

    @Override
    public void reshExamData(int index) {
        LogUtils.e("reshExamData----->>>>" + index);
        DataHelper.postAllQuestion(3);
        int location = getQuestionNumByIndex(index);
        examCoreNavigator.showLoadingView();
        answerSheetOptionBeansObs.get().size();
        ExamCoreMessageHandlerManager.getInstance().clearExamCoreMessage();
        examDataFlags.clear();
        examCoreRepository.getExamSheetListData(paperType, paperId, false, location);
    }

    @Override
    public void shaperWechat(String questionId) {
        if (StringUtils.isEmpty(questionId)) {
            return;
        }
        SdkHelper.initJpushConfig(BaseConfig.deBug);
        SdkHelper.shaperWx(examCoreFragment.getActivity(), questionId);
    }

    @Override
    public void noExamPermiss(boolean need, boolean ponter) {
        if (ponter) {
            PointHelper.joinPointData(PointerMsgType.POINTER_A_PROBLEMPAGE_TRIGGERBOUNDCED, true);
        }
        examCoreNavigator.noExamPermiss(need);
    }

    @Override
    public void viewPageListener(int postion, boolean first) {
        examCoreNavigator.showSaveView();
        checkGuilder(postion, first);
        numObs.set(String.valueOf(answerSheetOptionBeansObs.get().get(postion).getQuestionNum()));
        checkPageFinsh(postion, false);
    }

    @Override
    public void checkGuilder(int postion, boolean first) {
        LogUtils.e("开启----->>>1");
        examCoreNavigator.showGuilderView(0);
        if (!first) {
            examCoreNavigator.afterGetLocalData(String.valueOf(postion));
        }
    }

    @Override
    public void initFaceBackPopup() {
        if (examCoreNavigator == null) {
            return;
        }
        examCoreNavigator.initFaceBackPopup();
    }

    @Override
    public void initLoadingPopup() {
        if (examCoreNavigator == null) {
            return;
        }
        examCoreNavigator.initLoadingPopup();
    }

    @Override
    public void checkPointerRouth() {
        if (PointHelper.checkPoinRouth(PointCheckType.CHECK_MAIN_HOMEBT_CHAPER_EXAM)) {
            PointHelper.joinPointData(PointerMsgType.POINTER_A_HOME_STARTPRACTICE_CHAPTERLIST_PROBLEMPAGE, false);
        }
    }

    @Override
    public void restartExam() {
        examCoreNavigator.showLoadingView();
        answerSheetOptionBeansObs.get().size();
        examDataFlags.clear();
        ExamCoreMessageHandlerManager.getInstance().clearExamCoreMessage();
        examCoreRepository.getExamSheetListData(paperType, paperId, true, location);
        examCoreNavigator.setRestExam();
    }

    @Override
    public String getUserConfimHandPager() {
        int noCount = 0;
        for (ExamChaptersBean examChaptersBean : sheetLists) {
            for (AnswerSheetOptionBean answerSheetOptionBean : examChaptersBean.getItems()) {
                if (answerSheetOptionBean.isDone()) {
                } else {
                    noCount++;
                }
            }
        }
        if (noCount == 0) {
            return "您已答完所有题目，确认要交卷吗？交卷后，不能继续练习，只能重新练习哦~";
        }
        return "还有" + noCount + "个题目没有做,确认要交卷吗？交卷后,不能继续练习,只能重新练习哦~";
    }

    @Override
    public void canCelQuestion(String questionId) {
        Map<String, String> params = new HashMap<>();
        params.put("questionId", questionId);
        examCoreNavigator.showLoadingDataView(true, "取消中...");
        launchOnlyResult(mApi.userCanCelQuestion(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                examCoreNavigator.showLoadingDataView(false, null);
                examCoreNavigator.successSaveState(false);
                examCoreNavigator.showSaveView();
                //                ToastUtils.normal(data.Message, Gravity.CENTER);
                examCoreNavigator.showErrorToast(data.Message);
            }

            @Override
            public void error(String msg) {
                examCoreNavigator.showLoadingDataView(false, null);
                examCoreNavigator.showErrorToast(msg);
            }
        });
    }

    @Override
    public void saveQuestion(String questionId, String userAnswer, String paperType) {
        Map<String, String> params = new HashMap<>();
        params.put("questionId", questionId);
        params.put("userAnswer", userAnswer);
        params.put("paperType", paperType);
        examCoreNavigator.showLoadingDataView(true, "收藏中...");
        launchOnlyResult(mApi.userSaveQuestion(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                examCoreNavigator.showLoadingDataView(false, null);
                examCoreNavigator.successSaveState(true);
                examCoreNavigator.showSaveView();
                examCoreNavigator.showErrorToast(data.Message);
            }

            @Override
            public void error(String msg) {
                examCoreNavigator.showLoadingDataView(false, null);
                examCoreNavigator.showErrorToast(msg);
            }
        });
    }

    @Override
    public void postHandPaper() {
        if (examCoreNavigator == null) {
            return;
        }
        examCoreNavigator.showLoadingDataView(true, "交卷中...");
        DataHelper.userStartHandPaper(new ExamDataUploadListener() {
            @Override
            public void onExamHandPaper(boolean success) {
                examCoreNavigator.showLoadingDataView(false, "");
                if (success) {
                    //如果是摸底测评直接跳转到查看测试结果页面
                    if (ExamPaperTypeConfig.EXAM_EvaluationReport == Integer.parseInt(paperType)) {
                        //跳转到测评结果页面
//                        //跳转到结果页面
//                        ExamResultFragment resultFragment = new ExamResultFragment();
//                        // 传递参数
//                        initArguments().putString("paperType", "21"); // 智能练习类型
//                        initArguments().putString("paperId", paperId);
//                        startFragment(resultFragment);

                        examCoreNavigator.toExamResult("20");
                    } else {
                        examCoreNavigator.postHandPaperSuccess();
                    }
                } else {
                    ToastUtils.normal("交卷失败，请重新提交", Gravity.CENTER);
                }
            }
        });
    }

    @Override
    public void postFaceBackData(String opion, String questionId, String context, int currentPostion, AnswerGroupBean selectOption,
                                 int questionTabInfo) {
        Map<String, String> params = new HashMap<>();
        params.put("type", opion);
        params.put("questionId", questionId);
        params.put("refferPage", "3");
        if (selectOption != null && selectOption.getAnswerSheetOptionBean() != null &&
                ExamQuestionTypeConfig.EXAM_QUESTIONP_AL_PAN == selectOption.getAnswerSheetOptionBean().getQuestionType()) {
            params.put("content", "问题" + (questionTabInfo + 1) + "：" + (context != null ? context : ""));
        } else if (!StringUtils.isEmpty(context)) {
            params.put("content", context);
        }
        launchOnlyResult(mApi.userPostFaceBackData(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                examCoreNavigator.postFaceBackState(true);
                ToastUtils.normal("提交成功，感谢反馈", Gravity.CENTER);
                dissFaceBackView();
            }

            @Override
            public void error(String msg) {
                examCoreNavigator.showErrorToast(msg);
                examCoreNavigator.postFaceBackState(false);
            }
        });
    }

    @Override
    public void dissFaceBackView() {
        if (faceBackDisposable != null) {
            faceBackDisposable.dispose();
        }
        //        faceBackDisposable = RxJavaUtils.delay(4, new Consumer<Long>() {
        //            @Override
        //            public void accept(Long aLong) throws Exception {
        //                if (examCoreNavigator == null) return;
        //                examCoreNavigator.dissFaceBackView();
        //            }
        //        });
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.examsheetLl) {
            List<ExamChaptersBean> examChaptersBeans = sheetLists;
            if (examCoreNavigator == null) {
                return;
            }
            examCoreNavigator.initSheetPopup(examChaptersBeans);
        } else if (view.getId() == R.id.tv_submit) {
            if (examCoreNavigator == null) {
                return;
            }
            examCoreNavigator.startHandPaper(true);
        } else if (view.getId() == R.id.examSaveLl) {
            examCoreNavigator.saveQuestion(getIndexByQuestionNum(Integer.parseInt(numObs.get())));
        }
    }

    @Override
    public void initDownTime(long time) {
        if (examCoreNavigator == null) {
            return;
        }
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_MNZT || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
            downTimeObs.set(true);
            examCoreNavigator.showDownTime(true, time);
        } else {
            downTimeObs.set(false);
            examCoreNavigator.showDownTime(false, 0);
        }
    }

    @Override
    public void checkPageFinsh(int index, boolean onclick) {
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
            if ((maxNumObs.get() - 1) == index) {
                if (lastOptionHaveAnswer()) {
                    //                    showPageFinshView();
                }
            }
        } else {
            if (onclick) {
                showPageFinshView();
            } else {
                if (numObs.get() == String.valueOf(maxNumObs.get())) {
                    showPageFinshView();
                }
            }
        }

    }

    @Override
    public boolean lastOptionHaveAnswer() {
        return sheetLists.get(sheetLists.size() - 1).getItems().get(sheetLists.get(sheetLists.size() - 1).getItems().size() - 1).isDone();
    }

    @Override
    public void showPageFinshView() {
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
            examCoreNavigator.openSheetPopup();
        } else if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_TKLX || Integer.parseInt(paperType) ==
                ExamPaperTypeConfig.EXAM_PAPER_ZJLX) {

            if (maxSeeNum == doNum) {
                examCoreNavigator.showErrorToast("恭喜你完成所有试题练习");
            }
        }
    }

    /**
     * 检测下一题
     */
    public boolean checkNextPage(ExamContentAdapter adapter, int currentPosition, boolean isToast) {
        if (adapter != null) {
            if (adapter.getItemCount() > currentPosition) {
                AnswerGroupBean item = adapter.getItem(currentPosition);
                if (item != null && item.getAnswerSheetOptionBean() != null) {
                    if (item.getAnswerSheetOptionBean().getQuestionNum() == getSumCount()) {
                        if (isToast) {
                            ToastUtils.normal("当前已经是最后一题", Gravity.CENTER);
                        }
                        return false;
                    }
                    if (!UserHelper.isBecomeVip() && maxNumObs != null && maxNumObs.get() != null &&
                            item.getAnswerSheetOptionBean().getQuestionNum() == maxNumObs.get()) {
                        noExamPermiss(true, false);
                        return false;
                    }
                }
            }
        }
        return true;
    }

    public int getSumCount() {
        int count = 0;
        if (sheetLists != null) {
            for (ExamChaptersBean sheetList : sheetLists) {
                if (sheetList != null) {
                    count += sheetList.getQuestionCount();
                }
            }
        }
        return count;
    }

}
