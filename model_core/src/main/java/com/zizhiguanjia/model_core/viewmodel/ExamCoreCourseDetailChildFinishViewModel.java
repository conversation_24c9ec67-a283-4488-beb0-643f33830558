package com.zizhiguanjia.model_core.viewmodel;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_core.api.CoreExamServiceApi;
import com.zizhiguanjia.model_core.bean.ExamCoreCourseDetailChildFinishBean;
import com.zizhiguanjia.model_core.fragment.ExamCoreCourseDetailChildFinishFragment;
import com.zizhiguanjia.model_core.navigator.ExamCoreCourseDetailChildFinishNavigator;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能作用：课程详情习题答完的结果页面
 * 初始注释时间： 2023/11/26 20:42
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class ExamCoreCourseDetailChildFinishViewModel extends CommonViewModel {
    /**
     * 交互回调
     */
    private ExamCoreCourseDetailChildFinishNavigator mNavigator;
    /**
     * 页面实例
     */
    private ExamCoreCourseDetailChildFinishFragment mFragment;
    /**
     * 接口实例
     */
    private final CoreExamServiceApi mApi=new Http().create(CoreExamServiceApi.class);

    public void initParams(ExamCoreCourseDetailChildFinishFragment fragment, ExamCoreCourseDetailChildFinishNavigator navigator){
        this.mFragment = fragment;
        this.mNavigator=navigator;
        getHttpData();
    }
    public void getHttpData(){
        mNavigator.showLoading(true);
        Map<String,String> map = new HashMap<>();
        map.put("paperType",mFragment.getString("paperType"));
        map.put("papervalue",mFragment.getString("paperId"));
        launchOnlyResult(mApi.getCourseDetailChildFinishData(map), new OnHandleException<BaseData<ExamCoreCourseDetailChildFinishBean>>() {
            @Override
            public void success(BaseData<ExamCoreCourseDetailChildFinishBean> data) {
                mNavigator.setShowData(data.Data);
                mNavigator.showLoading(false);
            }

            @Override
            public void error(String msg) {
                mNavigator.showLoading(false);
            }
        });
    }
}
