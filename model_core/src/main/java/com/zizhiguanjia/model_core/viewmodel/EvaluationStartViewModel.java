package com.zizhiguanjia.model_core.viewmodel;

import android.annotation.SuppressLint;

import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.wb.lib_network.BaseViewModel;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_core.api.CoreExamServiceApi;
import com.zizhiguanjia.model_core.bean.EvaluationStartBean;
import com.zizhiguanjia.model_core.navigator.EvaluationStartNavigator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 摸底测评开始页ViewModel
 */
public class EvaluationStartViewModel extends BaseViewModel {

    // 页面数据
    public ObservableField<String> title = new ObservableField<>("");
    public ObservableField<String> subTitle = new ObservableField<>("");
    public ObservableField<String> description = new ObservableField<>("");
    public ObservableField<String> questionCount = new ObservableField<>("0");
    public ObservableField<String> estimateTime = new ObservableField<>("0");
    public ObservableField<Boolean> isFinished = new ObservableField<>(false);
    public ObservableField<String> lastScore = new ObservableField<>("0");
    public MutableLiveData<Boolean> isVip = new MutableLiveData<>();
    
    // 试卷ID
    private String paperId;
    
    // 章节覆盖率数据
    public ObservableArrayList<ChapterCoverageItem> chapterCoverages = new ObservableArrayList<>();
    
    // 导航器
    private EvaluationStartNavigator navigator;
    
    /**
     * 章节覆盖率项
     */
    public static class ChapterCoverageItem {
        public String chapterName;
        public String coverage;
        public String questionCount;
        
        public ChapterCoverageItem(String chapterName, String coverage, String questionCount) {
            this.chapterName = chapterName;
            this.coverage = coverage;
            this.questionCount = questionCount;
        }
    }
    
    /**
     * 初始化方法
     */
    public void init() {
        // 检查VIP状态
        isVip.setValue(UserHelper.isBecomeVip());
        
        // 加载摸底测评数据
        loadEvaluationData();
    }
    
    /**
     * 设置导航器
     */
    public void setNavigator(EvaluationStartNavigator navigator) {
        this.navigator = navigator;
    }
    
    /**
     * 加载摸底测评数据
     */
    @SuppressLint("CheckResult")
    private void loadEvaluationData() {
        try {
            // 准备请求参数
            Map<String, String> params = new HashMap<>();
//            params.put("paperType", "1"); // 摸底测评类型
            
            // 使用API接口
            CoreExamServiceApi api = new Http().create(CoreExamServiceApi.class);
            launchOnlyResult(api.getEvaluation(params), new BaseViewModel.OnHandleException<BaseData<Objects>>() {
                @Override
                public void success(BaseData<Objects> data) {
                    if (data != null && data.Data != null) {
                        // 处理API返回的数据
//                        processEvaluationData(data.Data);
//
//                        // 通知UI更新
//                        if (navigator != null) {
//                            navigator.showContentView();
//                        }
                    } else {
                        // API返回数据为空，使用模拟数据
                        mockEvaluationData();
                    }
                }

                @Override
                public void error(String msg) {
                    LogUtils.e("获取摸底测评数据失败: " + msg);
                    
                    // 显示错误信息
                    if (navigator != null) {
                        navigator.showError(msg);
                    }
                    
                    // 在请求失败时使用模拟数据
                    mockEvaluationData();
                }
            });
        } catch (Exception e) {
            LogUtils.e("加载摸底测评数据异常：" + e.getMessage());
            e.printStackTrace();
            
            // 异常时使用模拟数据
            mockEvaluationData();
        }
    }
    
    /**
     * 处理摸底测评API返回的数据
     */
    private void processEvaluationData(EvaluationStartBean data) {
        // 更新UI数据
        title.set(data.getTitle());
        subTitle.set(data.getSubTitle());
        description.set(data.getDescription());
        questionCount.set(String.valueOf(data.getQuestionCount()) + "题");
        estimateTime.set(String.valueOf(data.getEstimateTime()) + "分钟");
        isFinished.set(data.isFinished());
        lastScore.set(String.format("%.1f", data.getLastScore()));
        isVip.setValue(data.isVip());
        
        // 保存试卷ID
        paperId = data.getPaperId();
        
        // 更新章节覆盖率数据
        updateChapterCoverages(data.getChapterCoverages());
    }
    
    /**
     * 更新章节覆盖率数据
     */
    private void updateChapterCoverages(List<EvaluationStartBean.ChapterCoverage> coverages) {
        // 清空现有数据
        chapterCoverages.clear();
        
        // 检查数据有效性
        if (coverages == null || coverages.isEmpty()) {
            return;
        }
        
        // 添加新数据
        for (EvaluationStartBean.ChapterCoverage coverage : coverages) {
            ChapterCoverageItem item = new ChapterCoverageItem(
                    coverage.getChapterName(),
                    String.format("%.1f%%", coverage.getCoverage()),
                    String.valueOf(coverage.getQuestionCount()) + "题"
            );
            chapterCoverages.add(item);
        }
    }
    
    /**
     * 开始测评
     */
    public void startEvaluation() {
        // 检查试卷ID
        if (paperId == null || paperId.isEmpty()) {
            if (navigator != null) {
                navigator.showError("试卷信息不完整，无法开始测评");
            }
            return;
        }
        
        // 导航到测评页面
        if (navigator != null) {
            navigator.navigateToEvaluation(paperId);
        }
    }
    
    /**
     * 模拟摸底测评数据
     */
    private void mockEvaluationData() {
        // 模拟基本信息
        title.set("摸底测评");
        subTitle.set("测试你的基础水平");
        description.set("通过这次测评，我们将全面评估您的知识掌握情况，为您提供个性化的学习计划。");
        questionCount.set("50题");
        estimateTime.set("30分钟");
        isFinished.set(false);
        lastScore.set("0.0");
        
        // 生成模拟的试卷ID
        paperId = "mock-evaluation-paper-id";
        
        // 模拟章节覆盖率数据
        List<ChapterCoverageItem> mockItems = new ArrayList<>();
        mockItems.add(new ChapterCoverageItem("安全生产法律法规", "30.0%", "15题"));
        mockItems.add(new ChapterCoverageItem("施工现场安全管理", "40.0%", "20题"));
        mockItems.add(new ChapterCoverageItem("职业健康与环境保护", "30.0%", "15题"));
        
        // 更新到ObservableArrayList
        chapterCoverages.clear();
        chapterCoverages.addAll(mockItems);
    }
} 