package com.zizhiguanjia.model_core.viewmodel;

import androidx.databinding.ObservableField;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.utils.DataUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.model_core.config.ExamQuestionTypeConfig;
import com.zizhiguanjia.model_core.config.ExamResultStyleTypeConfig;
import com.zizhiguanjia.model_core.config.QuestionTypeConfig;
import com.zizhiguanjia.model_core.listener.ExamUserAnswerListener;
import com.zizhiguanjia.model_core.listener.IExamUserAnswer;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.model.OriginImageBean;
import com.zizhiguanjia.model_core.model.RequestUserAnswerBean;
import com.zizhiguanjia.model_core.navigator.ExamNavigator;
import com.zizhiguanjia.model_core.utils.ExamUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;

public class ExamViewModel extends CommonViewModel implements IExamUserAnswer {
    private AnswerGroupBean answerGroupBean;
    private ExamNavigator navigator;
    private int examType;
    public ObservableField<Boolean> isConfimObs=new ObservableField<>();;
    public ObservableField<Integer>questionTypeStateObs=new ObservableField<>();
    @Override
    public void showContextView(AnswerGroupBean answerGroupBean, int paperType, ExamNavigator examNavigator) {
        questionTypeStateObs.set(8888);
        if(DataUtils.isEmpty(answerGroupBean))return;
        initData(answerGroupBean, paperType, examNavigator);
    }
    @Override
    public void initData(AnswerGroupBean answerGroupBean, int paperType, ExamNavigator examNavigator) {
        this.answerGroupBean=answerGroupBean;
        this.examType=paperType;
        this.navigator = examNavigator;
        initAnswerGroupBean(answerGroupBean);
    }


    @Override
    public void initAnswerGroupBean(AnswerGroupBean answerGroupBean) {
        if(getAnswerItemBean()==null)return;
        if(getAnswerItemBean().getQuestionType()== QuestionTypeConfig.QuestionType_CASE){
            //暂不支持
            initCaseView();
        } else if(getAnswerItemBean().getQuestionType()== QuestionTypeConfig.QuestionType_ALCHOICE){
            initAlChoiceData();
        } else {
            initDataCommon();
        }
    }

    private void initCaseView() {
        if(ObjectUtil.hasEmpty(answerGroupBean))return;
        questionTypeStateObs.set(2);
        navigator.showCaseMaterialsView();
        navigator.showCaseQuestionOptionsView();
    }

    @Override
    public ExamPackAccessDataBean.RecordsBean getAnswerItemBean() {
        return answerGroupBean.getAnswerSheetOptionBean();
    }

    @Override
    public void initAlChoiceData() {
        questionTypeStateObs.set(3);
        navigator.showAlXzView();
    }

    @Override
    public void initDataCommon() {
        if(ObjectUtil.hasEmpty(answerGroupBean))return;
        questionTypeStateObs.set(1);
        getContextTitle();
        getOptionList();
        if(ExamUtils.getInstance().getExamQuestionResultStyle(examType)== ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_SELECT_NO){
            isConfimObs.set(true);
            getResultData(true);
        }else {
//            if(examType== ExamPaperTypeConfig.EXAM_PAPER_ERROR||examType== ExamPaperTypeConfig.EXAM_PAPER_SAVE){
//                try {
//                    answerGroupBean.getAnswerSheetOptionBean().getUserAnswer().clear();
//                }catch (Exception e){
//
//                }
//            }
            if(answerGroupBean.getAnswerSheetOptionBean().getUserAnswer()==null||answerGroupBean.getAnswerSheetOptionBean().getUserAnswer().size()==0){
                isConfimObs.set(false);
                return;
            }else {
                isConfimObs.set(ExamUtils.getInstance().getExamQuestionResultStyle(examType)== ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_NOR?false:true);
                getResultData(ExamUtils.getInstance().getExamQuestionResultStyle(examType)== ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_NOR?false:true);
            }
        }
    }

    @Override
    public void getContextTitle() {
        navigator.showContextTitlteView(getAnswerItemBean().getContent(),getAnswerItemBean().getQuestionType(),getAnswerItemBean().getImages());
    }

    @Override
    public void getOptionList() {
        navigator.showOptionView(getAnswerItemBean().getQuestions(),getAnswerItemBean().getQuestionType(),getAnswerItemBean().getUserAnswer(),getRightList(),getAnswerItemBean().getQuestionState());
    }

    @Override
    public void getResultData(boolean isconfig) {
        List<OriginImageBean> originImage=new ArrayList<>();
        List<String> rights=new ArrayList<>();
        List<String> jxStrs=new ArrayList<>();
        for(ExamPackAccessDataBean.RecordsBean.AnswersBean answersBean:getAnswerItemBean().getAnswers()){
            rights.add(answersBean.getText());
        }
        for(ExamPackAccessDataBean.RecordsBean.AnswerKeyBean answersBean:getAnswerItemBean().getAnswerKey()){
            originImage.addAll(answersBean.getImages());
            jxStrs.add(answersBean.getText());
        }
        navigator.showResultView(getAnswerItemBean().getUserAnswer(),rights,jxStrs.get(0),originImage,isconfig);
    }

    @Override
    public List<String> getRightList() {
        List<String> right=new ArrayList<>();
        for(ExamPackAccessDataBean.RecordsBean.AnswersBean answersBean:getAnswerItemBean().getAnswers()){
            right.add(answersBean.getText());
        }
        return right;
    }
    @Override
    public void userConfim(String option, ExamUserAnswerListener examToAutoPageListener){
        if(getAnswerItemBean().getQuestionType()== ExamQuestionTypeConfig.EXAM_QUESTION_DUO
                ||getAnswerItemBean().getQuestionType()== ExamQuestionTypeConfig.EXAM_QUESTION_DAN
                ||getAnswerItemBean().getQuestionType()==ExamQuestionTypeConfig.EXAM_QUESTION_PAN){
            //单选
            List<String> userAs= Arrays.asList(StringUtils.convertStrToArray2(option));
            getAnswerItemBean().getUserAnswer().clear();
            getAnswerItemBean().getUserAnswer().addAll(userAs);
            boolean right=ExamUtils.getInstance().getUserRight(getAnswerItemBean().getUserAnswer(),getRightList());
            getAnswerItemBean().setIsRight(right);
            getResultData(true);
            RequestUserAnswerBean requestUserAnswerBean=new RequestUserAnswerBean();
            requestUserAnswerBean.setId(getAnswerItemBean().getQuestionId()+"");
            requestUserAnswerBean.setRight(right);
            requestUserAnswerBean.setQuestionNum(getAnswerItemBean().getQuestionNum()+"");
            requestUserAnswerBean.setUserAnswer( getAnswerItemBean().getUserAnswer());
            DataHelper.uploadRecolder(GsonUtils.gsonString(requestUserAnswerBean));
            isConfimObs.set(ExamUtils.getInstance().getExamQuestionResultStyle(examType)== ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_NOR?false:true);
            if(ExamUtils.getInstance().getExamQuestionResultStyle(examType)== ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_NOR){
                if(examToAutoPageListener==null)return;
                examToAutoPageListener.toPageByAuto(true);
            }else {
                if(examToAutoPageListener==null)return;
                examToAutoPageListener.toPageByAuto(right);
            }
        }
    }

}
