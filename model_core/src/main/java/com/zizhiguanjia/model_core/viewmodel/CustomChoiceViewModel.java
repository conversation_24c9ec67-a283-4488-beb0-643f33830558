package com.zizhiguanjia.model_core.viewmodel;

import com.wb.lib_network.BaseViewModel;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.model_core.listener.CustomChoiceListener;
import com.zizhiguanjia.model_core.listener.ExamToAutoPageListener;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.model.OriginImageBean;
import com.zizhiguanjia.model_core.navigator.CustomChoiceNavigator;
import com.zizhiguanjia.model_core.repository.ExamCustomChoiceRepository;

import java.util.List;

import androidx.databinding.ObservableField;

public class CustomChoiceViewModel extends BaseViewModel implements CustomChoiceListener {
    ObservableField<AnswerGroupBean> answerGroupBeanObservableField=new ObservableField<>();
    private ExamCustomChoiceRepository customChoiceRepository;
    private CustomChoiceNavigator choiceNavigator;
    public ObservableField<Integer> questionTypeStateObs=new ObservableField<>();
    public ObservableField<Boolean> isConfimObs=new ObservableField<>();
    public ObservableField<ExamCoreThemeConfigThemeBean> themeBean = new ObservableField<>();
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    private ExamCoreStyleConfigBean styleConfigBean;
    public void initParams(AnswerGroupBean answerGroupBean,int questionType,CustomChoiceNavigator customChoiceNavigator){
        customChoiceRepository=new ExamCustomChoiceRepository();
        this.choiceNavigator=customChoiceNavigator;
        answerGroupBeanObservableField.set(answerGroupBean);
        customChoiceRepository.init(this,answerGroupBean,questionType);
    }
    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean.set(themeBean);
        this.textSizeBean = textSizeBean;
    }

    @Override
    public void showResultView(List<String> mUserAnswers, List<String> mRightAnswers, String txt, List<OriginImageBean> objects, boolean jtjq) {
        choiceNavigator.showResultView(mUserAnswers,mRightAnswers,txt,objects,jtjq);
    }

    @Override
    public void showContextTitlteView(String context, int questionType,List<OriginImageBean> iamges) {
        choiceNavigator.showContextTitlteView(context,questionType,iamges);
    }

    @Override
    public void showOptionView(List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> examOptionBeans, int questionType, List<String> userAnswer, List<String> answer, int questionState) {
        choiceNavigator.showOptionView(examOptionBeans,questionType,userAnswer,answer,questionState);
    }

    @Override
    public void onQuestComfig(boolean comfig) {
        isConfimObs.set(comfig);
    }

    @Override
    public void onQuestionTypeState(int state) {
        questionTypeStateObs.set(state);
    }

    @Override
    public void getQuestionsListDatas(List<String> arrs) {
        if(choiceNavigator==null)return;
        choiceNavigator.getQuestionListData(arrs);
    }

    @Override
    public void showOptionTagsList(String context, int questionType,List<OriginImageBean> iamges) {
        if(choiceNavigator==null)return;
        choiceNavigator.showOptionTagsList(context,questionType,iamges);
    }

    @Override
    public void userConfim(String option, ExamToAutoPageListener examToAutoPageListener) {
        customChoiceRepository.userConfim(option,examToAutoPageListener);
    }

    @Override
    public List<String> getRightList() {
        return customChoiceRepository.getRightList();
    }
    public boolean checkQuestionRight(){
        return customChoiceRepository.checkQuestionRight();
    }
    @Override
    public List<String> getUserAnswer() {
        return customChoiceRepository.getUserAnswer();
    }

    @Override
    public void userQhQuestion(int index) {
        customChoiceRepository.reshIndex(index);
    }
}
