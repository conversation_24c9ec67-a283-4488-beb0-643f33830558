package com.zizhiguanjia.model_core.viewmodel;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.wb.lib_network.BaseViewModel;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_core.api.CoreExamServiceApi;
import com.zizhiguanjia.model_core.bean.ExamEvaluationReportBean;
import com.zizhiguanjia.model_core.navigator.ExamResultNavigator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExamResultViewModel extends BaseViewModel {

    // 总题目数
    public ObservableField<String> totalQuestions = new ObservableField<>();
    public ObservableField<String> paperValue = new ObservableField<>();
    // 正确题目数
    public ObservableField<String> correctQuestions = new ObservableField<>();
    // 错误题目数
    public ObservableField<String> wrongQuestions = new ObservableField<>();
    // 正确率
    public ObservableField<String> correctRate = new ObservableField<>();
    // 分数
    public ObservableField<String> score = new ObservableField<>();
    
    // 试卷类型 - 用于UI判断是否显示成绩
    public ObservableField<String> paperType = new ObservableField<>("21"); // 默认为智能练习类型

    // 薄弱知识点
    public ObservableField<String[]> weakPoints = new ObservableField<>();
    // 已掌握知识点
    public ObservableField<String[]> masteredPoints = new ObservableField<>();

    // 单选题结果
    public MutableLiveData<List<Boolean>> singleChoiceResults = new MutableLiveData<>();
    // 单选题题号
    public MutableLiveData<List<Integer>> singleChoiceNumbers = new MutableLiveData<>();

    // 多选题结果
    public MutableLiveData<List<Boolean>> multipleChoiceResults = new MutableLiveData<>();
    // 多选题题号
    public MutableLiveData<List<Integer>> multipleChoiceNumbers = new MutableLiveData<>();

    // 判断题结果
    public MutableLiveData<List<Boolean>> judgmentResults = new MutableLiveData<>();
    // 判断题题号
    public MutableLiveData<List<Integer>> judgmentNumbers = new MutableLiveData<>();
    public MutableLiveData<Boolean> isContinue = new MutableLiveData<>();

    // 导航器
    private ExamResultNavigator navigator;

    // 参数
    private String paperId;

    /**
     * 初始化方法，设置参数并加载数据
     *
     * @param paperType 试卷类型（21表示智能练习）
     * @param paperId   试卷ID
     */
    public void init(String paperType, String paperId) {
        this.paperType.set(paperType);
        this.paperId = paperId;

        // 加载考试结果数据
        loadExamResultData();
    }

    /**
     * 加载考试结果数据
     */
    private void loadExamResultData() {
        // 检查参数
        if (paperId == null || paperId.isEmpty()) {
            paperId = "1"; // 使用默认ID
        }

        // 请求考试结果数据
        getEvaluationReport(paperId, this.paperType.get());
    }

    /**
     * 设置导航器
     */
    public void setNavigator(ExamResultNavigator navigator) {
        this.navigator = navigator;
    }

    /**
     * 获取智能练习结果数据
     *
     * @param paperId 试卷ID
     * @param paperTypeValue 试卷类型值
     */
    public void getEvaluationReport(String paperId, String paperTypeValue) {
        Map<String, String> params = new HashMap<>();
        params.put("paperType", paperTypeValue); // 传入试卷类型
        params.put("paperId", paperId);

        CoreExamServiceApi api = new Http().create(CoreExamServiceApi.class);
        launchOnlyResult(api.getEvaluationReport(params), new BaseViewModel.OnHandleException<BaseData<ExamEvaluationReportBean>>() {
            @Override
            public void success(BaseData<ExamEvaluationReportBean> data) {
                if (data != null && data.Data != null) {
                    ExamEvaluationReportBean report = data.Data;
                    // 处理返回的数据
                    processReportData(report);

                    // 通知UI更新
                    if (navigator != null) {
                        navigator.showContentView();
                    }
                } else {
                    // 如果API返回为空，则使用模拟数据
                    mockEvaluationReportData();
                }
            }

            @Override
            public void error(String msg) {
                LogUtils.d("获取考试结果失败: " + msg);
                // 在请求失败时使用模拟数据
                mockEvaluationReportData();
            }
        });
    }

    /**
     * 处理API返回的报告数据
     */
    private void processReportData(ExamEvaluationReportBean report) {
        // 处理统计数据
        int total = (int) report.getTotal();
        int rightCount = (int) report.getRightCount();
        int errorCount = (int) report.getErrorCount();
        int passRate = (int) report.getPassRate();
        double scores = report.getScores();
        boolean canContinue = report.isCanContinue();
        paperValue.set(report.getPaperValue());
        totalQuestions.set(String.valueOf(total));
        correctQuestions.set(String.valueOf(rightCount));
        wrongQuestions.set(String.valueOf(errorCount));
        correctRate.set(passRate + "%");
        score.set(String.format("%.0f", scores));
        isContinue.setValue(canContinue);

        // 处理知识点数据
        weakPoints.set(processKnowledgePoints(report.getWeakKnowledgePoints()));
        masteredPoints.set(processKnowledgePoints(report.getMasterKnowledgePoints()));

        // 处理题目结果数据
        processQuestionResults(report);
    }

    /**
     * 处理题目结果数据
     */
    private void processQuestionResults(ExamEvaluationReportBean report) {
        if (report.getQuestions() != null) {
            for (ExamEvaluationReportBean.QuestionCategory category : report.getQuestions()) {
                String questionName = category.getQuestionName();
                List<Boolean> results = new ArrayList<>();
                List<Integer> numbers = new ArrayList<>();

                // 处理每一题的结果
                if (category.getItems() != null) {
                    for (ExamEvaluationReportBean.QuestionItem item : category.getItems()) {
                        // 从QuestionNum中获取整数题号
                        int questionNum = (int) item.getQuestionNum(); // 保留整数部分
                        numbers.add(questionNum);

                        if (item.isDone()) {
                            results.add(item.isRight()); // 已做的题，添加正确/错误状态
                        } else {
                            results.add(null); // 未做的题，添加null
                        }
                    }
                }

                // 根据题目类型设置到相应的LiveData
                if ("单选题".equals(questionName)) {
                    singleChoiceResults.setValue(results);
                    singleChoiceNumbers.setValue(numbers);
                } else if ("多选题".equals(questionName)) {
                    multipleChoiceResults.setValue(results);
                    multipleChoiceNumbers.setValue(numbers);
                } else if ("判断题".equals(questionName)) {
                    judgmentResults.setValue(results);
                    judgmentNumbers.setValue(numbers);
                }
            }
        }
    }

    /**
     * 处理知识点列表，将List转换为数组
     */
    private String[] processKnowledgePoints(List<String> knowledgePoints) {
        if (knowledgePoints != null && !knowledgePoints.isEmpty()) {
            return knowledgePoints.toArray(new String[0]);
        }
        return new String[0]; // 返回空数组
    }

    /**
     * 模拟生成评估报告数据
     */
    private void mockEvaluationReportData() {
        // 模拟分数和统计数据
        setScore("11");
        setTotalQuestions("20");
        setCorrectQuestions("5");
        setWrongQuestions("2");
        setCorrectRate("55%");

        // 模拟知识点数据
        setWeakPoints(new String[]{
                "法律法规——建筑施工企业资质管理",
                "特种设备管理——特种设备使用管理",
                "安全生产管理——安全生产教育培训",
                "职业健康——职业病防治职业病防治职业病防治职业病防治"
        });

        setMasteredPoints(new String[]{
                "法律法规——建筑施工企业资质管理",
                "特种设备管理——特种设备使用管理",
                "安全生产管理——安全生产教育培训",
                "职业健康——职业病防治"
        });

        // 模拟题目结果数据
        List<Boolean> singleResults = new ArrayList<>();
        singleResults.add(true);   // 第1题答对
        singleResults.add(false);  // 第2题答错
        singleResults.add(null);   // 第3题未做
        singleResults.add(null);
        singleResults.add(null);
        singleResults.add(null);
        singleResults.add(null);
        singleChoiceResults.setValue(singleResults);

        // 模拟单选题题号
        List<Integer> singleNumbers = new ArrayList<>();
        singleNumbers.add(1);
        singleNumbers.add(2);
        singleNumbers.add(3);
        singleNumbers.add(4);
        singleNumbers.add(5);
        singleNumbers.add(6);
        singleNumbers.add(7);
        singleChoiceNumbers.setValue(singleNumbers);

        List<Boolean> multipleResults = new ArrayList<>();
        multipleResults.add(true);
        multipleResults.add(false);
        multipleResults.add(null);
        multipleResults.add(null);
        multipleResults.add(null);
        multipleResults.add(null);
        multipleResults.add(null);
        multipleChoiceResults.setValue(multipleResults);

        // 模拟多选题题号
        List<Integer> multipleNumbers = new ArrayList<>();
        multipleNumbers.add(5);
        multipleNumbers.add(6);
        multipleNumbers.add(7);
        multipleNumbers.add(8);
        multipleNumbers.add(9);
        multipleNumbers.add(10);
        multipleNumbers.add(11);
        multipleChoiceNumbers.setValue(multipleNumbers);

        List<Boolean> judgmentResults = new ArrayList<>();
        judgmentResults.add(true);
        judgmentResults.add(false);
        judgmentResults.add(null);
        judgmentResults.add(null);
        judgmentResults.add(null);
        judgmentResults.add(null);
        judgmentResults.add(null);
        this.judgmentResults.setValue(judgmentResults);

        // 模拟判断题题号
        List<Integer> judgmentNumbers = new ArrayList<>();
        judgmentNumbers.add(8);
        judgmentNumbers.add(9);
        judgmentNumbers.add(10);
        judgmentNumbers.add(11);
        judgmentNumbers.add(12);
        judgmentNumbers.add(13);
        judgmentNumbers.add(14);
        this.judgmentNumbers.setValue(judgmentNumbers);

        // 通知UI更新
        if (navigator != null) {
            navigator.showContentView();
        }
    }

    public void setTotalQuestions(String totalQuestions) {
        this.totalQuestions.set(totalQuestions);
    }

    public void setCorrectQuestions(String correctQuestions) {
        this.correctQuestions.set(correctQuestions);
    }

    public void setWrongQuestions(String wrongQuestions) {
        this.wrongQuestions.set(wrongQuestions);
    }

    public void setCorrectRate(String correctRate) {
        this.correctRate.set(correctRate);
    }

    public void setScore(String score) {
        this.score.set(score);
    }

    public void setWeakPoints(String[] weakPoints) {
        this.weakPoints.set(weakPoints);
    }

    public void setMasteredPoints(String[] masteredPoints) {
        this.masteredPoints.set(masteredPoints);
    }
} 