package com.zizhiguanjia.model_core.viewmodel;

import android.annotation.SuppressLint;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.wb.lib_network.BaseViewModel;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_core.api.CoreExamServiceApi;
import com.zizhiguanjia.model_core.bean.HighFrequencyBean;
import com.zizhiguanjia.model_core.navigator.HighFrequencyNavigator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 高频考题ViewModel
 */
public class HighFrequencyViewModel extends BaseViewModel {

    // 科目信息
    public ObservableField<String> subject = new ObservableField<>();

    // 题库总量
    public ObservableField<String> totalQuestions = new ObservableField<>();

    // 高频考题数量
    public ObservableField<String> highFrequencyCount = new ObservableField<>();

    // 已学习题目数量
    public ObservableField<String> learnedCount = new ObservableField<>();

    // 进度百分比
    public ObservableField<String> progressPercent = new ObservableField<>();
    
    // 是否是VIP
    public MutableLiveData<Boolean> isVip = new MutableLiveData<>();

    // 是否正在加载数据
    public MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    
    // 高频考点数据结构
    public static class KnowledgeCategory {
        private String title;
        private List<String> items;
        
        public KnowledgeCategory(String title) {
            this.title = title;
            this.items = new ArrayList<>();
        }
        
        public void addItem(String item) {
            items.add(item);
        }
        
        public String getTitle() {
            return title;
        }
        
        public List<String> getItems() {
            return items;
        }
    }
    
    // 高频考点列表
    public ObservableField<List<KnowledgeCategory>> knowledgeCategories = new ObservableField<>();
    
    // 导航器
    private HighFrequencyNavigator navigator;
    
    /**
     * 设置导航器
     */
    public void setNavigator(HighFrequencyNavigator navigator) {
        this.navigator = navigator;
    }
    
    /**
     * 初始化数据
     */
    public void init() {
        // 初始化科目信息
        initSubjectInfo();
        
        // 设置默认VIP状态
        boolean isUserVip = UserHelper.isBecomeVip();
        isVip.setValue(isUserVip);

        // 初始化loading状态为true，因为即将开始加载数据
        isLoading.setValue(true);
        LogUtils.e("HighFrequencyViewModel - init - 初始化loading状态为true，准备加载数据");

        // 加载高频考题数据
        loadHighFrequencyData();
    }
    
    /**
     * 初始化科目信息
     */
    private void initSubjectInfo() {
        // 获取当前选中的证书信息
        String certificateInfo = CertificateHelper.initCertificate(true);
        subject.set(certificateInfo);
    }
    
    /**
     * 加载高频考题数据
     */
    @SuppressLint("CheckResult")
    public void loadHighFrequencyData() {
        try {
            LogUtils.e("HighFrequencyViewModel - loadHighFrequencyData - 开始加载数据");

            // 准备请求参数
            Map<String, String> params = new HashMap<>();
            params.put("paperType", "22"); // 高频考题类型

            // 使用API接口
            CoreExamServiceApi api = new Http().create(CoreExamServiceApi.class);
            launchOnlyResult(api.getHighFrequency(params), new BaseViewModel.OnHandleException<BaseData<HighFrequencyBean>>() {
                @Override
                public void success(BaseData<HighFrequencyBean> data) {
                    // 加载完成，隐藏loading
                    isLoading.setValue(false);
                    LogUtils.e("HighFrequencyViewModel - loadHighFrequencyData - API成功，设置loading为false");

                    if (data != null && data.Data != null) {
                        // 处理API返回的数据
                        processHighFrequencyData(data.Data);

                        // 通知UI更新
                        if (navigator != null) {
                            navigator.showContentView();
                        }
                    } else {
                        // API返回数据为空，使用模拟数据
                        mockHighFrequencyData();
                    }
                }

                @Override
                public void error(String msg) {
                    // 加载失败，隐藏loading
                    isLoading.setValue(false);
                    LogUtils.e("HighFrequencyViewModel - loadHighFrequencyData - API失败，设置loading为false");

                    LogUtils.e("获取高频考题数据失败: " + msg);
                    // 在请求失败时使用模拟数据
                    mockHighFrequencyData();
                }
            });
        } catch (Exception e) {
            // 异常时隐藏loading
            isLoading.setValue(false);
            LogUtils.e("HighFrequencyViewModel - loadHighFrequencyData - 异常，设置loading为false");

            LogUtils.e("加载高频考题数据异常：" + e.getMessage());
            e.printStackTrace();

            // 异常时使用模拟数据
            mockHighFrequencyData();
        }
    }
    
    /**
     * 处理高频考题API返回的数据
     */
    private void processHighFrequencyData(HighFrequencyBean data) {
        // 更新题库总量
        totalQuestions.set((int)data.getAllQuestionCount() + "道");
        
        // 更新高频考题数量
        highFrequencyCount.set((int)data.getHfQuestionCount() + "道");
        
        // 更新已学习题目数量
        learnedCount.set((int)data.getDoQuestionCount() + "道");
        
        // 更新进度百分比
        String progress = data.getDoQuestionProgress();
        if (progress == null || progress.isEmpty()) {
            progressPercent.set("0%");
        } else {
            // 确保进度数据包含 % 符号
            if (!progress.endsWith("%")) {
                progress = progress + "%";
            }
            progressPercent.set(progress);
        }
        
        // 更新VIP状态
        isVip.setValue(data.isVip());
        
        // 更新科目信息
        String subjectInfo = data.getMajorName();
        if (data.getSubMajorName() != null && !data.getSubMajorName().isEmpty()) {
            subjectInfo += " " + data.getSubMajorName();
        }
        if (data.getAreaName() != null && !data.getAreaName().isEmpty()) {
            subjectInfo += " " + data.getAreaName();
        }
        subject.set(subjectInfo);
        
        // 更新知识点分类列表
        if (data.getKnowledgePoints() != null && !data.getKnowledgePoints().isEmpty()) {
            processKnowledgePoints(data.getKnowledgePoints());
        }
    }
    
    /**
     * 处理知识点数据
     */
    private void processKnowledgePoints(List<HighFrequencyBean.KnowledgePoint> knowledgePoints) {
        List<KnowledgeCategory> categories = new ArrayList<>();
        
        // 只处理一级知识点
        for (HighFrequencyBean.KnowledgePoint point : knowledgePoints) {
            if (point.getLevel() == 1.0) {
                KnowledgeCategory category = new KnowledgeCategory(point.getTitle());
                
                // 处理二级知识点
                if (point.getChildren() != null) {
                    processSecondLevelPoints(category, point.getChildren());
                }
                
                categories.add(category);
            }
        }
        
        knowledgeCategories.set(categories);
    }
    
    /**
     * 处理二级知识点
     */
    private void processSecondLevelPoints(KnowledgeCategory parentCategory, List<HighFrequencyBean.KnowledgePoint> children) {
        for (HighFrequencyBean.KnowledgePoint point : children) {
            // 添加二级知识点
            String secondLevelTitle = point.getTitle();
            parentCategory.addItem(secondLevelTitle);
            
            // 处理三级知识点
            if (point.getChildren() != null && !point.getChildren().isEmpty()) {
                for (HighFrequencyBean.KnowledgePoint thirdLevel : point.getChildren()) {
                    String thirdLevelTitle = "  • " + thirdLevel.getTitle();
                    parentCategory.addItem(thirdLevelTitle);
                }
            }
        }
    }
    
    /**
     * 将数字转换为中文数字（一、二、三...）
     */
    private String getChineseNumber(int number) {
        String[] chineseNumbers = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};
        if (number >= 1 && number <= 10) {
            return chineseNumbers[number - 1];
        }
        return String.valueOf(number);
    }
    
    /**
     * 模拟高频考题数据
     */
    private void mockHighFrequencyData() {
        // 创建模拟数据
        List<KnowledgeCategory> categories = new ArrayList<>();
        
        // 安全生产法律法规
        KnowledgeCategory category1 = new KnowledgeCategory("一、安全生产法律法规");
        category1.addItem("《中华人民共和国刑法》");
        category1.addItem("《中华人民共和国安全生产法》");
        category1.addItem("《生产安全事故应急条例》");
        category1.addItem("《生产安全事故罚款处罚规定》（试行）");
        categories.add(category1);
        
        // 建筑施工安全管理
        KnowledgeCategory category2 = new KnowledgeCategory("二、建筑施工安全管理");
        category2.addItem("《中华人民共和国建筑法》");
        category2.addItem("《建设工程安全生产管理条例》");
        category2.addItem("《危险性较大的分部分项工程安全管理规定》");
        categories.add(category2);
        
        // 消防安全管理
        KnowledgeCategory category3 = new KnowledgeCategory("三、消防安全管理");
        category3.addItem("《中华人民共和国消防法》");
        category3.addItem("《建设工程施工现场消防安全技术规范(GB50720-2011)》");
        category3.addItem("《施工现场临时用电安全技术规范》（JGJ46-2005）");
        categories.add(category3);
        
        // 职业健康管理
        KnowledgeCategory category4 = new KnowledgeCategory("四、职业健康管理");
        category4.addItem("《中华人民共和国职业病防治法》");
        category4.addItem("《职业病危害因素分类目录》");
        categories.add(category4);
        
        // 应急管理与事故处理
        KnowledgeCategory category5 = new KnowledgeCategory("五、应急管理与事故处理");
        category5.addItem("《生产安全事故报告和调查处理条例》");
        category5.addItem("《生产安全事故应急预案管理办法》");
        categories.add(category5);
        
        // 设置模拟数据
        knowledgeCategories.set(categories);
        
        // 设置模拟统计数据
        totalQuestions.set("3200道");
        highFrequencyCount.set("300道");

        // 根据VIP状态设置不同的学习进度数据
        Boolean vipStatus = isVip.getValue();
        if (vipStatus != null && vipStatus) {
            // VIP用户显示有学习进度的数据
            learnedCount.set("0道");
            progressPercent.set("0%");
        } else {
            // 非VIP用户不显示学习进度
            learnedCount.set("0道");
            progressPercent.set("0%");
        }
        
        // 确保loading状态已隐藏
        isLoading.setValue(false);
        LogUtils.e("HighFrequencyViewModel - mockHighFrequencyData - 模拟数据加载完成，设置loading为false");

        // 通知UI更新
        if (navigator != null) {
            navigator.showContentView();
        }
    }
    
    /**
     * 开始学习
     */
    public void startLearning() {
        if (navigator != null) {
            navigator.navigateToLearning();
        }
    }
} 