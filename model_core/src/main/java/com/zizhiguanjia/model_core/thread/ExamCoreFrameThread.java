package com.zizhiguanjia.model_core.thread;

import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_core.listener.ExamDataClearListenter;
import com.zizhiguanjia.model_core.listener.ExamThreadListenter;
import com.zizhiguanjia.model_core.listener.IExamCoreFrame;
import com.zizhiguanjia.model_core.manager.ExamCoreDataManager;
import com.zizhiguanjia.model_core.manager.ExamCoreMessageHandlerManager;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;

import java.util.Arrays;
import java.util.List;
import java.util.StringTokenizer;

public class ExamCoreFrameThread extends Thread implements IExamCoreFrame {
    public boolean isInterrupt;
    private List<AnswerGroupBean> answerGroupBeans;
    private Integer lastIndex=-1;
    private ExamThreadListenter call;
    private boolean mFirset=true;
    @Override
    public void setLastIndex(Integer lastIndex) {
        this.lastIndex = lastIndex;
    }
    @Override
    public void setCall(ExamThreadListenter call) {
        this.call = call;
    }
    @Override
    public void setAnswerGroupBeans(List<AnswerGroupBean> answerGroupBeans) {
        this.answerGroupBeans = answerGroupBeans;
    }
    @Override
    public void run() {
        while (!isInterrupt) {
            int index=-1;
            try {
                index= ExamCoreMessageHandlerManager.getInstance().getmDetectResultQueue().take();
            }catch (Exception e){
                continue;
            }
            if(index==-1){
            }else {
                if((index==0&&lastIndex==0)||(index!=lastIndex||lastIndex==-1)){
                    lastIndex=index;
                    if(call!=null){
                        ExamCoreDataManager.getInstance().getQuestionIdByLocalAddress(index, answerGroupBeans, new ExamDataClearListenter() {
                            @Override
                            public void afterClearSheetData(List<AnswerGroupBean> answerSheetOptionBeans, int localAddress, int QuestionCount, int Duration,int qqq) {

                            }
                            @Override
                            public void afterGetLocalData(String loacalData) {
                                if(StringUtils.isEmpty(loacalData))return;
                                String [] loacas=convertStrToArray2(loacalData);
                                List<String>strList= Arrays.asList(StringUtils.convertStrToArray2(loacas[0]));
                                call.getReadPostLoacl(strList,loacas[1],loacas[2]);
                            }
                        },mFirset);
                    }
                }
            }

        }
    }
    private   String[] convertStrToArray2(String str){
        StringTokenizer st = new StringTokenizer(str,"|");
        String[] strArray = new String[st.countTokens()];
        int i=0;
        while(st.hasMoreTokens()){
            strArray[i++] = st.nextToken().trim();
        }
        return strArray;
    }
    @Override
    public void interrupt() {
        isInterrupt = true;
        super.interrupt();
    }
}
