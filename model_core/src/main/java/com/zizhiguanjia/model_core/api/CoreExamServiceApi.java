package com.zizhiguanjia.model_core.api;

import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.model_core.bean.ExamCoreCourseDetailChildFinishBean;
import com.zizhiguanjia.model_core.bean.EvaluationStartBean;
import com.zizhiguanjia.model_core.bean.ExamEvaluationReportBean;
import com.zizhiguanjia.model_core.bean.HighFrequencyBean;

import java.util.Map;
import java.util.Objects;

import io.reactivex.Observable;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface CoreExamServiceApi {

    @POST(BaseAPI.VERSION_DES + "/API/ExamationCollection/CollectQuestion")
    @FormUrlEncoded
    Observable<BaseData> userSaveQuestion(@FieldMap Map<String, String> paeams);

    @POST(BaseAPI.VERSION_DES + "/API/ExamationCollection/CancelCollectionQuestion")
    @FormUrlEncoded
    Observable<BaseData> userCanCelQuestion(@FieldMap Map<String, String> paeams);

    @POST(BaseAPI.VERSION_DES + "/API/Common/AddFeedback")
    @FormUrlEncoded
    Observable<BaseData> userPostFaceBackData(@FieldMap Map<String, String> paeams);

    @POST(BaseAPI.VERSION_DES + "/API/Exam/SetUnSubmitReason")
    @FormUrlEncoded
    Observable<BaseData> userExit(@FieldMap Map<String, String> paeams);

    /**
     * 课程详情习题答完的结果
     */
    @POST(BaseAPI.VERSION_DES + "/API/Exam/GetCourseReport")
    @FormUrlEncoded
    Observable<BaseData<ExamCoreCourseDetailChildFinishBean>> getCourseDetailChildFinishData(@FieldMap Map<String, String> map);

    //获取智能练习结果
    @POST(BaseAPI.VERSION_DES + "/API/Exam/GetEvaluationReport")
    @FormUrlEncoded
    Observable<BaseData<ExamEvaluationReportBean>> getEvaluationReport(@FieldMap Map<String, String> map);
    //高频考题
    @POST(BaseAPI.VERSION_DES + "/API/Exam/GetHighFrequency")
    @FormUrlEncoded
    Observable<BaseData<HighFrequencyBean>> getHighFrequency(@FieldMap Map<String, String> map);
    //摸底测评开始页
    @POST(BaseAPI.VERSION_DES + "/API/Exam/GetEvaluation")
    @FormUrlEncoded
    Observable<BaseData<Objects>> getEvaluation(@FieldMap Map<String, String> map);
}
