package com.zizhiguanjia.model_core.event;

import com.jeremyliao.liveeventbus.core.LiveEvent;

/**
 * 功能作用：课程详情进入下一节
 * 初始注释时间： 2023/11/26 21:23
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class CourseDetailNextEvent implements LiveEvent {
    private String code;

    public String getCode() {
        return code;
    }

    public CourseDetailNextEvent(String code) {
        this.code = code;
    }
}
