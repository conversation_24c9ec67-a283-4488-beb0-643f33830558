package com.zizhiguanjia.model_core.fragment;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.databinding.Observable;
import androidx.lifecycle.ViewModelProvider;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.constants.CoreExamRouterPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.databinding.CoreHighFrequencyLayoutBinding;
import com.zizhiguanjia.model_core.navigator.HighFrequencyNavigator;
import com.zizhiguanjia.model_core.viewmodel.HighFrequencyViewModel;

import java.util.List;

@Route(path = CoreExamRouterPath.HIGH_FREQUENCY_FRAGMENT)
@BindRes(statusBarStyle = BarStyle.LIGHT_CONTENT, swipeBack = SwipeStyle.NONE)
public class HighFrequencyFragment extends BaseFragment implements HighFrequencyNavigator, TitleBar.OnLeftBarListener {

    private CoreHighFrequencyLayoutBinding binding;
    
    @BindViewModel
    HighFrequencyViewModel viewModel;
    
    @Override
    public int initLayoutResId() {
        return R.layout.core_high_frequency_layout;
    }
    
    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();
        
        // 初始化ViewModel
        if (viewModel == null) {
            viewModel = new ViewModelProvider(this, 
                new androidx.lifecycle.ViewModelProvider.NewInstanceFactory()).get(HighFrequencyViewModel.class);
        }
        
        binding.setViewModel(viewModel);
        
        // 设置导航器
        viewModel.setNavigator(this);
        
        // 设置沉浸式状态栏
        setImmersiveStatusBar();
        
        // 设置返回按钮点击事件
        binding.btnBack.setOnClickListener(v -> finish());

        // 设置loading状态下的返回按钮点击事件
        binding.btnBackLoading.setOnClickListener(v -> finish());

        // 设置立即学习按钮点击事件
        binding.btnStartLearning.setOnClickListener(v -> startLearning());
        
        // 检查VIP状态
        checkVipStatus();
        
        // 初始化知识点列表
        initKnowledgeCategoriesObserver();

        // 初始化loading状态观察者
        initLoadingObserver();

        // 初始显示loading状态
        updateLoadingState(true);

        // 初始化数据
        viewModel.init();
    }
    
    /**
     * 检查VIP状态
     */
    private void checkVipStatus() {
        boolean isVip = UserHelper.isBecomeVip();
        viewModel.isVip.setValue(isVip);
        
        if (isVip) {
            // 获取用户学习进度数据
            getUserLearningProgress();
        }
    }
    
    /**
     * 获取用户学习进度数据
     */
    private void getUserLearningProgress() {
        // VIP用户的学习进度数据会在 ViewModel 的 loadHighFrequencyData() 方法中
        // 通过 API 调用自动获取和更新，这里不需要额外处理
        // 如果 API 调用失败，会使用默认值
    }
    
    /**
     * 初始化知识点类别观察者
     */
    private void initKnowledgeCategoriesObserver() {
        viewModel.knowledgeCategories.addOnPropertyChangedCallback(new Observable.OnPropertyChangedCallback() {
            @Override
            public void onPropertyChanged(Observable sender, int propertyId) {
                updateKnowledgeCategories();
            }
        });
    }

    /**
     * 初始化loading状态观察者
     */
    private void initLoadingObserver() {
        viewModel.isLoading.observe(this, isLoading -> {
            LogUtils.e("HighFrequencyFragment - Loading状态变化: " + isLoading);

            // 直接控制视图的显示/隐藏
            updateLoadingState(isLoading);
        });
    }

    /**
     * 更新loading状态的UI显示
     */
    private void updateLoadingState(boolean isLoading) {
        try {
            if (binding != null) {
                if (isLoading) {
                    // 显示loading，隐藏内容
                    binding.layoutLoading.setVisibility(android.view.View.VISIBLE);
                    binding.layoutContent.setVisibility(android.view.View.GONE);
                    LogUtils.e("HighFrequencyFragment - 显示Loading界面");
                } else {
                    // 隐藏loading，显示内容
                    binding.layoutLoading.setVisibility(android.view.View.GONE);
                    binding.layoutContent.setVisibility(android.view.View.VISIBLE);
                    LogUtils.e("HighFrequencyFragment - 显示内容界面");
                }
            }
        } catch (Exception e) {
            LogUtils.e("HighFrequencyFragment - updateLoadingState - 异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 更新知识点类别列表
     */
    private void updateKnowledgeCategories() {
        // 清空现有视图
        binding.llKnowledgeCategories.removeAllViews();
        
        // 获取知识点类别数据
        List<HighFrequencyViewModel.KnowledgeCategory> categories = viewModel.knowledgeCategories.get();
        if (categories == null || categories.isEmpty()) {
            return;
        }
        
        // 为每个类别创建视图
        for (int i = 0; i < categories.size(); i++) {
            HighFrequencyViewModel.KnowledgeCategory category = categories.get(i);
            
            // 创建类别标题
            TextView titleView = new TextView(getContext());
            titleView.setText(category.getTitle());
            titleView.setTextColor(Color.parseColor("#333333"));
            titleView.setTextSize(16);
            titleView.setTypeface(android.graphics.Typeface.DEFAULT_BOLD);
            
            // 如果不是第一个类别，添加上边距
            if (i > 0) {
                LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                titleParams.topMargin = DpUtils.dp2px(getContext(), 24);
                titleView.setLayoutParams(titleParams);
            }
            
            // 添加到容器中
            binding.llKnowledgeCategories.addView(titleView);
            
            // 创建类别项
            for (String item : category.getItems()) {
                TextView itemView = new TextView(getContext());
                itemView.setText(item);
                itemView.setTextColor(Color.parseColor("#333333"));
                itemView.setTextSize(14);
                
                // 设置边距
                LinearLayout.LayoutParams itemParams = new LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                itemParams.topMargin = DpUtils.dp2px(getContext(), 8);
                itemParams.leftMargin = DpUtils.dp2px(getContext(), 16);
                itemView.setLayoutParams(itemParams);
                
                // 添加到容器中
                binding.llKnowledgeCategories.addView(itemView);
            }
        }
    }
    
    /**
     * 设置沉浸式状态栏
     */
    private void setImmersiveStatusBar() {
        if (getActivity() != null) {
            Window window = getActivity().getWindow();

            // 清除半透明状态栏标志
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);

            // 添加绘制系统栏背景的标志
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            // 设置状态栏为白色
            window.setStatusBarColor(Color.WHITE);

            // 设置状态栏图标为深色（适用于Android M及以上版本）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            }
        }
    }
    
    /**
     * 开始学习
     */
    private void startLearning() {
        // 检查用户是否已登录
        if (!AccountHelper.isUserLogin()) {
            com.wb.lib_utils.utils.ToastUtils.normal("请先登录");
            startFragment(AccountHelper.showAccountLogin());
            return;
        }
        
        // 检查用户是否是VIP
        boolean isVip = UserHelper.isBecomeVip();
        Boolean viewModelVip = viewModel.isVip.getValue();
        if (viewModelVip != null) {
            isVip = viewModelVip;
        }
        
        if (!isVip) {
            // 非VIP用户，显示购买对话框
            MessageHelper.openNoPremissBuyDialog(getActivity(), true, "PAY_UPDATE");
            return;
        }
        
        // 是VIP用户，导航到学习页面
        navigateToLearning();
    }
    
    @Override
    public void navigateToLearning() {
        // 导航到高频考题练习
        initArguments().putString("title","高频考题");
        initArguments().putBoolean("restart",true);
        initArguments().putString("paperType", "22");
//        initArguments().putString("paperId","21");
        startFragment(CoreExamHelper.mainPage(getContext()));
    }
    
    @Override
    public void showContentView() {
        LogUtils.e("HighFrequencyFragment - showContentView - 数据加载完成，显示内容");

        // 更新知识点类别
        updateKnowledgeCategories();

        // 强制显示内容，隐藏loading
        updateLoadingState(false);

        // 数据已经在ViewModel中通过API加载，不需要设置默认值
        // 如果API数据为空，ViewModel会使用模拟数据
    }
    
    @Override
    public void onClicked(View v) {
        // 处理返回按钮点击
        finish();
    }
} 