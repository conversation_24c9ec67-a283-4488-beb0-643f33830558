package com.zizhiguanjia.model_core.fragment;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.databinding.Observable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.constants.CoreExamRouterPath;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.adapter.ExamResultAdapter;
import com.zizhiguanjia.model_core.databinding.CoreExamResultLayoutBinding;
import com.zizhiguanjia.model_core.navigator.ExamResultNavigator;
import com.zizhiguanjia.model_core.viewmodel.ExamResultViewModel;

import java.util.List;

@Route(path = CoreExamRouterPath.EXAM_RESULT_FRAGMENT)
@BindRes(statusBarStyle = BarStyle.LIGHT_CONTENT, swipeBack = SwipeStyle.NONE)
public class ExamResultFragment extends BaseFragment implements ExamResultNavigator, TitleBar.OnLeftBarListener {

    private CoreExamResultLayoutBinding binding;

    @BindViewModel
    ExamResultViewModel viewModel;
    private String paperType;

    @Override
    public int initLayoutResId() {
        return R.layout.core_exam_result_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();
        binding.setViewModel(viewModel);

        // 设置导航器
        viewModel.setNavigator(this);

        // 设置沉浸式状态栏
        setImmersiveStatusBar();

        // 设置返回按钮点击事件
        binding.btnBack.setOnClickListener(v -> finish());

        // 获取传递的参数
        String paperId = initArguments().getString("paperId", "");
        // 默认为智能练习类型
        paperType = initArguments().getString("paperType", "21");

        // 初始化列表
        initResultList();

        // 初始化按钮点击事件
        initListeners();

        // 监听知识点数据变化
        observeKnowledgePoints();

        // 初始化ViewModel，请求数据
        viewModel.init(paperType, paperId);
    }

    /**
     * 监听知识点数据变化
     */
    private void observeKnowledgePoints() {
        // 监听薄弱知识点变化
        viewModel.weakPoints.addOnPropertyChangedCallback(new Observable.OnPropertyChangedCallback() {
            @Override
            public void onPropertyChanged(Observable sender, int propertyId) {
                updateWeakPoints();
            }
        });

        // 监听已掌握知识点变化
        viewModel.masteredPoints.addOnPropertyChangedCallback(new Observable.OnPropertyChangedCallback() {
            @Override
            public void onPropertyChanged(Observable sender, int propertyId) {
                updateMasteredPoints();
            }
        });
    }

    /**
     * 更新薄弱知识点视图
     */
    private void updateWeakPoints() {
        // 清空现有视图
        binding.llWeakPoints.removeAllViews();

        // 获取薄弱知识点数据
        String[] weakPoints = viewModel.weakPoints.get();
        if (weakPoints == null || weakPoints.length == 0) {
            // 如果没有薄弱知识点，显示一个提示信息
            TextView emptyView = new TextView(getContext());
            emptyView.setText("暂无薄弱知识点");
            emptyView.setTextColor(Color.parseColor("#999999"));
            emptyView.setTextSize(14);
            binding.llWeakPoints.addView(emptyView);
            return;
        }

        // 为每个薄弱知识点创建视图
        for (String point : weakPoints) {
            View itemView = LayoutInflater.from(getContext()).inflate(R.layout.item_knowledge_point, null);
            TextView textView = itemView.findViewById(R.id.tv_knowledge_point);
            textView.setText(point);

            // 设置视图参数
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.topMargin = DpUtils.dp2px(getContext(), 8);

            // 添加到容器中
            binding.llWeakPoints.addView(itemView, params);
        }
    }

    /**
     * 更新已掌握知识点视图
     */
    private void updateMasteredPoints() {
        // 清空现有视图
        binding.llMasteredPoints.removeAllViews();

        // 获取已掌握知识点数据
        String[] masteredPoints = viewModel.masteredPoints.get();
        if (masteredPoints == null || masteredPoints.length == 0) {
            // 如果没有已掌握知识点，显示一个提示信息
            TextView emptyView = new TextView(getContext());
            emptyView.setText("暂无已掌握知识点");
            emptyView.setTextColor(Color.parseColor("#999999"));
            emptyView.setTextSize(14);
            binding.llMasteredPoints.addView(emptyView);
            return;
        }

        // 为每个已掌握知识点创建视图
        for (String point : masteredPoints) {
            View itemView = LayoutInflater.from(getContext()).inflate(R.layout.item_mastered_point, null);
            TextView textView = itemView.findViewById(R.id.tv_knowledge_point);
            textView.setText(point);

            // 设置视图参数
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.topMargin = DpUtils.dp2px(getContext(), 8);

            // 添加到容器中
            binding.llMasteredPoints.addView(itemView, params);
        }
    }

    /**
     * 设置沉浸式状态栏
     */
    private void setImmersiveStatusBar() {
        if (getActivity() != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                Window window = getActivity().getWindow();
                window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                window.setStatusBarColor(Color.TRANSPARENT);
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
        }
    }

    private void initResultList() {
        // 初始化单选题结果列表
        binding.rvSingleChoice.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        ExamResultAdapter singleAdapter = new ExamResultAdapter(ExamResultAdapter.TYPE_SINGLE);
        binding.rvSingleChoice.setAdapter(singleAdapter);
        
        // 设置单选题点击事件
        singleAdapter.setOnItemClickListener((questionNumber, isWrong) -> {
            handleQuestionClick(questionNumber, isWrong, ExamResultAdapter.TYPE_SINGLE);
        });
        
        // 初始化多选题结果列表
        binding.rvMultipleChoice.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        ExamResultAdapter multipleAdapter = new ExamResultAdapter(ExamResultAdapter.TYPE_MULTIPLE);
        binding.rvMultipleChoice.setAdapter(multipleAdapter);
        
        // 设置多选题点击事件
        multipleAdapter.setOnItemClickListener((questionNumber, isWrong) -> {
            handleQuestionClick(questionNumber, isWrong, ExamResultAdapter.TYPE_MULTIPLE);
        });
        
        // 初始化判断题结果列表
        binding.rvJudgment.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        ExamResultAdapter judgmentAdapter = new ExamResultAdapter(ExamResultAdapter.TYPE_JUDGMENT);
        binding.rvJudgment.setAdapter(judgmentAdapter);
        
        // 设置判断题点击事件
        judgmentAdapter.setOnItemClickListener((questionNumber, isWrong) -> {
            handleQuestionClick(questionNumber, isWrong, ExamResultAdapter.TYPE_JUDGMENT);
        });
        
        // 观察单选题数据变化
        viewModel.singleChoiceResults.observe(getViewLifecycleOwner(), results -> {
            updateAdapterData(singleAdapter, results, viewModel.singleChoiceNumbers.getValue());
        });
        
        viewModel.singleChoiceNumbers.observe(getViewLifecycleOwner(), numbers -> {
            updateAdapterData(singleAdapter, viewModel.singleChoiceResults.getValue(), numbers);
        });
        
        // 观察多选题数据变化
        viewModel.multipleChoiceResults.observe(getViewLifecycleOwner(), results -> {
            updateAdapterData(multipleAdapter, results, viewModel.multipleChoiceNumbers.getValue());
        });
        
        viewModel.multipleChoiceNumbers.observe(getViewLifecycleOwner(), numbers -> {
            updateAdapterData(multipleAdapter, viewModel.multipleChoiceResults.getValue(), numbers);
        });
        
        // 观察判断题数据变化
        viewModel.judgmentResults.observe(getViewLifecycleOwner(), results -> {
            updateAdapterData(judgmentAdapter, results, viewModel.judgmentNumbers.getValue());
        });
        
        viewModel.judgmentNumbers.observe(getViewLifecycleOwner(), numbers -> {
            updateAdapterData(judgmentAdapter, viewModel.judgmentResults.getValue(), numbers);
        });
    }
    
    /**
     * 更新适配器数据
     *
     * @param adapter 适配器
     * @param results 结果列表
     * @param numbers 题号列表
     */
    private void updateAdapterData(ExamResultAdapter adapter, List<Boolean> results, List<Integer> numbers) {
        if (results == null || numbers == null || results.isEmpty() || numbers.isEmpty() || results.size() != numbers.size()) {
            return;
        }

        int[] questionNums = new int[numbers.size()];
        for (int i = 0; i < numbers.size(); i++) {
            questionNums[i] = numbers.get(i);
        }

        Boolean[] states = results.toArray(new Boolean[0]);
        adapter.setDataItems(questionNums, states);
    }
    
    /**
     * 处理题目点击事件
     * @param questionNumber 题号
     * @param isWrong 是否错题
     * @param questionType 题目类型
     */
    private void handleQuestionClick(int questionNumber, boolean isWrong, int questionType) {
        String questionTypeText;
        switch (questionType) {
            case ExamResultAdapter.TYPE_SINGLE:
                questionTypeText = "单选题";
                break;
            case ExamResultAdapter.TYPE_MULTIPLE:
                questionTypeText = "多选题";
                break;
            case ExamResultAdapter.TYPE_JUDGMENT:
                questionTypeText = "判断题";
                break;
            default:
                questionTypeText = "未知题型";
                break;
        }
        
        // 这里实现题目点击的具体逻辑
        if (isWrong) {
            // 错题处理逻辑
            navigateToWrongQuestion(questionNumber, questionType);
        } else {
            // 正确题处理逻辑
            navigateToCorrectQuestion(questionNumber, questionType);
        }
    }
    
    /**
     * 跳转到错题详情页
     */
    private void navigateToWrongQuestion(int questionNumber, int questionType) {
        // 获取paperId和paperType
        String paperId = initArguments().getString("paperId", "");
        String paperType = initArguments().getString("paperType", "21"); // 默认智能练习类型
        
        // 设置参数，准备跳转到错题详情页
        initArguments().putString("title", "查看解析页面");
        initArguments().putBoolean("restart", false);
        initArguments().putString("paperType", "8"); // 错题集类型
        String paperValue = viewModel.paperValue.get();
        initArguments().putString("paperId", paperValue);
        initArguments().putString("paperValue", paperValue);
        initArguments().putInt("questionNumber", questionNumber);
        initArguments().putInt("location", questionNumber); // 定位到特定题目
        
        // 显示Toast提示
        com.wb.lib_utils.utils.ToastUtils.normal("正在查看错题: 第" + questionNumber + "题");
        
        // 跳转到错题详情页面
        startFragment(CoreExamHelper.mainPage(getContext()));
    }
    
    /**
     * 跳转到正确题详情页
     */
    private void navigateToCorrectQuestion(int questionNumber, int questionType) {
        // 获取paperId和paperType
        String paperId = initArguments().getString("paperId", "");
        String paperType = initArguments().getString("paperType", "8"); // 默认智能练习类型
        
        // 设置参数，准备跳转到题目详情页
        initArguments().putString("title", "题目详情");
        initArguments().putBoolean("restart", false);
        initArguments().putString("paperType", paperType); // 使用原始试卷类型
        initArguments().putString("paperId", paperId);
        initArguments().putInt("questionNumber", questionNumber);
        initArguments().putInt("location", questionNumber); // 定位到特定题目
        
        // 显示Toast提示
        com.wb.lib_utils.utils.ToastUtils.normal("正在查看题目: 第" + questionNumber + "题");
        
        // 跳转到题目详情页面
        startFragment(CoreExamHelper.mainPage(getContext()));
    }

    private void initListeners() {
        // 马上开始学习按钮点击事件
        binding.btnStartLearning.setOnClickListener(v -> {
            if (!Boolean.TRUE.equals(viewModel.isContinue.getValue())) {
                // 这里处理开始学习的逻辑
                PointHelper.joinPointData(PointerMsgType.POINTER_A_100_TRIGGERBOUNCED, true);
                Integer.parseInt("21");
                MessageHelper.openNoPremissBuyDialog(getActivity(), true, PayRouthConfig.PAY_EXAM_PAY);

            } else {
                //继续练习
                initArguments().putString("title", paperType.equals("21") ? "智能练习" : "摸底测评");
                initArguments().putBoolean("restart", true);
                initArguments().putString("paperType", paperType);
                initArguments().putInt("location", 0);
                startFragment(CoreExamHelper.mainPage(getContext()));
                finish();
            }
        });
    }

    @Override
    public void onClicked(View v) {
        // 处理返回按钮点击
        finish();
    }

    @Override
    public void showContentView() {
        // 数据加载完成后显示内容
        // 更新知识点视图
        updateWeakPoints();
        updateMasteredPoints();
        
        // 根据paperType动态设置成绩区域高度
        if (viewModel != null && viewModel.paperType.get() != null) {
            ViewGroup.LayoutParams params = binding.flScoreArea.getLayoutParams();
            if (params != null) {
                // 如果是摸底测评类型，设置更高的高度
                if ("20".equals(viewModel.paperType.get())) {
                    params.height = DpUtils.dp2px(getContext(), 220);
                    binding.llScoreContent.setVisibility(TextView.VISIBLE);
                } else {
                    binding.llScoreContent.setVisibility(TextView.GONE);
                    params.height = DpUtils.dp2px(getContext(), 120);
                }
                binding.flScoreArea.setLayoutParams(params);
            }
        }
    }
} 