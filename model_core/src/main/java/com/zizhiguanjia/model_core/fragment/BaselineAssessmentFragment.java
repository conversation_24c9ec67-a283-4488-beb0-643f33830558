package com.zizhiguanjia.model_core.fragment;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.lifecycle.ViewModelProvider;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.constants.CoreExamRouterPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.navigator.EvaluationStartNavigator;
import com.zizhiguanjia.model_core.viewmodel.EvaluationStartViewModel;

/**
 * 摸底测评页面的Fragment
 * 显示摸底测评的介绍和三个步骤
 */
@Route(path = CoreExamRouterPath.BASELINE_ASSESSMENT_FRAGMENT)
@BindRes(statusBarStyle = BarStyle.LIGHT_CONTENT, swipeBack = SwipeStyle.NONE)
public class BaselineAssessmentFragment extends BaseFragment implements EvaluationStartNavigator {

    private TextView tvSubjectInfo;
    private TextView tvLocation;
    private TextView tvLevel;
    private Button btnStartAssessment;
    
    @BindViewModel
    EvaluationStartViewModel viewModel;
    
    private String paperId = ""; // 存储API返回的试卷ID

    @Override
    public int initLayoutResId() {
        return R.layout.fragment_baseline_assessment;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        LogUtils.e("BaselineAssessmentFragment - initView");
        
        // 初始化ViewModel
        if (viewModel == null) {
            viewModel = new ViewModelProvider(this, 
                new androidx.lifecycle.ViewModelProvider.NewInstanceFactory())
                .get(EvaluationStartViewModel.class);
        }
        
        // 设置导航器
        viewModel.setNavigator(this);
        
        // 初始化视图
        ImageView ivBack = findViewById(R.id.iv_back);
        tvSubjectInfo = findViewById(R.id.tv_subject_info);
        tvLocation = findViewById(R.id.tv_location);
        tvLevel = findViewById(R.id.tv_level);
        btnStartAssessment = findViewById(R.id.btn_start_assessment);
        
        // 设置沉浸式状态栏
        setImmersiveStatusBar();
        
        // 设置返回按钮点击事件
        if (ivBack != null) {
            ivBack.setOnClickListener(v -> {
                if (getActivity() != null) {
                    getActivity().onBackPressed();
                }
            });
        }
        
        // 设置开始测评按钮点击事件
        if (btnStartAssessment != null) {
            btnStartAssessment.setOnClickListener(v -> {
                startAssessment();
            });
        }
        
        // 更新科目信息
        updateSubjectInfo();
        
        // 初始化数据
        viewModel.init();
    }
    
    /**
     * 设置沉浸式状态栏
     */
    private void setImmersiveStatusBar() {
        if (getActivity() != null) {
            Window window = getActivity().getWindow();
            
            // 清除半透明状态栏标志
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            
            // 添加绘制系统栏背景的标志
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            
            // 设置状态栏为白色
            window.setStatusBarColor(Color.WHITE);
            
            // 设置状态栏图标为深色（适用于Android M及以上版本）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            }
        }
    }
    
    /**
     * 更新科目信息
     */
    private void updateSubjectInfo() {
        // 获取当前选择的证书信息
        String certificateInfo = CertificateHelper.initCertificate(AccountHelper.isUserLogin());
        if (certificateInfo != null && !certificateInfo.isEmpty()) {
            // 尝试解析证书信息
            String[] parts = certificateInfo.split(" ");

            if (parts.length >= 1) {
                tvSubjectInfo.setText(parts[0]); // 科目名称
            }

            if (parts.length >= 2) {
                tvLocation.setText(parts[1]); // 地区
            }

            if (parts.length >= 3) {
                tvLevel.setText(parts[2]); // 等级
            }

            LogUtils.e("BaselineAssessmentFragment - 更新科目信息: " + certificateInfo);
        } else {
            LogUtils.e("BaselineAssessmentFragment - 未获取到证书信息");
            tvSubjectInfo.setText("请选择科目");
            tvLocation.setText("");
            tvLevel.setText("");
        }
    }
    
    /**
     * 开始测评
     */
    private void startAssessment() {
        LogUtils.e("BaselineAssessmentFragment - 开始测评");
        
        // 检查登录状态
        if (!AccountHelper.checkLoginState()) {
            return;
        }
        
        // 跳转到摸底测评页面
        navigateToEvaluation(paperId);
    }
    
    @Override
    public void showContentView() {
        // 摸底测评页面已经显示了内容，不需要额外处理
    }
    
    @Override
    public void showError(String message) {
        ToastUtils.normal(message);
    }
    
    @Override
    public void navigateToEvaluation(String paperId) {
        // 保存试卷ID
        if (paperId != null && !paperId.isEmpty()) {
            this.paperId = paperId;
        }
        
        // 跳转到测评页面
        initArguments().putString("title", "摸底测评");
        initArguments().putBoolean("restart", false);
        initArguments().putString("paperType", "20"); // 摸底测评类型
        
        // 如果有有效的试卷ID，则传递
        if (this.paperId != null && !this.paperId.isEmpty()) {
            initArguments().putString("paperId", this.paperId);
        }
        
        startFragment(CoreExamHelper.mainPage(getContext()));
        ToastUtils.normal("开始摸底测评");
        finish();
    }
    
    /**
     * 页面恢复时更新科目信息
     */
    @Override
    public void onResume() {
        super.onResume();
        updateSubjectInfo();
    }
} 