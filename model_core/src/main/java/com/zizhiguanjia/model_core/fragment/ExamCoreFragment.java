package com.zizhiguanjia.model_core.fragment;

import static com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig.PAY_MSG_FAIL;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Parcelable;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import androidx.lifecycle.Observer;
import androidx.viewpager2.widget.ViewPager2;
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.exception.RxException;
import com.wb.lib_rxtools.subsciber.BaseSubscriber;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.constants.CoreExamRouterPath;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.lib_base.helper.ListHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.listeners.ExamGuilderListener;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.adapter.ExamContentAdapter;
import com.zizhiguanjia.model_core.config.ExamTypeConfig;
import com.zizhiguanjia.model_core.config.PageTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreExamContentBinding;
import com.zizhiguanjia.model_core.dialog.ExamChangeThemeDialog;
import com.zizhiguanjia.model_core.dialog.ExamChangeThemeDialogCallback;
import com.zizhiguanjia.model_core.dialog.ExamFaceBackPopup;
import com.zizhiguanjia.model_core.dialog.SheetAnswerDialog;
import com.zizhiguanjia.model_core.listener.ExamDataClearListenter;
import com.zizhiguanjia.model_core.listener.ExamFaceBackListener;
import com.zizhiguanjia.model_core.listener.ExamSheetCall;
import com.zizhiguanjia.model_core.listener.ExamUserAnswerListener;
import com.zizhiguanjia.model_core.manager.ExamCoreDataManager;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamAnswerSheetBean;
import com.zizhiguanjia.model_core.model.ExamChaptersBean;
import com.zizhiguanjia.model_core.model.ExamFaceBackModel;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.navigator.ExamCoreNavigator;
import com.zizhiguanjia.model_core.viewmodel.ExamCoreViewModel;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

@Route(path = CoreExamRouterPath.MAIN_FRAGMENT)
@BindRes(statusBarStyle = BarStyle.LIGHT_CONTENT, swipeBack = SwipeStyle.NONE)
public class ExamCoreFragment extends BaseFragment implements TitleBar.OnLeftBarListener, ExamFaceBackListener, ExamSheetCall, ExamCoreNavigator, ExamDataClearListenter, ExamUserAnswerListener {
    private CoreExamContentBinding binding;
    private String title, paperType, paperId;
    private boolean restart;
    private int location;
    private ExamContentAdapter adapter;
    @BindViewModel
    ExamCoreViewModel model;
    private BasePopupView faceBackPopup;
    private ExamFaceBackPopup examFaceBackPopup;
    private LoadingPopupView loadingPopupView;
    private SheetAnswerDialog sheetAnswerDialog;
    private BasePopupView sheetPopup;
    private boolean showShet = false;
    private Disposable dowmTimeDisp;
    private boolean closePage = false;

    /**
     * 主题修改弹窗
     */
    private ExamChangeThemeDialog changeThemeDialog;

    @Override
    public int initLayoutResId() {
        return R.layout.core_exam_content;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();
        initData();
        model.firsShowViewObs.set(true);
        binding.examMultipleStatusView.showLoading();
        binding.setMode(model);
        initListenter();
        model.isCanBack = initArguments().getBoolean("isBack", true);
        model.initParam(title, paperType, paperId, restart, location, this, this);
        model.initThemeData();
        initListenterOnKey();
        binding.icdExamMust.llBottomAction.tvSubmit.setVisibility(getStateExam() ? View.GONE : View.VISIBLE);
        binding.icdExamMust.btnPrePage.setOnClickListener(v -> {
            if (binding.icdExamMust.examViewpager2.getCurrentItem() == 0) {
                ToastUtils.normal("当前已经是第一题", Gravity.CENTER);
            }
            binding.icdExamMust.examViewpager2.setCurrentItem(Math.max(binding.icdExamMust.examViewpager2.getCurrentItem() - 1, 0));
        });
        binding.icdExamMust.btnNextPage.setOnClickListener(v -> {
            this.goToNextPage();
        });
        binding.icdExamMust.btnCheckResult.setOnClickListener(view -> {
            if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_INTELLIGENT_PRACTICE) {
                if (!NoDoubleClickUtils.isDoubleClick()) {
                    if (!onces) {
                        LogUtils.e("发送----");
                        closePage = true;
                        onces = true;
                        model.destory();
                        initiativeExitExam();
                    }
                }
            }
            toExamResult("21");
        });
    }

    boolean onces = false;

    private void initListenterOnKey() {
        getView().setFocusableInTouchMode(true);
        getView().requestFocus();
        getView().setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ZJLX || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_TKLX || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ERROR || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_COURSE_DETAIL_QUESTION || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_INTELLIGENT_PRACTICE || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_EvaluationReport || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_HIGH_FREQUENCY) {
                        if (!NoDoubleClickUtils.isDoubleClick()) {
                            if (!onces) {
                                LogUtils.e("发送----");
                                closePage = true;
                                onces = true;
                                model.destory();
                                initiativeExitExam();
                            }
                            return true;
                        } else {
                            return true;
                        }
                    }
                    return false;
                }
                return false;
            }
        });
    }

    @Override
    public void initViewData() {
        model.initFaceBackPopup();
        model.initLoadingPopup();
        model.checkPointerRouth();
        binding.examContentTb.getCenterTextView().setText(title);
    }

    @Override
    public void initObservable() {
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if (msgEvent.getCode() == PayMsgTypeConfig.PAY_MSG_SUCCESS) {
                    LogUtils.e("题库开始支付");
                    RxJavaUtils.delay(1, new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) throws Exception {
                            MainThreadUtils.post(new Runnable() {
                                @Override
                                public void run() {
                                    model.reshExamData(getCurrentPostion());
                                }
                            });

                        }
                    });
                } else if (msgEvent.getCode() == ExamTypeConfig.EXAM_BUYVIP_NO_PERMISS) {
                    model.noExamPermiss(false, false);
                } else if (msgEvent.getCode() == ExamTypeConfig.EXAM_SHAPER_TYPE) {
                    model.shaperWechat(adapter.getQuestionId(getCurrentPostion()));
                } else if (msgEvent.getCode() == ExamTypeConfig.EXAM_FACEBACK_TYPE) {
                    showFaceView();
                } else if (msgEvent.getCode() == ExamTypeConfig.EXAM_BUYVIP_TYPE) {
                    model.noExamPermiss(true, true);
                } else if (msgEvent.getCode() == 0x333) {
                    startFragment(CommonHelper.showPermissView());
                } else if (msgEvent.getCode() == 789789) {
                    finish();
                } else if (msgEvent.getCode() == 0x96121) {
                    LogUtils.e("开启通知了1-----" + closePage);
                    if (closePage) {
                        return;
                    }
                    if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ZJLX || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_TKLX || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ERROR) {
                        LogUtils.e("开启通知了3-----" + paperType);
                        String time = msgEvent.getMsg();
                        LogUtils.e("开启通知了4-----" + time);
                        MessageHelper.openLearningPlanDialog(getActivity(), time, 1);
                        //                        MainThreadUtils.post(new Runnable() {
                        //                            @Override
                        //                            public void run() {
                        //                                MessageHelper.openLearningPlanDialog(getActivity(),time,1);
                        //                            }
                        //                        });
                    } else {
                        LogUtils.e("开启通知了2-----");
                    }

                } else if (msgEvent.getCode() == PAY_MSG_FAIL) {
                    //                    payFail();
                }
            }
        });
    }

    private void payFail() {
        MessageHelper.openPayFailServer(getActivity(), this);
    }

    @Override
    public void onInVisible() {
        LogUtils.e("看看也没返回的----->>>>");
        if (closePage) {
            return;
        }
        if (model.canPostRecordData()) {
            DataHelper.postAllQuestion(1);
        }

    }

    /**
     * 修改显示数据
     */
    public void changeShowData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean, ExamCoreThemeConfigTextSizeBean textSizeBean) {
        if (adapter != null) {
            adapter.setThemeConfigData(styleConfigBean, themeBean, textSizeBean);
        }
        //除模拟考试外，其他的答题卡都有
        if (ExamPaperTypeConfig.isExamination(Integer.parseInt(paperType), getArguments().getString("title"))) {
            binding.icdExamMust.llBottomAction.lnResultCountFalse.setVisibility(View.GONE);
            binding.icdExamMust.llBottomAction.lnResultCountTrue.setVisibility(View.GONE);
        } else {
            binding.icdExamMust.llBottomAction.lnResultCountFalse.setVisibility(View.VISIBLE);
            binding.icdExamMust.llBottomAction.lnResultCountTrue.setVisibility(View.VISIBLE);
        }

        binding.icdExamMust.lnPreNextOptions.setVisibility(styleConfigBean.isShowPreNextButtons() ? View.VISIBLE : View.GONE);
        binding.icdExamMust.btnPrePage.setActivated(binding.icdExamMust.examViewpager2.getCurrentItem() != 0);
        binding.icdExamMust.btnNextPage.setActivated(adapter != null && adapter.getItemCount() != 1 && binding.icdExamMust.examViewpager2.getCurrentItem() != adapter.getItemCount() - 1);
        binding.examContentTb.setStatusBarColor(Color.parseColor(themeBean.getToolsBarBgColor()));
        binding.examContentTb.setBackgroundColor(Color.parseColor(themeBean.getToolsBarBgColor()));
        binding.examContentTb.getRightTextView().setTextColor(Color.parseColor(themeBean.getToolsBarTextColor()));
        binding.examContentTb.getCenterTextView().setTextColor(Color.parseColor(themeBean.getToolsBarTextColor()));
        binding.examContentTb.getLeftImageButton().setImageTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getToolsBarTextColor())));
        binding.icdExamMust.llTopAction.tvDownTimes.setTextColor(Color.parseColor(themeBean.getTextColor()));
    }

    @Override
    public void initData() {
        //4-题型练习，16-易错题
        paperType = getArguments().getString("paperType");
        title = getArguments().getString("title");
        paperId = getArguments().getString("paperId");
        restart = getArguments().getBoolean("restart", false);
        location = getArguments().getInt("location", -1);
        adapter = new ExamContentAdapter(getActivity(), Integer.parseInt(paperType));
        //        binding.icdExamMust.examViewpager2.setOrientation(ViewPager2.ORIENTATION_VERTICAL);
        binding.icdExamMust.examViewpager2.setAdapter(adapter);
    }

    @Override
    public void initListenter() {
        adapter.setExamContentUserAnswerCall(this);
        binding.icdExamMust.examViewpager2.registerOnPageChangeCallback(onPageChangeCallback);
        binding.examContentTb.setBackListener(this);
        binding.examContentTb.getRightTextView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (changeThemeDialog == null) {
                    changeThemeDialog = new ExamChangeThemeDialog(requireContext(), paperType, model.getStyleConfigBean(), initArguments().getString("title"));
                    changeThemeDialog.setCallback(new ExamChangeThemeDialogCallback() {
                        @Override
                        public void changeThemeType(int type) {
                            model.getStyleConfigBean().setThemeType(paperType, title, type);
                            model.initThemeData();
                        }

                        /**
                         * @param type
                         */
                        @Override
                        public void changeTextSize(int type) {
                            model.getStyleConfigBean().setTextSizeType(paperType, title, type);
                            model.initThemeData();
                        }

                        /**
                         * 切换上一题下一题显示状态
                         */
                        @Override
                        public void changePreNextShowButtons(boolean isChecked) {
                            binding.icdExamMust.lnPreNextOptions.setVisibility(isChecked ? View.VISIBLE : View.GONE);
                            binding.icdExamMust.btnPrePage.setActivated(binding.icdExamMust.examViewpager2.getCurrentItem() != 0);
                            binding.icdExamMust.btnNextPage.setActivated(adapter.getItemCount() != 1 && binding.icdExamMust.examViewpager2.getCurrentItem() != adapter.getItemCount() - 1);
                            model.getStyleConfigBean().setShowPreNextButtons(paperType, title, isChecked);
                            model.initThemeData();
                        }

                        /**
                         * 修改为背题模式
                         */
                        @Override
                        public void changeMemoryModel(boolean isChecked) {
                            model.getStyleConfigBean().setShowMemoryModel(paperType, title, isChecked);
                            model.initThemeData();
                        }

                        /**
                         * 是否自动跳转下一题
                         */
                        @Override
                        public void setAutoJumpNext(boolean isChecked) {
                            model.getStyleConfigBean().setAutoJumpNext(paperType, title, isChecked);
                            model.initThemeData();
                        }
                    });
                } else {
                    changeThemeDialog.setPagerType(paperType, initArguments().getString("title"));
                    changeThemeDialog.setStyleConfigBean(model.getStyleConfigBean());
                }
                new PopupManager.Builder(requireActivity()).dismissOnTouchOutside(true).dismissOnTouchOutside(true).asCustom(changeThemeDialog).show();
            }
        });
    }

    OnPageChangeCallback onPageChangeCallback = new OnPageChangeCallback() {
        @Override
        public void onPageSelected(int position) {
            super.onPageSelected(position);
            model.viewPageListener(position, model.firsShowViewObs.get());
            model.firsShowViewObs.set(false);
            if (position == 0) {
                binding.icdExamMust.btnPrePage.setActivated(false);
            } else {
                binding.icdExamMust.btnPrePage.setActivated(true);
            }
            if (adapter.getItemCount() == 1 || position == adapter.getItemCount() - 1) {
                binding.icdExamMust.btnNextPage.setActivated(false);
            } else {
                binding.icdExamMust.btnNextPage.setActivated(true);
            }

            // 检查是否需要显示结果按钮（智能练习类型）
            if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_INTELLIGENT_PRACTICE) {
                checkAndShowResultButton();
            }

            // 如果是摸底测评，每题显示倒计时1分钟
            if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_EvaluationReport) {
                // 取消之前的倒计时
                if (questionDownTimeDisp != null) {
                    questionDownTimeDisp.dispose();
                    questionDownTimeDisp = null;
                }

                // 获取当前题目的类型和小问数量
                AnswerGroupBean currentQuestion = adapter.getItem(position);
//                int questionType = currentQuestion != null && currentQuestion.getAnswerSheetOptionBean() != null ?
//                        currentQuestion.getAnswerSheetOptionBean().getQue() : 0;
                int subQuestionCount = 1; // 默认为1个小问

                // 如果是案例题(que=5)，获取小问数量
//                if (questionType == 5) {
//                    // 获取案例题的小问数量
//                    if (currentQuestion.getAnswerSheetOptionBean() != null &&
//                        currentQuestion.getAnswerSheetOptionBean().getExamQuestions() != null) {
//                        subQuestionCount = Math.max(1, currentQuestion.getAnswerSheetOptionBean().getExamQuestions().size());
//                    }
//                }

                // 设置倒计时时间，每个小问1分钟
                long countdownTime = subQuestionCount * 60;
                startQuestionCountdown(countdownTime, position);
            }
        }

        private boolean haveChange = false;

        @Override
        public void onPageScrollStateChanged(int state) {
            super.onPageScrollStateChanged(state);
            if (state == 0) {
                if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_INTELLIGENT_PRACTICE) {
                    // 移除自动显示结果按钮的逻辑，改为在用户点击答案后才显示
                    // toResult();
                    // 检查当前页面是否已经回答，如果已回答则显示按钮
                    checkAndShowResultButton();
                    return;
                } else if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_EvaluationReport) {
                    boolean haveNext = model.checkNextPage(adapter, getCurrentPostion(), false);
                    if (!haveNext && Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_EvaluationReport) {
                        //如果是最后一道题，当前是摸底测评，倒计时完整自动提示是否交卷、显示底部交卷按钮
                        binding.icdExamMust.llBottomAction.tvSubmit.setVisibility(View.VISIBLE);
//                        startHandPaper(true);
                    } else {
                        binding.icdExamMust.llBottomAction.tvSubmit.setVisibility(View.GONE);
                    }
                    return;
                }
                if (!haveChange && model.checkNextPage(adapter, getCurrentPostion(), true)) {
                }
                haveChange = false;
            }
        }

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            super.onPageScrolled(position, positionOffset, positionOffsetPixels);
            if (!haveChange) {
                haveChange = positionOffset != 0;
            }
        }
    };

    // 题目倒计时的Disposable
    private Disposable questionDownTimeDisp;

    /**
     * 开始题目倒计时
     *
     * @param seconds  倒计时秒数
     * @param position 当前题目位置
     */
    private void startQuestionCountdown(long seconds, int position) {
        // 在顶部显示倒计时
        binding.icdExamMust.llTopAction.tvDownTimes.setVisibility(View.VISIBLE);
        binding.icdExamMust.llTopAction.tvDownTimes.setTextColor(Color.RED);

        questionDownTimeDisp = RxJavaUtils.countDown(seconds, new BaseSubscriber<Long>() {
            @Override
            public void onError(RxException e) {
                LogUtils.e("题目倒计时错误: " + e.getMessage());
            }

            @Override
            public void onSuccess(Long aLong) {
                if (aLong == 0 || aLong < 0) {
                    // 倒计时结束
                    binding.icdExamMust.llTopAction.tvDownTimes.setText("00:00");

                    // 如果是最后一题，弹出交卷提示
                    if (position >= adapter.getItemCount() - 1) {
                        // 最后一题倒计时结束，弹出交卷提示
                        startHandPaper(true);
                    } else {
                        // 自动跳到下一题
                        binding.icdExamMust.examViewpager2.setCurrentItem(position + 1);
                    }
                } else {
                    // 更新倒计时显示 (分:秒)
                    int minutes = (int) (aLong / 60);
                    int seconds = (int) (aLong % 60);
                    binding.icdExamMust.llTopAction.tvDownTimes.setText(String.format("%02d:%02d", minutes, seconds));
                }
            }
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // 释放题目倒计时资源
        if (questionDownTimeDisp != null && !questionDownTimeDisp.isDisposed()) {
            questionDownTimeDisp.dispose();
            questionDownTimeDisp = null;
        }
    }

    //如果是最后一题，并且是智能练习type21则显示查看结果按钮

    public void toResult() {
        boolean haveNext = model.checkNextPage(adapter, getCurrentPostion(), false);
        if (!haveNext && Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_INTELLIGENT_PRACTICE) {
            binding.icdExamMust.btnCheckResult.setVisibility(View.VISIBLE);
        } else {
            binding.icdExamMust.btnCheckResult.setVisibility(View.GONE);
        }
    }

    /**
     * 检查并显示结果按钮（仅在用户点击最后一题答案后显示）
     */
    private void checkAndShowResultButton() {
        try {
            // 只有智能练习类型才需要显示结果按钮
            if (Integer.parseInt(paperType) != ExamPaperTypeConfig.EXAM_INTELLIGENT_PRACTICE) {
                return;
            }

            // 检查是否是最后一题
            boolean haveNext = model.checkNextPage(adapter, getCurrentPostion(), false);
            if (!haveNext) {
                // 检查当前题目是否已经被回答
                if (adapter != null && getCurrentPostion() < adapter.getItemCount()) {
                    AnswerGroupBean currentQuestion = adapter.getData().get(getCurrentPostion());
                    if (currentQuestion != null && currentQuestion.getAnswerSheetOptionBean() != null) {
                        // 检查用户是否已经选择了答案
                        List<String> userAnswer = currentQuestion.getAnswerSheetOptionBean().getUserAnswer();
                        if (userAnswer != null && !userAnswer.isEmpty()) {
                            // 用户已经回答了最后一题，显示查看结果按钮
                            binding.icdExamMust.btnCheckResult.setVisibility(View.VISIBLE);
                            LogUtils.e("ExamCoreFragment - 最后一题已回答，显示查看结果按钮");
                        } else {
                            // 用户还没有回答最后一题，隐藏按钮
                            binding.icdExamMust.btnCheckResult.setVisibility(View.GONE);
                        }
                    }
                }
            } else {
                // 不是最后一题，隐藏按钮
                binding.icdExamMust.btnCheckResult.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            LogUtils.e("ExamCoreFragment - checkAndShowResultButton - 异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void showExamView(ExamAnswerSheetBean commonGroupExamBeans) {
        model.getExamCount(commonGroupExamBeans);
        ExamCoreDataManager.getInstance().cleanDataToSheetExamData(commonGroupExamBeans, this);
    }

    @Override
    public void showEmptyView() {
        if (model.firsShowViewObs.get()) {
            if (binding.examMultipleStatusView == null) {
                return;
            }
            try {
                binding.examMultipleStatusView.showEmpty();
            } catch (Exception e) {
            }

        }
    }

    @Override
    public void showContextView() {
        binding.examMultipleStatusView.showContent();
    }

    @Override
    public void setGoToPage(int index, List<AnswerGroupBean> answerSheetOptionBeans) {
        adapter.setDataItems(answerSheetOptionBeans);
        binding.icdExamMust.btnPrePage.setActivated(binding.icdExamMust.examViewpager2.getCurrentItem() != 0);
        binding.icdExamMust.btnNextPage.setActivated(adapter.getItemCount() != 1 && binding.icdExamMust.examViewpager2.getCurrentItem() != adapter.getItemCount() - 1);
        binding.icdExamMust.examViewpager2.setCurrentItem(index, false);
        if (getCurrentPostion() == index) {
            afterGetLocalData("" + index);
        }
    }

    @Override
    public int getCurrentPostion() {
        return binding.icdExamMust.examViewpager2.getCurrentItem();
    }

    @Override
    public void dissFaceBackView() {
        if (faceBackPopup == null) {
            return;
        }
        if (faceBackPopup.isShow()) {
            faceBackPopup.dismiss();
        }
    }

    @Override
    public void initSheetPopup(List<ExamChaptersBean> examChaptersBeans) {
        if (showShet) {
            return;
        }
        showShet = true;
        initArguments().putParcelableArrayList("pppp", (ArrayList<? extends Parcelable>) examChaptersBeans);
        if (examChaptersBeans.isEmpty()) {
            return;
        }
        int ids = -1;
        try {
            ids = Integer.parseInt(getArguments().getString("paperId", "-1"));
        } catch (Exception e) {
            ids = -1;
        }
        sheetAnswerDialog = new SheetAnswerDialog(getContext(), examChaptersBeans, this, Integer.parseInt(getArguments().getString("paperType", "0")), ids, initArguments().getString("title"));
        sheetAnswerDialog.setThemeConfigData(model.styleConfigBean, model.themeBean.get(), model.textSizeBean);
        sheetPopup = new PopupManager.Builder(getContext()).hasShadowBg(true).maxHeight(DpUtils.getScreenHeight(getContext()) * 673 / 952).asCustom(sheetAnswerDialog);
        openSheetPopup();
    }

    @Override
    public void openSheetPopup() {
        if (sheetPopup == null) {
            return;
        }
        TextView textView = sheetAnswerDialog.getPopupContentView().findViewById(R.id.tvExamCurrentCount);
        textView.setText(String.valueOf(model.getQuestionNumByIndex(getCurrentPostion())));
        if (sheetPopup.isShow()) {
            sheetPopup.dismiss();
        }
        sheetPopup.show();
        showShet = false;
    }

    @Override
    public void setRestExam() {
        model.numObs.set("1");
    }

    @Override
    public void saveQuestion(int index) {
        try {
            if (adapter == null) {
                return;
            }
            AnswerGroupBean answerGroupBean = adapter.getData().get(getCurrentPostion());
            if (answerGroupBean == null) {
                return;
            }
            ExamPackAccessDataBean.RecordsBean recordsBean = answerGroupBean.getAnswerSheetOptionBean();
            if (recordsBean == null) {
                return;
            }
            ;
            if (recordsBean.isCollection()) {
                //开始取消
                model.canCelQuestion(String.valueOf(recordsBean.getQuestionId()));
            } else {
                //收藏
                model.saveQuestion(String.valueOf(recordsBean.getQuestionId()), GsonUtils.newInstance().listToJson(recordsBean.getUserAnswer()), paperType + "");
            }
        } catch (Exception e) {

        }

    }

    @Override
    public void successSaveState(boolean success) {
        if (adapter == null) {
            return;
        }
        AnswerGroupBean answerGroupBean = adapter.getData().get(getCurrentPostion());
        if (answerGroupBean == null) {
            return;
        }
        ExamPackAccessDataBean.RecordsBean recordsBean = answerGroupBean.getAnswerSheetOptionBean();
        if (recordsBean == null) {
            return;
        }
        ;
        recordsBean.setCollection(success ? true : false);
    }

    @Override
    public void showDownTime(boolean visition, long times) {
        if (visition) {
            if (times == 0) {
                startHandPaper(false);
            } else {
                startDownTime(times);
            }
        }
    }

    @Override
    public void startHandPaper(boolean isTs) {
        if (isTs) {
            if (!model.userDo) {
                showErrorToast("无法提交空白试卷，请作答后提交！");
                return;
            }
            MessageHelper.openGeneralCentDialog(getActivity(), "确认交卷?", model.getUserConfimHandPager(), "继续做题", "确定交卷", false, true, new GeneralDialogListener() {
                @Override
                public void onCancel() {
                }

                @Override
                public void onConfim() {
                    model.postHandPaper();
                }

                /**
                 *
                 */
                @Override
                public void onDismiss() {

                }
            });
        } else {
            MessageHelper.openGeneralCentDialog(getActivity(), "交卷?", "考试时间已到!", "重新练习", "确定交卷", false, true, new GeneralDialogListener() {
                @Override
                public void onCancel() {
                    model.restartExam();
                }

                @Override
                public void onConfim() {
                    model.postHandPaper();
                }

                /**
                 *
                 */
                @Override
                public void onDismiss() {

                }
            });
        }
    }

    @Override
    public void startDownTime(long times) {
        if (dowmTimeDisp != null) {
            dowmTimeDisp.dispose();
        }
        if (model.themeBean != null && model.themeBean.get() != null) {
            binding.icdExamMust.llTopAction.tvDownTimes.setTextColor(Color.parseColor(model.themeBean.get().getTextColor()));
        }
        dowmTimeDisp = RxJavaUtils.countDown(times, new BaseSubscriber<Long>() {
            @Override
            public void onError(RxException e) {
                //                ToastUtils.normal("倒计时错误", Gravity.CENTER);
            }

            @Override
            public void onSuccess(Long aLong) {
//                if (aLong == 0 || aLong < 0) {
//                    //交卷
//                    binding.icdExamMust.llTopAction.tvDownTimes.setText("00:00:00");
//                    startHandPaper(false);
//                } else {
                //倒计时
                String timeStr = DateUtil.secondToTime(Integer.parseInt(aLong + ""));
                binding.icdExamMust.llTopAction.tvDownTimes.setText(timeStr);
//                }
            }
        });
    }

    @Override
    public void postHandPaperSuccess() {

        if (getArguments().getString("paperType") != null && getArguments().getString("paperType").matches("[0-9]+")) {
            initArguments().putInt("paperType", Integer.parseInt(getArguments().getString("paperType")));
        }
        if (!StringUtils.isEmpty(getArguments().getString("paperId", "")) && getArguments().getString("paperId").matches("[0-9]+")) {
            initArguments().putInt("paperValue", Integer.parseInt(getArguments().getString("paperId")));
        } else {
            initArguments().remove("paperValue");
        }
        startFragment(ListHelper.toPageExamResult());
        finish();
    }

    @Override
    public void toExamResult(String paperType) {
        //跳转到结果页面
        ExamResultFragment resultFragment = new ExamResultFragment();
        // 传递参数
        initArguments().putString("paperType", paperType); // 智能练习类型
        initArguments().putString("paperId", paperId);
        startFragment(resultFragment);
        finish();
    }

    @Override
    public void updataExamPage(int postion, AnswerGroupBean answerGroupBean) {
        try {
            model.answerSheetOptionBeansObs.get().get(postion).setPageState(answerGroupBean.getPageState());
        } catch (Exception e) {
        }
        LogUtils.e("开始更新1------>>>" + postion + "******" + answerGroupBean.getAnswerSheetOptionBean().getQuestionNum() + "***" + answerGroupBean.getQuestionNum());
        adapter.setCurrentNum(answerGroupBean.getQuestionNum());
        adapter.updateDataItem(postion, answerGroupBean);
    }

    @Override
    public void showSaveView() {
        AnswerGroupBean answerGroupBean = getCurrentAdapterAns();
        if (answerGroupBean == null) {
            return;
        }
        if (answerGroupBean.getPageState() == PageTypeConfig.PAGE_STATE_LOADING) {
            return;
        }
        if (answerGroupBean.getAnswerSheetOptionBean() == null) {
            return;
        }
        if (answerGroupBean.getAnswerSheetOptionBean().isCollection()) {
            //收藏

            binding.icdExamMust.llBottomAction.tvUserSave.setText("已收藏");
            binding.icdExamMust.llBottomAction.tvUserSave.setTag(true);
            binding.icdExamMust.llBottomAction.imgUserSave.setImageResource(R.drawable.core_save_yes);
            binding.icdExamMust.llBottomAction.imgUserSave.setImageTintList(ColorStateList.valueOf(Color.parseColor("#3163f6")));
        } else {
            //未收藏

            binding.icdExamMust.llBottomAction.tvUserSave.setTag(false);
            binding.icdExamMust.llBottomAction.tvUserSave.setText("收藏");
            binding.icdExamMust.llBottomAction.imgUserSave.setImageResource(R.drawable.core_save_no);
            if (model.themeBean.get() != null) {
                binding.icdExamMust.llBottomAction.imgUserSave.setImageTintList(ColorStateList.valueOf(Color.parseColor(model.themeBean.get().getBottomContainerText())));
            }
        }

    }

    @Override
    public AnswerGroupBean getCurrentAdapterAns() {
        return adapter.getData().get(getCurrentPostion());
    }

    @Override
    public void noExamPermiss(boolean need) {
        PointHelper.joinPointData(PointerMsgType.POINTER_A_100_TRIGGERBOUNCED, true);
        //        new PopupManager.Builder(getContext()).maxHeight(DpUtils.dp2px(getContext(), 436 / 825)).asCustom(new NewNoBuyDialog(getContext(), new OnConfirmListener() {
        //            @Override
        //            public void onConfirm() {
        //                initArguments().putString("url", ConfigHelper.getPayUrl());
        //                initArguments().putInt("payType", 1);
        //                startFragment(CommonHelper.showCommonWeb());
        //            }
        //        }, getActivity(), need, PayRouthConfig.PAY_EXAM_PAY)).show();


        MessageHelper.openNoPremissBuyDialog(getActivity(), need, Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG ? "24" : PayRouthConfig.PAY_EXAM_PAY);

    }

    @Override
    public void showErrorToast(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return;
        }
        ToastUtils.normal(msg, Gravity.CENTER);

    }

    @Override
    public void showFaceView() {
        if (faceBackPopup == null) {
            return;
        }
        if (faceBackPopup.isShow()) {
            faceBackPopup.dismiss();
        }
        faceBackPopup.show();

    }

    BasePopupView guildPop = null;

    @Override
    public void showGuilderView(int type) {
        int guilder = KvUtils.get("exam_guilder_again3", 1);
        LogUtils.e("开启-----2>>>" + guilder);
        if (guilder == 1 || guilder == 2) {
            MessageHelper.openGuilder(getActivity(), guilder == 1 ? 1 : 2, examGuilderListener);
        }
    }

    ExamGuilderListener examGuilderListener = new ExamGuilderListener() {
        @Override
        public void onLeftExam() {
            goToNextPage();
        }

        @Override
        public void onRightExam() {
            binding.icdExamMust.examViewpager2.setCurrentItem(getCurrentPostion() - 1);

        }
    };

    @Override
    public void afterClearSheetData(List<AnswerGroupBean> answerSheetOptionBeans, int localAddress, int QuestionCount, int Duration, int RemainingQuestionsCount) {
        if (model.checkAnswerGroupBeanValues(answerSheetOptionBeans)) {
            LogUtils.e("清理-----》》》11111");
            showEmptyView();
        } else {
            LogUtils.e("清理-----》》》22222");
            model.initDataSheet(answerSheetOptionBeans, localAddress, QuestionCount, Duration, RemainingQuestionsCount);
        }
    }

    @Override
    public void afterGetLocalData(String loacalData) {
        model.getPacketAccessData(loacalData);
    }

    @Override
    public void initFaceBackPopup() {
        examFaceBackPopup = new ExamFaceBackPopup(getContext(), this);
        faceBackPopup = new PopupManager.Builder(getContext()).autoDismiss(false).dismissOnBackPressed(true).autoFocusEditText(false).enableDrag(false).dismissOnTouchOutside(true).hasShadowBg(true).asCustom(examFaceBackPopup);
    }

    @Override
    public void showLoadingDataView(boolean success, String msg) {
        if (loadingPopupView == null) {
            return;
        }
        loadingPopupView.setTitle(msg);
        if (success) {
            if (loadingPopupView.isShow()) {
                loadingPopupView.dismiss();
            }
            loadingPopupView.show();
        } else {
            if (loadingPopupView.isShow()) {
                loadingPopupView.dismiss();
            }
        }
    }

    @Override
    public void postFaceBackState(boolean success) {
        if (examFaceBackPopup == null) {
            return;
        }
        if (faceBackPopup == null) {
            return;
        }
        examFaceBackPopup.reshView(success ? 3 : 2);
    }

    @Override
    public void initLoadingPopup() {
        loadingPopupView = new PopupManager.Builder(getContext()).asLoading("", R.layout.popup_center_impl_loading);
    }

    @Override
    public void showNoNetworkView() {
        binding.examMultipleStatusView.showNoNetwork();
    }

    @Override
    public void showErrorView() {
        binding.examMultipleStatusView.showError();
    }

    @Override
    public void showLoadingView() {
        binding.examMultipleStatusView.showLoading();
    }

    @Override
    public void toPageByAuto(boolean next) {
        model.calculateResidueCount();
        if (next) {
            this.goToNextPage();
        }
        model.findNoVipPermiss(getCurrentPostion(), model.getResidueCount());
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
            if ((model.maxNumObs.get() - 1) == binding.icdExamMust.examViewpager2.getCurrentItem()) {
                if (next) {
                    model.showPageFinshView();
                }
            }
        }
    }

    @Override
    public void userSelectAnswerData(int questionNum, boolean rights) {
        LogUtils.e("看看选择的案例" + questionNum + "---" + rights);
        if (rights) {
            this.goToNextPage();
        }

        model.updataModelByQuestionNum(questionNum, rights);
        model.checkPageFinsh(getCurrentPostion(), true);

        // 检查是否是智能练习且为最后一题，如果是则显示查看结果按钮
        checkAndShowResultButton();

        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_COURSE_DETAIL_QUESTION && model.doNum == model.getExamSheetCount()) {
            closePage = true;
            onces = true;
            model.destory();
            initiativeExitExam();
            //全部答完
            initArguments().putString("paperType", paperType);
            initArguments().putString("title", title);
            initArguments().putString("paperId", paperId);
            initArguments().putBoolean("restart", restart);
            initArguments().putInt("location", location);
            startFragment(new ExamCoreCourseDetailChildFinishFragment());
            finish();
        }
    }

    @Override
    public void onUserSelectSheetQuestionNum(int questionNum, boolean isSee) {
        LogUtils.e("调用了-----");
        int index = model.getIndexByQuestionNum(questionNum);
        if (isSee) {
            binding.icdExamMust.examViewpager2.setCurrentItem(index, false);
            //            MyPagerHelper.setCurrentItem(binding.icdExamMust.examViewpager2, index, 10);
            model.getPacketAccessData(String.valueOf(index));
        } else {
            //无权限
            noExamPermiss(false);
        }
    }

    @Override
    public void onUserSelectRestartExam(boolean restart) {
        if (restart) {
            model.restartExam();
        } else {
            //交卷
            startHandPaper(true);
        }
    }

    @Override
    public void userSelectFaceBack(String opion, String context) {
        String questionId = adapter.getQuestionId(getCurrentPostion());
        if (StringUtils.isEmpty(questionId)) {
            return;
        }
        model.postFaceBackData(opion, questionId, context, getCurrentPostion(), adapter.getItem(getCurrentPostion()), adapter.getQuestionTabInfo(getCurrentPostion()));
    }

    @Override
    public void initData(List<ExamFaceBackModel> examFaceBackModels) {

    }

    @Override
    public void onClicked(View v) {
        if (!NoDoubleClickUtils.isDoubleClick()) {
            if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ZJLX || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_TKLX || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ERROR || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_COURSE_DETAIL_QUESTION || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_INTELLIGENT_PRACTICE || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_EvaluationReport || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_HIGH_FREQUENCY) {
                closePage = true;
                onces = true;
                model.destory();
                initiativeExitExam();
            } else {
                finish();
            }
        }

    }

    /**
     * 用户点击返回键
     */
    public void initiativeExitExam() {
        LogUtils.e("看看留下的------>>>>");
        //背题模式不提交做题记录
        if (model.getStyleConfigBean() != null && model.getStyleConfigBean().isShowMemoryModel()) {
            model.userInitiativeExit(paperType);
        }
    }

    /**
     * 是否显示交卷
     */
    private boolean getStateExam() {
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_MNZT || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_LNZT || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS) {
            return false;
        } else {
            return true;
        }
    }

    //上一次跳转下一页时间
    private long lastNextTime = 0;

    /**
     * 跳转到下一页
     */
    private void goToNextPage() {
        if (System.currentTimeMillis() - lastNextTime < 200) {
            return;
        }
        lastNextTime = System.currentTimeMillis();
        //答题的话不做拦截，非答题做拦截判断
        if (!ExamPaperTypeConfig.isExaminationAndAnalysis(Integer.parseInt(paperType), initArguments().getString("title"))) {
            if (model.getStyleConfigBean() != null && !model.getStyleConfigBean().isAutoJumpNext()) {
                return;
            }
        }

        if (adapter != null && model.checkNextPage(adapter, getCurrentPostion(), true)) {
            ViewPager2 viewPager2 = binding.icdExamMust.examViewpager2;
            viewPager2.setCurrentItem(Math.min(viewPager2.getCurrentItem() + 1, adapter.getItemCount()));
        }
    }
}
