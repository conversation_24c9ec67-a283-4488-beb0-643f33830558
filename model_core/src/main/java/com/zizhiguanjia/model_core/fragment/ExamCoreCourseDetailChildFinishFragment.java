package com.zizhiguanjia.model_core.fragment;

import android.os.Bundle;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.constants.CoreExamRouterPath;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.bean.ExamCoreCourseDetailChildFinishBean;
import com.zizhiguanjia.model_core.event.CourseDetailNextEvent;
import com.zizhiguanjia.model_core.navigator.ExamCoreCourseDetailChildFinishNavigator;
import com.zizhiguanjia.model_core.viewmodel.ExamCoreCourseDetailChildFinishViewModel;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatTextView;

/**
 * 功能作用：课程详情习题答完的结果页面
 * 初始注释时间： 2023/11/26 20:42
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
@Route(path = CoreExamRouterPath.COURSE_DETAIL_CHILD_FINISH)
public class ExamCoreCourseDetailChildFinishFragment extends BaseFragment implements ExamCoreCourseDetailChildFinishNavigator {

    @BindViewModel
    ExamCoreCourseDetailChildFinishViewModel mViewModel;

    /**
     * 加载中
     */
    private BasePopupView mLoadingView;
    /**
     * 提示
     */
    private AppCompatTextView mTvFinishState;

    /**
     * 重置
     */
    private AppCompatButton mBtnReset;

    /**
     * 下一节
     */
    private AppCompatButton mBtnNext;

    /**
     * 初始化布局
     */
    @Override
    public int initLayoutResId() {
        return R.layout.core_exam_course_detail_child_finish;
    }

    /**
     * 初始化控制、监听等轻量级操作
     */
    @Override
    public void initView(Bundle savedInstanceState) {
        //加载中
        mLoadingView = new PopupManager.Builder(getContext()).asLoading("", R.layout.popup_center_impl_loading);
        //初始化
        mViewModel.initParams(this, this);
        //id初始化
        mTvFinishState = findViewById(R.id.tv_finish_state);
        mBtnNext = findViewById(R.id.btn_next);
        mBtnReset = findViewById(R.id.btn_reset);
        //标题栏左侧按钮
        ((TitleBar)findViewById(R.id.tbflutter)).getLeftImageButton().setOnClickListener(v -> finish());
        ((TitleBar)findViewById(R.id.tbflutter)).getCenterTextView().setText(getArguments().getString("title","本节习题"));
        //重做题点击
        mBtnReset.setOnClickListener(v -> {
            initArguments().putString("paperType",getArguments() != null ? getArguments().getString("paperType") : "");
            initArguments().putString("title",getArguments() != null ? getArguments().getString("title") : "");
            initArguments().putString("paperId",getArguments() != null ? getArguments().getString("paperId") : "");
            initArguments().putBoolean("restart", true);
            initArguments().putInt("location",getArguments() != null ? getArguments().getInt("location") : -1);
            startFragment(new ExamCoreFragment());
            finish();
        });
        //下一节点击
        mBtnNext.setOnClickListener(v -> {
            LiveEventBus.get(CourseDetailNextEvent.class).post(new CourseDetailNextEvent(getArguments() != null ? getArguments().getString("paperId") : ""));
            finish();
        });
    }

    /**
     * 处理重量级数据、逻辑
     */
    @Override
    public void initViewData() {
        super.initViewData();
    }

    /**
     * 初始化界面观察者的监听
     * 接收数据结果
     */
    @Override
    public void initObservable() {
        super.initObservable();
    }

    /**
     * 设置显示数据
     *
     * @param data 要显示的数据
     */
    @Override
    public void setShowData(ExamCoreCourseDetailChildFinishBean data) {
        if (data != null) {
            mTvFinishState.setText(data.getTips() != null ? data.getTips() : "");
            if(1 == data.getHasNext()){
                mBtnNext.setVisibility(View.VISIBLE);
            }else {
                mBtnNext.setVisibility(View.INVISIBLE);
            }
        }
    }

    /**
     * 是否加载中
     *
     * @param loading true显示加载中
     */
    @Override
    public void showLoading(boolean loading) {
        if (loading) {
            if (!mLoadingView.isShow()) {
                mLoadingView.show();
            }
        } else {
            if (mLoadingView.isShow()) {
                mLoadingView.dismiss();
            }
        }
    }
}
