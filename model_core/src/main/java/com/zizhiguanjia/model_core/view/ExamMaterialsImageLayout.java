package com.zizhiguanjia.model_core.view;

import android.content.Context;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.wb.lib_image_loadcal.ImageManager;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.ImageViewerPopupView;
import com.wb.lib_pop.interfaces.OnSrcViewUpdateListener;
import com.wb.lib_pop.interfaces.PopupImageLoader;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.adapter.ExamMaterialsImageAdapter;
import com.zizhiguanjia.model_core.databinding.CoreExamViewExammaterialsImageBinding;
import com.zizhiguanjia.model_core.listener.OnRecycleItemClickListener;
import com.zizhiguanjia.model_core.model.OriginImageBean;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.GridLayoutManager;

public class ExamMaterialsImageLayout extends LinearLayout implements View.OnClickListener, OnRecycleItemClickListener {
    private List<OriginImageBean> originListUrls;
    private CoreExamViewExammaterialsImageBinding imageBinding;
    private ExamMaterialsImageAdapter mAdapter;
    private boolean imageFlags=true;
    private ExamCoreStyleConfigBean styleConfigBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    private ExamCoreThemeConfigThemeBean themeBean;
    public ExamMaterialsImageLayout(Context context) {
        this(context, null);
    }

    public ExamMaterialsImageLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ExamMaterialsImageLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public ExamMaterialsImageLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }

    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        imageBinding.imageTags.setTextColor(Color.parseColor(themeBean.getTextColor()));
        if(mAdapter != null){
            mAdapter.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        }
    }

    private void initView() {
        imageBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.core_exam_view_exammaterials_image, this, true);
        mAdapter = new ExamMaterialsImageAdapter();
        imageBinding.multiRecy.setLayoutManager(new GridLayoutManager(getContext(), 2));
        imageBinding.multiRecy.addItemDecoration(new GridSpaceItemDecoration(2, DpUtils.dp2px(getContext(), 14), DpUtils.dp2px(getContext(), 11)));
        imageBinding.multiRecy.setAdapter(mAdapter);
        mAdapter.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        imageBinding.singleImg.setOnClickListener(this);
        initListener();
    }
    public ExamMaterialsImageLayout setImageFlags(boolean imageFlags){
        this.imageFlags=imageFlags;
        return this;
    }
    public ExamMaterialsImageLayout setImageUrls(List<OriginImageBean> originListUrl) {
        this.originListUrls = originListUrl;
        if(originListUrls==null){
        }else {
        }
        return this;
    }

    public void build() {
        initData();
    }

    private void initData() {
        imageBinding.setImageSize(originListUrls == null ? 0 : originListUrls.size());
        if (originListUrls != null) {
            if (originListUrls.size() == 1) {
                OriginImageBean originImageBeans = originListUrls.get(0);
                if(originImageBeans==null){
                    imageBinding.setImageSize(0);
                    return;
                }
                ImageManager.getInstance().displayImage(originImageBeans.getThumbnailUrl(), imageBinding.singleImg);
                imageBinding.singleImg.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if(originImageBeans==null)return;
                        OriginImageBean originImageBeans = (OriginImageBean) originListUrls.get(0);
                        List<Object> bigImage=new ArrayList<>();
                        bigImage.clear();
                        bigImage.add(originImageBeans);
                        new PopupManager.Builder(getContext()).asImageViewer(imageBinding.singleImg, 0, bigImage, false, false, Color.WHITE, Color.WHITE, -1, true, new OnSrcViewUpdateListener() {
                            @Override
                            public void onSrcViewUpdate(@NonNull ImageViewerPopupView popupView, int position) {
                            }

                        }, new PopupImageLoader() {
                            @Override
                            public void loadImage(int position, @NonNull Object uri, @NonNull ImageView imageView) {
                                OriginImageBean originImageBeans = (OriginImageBean) originListUrls.get(position % originListUrls.size());
                                ImageManager.getInstance().displayImage(originImageBeans.getThumbnailUrl(), imageView);
                                Glide.with(imageView).downloadOnly().load(originImageBeans.getSourceUrl()).into(new SimpleTarget<File>() {
                                    @Override
                                    public void onResourceReady(@NonNull File resource, @Nullable Transition<? super File> transition) {
                                        int[] size =getImageSize(resource);
                                        Glide.with(imageView).load(resource).apply(new RequestOptions().override(size[0], size[1])).into(imageView);
                                    }
                                });
                            }
                            @Override
                            public File getImageFile(@NonNull Context context, @NonNull Object uri) {
                                OriginImageBean originImageBean= (OriginImageBean) uri;
                                try {
                                    return Glide.with(context).downloadOnly().load(originImageBean.getSourceUrl()).submit().get();
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                return null;
                            }
                        }).show();
                    }
                });
                imageBinding.imageTags.setVisibility(imageFlags?VISIBLE:GONE);
            } else {
                mAdapter.setImageTags(imageFlags);
                mAdapter.setDataItems(originListUrls);
            }
        }
    }

    private void initListener() {
        imageBinding.singleImg.setOnClickListener(this);
        mAdapter.setOnItemClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.single_img) {
            try {
                if(originListUrls==null)return;
                OriginImageBean originImageBeans = (OriginImageBean) originListUrls.get(0);
                List<Object> bigImage=new ArrayList<>();
                bigImage.clear();
                bigImage.add(originImageBeans);




                new PopupManager.Builder(getContext()).asImageViewer(imageBinding.singleImg, 0, bigImage, false, false, Color.WHITE, Color.WHITE, -1, true, new OnSrcViewUpdateListener() {
                    @Override
                    public void onSrcViewUpdate(@NonNull ImageViewerPopupView popupView, int position) {
                    }

                }, new PopupImageLoader() {
                    @Override
                    public void loadImage(int position, @NonNull Object uri, @NonNull ImageView imageView) {
                        OriginImageBean originImageBeans = (OriginImageBean) originListUrls.get(position % originListUrls.size());
                        ImageManager.getInstance().displayImage(originImageBeans.getThumbnailUrl(), imageView);
                        Glide.with(imageView).downloadOnly().load(originImageBeans.getSourceUrl()).into(new SimpleTarget<File>() {
                            @Override
                            public void onResourceReady(@NonNull File resource, @Nullable Transition<? super File> transition) {
                                int[] size =getImageSize(resource);
                                Glide.with(imageView).load(resource).apply(new RequestOptions().override(size[0], size[1])).into(imageView);
                            }
                        });
                    }

                    @Override
                    public File getImageFile(@NonNull Context context, @NonNull Object uri) {
                        OriginImageBean originImageBean= (OriginImageBean) uri;
                        try {
                            return Glide.with(context).downloadOnly().load(originImageBean.getSourceUrl()).submit().get();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        return null;
                    }
                }).show();
            }catch (Exception e){

            }

        }
    }
    @Override
    public void OnItemOnclic(View mView, int postions) {
        if(originListUrls==null)return;
        List<Object> bigImage=new ArrayList<>();
        bigImage.clear();
        bigImage.addAll(originListUrls);
        new PopupManager.Builder(getContext()).asImageViewer((ImageView) mView, postions, bigImage, false, false, Color.WHITE, Color.WHITE, -1, true, new OnSrcViewUpdateListener() {
            @Override
            public void onSrcViewUpdate(@NonNull ImageViewerPopupView popupView, int position) {
            }

        }, new PopupImageLoader() {
            @Override
            public void loadImage(int position, @NonNull Object uri, @NonNull ImageView imageView) {
                OriginImageBean originImageBeans = (OriginImageBean) originListUrls.get(position % originListUrls.size());
                ImageManager.getInstance().displayImage(originImageBeans.getThumbnailUrl(), imageView);
                Glide.with(imageView).downloadOnly().load(originImageBeans.getSourceUrl()).into(new SimpleTarget<File>() {
                    @Override
                    public void onResourceReady(@NonNull File resource, @Nullable Transition<? super File> transition) {
                        int[] size =getImageSize(resource);
                        Glide.with(imageView).load(resource).apply(new RequestOptions().override(size[0], size[1])).into(imageView);
                    }
                });
            }

            @Override
            public File getImageFile(@NonNull Context context, @NonNull Object uri) {
                OriginImageBean originImageBean= (OriginImageBean) uri;
                try {
                    return Glide.with(context).downloadOnly().load(originImageBean.getSourceUrl()).submit().get();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return null;
            }
        }).show();
    }

    public  int[] getImageSize(File file) {
        if (file == null) return new int[]{0, 0};
        BitmapFactory.Options opts = new BitmapFactory.Options();
        opts.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(file.getAbsolutePath(), opts);
        return new int[]{opts.outWidth, opts.outHeight};
    }
}
