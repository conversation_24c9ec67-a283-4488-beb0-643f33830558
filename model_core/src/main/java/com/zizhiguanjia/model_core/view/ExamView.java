package com.zizhiguanjia.model_core.view;

import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;

import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreExamUserViewBinding;
import com.zizhiguanjia.model_core.factory.CustomViewModelFactory;
import com.zizhiguanjia.model_core.listener.ExamContentSelectQuestionTabCall;
import com.zizhiguanjia.model_core.listener.ExamContentUserAnswerCall;
import com.zizhiguanjia.model_core.listener.ExamToAutoPageListener;
import com.zizhiguanjia.model_core.listener.ExamUserAnswerListener;
import com.zizhiguanjia.model_core.listener.ExamUserSelectOptionCall;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.model.OriginImageBean;
import com.zizhiguanjia.model_core.navigator.ExamNavigator;
import com.zizhiguanjia.model_core.utils.ExamUtils;
import com.zizhiguanjia.model_core.viewmodel.ExamViewModel;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.ViewModelStoreOwner;

public class ExamView extends LinearLayout implements ViewModelStoreOwner, ExamNavigator, ExamUserSelectOptionCall {
    private ViewModelStore mViewModelStore;
    private ExamViewModel examViewModel;
    private CoreExamUserViewBinding binding;
    private Activity mActivity;
    private int examType;
    private AnswerGroupBean mCommonGroupExamBean;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    private ExamCoreStyleConfigBean styleConfigBean;

    public ExamView(Context context) {
        this(context, null);
    }

    public ExamView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ExamView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public ExamView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }

    private ExamUserAnswerListener examUserAnswerListener;
    private ExamContentSelectQuestionTabCall examContentSelectQuestionTabCall;

    public void setExamContentActionCall(ExamUserAnswerListener examContentActionCall) {
        this.examUserAnswerListener = examContentActionCall;
    }

    public void setExamContentSelectQuestionTabCall(ExamContentSelectQuestionTabCall examContentSelectQuestionTabCall) {
        this.examContentSelectQuestionTabCall = examContentSelectQuestionTabCall;
    }

    @Override
    public void initView() {
        binding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.core_exam_user_view, this, true);
//        binding.msvExamQuestion.showLoading();
        binding.test1.setVisibility(VISIBLE);
        initModel();
        initListener();
    }

    @Override
    public void initModel() {
        mViewModelStore = new ViewModelStore();
        examViewModel = new ViewModelProvider(this, new CustomViewModelFactory()).get(ExamViewModel.class);
        binding.setModel(examViewModel);
    }

    @Override
    public void initListener() {
        binding.icdExamViewContext.examQuestionOptionEov.setCall(this);
    }

    @Override
    public void reshData(AnswerGroupBean answerGroupBean, Activity activity, int paperType) {
        this.mCommonGroupExamBean = answerGroupBean;
        this.mActivity = activity;
        this.examType = paperType;
        binding.test1.setVisibility(GONE);
        examViewModel.showContextView(answerGroupBean, paperType, this);
    }

    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        binding.icdExamViewAlxzContext.testCCV.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.icdExamViewCaseQuestionContext.ecpvCaseOptions.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.icdExamViewContext.examQuestionResultErlt.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.icdExamViewContext.examQuestionTopEmlv.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.icdExamViewContext.examQuestionOptionEov.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
    }

    public void showExamLoad(){
//        binding.msvExamQuestion.showLoading();
    }
    @Override
    public void showAlXzView() {
        try {
            binding
                    .icdExamViewAlxzContext.testCCV
                    .setmAnswerGroupBean(mCommonGroupExamBean)
                    .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                    .setExamToAutoPageListener(new ExamToAutoPageListener() {
                        @Override
                        public void toPageByAuto(boolean nextPage) {
                            LogUtils.e("看看案例选择"+nextPage);
                            examUserAnswerListener.toPageByAuto(nextPage);
                        }
                    })
                    .setExamContentSelectQuestionTabCall(new ExamContentSelectQuestionTabCall() {
                        @Override
                        public void onTabChange(int tabPosition) {
                            if(examContentSelectQuestionTabCall != null) {
                                examContentSelectQuestionTabCall.onTabChange(tabPosition);
                            }
                        }
                    })
                    .setmExamType(examType)
                    .setExamContentUserAnswerCall(new ExamContentUserAnswerCall() {
                        @Override
                        public void userSelectAnswerData(int questionNum, boolean doing) {
                            LogUtils.e("案例选择---->>>"+doing);
                            examUserAnswerListener.userSelectAnswerData(questionNum,doing);
                        }
                    })
                    .buildView();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showContextTitlteView(String context, int questionType, List<OriginImageBean> images) {
        binding.icdExamViewContext.examQuestionTopEmlv
                .setImage(images)
                .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setContext(context, ExamUtils.getInstance().getQurstionTypeStr(questionType))
                .build();
    }

    @Override
    public void showOptionView(List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> examOptionBeans, int questionType, List<String> userAnswer, List<String> answer, int questionState) {
        binding.icdExamViewContext.examQuestionOptionEov
                .setExamOptionBeans(examOptionBeans)
                .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setQuestionType(questionType)
                .setUserAnswer(userAnswer)
                .setQuestionState(questionState)
                .setRightAnswer(answer)
                .setExamType(examType)
                .build();
    }

    @Override
    public void showResultView(List<String> mUserAnswers, List<String> mRightAnswers, String txt, List<OriginImageBean> objects, boolean jtjq) {
        binding.icdExamViewContext.examQuestionResultErlt
                .setmUserAnswers(mUserAnswers)
                .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setmRightAnswers(mRightAnswers)
                .setContext(txt)
                .setImage(objects)
                .setErrorTip(mCommonGroupExamBean.getAnswerSheetOptionBean().getErrorTip())
                .setJtjqTxt(mCommonGroupExamBean.getAnswerSheetOptionBean().getSkill())
                .setExamcodeTitle(mCommonGroupExamBean.getAnswerSheetOptionBean().getExamCodeTitle())
                .build();
    }

    @Override
    public void showCaseMaterialsView() {
//        binding.icdExamViewCaseQuestionContext.examCasequestionTopEmlv
//                .setContext(mCommonGroupExamBean.getAnswerSheetOptionBean().getContent(),
//                        ExamUtils.getInstance().getQurstionTypeStr((mCommonGroupExamBean.getAnswerSheetOptionBean().getQuestionType())))
//                //Arrays.asList(StringUtils.convertStrToArray2(mCommonGroupExamBean.getAnswerSheetOptionBean().getContentImage()))
//                .setImage(null)
//                .build();
    }

    @Override
    public void showCaseQuestionOptionsView() {
        binding.icdExamViewCaseQuestionContext.ecpvCaseOptions
                .with(mActivity)
                .setContext(mCommonGroupExamBean.getAnswerSheetOptionBean().getContent(),
                        ExamUtils.getInstance().getQurstionTypeStr((mCommonGroupExamBean.getAnswerSheetOptionBean().getQuestionType())))
                .setImage(null)
                .setQuestionIds(String.valueOf(mCommonGroupExamBean.getAnswerSheetOptionBean().getQuestionId()))
                .setQuestionNum(String.valueOf(mCommonGroupExamBean.getAnswerSheetOptionBean().getQuestionNum()))
                .setAnalysisListData(mCommonGroupExamBean.getAnswerSheetOptionBean().getAnswerKey())
                .setRightListData(mCommonGroupExamBean.getAnswerSheetOptionBean().getAnswers())
                .setUserListDatas(mCommonGroupExamBean.getAnswerSheetOptionBean().getUserAnswer())
                .setDatas(mCommonGroupExamBean.getAnswerSheetOptionBean().getQuestions())
                .setQuestionState(mCommonGroupExamBean.getAnswerSheetOptionBean().getQuestionState())
                .setJtjq(examType==ExamPaperTypeConfig.EXAM_PAPER_SAVE?"":mCommonGroupExamBean.getAnswerSheetOptionBean().getSkill())
                .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setCaseDataCall(new ExamContentUserAnswerCall() {
                    @Override
                    public void userSelectAnswerData(int questionNum, boolean doing) {
                        LogUtils.e("看看做题的状态----"+doing);
                        if(doing){
                            examUserAnswerListener.toPageByAuto(true);
                        }
                    }
                }).build();
    }

    @NonNull
    @Override
    public ViewModelStore getViewModelStore() {
        return mViewModelStore;
    }

    @Override
    protected void onDetachedFromWindow() {
        mViewModelStore.clear();
        super.onDetachedFromWindow();
    }

    @Override
    public void getUserSelectOption(String option) {
        LogUtils.e("看看计算----->>>"+examType);
        if(examUserAnswerListener==null)return;
        examViewModel.userConfim(option,examUserAnswerListener);
        boolean right=ExamUtils.getInstance().getUserRight(examViewModel.getAnswerItemBean().getUserAnswer(),examViewModel.getRightList());
        examUserAnswerListener.userSelectAnswerData(mCommonGroupExamBean.getQuestionNum(),right);
    }

    public int getQuestionTabInfo(){
      return   binding.icdExamViewAlxzContext.testCCV.getQuestionTabInfo();
    }
}
