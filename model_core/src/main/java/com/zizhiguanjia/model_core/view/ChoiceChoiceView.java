package com.zizhiguanjia.model_core.view;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;

import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.adapter.ExamCaseQuestionHListAdapter;
import com.zizhiguanjia.model_core.databinding.CoreExamCustomchoiceLayoutBinding;
import com.zizhiguanjia.model_core.factory.CustomChoiceViewModelFactory;
import com.zizhiguanjia.model_core.listener.ExamCaseQuestionQhCall;
import com.zizhiguanjia.model_core.listener.ExamContentSelectQuestionTabCall;
import com.zizhiguanjia.model_core.listener.ExamContentUserAnswerCall;
import com.zizhiguanjia.model_core.listener.ExamToAutoPageListener;
import com.zizhiguanjia.model_core.listener.ExamUserSelectOptionCall;
import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.model.OriginImageBean;
import com.zizhiguanjia.model_core.navigator.CustomChoiceNavigator;
import com.zizhiguanjia.model_core.utils.ExamUtils;
import com.zizhiguanjia.model_core.viewmodel.CustomChoiceViewModel;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 选择题自定义View
 */
public class ChoiceChoiceView extends LinearLayout implements ViewModelStoreOwner , CustomChoiceNavigator, ExamCaseQuestionQhCall, ExamUserSelectOptionCall {
    private ViewModelStore mViewModelStore;
    private CustomChoiceViewModel customChoiceViewModel;
    //CoreExamUserViewBinding
    private CoreExamCustomchoiceLayoutBinding binding;
    private AnswerGroupBean mAnswerGroupBean;
    private int mExamType;
    private ExamContentUserAnswerCall examContentUserAnswerCall;
    private ExamContentSelectQuestionTabCall examContentSelectQuestionTabCall;
    private ExamToAutoPageListener examToAutoPageListener;
    private ExamCaseQuestionHListAdapter mAdapter;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    private ExamCoreStyleConfigBean styleConfigBean;
    private int questionTabPosition = 0;
    public ChoiceChoiceView(Context context) {
        this(context,null);
    }
    public ChoiceChoiceView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }
    public ChoiceChoiceView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr,0);
    }
    public ChoiceChoiceView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }
    public ChoiceChoiceView setExamContentUserAnswerCall(ExamContentUserAnswerCall examContentUserAnswerCall) {
        this.examContentUserAnswerCall = examContentUserAnswerCall;
        return this;
    }

    public ChoiceChoiceView setExamContentSelectQuestionTabCall(ExamContentSelectQuestionTabCall examContentSelectQuestionTabCall) {
        this.examContentSelectQuestionTabCall = examContentSelectQuestionTabCall;
        return this;
    }

    public ChoiceChoiceView setExamToAutoPageListener(ExamToAutoPageListener examToAutoPageListeners) {

        this.examToAutoPageListener = examToAutoPageListeners;
        return this;
    }

    public ChoiceChoiceView setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        binding.slCard.setCardBackgroundColor(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectCardBgColor())));
        binding.viewLine.setBackgroundColor(Color.parseColor(themeBean.getLineColor()));
        binding.examQuestionTopEmlv.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.emvQuestionTags.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.examQuestionOptionEov.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.examQuestionResultErlt.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        customChoiceViewModel.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        if(mAdapter != null) {
            mAdapter.setThemeConfigData(styleConfigBean, themeBean, textSizeBean);
        }
        return this;
    }
    private void initView(){
        binding= DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.core_exam_customchoice_layout,this,true);
        mViewModelStore=new ViewModelStore();
        customChoiceViewModel=new ViewModelProvider(this,new CustomChoiceViewModelFactory()).get(CustomChoiceViewModel.class);
        binding.setModel(customChoiceViewModel);

        mAdapter=new ExamCaseQuestionHListAdapter(this);
        binding.rcvQuestion.setLayoutManager(new LinearLayoutManager(getContext(), RecyclerView.HORIZONTAL,false));
        binding.rcvQuestion.addItemDecoration(new GridSpaceItemDecoration(6, DpUtils.dp2px(getContext(), 0), DpUtils.dp2px(getContext(), 8)));
        binding.rcvQuestion.setAdapter(mAdapter);
        binding.examQuestionOptionEov.setCall(this);
        mAdapter.setThemeConfigData(styleConfigBean, themeBean, textSizeBean);
    }

    public ChoiceChoiceView setmExamType(int mExamType) {
        this.mExamType = mExamType;
        return this;
    }

    public ChoiceChoiceView setmAnswerGroupBean(AnswerGroupBean mAnswerGroupBean) {
        this.mAnswerGroupBean = mAnswerGroupBean;
        return  this;
    }
    public void buildView() throws Exception {
        if(mAnswerGroupBean==null){
            throw new Exception("缺少mAnswerGroupBean");
        }
        customChoiceViewModel.initParams(mAnswerGroupBean,mExamType,this);
    }
    @NonNull
    @Override
    public ViewModelStore getViewModelStore() {
        return mViewModelStore;
    }
    @Override
    protected void onDetachedFromWindow() {
        mViewModelStore.clear();
        super.onDetachedFromWindow();
    }

    @Override
    public void showResultView(List<String> mUserAnswers, List<String> mRightAnswers, String txt, List<OriginImageBean> objects, boolean jtjq) {
        binding.examQuestionResultErlt
                .setmUserAnswers(mExamType== ExamPaperTypeConfig.EXAM_PAPER_ERROR||mExamType==ExamPaperTypeConfig.EXAM_PAPER_SAVE?(jtjq?mUserAnswers:new ArrayList<>()):mUserAnswers)
                .setmRightAnswers(mRightAnswers)
                .setContext(txt)
                .setImage(objects)
                .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setErrorTip(mAnswerGroupBean.getAnswerSheetOptionBean().getErrorTip())
                .setJtjqTxt(mExamType==ExamPaperTypeConfig.EXAM_PAPER_SAVE?"":mAnswerGroupBean.getAnswerSheetOptionBean().getSkill())
                .build();
    }

    @Override
    public void showContextTitlteView(String context, int questionType,List<OriginImageBean> iamges) {
        binding.examQuestionTopEmlv
                .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setContext(context, ExamUtils.getInstance().getQurstionTypeStr(questionType))
                .setImage(iamges)
                .build();
    }

    @Override
    public void showOptionView(List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> examOptionBeans, int questionType, List<String> userAnswer, List<String> answer, int questionState) {
         binding.examQuestionOptionEov
                 .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setExamOptionBeans(examOptionBeans)
                .setQuestionType(questionType)
                .setUserAnswer(userAnswer)
                .setQuestionState(questionState)
                .setRightAnswer(answer)
                .setExamType(mExamType)
                .build();
    }

    @Override
    public void getQuestionListData(List<String> strings) {
        mAdapter.setDataItems(strings == null || strings.isEmpty() || strings.size() == 1 ? new ArrayList<>() : strings);
    }

    @Override
    public void showOptionTagsList(String context, int questionType,List<OriginImageBean> iamges) {
        binding.emvQuestionTags
                .setmDefaultTags(false)
                .setImage(iamges)
                .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setContext(context, ExamUtils.getInstance().getQurstionTypeStr(questionType))
                .build();
    }

    @Override
    public void userQhQuestion(int postion) {
        questionTabPosition = postion;
        customChoiceViewModel.userQhQuestion(postion);
        if(examContentSelectQuestionTabCall != null){
            examContentSelectQuestionTabCall.onTabChange(postion);
        }
    }

    @Override
    public void getUserSelectOption(String option) {
        customChoiceViewModel.userConfim(option,examToAutoPageListener);
        if(examContentUserAnswerCall==null)return;
        boolean right=customChoiceViewModel.checkQuestionRight();
        LogUtils.e("看看计算的结果"+mExamType+"****"+right);
        examContentUserAnswerCall.userSelectAnswerData(mAnswerGroupBean.getQuestionNum(),right);
    }
    public int getQuestionTabInfo(){
        return questionTabPosition;
    }
}
