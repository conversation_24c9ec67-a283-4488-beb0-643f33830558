package com.zizhiguanjia.model_core.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.adapter.ExamOptionAdapter;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.config.ExamQuestionTypeConfig;
import com.zizhiguanjia.model_core.config.ExamResultStyleTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreExamOptionLayoutBinding;
import com.zizhiguanjia.model_core.listener.ExamUserSelectOptionCall;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.utils.ExamUtils;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ObservableField;
import androidx.recyclerview.widget.LinearLayoutManager;

public class ExamOptionView extends LinearLayout implements ExamUserSelectOptionCall {
    private ExamOptionAdapter mAdapter;
    private CoreExamOptionLayoutBinding binding;
    private List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> examOptionBeans;
    private List<String> mRightAnswer;
    private List<String> mUserAnswer;
    private int mQuestionType;
    private int examType;
    private int questionState;
    private ExamUserSelectOptionCall call;
    public int examOptionState=0;
    public ObservableField<ExamCoreThemeConfigThemeBean> themeBean = new ObservableField<>();
    private ExamCoreStyleConfigBean styleConfigBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    public void setCall(ExamUserSelectOptionCall call) {
        this.call = call;
    }
    public ExamOptionView(Context context) {
        this(context,null);
    }
    public ExamOptionView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }
    public ExamOptionView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr,0);
    }
    public ExamOptionView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }

    public ExamOptionView setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.themeBean.set(themeBean);
        this.styleConfigBean = styleConfigBean;
        this.textSizeBean = textSizeBean;
        if(mAdapter != null){
            mAdapter.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        }
        return this;
    }

    private void initView(){
        binding=DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.core_exam_option_layout,this,true);
        binding.setConfim(false);
        binding.setModel(this);
        binding.examOptionRcv.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter=new ExamOptionAdapter();
        mAdapter.setThemeConfigData(styleConfigBean,themeBean.get(),textSizeBean);
        binding.examOptionRcv.setAdapter(mAdapter);
    }
    public ExamOptionView setExamOptionBeans(List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> examOptionBeans) {
        this.examOptionBeans = examOptionBeans;
        return this;
    }
    public ExamOptionView setRightAnswer( List<String> answer) {
        this.mRightAnswer = answer;
        return this;
    }
    public ExamOptionView setQuestionState( int questionStates) {
        this.questionState = questionStates;
        return this;
    }
    public ExamOptionView setExamType(int examType) {
        this.examType = examType;
        return this;
    }
    public ExamOptionView setQuestionType(int questionType) {
        this.mQuestionType = questionType;
        return this;
    }
    public ExamOptionView setUserAnswer( List<String> userAnswer) {
        this.mUserAnswer = userAnswer;
        return this;
    }
    public void build(){
        if(examOptionBeans==null){
            examOptionBeans=new ArrayList<>();
        }
        if(examType== ExamPaperTypeConfig.EXAM_PAPER_LNZT||examType==ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM||examType==ExamPaperTypeConfig.EXAM_PAPER_MNZT ||examType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS){
            examOptionState=1;
        }else {
            examOptionState=0;
        }
        mAdapter.setTkType(examType);
        mAdapter.setType(mQuestionType);
        mAdapter.setmRightAnswer(mRightAnswer);
        mAdapter.setmUserAnswer(mUserAnswer);
        mAdapter.setQuestionState(questionState);
        binding.setType(mQuestionType);
        mAdapter.setDataItems(examOptionBeans);
        if(mUserAnswer==null||mUserAnswer.size()==0){
            binding.setConfim(false);
            mAdapter.setConfim(false);
        }else {
            binding.setConfim(true);
            mAdapter.setConfim(true);
        }
        mAdapter.setCall(this);
    }
    public void onClick(View view){
        if(call==null)return;
        if(ExamUtils.getInstance().getExamQuestionResultStyle(examType)== ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_SELECT_NO){
            return;
        }
        if(examType== ExamPaperTypeConfig.EXAM_PAPER_LNZT||examType==ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM||examType==ExamPaperTypeConfig.EXAM_PAPER_MNZT || examType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS){
        }else {
            if(binding.getConfim())return;
        }
        if(mAdapter.getmUserAnswer()==null||mAdapter.getmUserAnswer().size()==0){
            ToastUtils.normal("多选题，至少选择一项！", Gravity.CENTER);
            return;
        }
        binding.setConfim(true);
        String strs=StringUtils.ListToStr(mAdapter.getmUserAnswer());
        mAdapter.setConfim(true);
        mAdapter.notifyDataSetChanged();
        call.getUserSelectOption(strs);
    }

    @Override
    public void getUserSelectOption(String option) {
        if(call==null)return;
        if(mQuestionType== ExamQuestionTypeConfig.EXAM_QUESTION_DUO)return;
        mAdapter.setConfim(true);
        call.getUserSelectOption(option);
    }
}
