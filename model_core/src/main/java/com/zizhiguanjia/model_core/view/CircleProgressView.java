package com.zizhiguanjia.model_core.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.zizhiguanjia.model_core.R;

public class CircleProgressView extends View {

    private Paint mBackPaint;
    private Paint mProgPaint;
    private int mProgress;
    private RectF mRectF;

    private int mBackColor = Color.parseColor("#7598FB");
    private int mProgColor = Color.parseColor("#FFFFFF");
    private float mBackWidth = 30;
    private float mProgWidth = 27;

    public CircleProgressView(Context context) {
        this(context, null);
    }

    public CircleProgressView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CircleProgressView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        // 获取自定义属性
        if (attrs != null) {
            TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.CircleProgressView);
            mBackColor = ta.getColor(R.styleable.CircleProgressView_circle_backColor, mBackColor);
            mProgColor = ta.getColor(R.styleable.CircleProgressView_circle_progColor, mProgColor);
            mBackWidth = ta.getDimension(R.styleable.CircleProgressView_circle_backWidth, mBackWidth);
            mProgWidth = ta.getDimension(R.styleable.CircleProgressView_circle_progWidth, mProgWidth);
            mProgress = ta.getInteger(R.styleable.CircleProgressView_circle_progress, 0);
            ta.recycle();
        }

        // 初始化背景圆弧画笔
        mBackPaint = new Paint();
        mBackPaint.setColor(mBackColor);
        mBackPaint.setStrokeWidth(mBackWidth);
        mBackPaint.setStyle(Paint.Style.STROKE);
        mBackPaint.setAntiAlias(true);
        mBackPaint.setStrokeCap(Paint.Cap.ROUND);

        // 初始化进度圆弧画笔
        mProgPaint = new Paint();
        mProgPaint.setColor(mProgColor);
        mProgPaint.setStrokeWidth(mProgWidth);
        mProgPaint.setStyle(Paint.Style.STROKE);
        mProgPaint.setAntiAlias(true);
        mProgPaint.setStrokeCap(Paint.Cap.ROUND);

        // 初始化绘制区域
        mRectF = new RectF();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int width = getMeasuredWidth();
        int height = getMeasuredHeight();
        int size = Math.min(width, height);
        setMeasuredDimension(size, size);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int width = getWidth();
        int height = getHeight();
        
        // 设置圆弧绘制区域
        float strokeWidth = Math.max(mBackWidth, mProgWidth);
        float offset = strokeWidth / 2;
        mRectF.set(offset, offset, width - offset, height - offset);
        
        // 绘制背景圆弧
        canvas.drawArc(mRectF, 0, 360, false, mBackPaint);
        
        // 绘制进度圆弧
        float sweepAngle = mProgress * 3.6f; // 360度 * 进度百分比
        canvas.drawArc(mRectF, -90, sweepAngle, false, mProgPaint);
    }

    /**
     * 设置进度
     * @param progress 进度值（0-100）
     */
    public void setProgress(int progress) {
        if (progress < 0) {
            progress = 0;
        }
        if (progress > 100) {
            progress = 100;
        }
        this.mProgress = progress;
        invalidate();
    }

    /**
     * 获取当前进度
     * @return 当前进度（0-100）
     */
    public int getProgress() {
        return mProgress;
    }
} 