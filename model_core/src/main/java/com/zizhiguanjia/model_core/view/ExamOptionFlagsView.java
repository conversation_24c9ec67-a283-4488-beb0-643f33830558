package com.zizhiguanjia.model_core.view;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.config.ExamQuestionTypeConfig;
import com.zizhiguanjia.model_core.config.UserAnswerTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreExamOptionflagsLayoutBinding;
import com.zizhiguanjia.model_core.utils.GetCharAscii;

import java.util.List;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

public class ExamOptionFlagsView extends LinearLayout {
    private CoreExamOptionflagsLayoutBinding binding;
    private int mExamPostion;
    private List<String> rightAnswer;
    private int state,type;
    private ExamCoreStyleConfigBean styleConfigBean;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    public ExamOptionFlagsView(Context context) {
        this(context,null);
    }
    public ExamOptionFlagsView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public ExamOptionFlagsView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr,0);
    }

    public ExamOptionFlagsView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }
    private void initView(){
        binding=DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.core_exam_optionflags_layout,this,true);
    }
    public ExamOptionFlagsView setQuestionType(){
        return this;
    }

    public ExamOptionFlagsView mExamPostion(int postion){
        this.mExamPostion=postion;
        return this;
    }
    public ExamOptionFlagsView setFlgasColor(int state,int type){
        this.state=state;
        this.type=type;
        return this;
    }

    public void build(){
        binding.examOptionFlagsTv.setText(GetCharAscii.asciiToString(mExamPostion+65));
        setFlagsColor(state,type);
    }
    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        binding.examOptionFlagsTv.setTextSize(textSizeBean.getSize() - 4);
        ViewGroup.LayoutParams params = binding.examOptionFlagsTv.getLayoutParams();
        params.width = DpUtils.sp2px(getContext(), textSizeBean.getSize().intValue() + 4);
        params.height = params.width;
        binding.examOptionFlagsTv.setLayoutParams(params);
    }
    
    private void setFlagsColor(int state,int type){
        if(state== UserAnswerTypeConfig.USER_ANSWER_NO){
            binding.examOptionFlagsTv.setBackgroundResource(type== ExamQuestionTypeConfig.EXAM_QUESTION_DUO? R.drawable.core_exam_multiple_nor_bg:R.drawable.core_exam_single_nor_bg);
            binding.examOptionFlagsTv.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectIconTextColorNo()));
            binding.examOptionFlagsTv.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectIconBgColorNo())));
        }else if(state== UserAnswerTypeConfig.USER_ANSWER_DO&& !this.styleConfigBean.isShowMemoryModel()){
            binding.examOptionFlagsTv.setBackgroundResource(type== ExamQuestionTypeConfig.EXAM_QUESTION_DUO?R.drawable.core_exam_multiple_right_bg:R.drawable.core_exam_single_right_bg);
            binding.examOptionFlagsTv.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectIconTextColorSure()));
            binding.examOptionFlagsTv.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectIconBgColorSure())));
        }else if(state== UserAnswerTypeConfig.USER_ANSWER_RIGHT){
            binding.examOptionFlagsTv.setBackgroundResource(type== ExamQuestionTypeConfig.EXAM_QUESTION_DUO?R.drawable.core_exam_multiple_right_bg:R.drawable.core_exam_single_right_bg);
            binding.examOptionFlagsTv.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectIconTextColorSure()));
            binding.examOptionFlagsTv.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectIconBgColorSure())));
        }else if(state== UserAnswerTypeConfig.USER_ANSWER_ERROR&& !this.styleConfigBean.isShowMemoryModel()){
            binding.examOptionFlagsTv.setBackgroundResource(type== ExamQuestionTypeConfig.EXAM_QUESTION_DUO?R.drawable.core_exam_multiple_error_bg:R.drawable.core_exam_single_error);
            binding.examOptionFlagsTv.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectIconTextColorError()));
            binding.examOptionFlagsTv.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectIconBgColorError())));
        }else {
            binding.examOptionFlagsTv.setBackgroundResource(type== ExamQuestionTypeConfig.EXAM_QUESTION_DUO? R.drawable.core_exam_multiple_nor_bg:R.drawable.core_exam_single_nor_bg);
            binding.examOptionFlagsTv.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectIconTextColorNo()));
            binding.examOptionFlagsTv.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectIconBgColorNo())));
        }
    }
}
