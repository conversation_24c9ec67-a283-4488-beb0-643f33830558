package com.zizhiguanjia.model_core.view;

import android.content.Context;
import android.view.animation.Interpolator;
import android.widget.Scroller;

public class XScroller extends Scroller {

    private boolean noDuration = false;// 标识是否没有滚动时间(true:没有；false:有)，默认为有

    public XScroller(Context context) {
        super(context);
    }

    public XScroller(Context context, Interpolator interpolator) {
        super(context, interpolator);
    }

    public XScroller(Context context, Interpolator interpolator, boolean flywheel) {
        super(context, interpolator, flywheel);
    }

    /**
     * 开始滚动
     *
     * @param startX   开始X位置
     * @param startY   开始Y位置
     * @param dx       目标X位置
     * @param dy       目标Y位置
     * @param duration 完成滚动的时间
     */
    @Override
    public void startScroll(int startX, int startY, int dx, int dy, int duration) {
        if (noDuration) {// 跨距离切换时，不需要有完成滚动时间延迟
            duration = 0;
        }
        super.startScroll(startX, startY, dx, dy, duration);
    }

    void setNoDuration(boolean noDuration) {
        this.noDuration = noDuration;
    }
}