package com.zizhiguanjia.model_core.view;

import static android.service.controls.ControlsProviderService.TAG;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;
import androidx.viewpager2.widget.ViewPager2;
import java.lang.reflect.Field;
public class ExamCoreViewPage extends  ViewPager{

    public ExamCoreViewPage(@NonNull Context context) {
        this(context,null);
    }

    public ExamCoreViewPage(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

//    public ExamCoreViewPage(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
////        super(context, attrs, defStyleAttr);
//        initView();
//    }
    XScroller loopScroller;
    private void initView(){
        loopScroller = new XScroller(getContext());
        try {
            Field field = ViewPager2.class.getDeclaredField("mScroller");
            field.setAccessible(true);
            field.set(this, loopScroller );// 利用反射设置mScroller域为自己定义的xScroller
        } catch (Exception e) {
            Log.e(TAG, "setViewPagerScrollSpeed error:" + e.toString());
        }
    }
    @Override
    public void setCurrentItem(int item, boolean smoothScroll) {
        int current = getCurrentItem();

        // 如果页面相隔大于1,就设置页面切换时完成滑动的时间为0
        if (Math.abs(current - item) > 1) {
            loopScroller.setNoDuration(true);// 滑动前设置动画时间为0
            super.setCurrentItem(item, smoothScroll);
            loopScroller.setNoDuration(false);// 滑动结束后设置动画时间恢复
        } else {
            loopScroller.setNoDuration(false);
            super.setCurrentItem(item, smoothScroll);
        }
    }
}
