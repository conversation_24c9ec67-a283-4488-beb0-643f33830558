package com.zizhiguanjia.model_core.view;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;

import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.model_core.databinding.CoreExamViewExammateriaisBinding;
import com.zizhiguanjia.model_core.model.OriginImageBean;
import com.zizhiguanjia.model_core.utils.ExamUtils;

import java.util.List;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

/**
 * 题干
 */
public class ExamMaterialsView extends LinearLayout {
    public CoreExamViewExammateriaisBinding exammateriaisBinding;
    private String context;
    private String flags;
    private List<OriginImageBean> originImageBeans;
    private boolean mDefaultTags = true;
    private ExamCoreStyleConfigBean styleConfigBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    public ExamMaterialsView(Context context) {
        this(context, null);
    }

    public ExamMaterialsView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ExamMaterialsView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public ExamMaterialsView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }

    public ExamMaterialsView setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.textSizeBean = textSizeBean;
        exammateriaisBinding.jxMain.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerPageCardBgColor())));
        exammateriaisBinding.exmaViewTitle.setTextColor(Color.parseColor(themeBean.getContentTextColor()));
        exammateriaisBinding.exmaViewTitle.setTextSize(textSizeBean.getSize());
        exammateriaisBinding.examViewMaterialsImage.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        post(this::build);
        return this;
    }

    private void initView() {
        exammateriaisBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.core_exam_view_exammateriais, this, true);
    }

    public ExamMaterialsView setContext(String txt, String flags) {
        this.context = txt;
        this.flags = flags;
        return this;
    }

    public ExamMaterialsView setmDefaultTags(boolean mDefaultTags) {
        this.mDefaultTags = mDefaultTags;
        return this;
    }

    public ExamMaterialsView setImage(List<OriginImageBean> objects) {
        this.originImageBeans = objects;
        return this;
    }

    public void build() {
        if (StringUtils.isEmpty(context) && (originImageBeans == null || originImageBeans.size() == 0)) {
            exammateriaisBinding.jxMain.setVisibility(GONE);
        } else {
            exammateriaisBinding.jxMain.setVisibility(VISIBLE);
            if (StringUtils.isEmpty(context)) {
                exammateriaisBinding.exmaViewTitle.setVisibility(GONE);
            } else {
                exammateriaisBinding.exmaViewTitle.setVisibility(VISIBLE);
                if(StringUtils.isEmpty(flags)){
                    exammateriaisBinding.exmaViewTitle.setText(context);
                }else {
                    exammateriaisBinding.exmaViewTitle.setText(StringUtils.isEmpty(context) ? "暂无解析" : ExamUtils.getInstance().getExamSpanny(context, flags, mDefaultTags,textSizeBean != null ? textSizeBean.getSize() : null));
                }

            }
            if (originImageBeans == null || originImageBeans.size() == 0) {
                exammateriaisBinding.examViewMaterialsImage.setVisibility(GONE);
            } else {
                exammateriaisBinding.examViewMaterialsImage.setVisibility(VISIBLE);
                exammateriaisBinding.examViewMaterialsImage.setImageUrls(originImageBeans).setImageFlags(false).build();
            }
        }

    }
}
