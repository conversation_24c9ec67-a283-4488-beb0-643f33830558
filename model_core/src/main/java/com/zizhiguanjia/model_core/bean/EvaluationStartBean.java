package com.zizhiguanjia.model_core.bean;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 摸底测评开始页数据实体类
 * 匹配接口返回的JSON格式
 */
public class EvaluationStartBean {
    
    @SerializedName("Title")
    private String title;              // 标题
    
    @SerializedName("SubTitle")
    private String subTitle;           // 副标题
    
    @SerializedName("Description")
    private String description;        // 描述
    
    @SerializedName("QuestionCount")
    private int questionCount;         // 题目总数
    
    @SerializedName("EstimateTime")
    private int estimateTime;          // 预估时间（分钟）
    
    @SerializedName("IsVip")
    private boolean isVip;             // 是否VIP
    
    @SerializedName("IsFinished")
    private boolean isFinished;        // 是否已完成
    
    @SerializedName("LastScore")
    private double lastScore;          // 上次得分
    
    @SerializedName("PaperId")
    private String paperId;            // 试卷ID
    
    @SerializedName("ChapterCoverages")
    private List<ChapterCoverage> chapterCoverages;  // 章节覆盖率列表

    /**
     * 章节覆盖率
     */
    public static class ChapterCoverage {
        @SerializedName("ChapterName")
        private String chapterName;    // 章节名称
        
        @SerializedName("Coverage")
        private double coverage;       // 覆盖率
        
        @SerializedName("QuestionCount")
        private int questionCount;     // 题目数量

        public String getChapterName() {
            return chapterName;
        }

        public void setChapterName(String chapterName) {
            this.chapterName = chapterName;
        }

        public double getCoverage() {
            return coverage;
        }

        public void setCoverage(double coverage) {
            this.coverage = coverage;
        }

        public int getQuestionCount() {
            return questionCount;
        }

        public void setQuestionCount(int questionCount) {
            this.questionCount = questionCount;
        }
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(int questionCount) {
        this.questionCount = questionCount;
    }

    public int getEstimateTime() {
        return estimateTime;
    }

    public void setEstimateTime(int estimateTime) {
        this.estimateTime = estimateTime;
    }

    public boolean isVip() {
        return isVip;
    }

    public void setVip(boolean vip) {
        isVip = vip;
    }

    public boolean isFinished() {
        return isFinished;
    }

    public void setFinished(boolean finished) {
        isFinished = finished;
    }

    public double getLastScore() {
        return lastScore;
    }

    public void setLastScore(double lastScore) {
        this.lastScore = lastScore;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public List<ChapterCoverage> getChapterCoverages() {
        return chapterCoverages;
    }

    public void setChapterCoverages(List<ChapterCoverage> chapterCoverages) {
        this.chapterCoverages = chapterCoverages;
    }
} 