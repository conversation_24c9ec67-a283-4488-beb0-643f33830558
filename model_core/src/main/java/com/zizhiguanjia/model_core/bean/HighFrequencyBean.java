package com.zizhiguanjia.model_core.bean;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 高频考题数据实体类
 * 匹配接口返回的JSON格式
 */
public class HighFrequencyBean {
    
    @SerializedName("MajorName")
    private String majorName;       // 主科目名称
    
    @SerializedName("SubMajorName")
    private String subMajorName;    // 子科目名称
    
    @SerializedName("AreaName")
    private String areaName;        // 地区名称
    
    @SerializedName("IsVip")
    private boolean isVip;          // 是否是VIP用户
    
    @SerializedName("AllQuestionCount")
    private double allQuestionCount;   // 总题目数量
    
    @SerializedName("HFQuestionCount")
    private double hfQuestionCount;    // 高频考题数量
    
    @SerializedName("DoQuestionCount")
    private double doQuestionCount;    // 已做题目数量
    
    @SerializedName("DoQuestionProgress")
    private String doQuestionProgress; // 学习进度(百分比)
    
    @SerializedName("KnowledgePoints")
    private List<KnowledgePoint> knowledgePoints;  // 知识点列表

    /**
     * 知识点分类
     */
    public static class KnowledgePoint {
        @SerializedName("Title")
        private String title;     // 知识点标题
        
        @SerializedName("LV")
        private double level;     // 知识点层级
        
        @SerializedName("Childs")
        private List<KnowledgePoint> children;  // 子知识点

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public double getLevel() {
            return level;
        }

        public void setLevel(double level) {
            this.level = level;
        }

        public List<KnowledgePoint> getChildren() {
            return children;
        }

        public void setChildren(List<KnowledgePoint> children) {
            this.children = children;
        }
    }

    public String getMajorName() {
        return majorName;
    }

    public void setMajorName(String majorName) {
        this.majorName = majorName;
    }

    public String getSubMajorName() {
        return subMajorName;
    }

    public void setSubMajorName(String subMajorName) {
        this.subMajorName = subMajorName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public boolean isVip() {
        return isVip;
    }

    public void setVip(boolean vip) {
        isVip = vip;
    }

    public double getAllQuestionCount() {
        return allQuestionCount;
    }

    public void setAllQuestionCount(double allQuestionCount) {
        this.allQuestionCount = allQuestionCount;
    }

    public double getHfQuestionCount() {
        return hfQuestionCount;
    }

    public void setHfQuestionCount(double hfQuestionCount) {
        this.hfQuestionCount = hfQuestionCount;
    }

    public double getDoQuestionCount() {
        return doQuestionCount;
    }

    public void setDoQuestionCount(double doQuestionCount) {
        this.doQuestionCount = doQuestionCount;
    }

    public String getDoQuestionProgress() {
        return doQuestionProgress;
    }

    public void setDoQuestionProgress(String doQuestionProgress) {
        this.doQuestionProgress = doQuestionProgress;
    }

    public List<KnowledgePoint> getKnowledgePoints() {
        return knowledgePoints;
    }

    public void setKnowledgePoints(List<KnowledgePoint> knowledgePoints) {
        this.knowledgePoints = knowledgePoints;
    }
} 