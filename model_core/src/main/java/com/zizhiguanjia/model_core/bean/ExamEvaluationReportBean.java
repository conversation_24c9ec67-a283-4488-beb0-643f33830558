package com.zizhiguanjia.model_core.bean;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 智能练习结果数据实体类
 * 匹配接口返回的JSON格式
 */
public class ExamEvaluationReportBean {
    
    @SerializedName("Scores")
    private double scores;             // 分数
    
    @SerializedName("Total")
    private double total;              // 总题数
    
    @SerializedName("RightCount")
    private double rightCount;         // 正确题数
    
    @SerializedName("ErrorCount")
    private double errorCount;         // 错误题数
    
    @SerializedName("PassRate")
    private double passRate;           // 通过率
    
    @SerializedName("ReportText")
    private String reportText;         // 评语内容
    
    @SerializedName("IsVip")
    private boolean isVip;             // 是否VIP用户
    
    @SerializedName("CanContinue")
    private boolean canContinue;       // 是否可以继续
    
    @SerializedName("PaperValue")
    private String paperValue;         // 试卷ID值
    
    @SerializedName("GoodsInfo")
    private GoodsInfo goodsInfo;       // 商品信息
    
    @SerializedName("Question")
    private List<QuestionCategory> questions;    // 题目分类列表
    
    @SerializedName("WeakKnowledgepoints")
    private List<String> weakKnowledgePoints;    // 薄弱知识点
    
    @SerializedName("MasterKnowledgepoints")
    private List<String> masterKnowledgePoints;  // 掌握知识点

    /**
     * 商品信息类
     */
    public static class GoodsInfo {
        @SerializedName("Goods")
        private Goods goods;
        
        @SerializedName("Details")
        private List<GoodsDetail> details;

        public Goods getGoods() {
            return goods;
        }

        public void setGoods(Goods goods) {
            this.goods = goods;
        }

        public List<GoodsDetail> getDetails() {
            return details;
        }

        public void setDetails(List<GoodsDetail> details) {
            this.details = details;
        }
    }

    /**
     * 商品基本信息类
     */
    public static class Goods {
        @SerializedName("MajorName")
        private String majorName;
        
        @SerializedName("Details")
        private String details;
        
        @SerializedName("Duration")
        private String duration;
        
        @SerializedName("TimeOutDate")
        private String timeOutDate;
        
        @SerializedName("ServiceContent")
        private String serviceContent;
        
        @SerializedName("Price")
        private double price;
        
        @SerializedName("GoodsId")
        private double goodsId;
        
        @SerializedName("GoodsType")
        private double goodsType;
        
        @SerializedName("OrderNum")
        private String orderNum;
        
        @SerializedName("OrderDate")
        private String orderDate;
        
        @SerializedName("OrderCount")
        private double orderCount;
        
        @SerializedName("PostPrice")
        private String postPrice;
        
        @SerializedName("OrderTitle")
        private String orderTitle;
        
        @SerializedName("OrderNote")
        private String orderNote;

        // Getters and setters
        public String getMajorName() {
            return majorName;
        }

        public void setMajorName(String majorName) {
            this.majorName = majorName;
        }

        public String getDetails() {
            return details;
        }

        public void setDetails(String details) {
            this.details = details;
        }

        public String getDuration() {
            return duration;
        }

        public void setDuration(String duration) {
            this.duration = duration;
        }

        public String getTimeOutDate() {
            return timeOutDate;
        }

        public void setTimeOutDate(String timeOutDate) {
            this.timeOutDate = timeOutDate;
        }

        public String getServiceContent() {
            return serviceContent;
        }

        public void setServiceContent(String serviceContent) {
            this.serviceContent = serviceContent;
        }

        public double getPrice() {
            return price;
        }

        public void setPrice(double price) {
            this.price = price;
        }

        public double getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(double goodsId) {
            this.goodsId = goodsId;
        }

        public double getGoodsType() {
            return goodsType;
        }

        public void setGoodsType(double goodsType) {
            this.goodsType = goodsType;
        }

        public String getOrderNum() {
            return orderNum;
        }

        public void setOrderNum(String orderNum) {
            this.orderNum = orderNum;
        }

        public String getOrderDate() {
            return orderDate;
        }

        public void setOrderDate(String orderDate) {
            this.orderDate = orderDate;
        }

        public double getOrderCount() {
            return orderCount;
        }

        public void setOrderCount(double orderCount) {
            this.orderCount = orderCount;
        }

        public String getPostPrice() {
            return postPrice;
        }

        public void setPostPrice(String postPrice) {
            this.postPrice = postPrice;
        }

        public String getOrderTitle() {
            return orderTitle;
        }

        public void setOrderTitle(String orderTitle) {
            this.orderTitle = orderTitle;
        }

        public String getOrderNote() {
            return orderNote;
        }

        public void setOrderNote(String orderNote) {
            this.orderNote = orderNote;
        }
    }

    /**
     * 商品详细信息类
     */
    public static class GoodsDetail {
        @SerializedName("GoodsName")
        private String goodsName;
        
        @SerializedName("Desc")
        private String desc;
        
        @SerializedName("Info")
        private String info;
        
        @SerializedName("Price")
        private double price;
        
        @SerializedName("OldPrice")
        private String oldPrice;
        
        @SerializedName("Select")
        private boolean select;
        
        @SerializedName("GoodsId")
        private double goodsId;
        
        @SerializedName("ShowInfo")
        private double showInfo;
        
        @SerializedName("Duration")
        private String duration;
        
        @SerializedName("TimeOutDate")
        private String timeOutDate;

        // Getters and setters
        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getInfo() {
            return info;
        }

        public void setInfo(String info) {
            this.info = info;
        }

        public double getPrice() {
            return price;
        }

        public void setPrice(double price) {
            this.price = price;
        }

        public String getOldPrice() {
            return oldPrice;
        }

        public void setOldPrice(String oldPrice) {
            this.oldPrice = oldPrice;
        }

        public boolean isSelect() {
            return select;
        }

        public void setSelect(boolean select) {
            this.select = select;
        }

        public double getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(double goodsId) {
            this.goodsId = goodsId;
        }

        public double getShowInfo() {
            return showInfo;
        }

        public void setShowInfo(double showInfo) {
            this.showInfo = showInfo;
        }

        public String getDuration() {
            return duration;
        }

        public void setDuration(String duration) {
            this.duration = duration;
        }

        public String getTimeOutDate() {
            return timeOutDate;
        }

        public void setTimeOutDate(String timeOutDate) {
            this.timeOutDate = timeOutDate;
        }
    }

    /**
     * 题目分类类
     */
    public static class QuestionCategory {
        @SerializedName("Items")
        private List<QuestionItem> items;
        
        @SerializedName("QuestionCount")
        private double questionCount;
        
        @SerializedName("QuestionName")
        private String questionName;
        
        @SerializedName("Sort")
        private double sort;

        public List<QuestionItem> getItems() {
            return items;
        }

        public void setItems(List<QuestionItem> items) {
            this.items = items;
        }

        public double getQuestionCount() {
            return questionCount;
        }

        public void setQuestionCount(double questionCount) {
            this.questionCount = questionCount;
        }

        public String getQuestionName() {
            return questionName;
        }

        public void setQuestionName(String questionName) {
            this.questionName = questionName;
        }

        public double getSort() {
            return sort;
        }

        public void setSort(double sort) {
            this.sort = sort;
        }
    }

    /**
     * 题目项类
     */
    public static class QuestionItem {
        @SerializedName("QuestionNum")
        private double questionNum;
        
        @SerializedName("QuestionId")
        private double questionId;
        
        @SerializedName("IsDone")
        private boolean isDone;
        
        @SerializedName("IsRight")
        private boolean isRight;

        public double getQuestionNum() {
            return questionNum;
        }

        public void setQuestionNum(double questionNum) {
            this.questionNum = questionNum;
        }

        public double getQuestionId() {
            return questionId;
        }

        public void setQuestionId(double questionId) {
            this.questionId = questionId;
        }

        public boolean isDone() {
            return isDone;
        }

        public void setDone(boolean done) {
            isDone = done;
        }

        public boolean isRight() {
            return isRight;
        }

        public void setRight(boolean right) {
            isRight = right;
        }
    }

    // 主类的getter和setter方法
    public double getScores() {
        return scores;
    }

    public void setScores(double scores) {
        this.scores = scores;
    }

    public double getTotal() {
        return total;
    }

    public void setTotal(double total) {
        this.total = total;
    }

    public double getRightCount() {
        return rightCount;
    }

    public void setRightCount(double rightCount) {
        this.rightCount = rightCount;
    }

    public double getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(double errorCount) {
        this.errorCount = errorCount;
    }

    public double getPassRate() {
        return passRate;
    }

    public void setPassRate(double passRate) {
        this.passRate = passRate;
    }

    public String getReportText() {
        return reportText;
    }

    public void setReportText(String reportText) {
        this.reportText = reportText;
    }

    public boolean isVip() {
        return isVip;
    }

    public void setVip(boolean vip) {
        isVip = vip;
    }

    public boolean isCanContinue() {
        return canContinue;
    }

    public void setCanContinue(boolean canContinue) {
        this.canContinue = canContinue;
    }

    public String getPaperValue() {
        return paperValue;
    }

    public void setPaperValue(String paperValue) {
        this.paperValue = paperValue;
    }

    public GoodsInfo getGoodsInfo() {
        return goodsInfo;
    }

    public void setGoodsInfo(GoodsInfo goodsInfo) {
        this.goodsInfo = goodsInfo;
    }

    public List<QuestionCategory> getQuestions() {
        return questions;
    }

    public void setQuestions(List<QuestionCategory> questions) {
        this.questions = questions;
    }

    public List<String> getWeakKnowledgePoints() {
        return weakKnowledgePoints;
    }

    public void setWeakKnowledgePoints(List<String> weakKnowledgePoints) {
        this.weakKnowledgePoints = weakKnowledgePoints;
    }

    public List<String> getMasterKnowledgePoints() {
        return masterKnowledgePoints;
    }

    public void setMasterKnowledgePoints(List<String> masterKnowledgePoints) {
        this.masterKnowledgePoints = masterKnowledgePoints;
    }
} 