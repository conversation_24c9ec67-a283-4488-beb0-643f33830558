package com.zizhiguanjia.model_core.bean;

/**
 * 功能作用：课程详情习题答完的结果页面
 * 初始注释时间： 2023/11/26 20:42
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class ExamCoreCourseDetailChildFinishBean {
    /**
     * 提示
     */
    private String Tips;
    /**
     * 是否有下一节
     */
    private Integer HasNext;

    public String getTips() {
        return Tips;
    }

    public void setTips(String tips) {
        Tips = tips;
    }

    public Integer getHasNext() {
        return HasNext;
    }

    public void setHasNext(Integer hasNext) {
        HasNext = hasNext;
    }
}
