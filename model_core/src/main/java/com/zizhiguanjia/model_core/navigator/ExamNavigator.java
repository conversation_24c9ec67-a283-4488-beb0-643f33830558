package com.zizhiguanjia.model_core.navigator;

import android.app.Activity;

import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.model.OriginImageBean;

import java.util.List;

public interface ExamNavigator {
    void initView();
    void initModel();
    void initListener();
    void reshData(AnswerGroupBean answerGroupBean, Activity activity, int paperType);
    void showAlXzView();
    void showContextTitlteView(String context,int questionType,List<OriginImageBean> images);
    void showOptionView(List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> examOptionBeans, int questionType, List<String> userAnswer, List<String> answer, int questionState);
    void showResultView(List<String> mUserAnswers,List<String> mRightAnswers,String txt,List<OriginImageBean> objects,boolean jtjq);
    void showCaseMaterialsView();
    void showCaseQuestionOptionsView();
}
