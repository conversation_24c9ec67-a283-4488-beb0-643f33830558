package com.zizhiguanjia.model_core.navigator;


import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;

import java.util.List;

public interface ExamQuestionNavigator {
    void showContextTitlteView(String context,int questionType,String images);
    void showOptionView(List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> examOptionBeans, int questionType, List<String> userAnswer, List<String> answer, int questionState);
    void showResultView(List<String> mUserAnswers,List<String> mRightAnswers,String txt,List<Object> objects,boolean jtjq);
    void showCaseQuestionOptionsView();
    void showCaseMaterialsView();
    void showCaseQestiontView();
    void showCommonQestiontView();
    void showAlXzView();
}
