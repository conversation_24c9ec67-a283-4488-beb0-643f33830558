package com.zizhiguanjia.model_core.navigator;


import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.model.OriginImageBean;

import java.util.List;

public interface CustomChoiceNavigator {
    void showResultView(List<String> mUserAnswers, List<String> mRightAnswers, String txt, List<OriginImageBean> objects, boolean jtjq);
    void showContextTitlteView(String context, int questionType,List<OriginImageBean> iamges);
    void showOptionView(List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> examOptionBeans, int questionType, List<String> userAnswer, List<String> answer, int questionState);
    void getQuestionListData(List<String> strings);
    void showOptionTagsList(String context,int questionType,List<OriginImageBean> iamges);
}
