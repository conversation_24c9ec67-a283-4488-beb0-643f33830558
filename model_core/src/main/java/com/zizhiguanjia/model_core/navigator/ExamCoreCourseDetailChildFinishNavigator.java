package com.zizhiguanjia.model_core.navigator;

import com.zizhiguanjia.model_core.bean.ExamCoreCourseDetailChildFinishBean;

/**
 * 功能作用：课程详情习题答完的结果页面
 * 初始注释时间： 2023/11/26 20:42
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public interface ExamCoreCourseDetailChildFinishNavigator {
    /**
     * 设置显示数据
     *
     * @param data 要显示的数据
     */
    void setShowData(ExamCoreCourseDetailChildFinishBean data);

    /**
     * 是否加载中
     *
     * @param loading true显示加载中
     */
    void showLoading(boolean loading);
}
