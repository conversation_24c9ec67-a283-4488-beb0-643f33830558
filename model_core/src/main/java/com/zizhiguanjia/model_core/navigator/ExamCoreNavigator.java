package com.zizhiguanjia.model_core.navigator;

import com.zizhiguanjia.model_core.model.AnswerGroupBean;
import com.zizhiguanjia.model_core.model.ExamAnswerSheetBean;
import com.zizhiguanjia.model_core.model.ExamChaptersBean;

import java.util.List;

public interface ExamCoreNavigator {
    void initData();
    void initListenter();
    void showExamView(ExamAnswerSheetBean commonGroupExamBeans);
    void showEmptyView();
    void showContextView();
    void setGoToPage(int index,List<AnswerGroupBean> answerSheetOptionBeans);
    int getCurrentPostion();
    void updataExamPage(int postion, AnswerGroupBean answerGroupBean);
    void showSaveView();
    AnswerGroupBean getCurrentAdapterAns();
    void noExamPermiss(boolean need);
    void showErrorToast(String msg);
    void showFaceView();
    void showGuilderView(int type);
    void afterGetLocalData(String loacalData);
    void initFaceBackPopup();
    void initLoadingPopup();
    void showNoNetworkView();
    void showErrorView();
    void showLoadingView();
    void showLoadingDataView(boolean success,String msg);
    void postFaceBackState(boolean success);
    void dissFaceBackView();
    void initSheetPopup(List<ExamChaptersBean> examChaptersBeans);
    void openSheetPopup();
    void setRestExam();
    void saveQuestion(int index);
    void successSaveState(boolean success);
    void showDownTime(boolean visition, long times);
    void startHandPaper(boolean isTs);
    void startDownTime(long times);
    void postHandPaperSuccess();
    void toExamResult(String paperType);
}
