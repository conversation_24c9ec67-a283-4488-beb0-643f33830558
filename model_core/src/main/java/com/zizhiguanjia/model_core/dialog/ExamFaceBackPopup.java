package com.zizhiguanjia.model_core.dialog;

import android.content.Context;
import android.graphics.PorterDuff;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.adapter.ExamFaceBackAdapter;
import com.zizhiguanjia.model_core.listener.ExamFaceBackListener;
import com.zizhiguanjia.model_core.listener.FaceBackLister;
import com.zizhiguanjia.model_core.model.ExamFaceBackModel;
import com.zizhiguanjia.model_core.repository.ExamFaceBackRepository;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.widget.ContentLoadingProgressBar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class ExamFaceBackPopup extends BottomPopupView implements ExamFaceBackListener, View.OnClickListener, TextWatcher, FaceBackLister {
    private RecyclerView mRecyclerView;
    private ExamFaceBackRepository mExamFaceBackRepository;
    private TextView mPostData;
    private LinearLayout mMainLayout,mReshLayout;
    private ContentLoadingProgressBar mContentLoadingProgressBar;
    private LinearLayout examFacebackResponse;
    private TextView mExamFacebackResponseTitle,mExamFaceviewTitle;
    private ImageView mExamFacebackResponseImg,mExamFacebackClose;
    private ExamFaceBackListener call;
    private ExamFaceBackAdapter mAdapter;
    private EditText contextEd;
    private TextView numTv;
    private LinearLayout mainTitleLL;
    public ExamFaceBackPopup(@NonNull Context context,ExamFaceBackListener call) {
        super(context);
        this.call=call;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return R.layout.core_exam_bottom_faceview;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        LogUtils.e("第一次发打开");
        mExamFaceBackRepository=new ExamFaceBackRepository(this);
        contextEd=this.findViewById(R.id.contextEd);
        numTv=this.findViewById(R.id.numTv);
        mRecyclerView=this.findViewById(R.id.exam_faceback_ry);
        mPostData=this.findViewById(R.id.exma_post_data_tv);
        mMainLayout=this.findViewById(R.id.exam_faceback_content);
        mReshLayout=this.findViewById(R.id.exam_faceback_resh);
        mExamFacebackClose=this.findViewById(R.id.exam_faceback_close);
        mainTitleLL=this.findViewById(R.id.mainTitleLL);
        mExamFaceviewTitle=this.findViewById(R.id.exam_faceview_title);
        mContentLoadingProgressBar=this.findViewById(R.id.exam_faceback_resh_clpb);
        examFacebackResponse=this.findViewById(R.id.exam_faceback_response);
        mExamFacebackResponseTitle=this.findViewById(R.id.exam_faceback_response_title);
        mExamFacebackResponseImg=this.findViewById(R.id.exam_faceback_response_img);
        mContentLoadingProgressBar.getIndeterminateDrawable().setColorFilter(ContextCompat.getColor(getContext(),R.color.core_exam_faceback_loadcolor), PorterDuff.Mode.MULTIPLY);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext(),LinearLayoutManager.HORIZONTAL,false));
        mAdapter=new ExamFaceBackAdapter(this);
        mRecyclerView.setAdapter(mAdapter);
        mExamFaceBackRepository.init();
        mPostData.setOnClickListener(this);
        mExamFacebackClose.setOnClickListener(this);
        contextEd.addTextChangedListener(this);
    }

    @Override
    public BasePopupView show() {
        if(mAdapter!=null){
            reshView(-1);
            mAdapter.cleanDataList();
            mAdapter.notifyDataSetChanged();
        }
        return super.show();
    }

    @Override
    public void userSelectFaceBack(String opion,String ss) {

    }

    @Override
    public void initData(List<ExamFaceBackModel> examFaceBackModels) {
        mAdapter.setDataItems(examFaceBackModels);
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.exma_post_data_tv){
            if(call==null)return;
            List<Long> lists = new ArrayList<>(mAdapter.getSelectOption());
            if(lists==null||lists.size()==0){
                ToastUtils.normal("请选择反馈信息！", Gravity.CENTER);
                return;
            }
            List<String>newStrs=new ArrayList<>();
            for(long str:lists){
                newStrs.add(String.valueOf(str));
            }
            call.userSelectFaceBack(StringUtils.ListToStr(newStrs),StringUtils.isEmpty(contextEd.getText().toString().trim())?"":contextEd.getText().toString().trim());
            reshView(1);
        }else if(v.getId()==R.id.exam_faceback_close){
            dismiss();
        }
    }
    public void reshView(int type){
        if(type==1){
            //正在请求
            mMainLayout.setVisibility(GONE);
            mReshLayout.setVisibility(VISIBLE);
            mContentLoadingProgressBar.setVisibility(VISIBLE);
            examFacebackResponse.setVisibility(GONE);
            mExamFaceviewTitle.setVisibility(GONE);
            mainTitleLL.setVisibility(GONE);
        }else if(type==-1){
            mMainLayout.setVisibility(VISIBLE);
            mReshLayout.setVisibility(GONE);
            mContentLoadingProgressBar.setVisibility(GONE);
            examFacebackResponse.setVisibility(VISIBLE);
            mExamFaceviewTitle.setVisibility(VISIBLE);
            mainTitleLL.setVisibility(VISIBLE);
        }else if(type==2){
            //请求出错
            dismiss();
//            mMainLayout.setVisibility(GONE);
//            mReshLayout.setVisibility(VISIBLE);
//            mExamFaceviewTitle.setVisibility(GONE);
//            mainTitleLL.setVisibility(GONE);
//            mContentLoadingProgressBar.setVisibility(GONE);
//            examFacebackResponse.setVisibility(VISIBLE);
//            mExamFacebackResponseTitle.setText("反馈失败！");
//            mExamFacebackResponseImg.setImageResource(R.drawable.core_exam_faceback_fail);
        }else {
            dismiss();
            //请求成功
//            mExamFaceviewTitle.setVisibility(GONE);
//            mainTitleLL.setVisibility(GONE);
//            mMainLayout.setVisibility(GONE);
//            mReshLayout.setVisibility(VISIBLE);
//            mContentLoadingProgressBar.setVisibility(GONE);
//            examFacebackResponse.setVisibility(VISIBLE);
//            mExamFacebackResponseImg.setImageResource(R.drawable.core_exam_faceback_success);
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void dismiss() {
        if(contextEd==null){

        }else {
            contextEd.setText("");
        }
        if(mAdapter==null){
        }else {
            mAdapter.cleanDataList();
        }
        super.dismiss();
    }

    @Override
    public void afterTextChanged(Editable s) {
        numTv.setText(s.length()+"");
    }

    @Override
    protected int getMaxWidth() {
        return DpUtils.getScreenWidth(getContext());
    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(),400);
    }

    @Override
    public void onUserOption(boolean isHave) {
        mPostData.setBackgroundResource(isHave?R.drawable.core_exam_button_bg:R.drawable.core_login_nor_bg);
    }

    @Override
    protected void doAfterShow() {
        super.doAfterShow();
        if(mAdapter==null){
        }else {
            mAdapter.cleanDataList();
            onUserOption(false);
        }
        LogUtils.e("打开了-*----");
    }
}
