package com.zizhiguanjia.model_core.dialog;

/**
 * 功能作用：弹窗修改回调
 * 初始注释时间： 2024/6/27 20:42
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public interface ExamChangeThemeDialogCallback {
    /**
     * 修改主题类型
     *
     * @param type
     */
    void changeThemeType(int type);

    /**
     * 修改字体大小
     */
    void changeTextSize(int type);

    /**
     * 切换上一题下一题显示状态
     */
    void changePreNextShowButtons(boolean isChecked);

    /**
     * 修改为背题模式
     */
    void changeMemoryModel(boolean isChecked);

    /**
     * 是否自动跳转下一题
     */
    void setAutoJumpNext(boolean isChecked);
}
