package com.zizhiguanjia.model_core.dialog;

import android.content.Context;
import android.view.Gravity;

import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreExamDialogChangeThemeBinding;

import androidx.annotation.NonNull;

/**
 * 功能作用：修改答题页面主题弹窗
 * 初始注释时间： 2024/6/29 13:22
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class ExamChangeThemeDialog extends BottomPopupView {

    private CoreExamDialogChangeThemeBinding binding;

    private ExamChangeThemeDialogCallback callback;
    private ExamCoreStyleConfigBean styleConfigBean;
    private String paperType;
    private String pageTitle;

    public void setCallback(ExamChangeThemeDialogCallback callback) {
        this.callback = callback;
    }

    public ExamChangeThemeDialog(@NonNull Context context, String paperType, ExamCoreStyleConfigBean styleConfigBean,String title) {
        super(context);
        this.pageTitle = title;
        this.setPagerType(paperType,title);
        this.setStyleConfigBean(styleConfigBean);
    }

    /**
     * 设置页面类型
     *
     * @param paperType paperType
     */
    public void setPagerType(String paperType,String title) {
        this.paperType = paperType;
        if(binding == null){
            return;
        }
        if (!ExamPaperTypeConfig.isExaminationAndAnalysis(Integer.parseInt(paperType),title)) {
            binding.lnAutoJumpNext.setVisibility(VISIBLE);
            binding.lnChangeMemoryModel.setVisibility(VISIBLE);
        } else {
            binding.lnAutoJumpNext.setVisibility(GONE);
            binding.lnChangeMemoryModel.setVisibility(GONE);
        }
    }


    public void setStyleConfigBean(ExamCoreStyleConfigBean styleConfigBean) {
        this.styleConfigBean = styleConfigBean;
        if(binding == null){
            return;
        }
        binding.swAutoJumpNext.setChecked(false);
        binding.swAutoJumpNext.setChecked(styleConfigBean.isAutoJumpNext());
        binding.swChangeMemoryModel.setChecked(false);
        binding.swChangeMemoryModel.setChecked(styleConfigBean.isShowMemoryModel());
        binding.swShowButtons.setChecked(false);
        binding.swShowButtons.setChecked(styleConfigBean.isShowPreNextButtons());
        changeTextSizeLevel(styleConfigBean == null ? null : styleConfigBean.getTextSizeType());
        changeThemeType(styleConfigBean == null ? null : styleConfigBean.getThemeType());
    }

    /**
     * 修改文本大小的显示
     */
    private void changeTextSizeLevel(Integer level) {
        binding.tvChangeTextSizeLevelSmall.setActivated(level != null && level == 1);
        binding.tvChangeTextSizeLevelDefault.setActivated(level == null || level == 2);
        binding.tvChangeTextSizeLevelBig.setActivated(level != null && level == 3);
        binding.tvChangeTextSizeLevelMoreBig.setActivated(level != null && level == 4);
    }

    /**
     * 修改文本大小的显示
     */
    private void changeThemeType(Integer type) {
        binding.imgShowModelDefaultIcon.setActivated(type == null || type == 1);
        binding.tvShowModelDefaultText.setActivated(type == null || type == 1);
        binding.imgShowModelNightIcon.setActivated(type != null && type == 3);
        binding.tvShowModelNightText.setActivated(type != null && type == 3);
        binding.imgShowModelSafeIcon.setActivated(type != null && type == 2);
        binding.tvShowModelSafeText.setActivated(type != null && type == 2);
    }

    @Override
    protected int initLayoutResId() {
        return R.layout.core_exam_dialog_change_theme;
    }

    @Override
    protected void initView() {
        super.initView();
        binding = CoreExamDialogChangeThemeBinding.bind(bottomPopupContainer.getChildAt(0));
        setPagerType(paperType,pageTitle);
        setStyleConfigBean(styleConfigBean);
        binding.lnShowModelDefault.setOnClickListener(v -> {
            changeThemeType(1);
            if (callback != null) {
                callback.changeThemeType(1);
            }
        });
        binding.lnShowModelSafe.setOnClickListener(v -> {
            changeThemeType(2);
            if (callback != null) {
                callback.changeThemeType(2);
            }
        });
        binding.lnShowModelNight.setOnClickListener(v -> {
            changeThemeType(3);
            if (callback != null) {
                callback.changeThemeType(3);
            }
        });
        binding.tvChangeTextSizeLevelSmall.setOnClickListener(v -> {
            changeTextSizeLevel(1);
            if (callback != null) {
                callback.changeTextSize(1);
            }
        });
        binding.tvChangeTextSizeLevelDefault.setOnClickListener(v -> {
            changeTextSizeLevel(2);
            if (callback != null) {
                callback.changeTextSize(2);
            }
        });
        binding.tvChangeTextSizeLevelBig.setOnClickListener(v -> {
            changeTextSizeLevel(3);
            if (callback != null) {
                callback.changeTextSize(3);
            }
        });
        binding.tvChangeTextSizeLevelMoreBig.setOnClickListener(v -> {
            changeTextSizeLevel(4);
            if (callback != null) {
                callback.changeTextSize(4);
            }
        });
        binding.swShowButtons.setOnCheckedChangeListener((switchView, isChecked) -> {
            if (callback != null) {
                callback.changePreNextShowButtons(isChecked);
            }
        });
        binding.swChangeMemoryModel.setOnCheckedChangeListener((switchView, isChecked) -> {
            if (!UserHelper.isBecomeVip() && isChecked) {
                switchView.setChecked(false);
                ToastUtils.normal("背题模式为付费功能，请先升级vip。", Gravity.CENTER);
                return;
            }
            if (callback != null) {
                callback.changeMemoryModel(isChecked);
            }
        });
        binding.swAutoJumpNext.setOnCheckedChangeListener((switchView, isChecked) -> {
            if (callback != null) {
                callback.setAutoJumpNext(isChecked);
            }
        });
    }

}
