<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingDefaultResource">

    <com.wb.lib_weiget.titlebar.TitleBar
        android:id="@+id/tbflutter"
        app:tb_leftType="imageButton"
        app:tb_centerText="本节习题"
        app:tb_centerTextColor="@color/white"
        app:tb_leftImageResource="@drawable/common_left_back_write_img"
        app:tb_fillStatusBar="true"
        app:tb_statusBarColor="#3163F6"
        app:tb_titleBarColor="#3163F6"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbflutter"
        android:background="@drawable/exam_core_course_detail_child_finish_bg"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="120dp"
        android:src="@drawable/icon_core_exam_course_detail_child_finish"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbflutter"
        app:layout_constraintWidth_percent="0.2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_finish_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_icon"
        android:textSize="22.5sp"
        android:textColor="#0E152B"
        android:paddingVertical="4dp"
        android:text="本节习题已做完"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_finish_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toBottomOf="@id/tv_finish_title"
        android:textSize="15sp"
        android:textColor="#333333"
        android:paddingVertical="2dp"
        tools:text="共5题，答对3题，正确率"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_finish_state_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_finish_state"
        android:textSize="15sp"
        android:textColor="#333333"
        android:paddingVertical="2dp"
        tools:text="建议后续加强本章的学习哦～"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_reset"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_percent="0.7"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_next"
        android:background="@drawable/hollow_max"
        android:backgroundTint="#3265F6"
        android:textColor="#3265F6"
        android:paddingVertical="12dp"
        android:layout_marginBottom="17dp"
        android:gravity="center"
        android:minHeight="0dp"
        android:textSize="16sp"
        android:text="重新练习"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_next"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_percent="0.7"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="150dp"
        android:background="@drawable/solid_max"
        android:backgroundTint="#3265F6"
        android:textColor="#fff"
        android:textSize="16sp"
        android:paddingVertical="12dp"
        android:gravity="center"
        android:minHeight="0dp"
        android:text="继续播放下一节"/>

</androidx.constraintlayout.widget.ConstraintLayout>