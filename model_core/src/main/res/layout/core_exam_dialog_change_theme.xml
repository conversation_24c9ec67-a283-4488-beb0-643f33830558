<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="40dp"
    android:background="@drawable/solid_radio_7_top"
    android:backgroundTint="@color/white">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_auto_jump_next"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="9dp"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="#333"
            android:textSize="15sp"
            android:text="答对自动跳到下一题" />

        <com.zizhiguanjia.lib_base.view.SwitchView
            android:id="@+id/sw_auto_jump_next"
            android:layout_width="40dp"
            android:layout_height="24dp"
            app:color_off="#E6E9EB"
            app:color_on="#3163F6"
            tools:ignore="InvalidId" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_change_memory_model"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="10dp"
        app:layout_constraintTop_toBottomOf="@id/ln_auto_jump_next">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="#333"
            android:textSize="15sp"
            android:text="切换为背题模式" />

        <com.zizhiguanjia.lib_base.view.SwitchView
            android:id="@+id/sw_change_memory_model"
            android:layout_width="40dp"
            android:layout_height="24dp"
            app:color_off="#E6E9EB"
            app:color_on="#3163F6"
            tools:ignore="InvalidId" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_show_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="10dp"
        app:layout_constraintTop_toBottomOf="@id/ln_change_memory_model">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="#333"
            android:textSize="15sp"
            android:text="显示上一题和下一题按钮" />

        <com.zizhiguanjia.lib_base.view.SwitchView
            android:id="@+id/sw_show_buttons"
            android:layout_width="40dp"
            android:layout_height="24dp"
            app:color_off="#E6E9EB"
            app:color_on="#3163F6"
            tools:ignore="InvalidId" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_change_text_size"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="10dp"
        app:layout_constraintTop_toBottomOf="@id/ln_show_buttons">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="#333"
            android:textSize="15sp"
            android:text="字体大小" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_change_text_size_level_small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="2dp"
            android:background="@drawable/hollow_max"
            android:backgroundTint="@drawable/core_exam_options_color_active"
            android:textColor="@drawable/core_exam_options_color_active"
            android:gravity="center"
            android:minWidth="60dp"
            android:paddingVertical="3dp"
            android:textSize="15sp"
            android:clickable="false"
            android:text="小" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_change_text_size_level_default"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="2dp"
            android:background="@drawable/hollow_max"
            android:backgroundTint="@drawable/core_exam_options_color_active"
            android:textColor="@drawable/core_exam_options_color_active"
            android:gravity="center"
            android:minWidth="60dp"
            android:paddingVertical="3dp"
            android:textSize="15sp"
            android:text="标准" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_change_text_size_level_big"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="2dp"
            android:background="@drawable/hollow_max"
            android:backgroundTint="@drawable/core_exam_options_color_active"
            android:textColor="@drawable/core_exam_options_color_active"
            android:gravity="center"
            android:minWidth="60dp"
            android:paddingVertical="3dp"
            android:textSize="15sp"
            android:text="大" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_change_text_size_level_more_big"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="2dp"
            android:background="@drawable/hollow_max"
            android:backgroundTint="@drawable/core_exam_options_color_active"
            android:textColor="@drawable/core_exam_options_color_active"
            android:gravity="center"
            android:minWidth="60dp"
            android:paddingVertical="3dp"
            android:textSize="15sp"
            android:text="超大" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_show_model_default"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="20dp"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/ln_show_model_safe"
        app:layout_constraintTop_toBottomOf="@id/ln_change_text_size">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_show_model_default_icon"
            android:layout_width="33dp"
            android:layout_height="33dp"
            android:background="@drawable/core_exam_change_theme_default" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_show_model_default_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@drawable/core_exam_options_model_color"
            android:textSize="15sp"
            android:paddingVertical="2dp"
            android:text="正常模式" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_show_model_safe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:layout_marginTop="20dp"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintLeft_toRightOf="@id/ln_show_model_default"
        app:layout_constraintRight_toLeftOf="@+id/ln_show_model_night"
        app:layout_constraintTop_toBottomOf="@id/ln_change_text_size">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_show_model_safe_icon"
            android:layout_width="33dp"
            android:layout_height="33dp"
            android:background="@drawable/core_exam_change_theme_safe" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_show_model_safe_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@drawable/core_exam_options_model_color"
            android:textSize="15sp"
            android:paddingVertical="2dp"
            android:text="护眼模式" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_show_model_night"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="10dp"
        android:layout_marginTop="20dp"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintLeft_toRightOf="@id/ln_show_model_safe"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ln_change_text_size">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_show_model_night_icon"
            android:layout_width="33dp"
            android:layout_height="33dp"
            android:background="@drawable/core_exam_change_theme_night" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_show_model_night_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@drawable/core_exam_options_model_color"
            android:textSize="15sp"
            android:paddingVertical="2dp"
            android:text="夜间模式" />

    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.constraintlayout.widget.ConstraintLayout>