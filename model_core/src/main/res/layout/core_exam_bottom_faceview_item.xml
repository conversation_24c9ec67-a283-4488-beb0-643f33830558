<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="data"
            type="com.zizhiguanjia.model_core.model.ExamFaceBackModel" />
    </data>
    <LinearLayout
        android:id="@+id/exam_faceback_main_rel"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginLeft="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:id="@+id/exam_faceback_bg"
            android:background="@drawable/core_exam_faceback_bg"
            android:layout_width="41dp"
            android:layout_height="41dp">
            <ImageView
                android:layout_centerInParent="true"
                android:id="@+id/iv_image"
                android:layout_width="24dp"
                android:layout_height="24dp"/>
        </RelativeLayout>
        <TextView
            android:gravity="center"
            android:text="@{data.iconName}"
            android:layout_marginTop="11dp"
            android:textColor="#666666"
            android:textSize="12sp"
            android:id="@+id/tv_text"
            android:layout_width="70dp"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</layout>