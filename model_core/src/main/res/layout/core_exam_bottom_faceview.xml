<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/core_exam_bottom_bg"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="23dp"
        android:layout_marginBottom="23dp">

        <ImageView
            android:layout_marginRight="12dp"
            android:layout_alignParentRight="true"
            android:id="@+id/exam_faceback_close"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/core_exam_close" />
        <LinearLayout
            android:id="@+id/mainTitleLL"
            android:layout_centerInParent="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/exam_faceview_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="您遇到了什么问题？"
                android:textColor="@color/black"
                android:textFontWeight="500"
                android:textSize="16sp" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="【可多选】"
                android:textColor="#999999"
                android:textSize="13sp" />
        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/exam_faceback_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <com.wb.lib_pop.widget.VerticalRecyclerView
            android:id="@+id/exam_faceback_ry"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/iv_triangle"
            android:background="@color/white" />
        <RelativeLayout
            android:layout_marginRight="12dp"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="22dp"
            android:layout_width="match_parent"
            android:background="@drawable/core_jiucuo_edit_bg"
            android:layout_height="139dp">

            <EditText
                android:id="@+id/contextEd"
                android:layout_width="match_parent"
                android:layout_height="109dp"
                android:background="@null"
                android:gravity="top|left"
                android:hint="您的反馈对我们至关重要，我们会持续努力！(选填)"
                android:maxLength="150"
                android:paddingLeft="20dp"
                android:paddingTop="13.5dp"
                android:paddingRight="20dp"
                android:paddingBottom="13.5dp"
                android:textColor="#333333"
                android:textColorHint="#CDCDCF"
                android:textSize="14sp" />
            <LinearLayout
                android:layout_marginBottom="10dp"
                android:layout_marginRight="15dp"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/numTv"
                    android:textColor="#476BFF"
                    android:textSize="14sp"
                    android:text="0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:text="/150"
                    android:textColor="#CDCDCF"
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </RelativeLayout>

        <TextView
            android:id="@+id/exma_post_data_tv"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginLeft="45dp"
            android:layout_marginTop="26.5dp"
            android:layout_marginRight="45dp"
            android:layout_marginBottom="29.5dp"
            android:background="@drawable/core_login_nor_bg"
            android:gravity="center"
            android:text="提交"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>

    <LinearLayout
        android:visibility="gone"
        android:paddingTop="38dp"
        android:id="@+id/exam_faceback_resh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        >

        <androidx.core.widget.ContentLoadingProgressBar
            android:id="@+id/exam_faceback_resh_clpb"
            style="?android:attr/progressBarStyleLarge"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_gravity="center"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/exam_faceback_response"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/exam_faceback_response_img"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:src="@drawable/core_exam_faceback_success" />

            <TextView
                android:id="@+id/exam_faceback_response_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:text="感谢您的反馈"
                android:textColor="#000000"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>