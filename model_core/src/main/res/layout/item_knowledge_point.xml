<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <View
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:background="@drawable/dot_red" />

    <TextView
        android:id="@+id/tv_knowledge_point"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:textColor="#333333"
        android:textSize="14sp" />

</LinearLayout> 