<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="imageSize"
            type="long" />
        <import type="android.view.View"/>
    </data>
    <LinearLayout
        android:gravity="center_horizontal"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">
        <LinearLayout
            android:gravity="center_horizontal"
            android:visibility="@{imageSize==1?View.VISIBLE:View.GONE}"
            android:layout_marginTop="8.5dp"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/single_img"
                android:scaleType="fitXY"
                android:layout_width="365dp"
                android:layout_height="123.5dp"/>

            <TextView
                android:id="@+id/imageTags"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6.5dp"
                android:text="图 (1)"
                android:textColor="#666666"
                android:textSize="12sp" />
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:layout_marginTop="8.5dp"
            android:visibility="@{imageSize>1?View.VISIBLE:View.GONE}"
            android:id="@+id/multi_recy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</layout>