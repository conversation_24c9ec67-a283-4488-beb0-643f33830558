<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <import type="android.view.View" />
        <variable
            name="mode"
            type="com.zizhiguanjia.model_core.viewmodel.ExamCoreViewModel" />
    </data>
    <LinearLayout
        android:id="@+id/relMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@{mode.bgColor}"
        android:orientation="vertical">
        <com.wb.lib_weiget.titlebar.TitleBar xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/exam_content_tb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tb_centerSubTextSize="18.5sp"
            app:tb_centerTextColor="@color/core_top_title_color"
            app:tb_centerType="textView"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            app:tb_leftType="imageButton"
            app:tb_statusBarColor="@color/core_top_bg_color"
            app:tb_titleBarColor="@color/translucent"
            app:tb_rightText="设置"
            app:tb_rightTextColor="@color/white"
            app:tb_rightType="textView"/>
        <com.wb.lib_weiget.views.MultipleStatusView xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/exam_multipleStatusView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/exam_content_tb"
            app:emptyView="@layout/layout_common_empty"
            app:errorView="@layout/layout_common_error"
            app:loadingView="@layout/public_loding_view"
            app:noNetworkView="@layout/layout_common_network">

            <include
                android:id="@+id/icdExamMust"
                layout="@layout/core_context_layout"
                app:model="@{mode}" />
        </com.wb.lib_weiget.views.MultipleStatusView>
    </LinearLayout>
</layout>