<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.zizhiguanjia.model_core.viewmodel.HighFrequencyViewModel" />

        <import type="android.view.View" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 主内容区域 -->
        <LinearLayout
            android:id="@+id/layout_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- 标题栏 -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#FFFFFF"
                android:paddingTop="30dp">

            <RelativeLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageButton
                    android:id="@+id/btn_back"
                    android:layout_width="48dp"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="返回"
                    android:padding="12dp"
                    android:src="@drawable/back" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                   android:layout_centerInParent="true"
                    android:text="高频考题"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </RelativeLayout>
        </RelativeLayout>

        <!-- 内容区域 -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 三栏信息区域 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#FFFFFF"
                    android:orientation="vertical"
                    android:paddingTop="16dp"
                   >

                    <!-- 学习科目 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:paddingBottom="12dp">

                        <TextView
                            android:layout_width="120dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="学习科目"
                            android:textColor="#999999"
                            android:textSize="16sp" />

                        <TextView
                            android:id="@+id/tv_subject"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginLeft="16dp"
                            android:text="@{viewModel.subject}"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            tools:text="建筑安全三类人员 北京 A本" />
                    </LinearLayout>

                    <!-- 题库总量 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingLeft="16dp"
                        android:paddingTop="12dp"
                        android:paddingRight="16dp"
                        android:paddingBottom="12dp">

                        <TextView
                            android:layout_width="120dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="题库总量"
                            android:textColor="#999999"
                            android:textSize="16sp" />

                        <TextView
                            android:id="@+id/tv_total_questions"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="16dp"
                            android:text="@{viewModel.totalQuestions}"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            tools:text="3200道" />
                    </LinearLayout>

                    <!-- 高频考题 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingLeft="16dp"
                        android:paddingTop="12dp"
                        android:paddingRight="16dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="高频考题"
                                android:textColor="#999999"
                                android:textSize="16sp" />

                            <TextView
                                android:id="@+id/tv_vip_tag"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="15dp"
                                android:background="@drawable/bg_orange_radius"
                                android:padding="4dp"
                                android:text="VIP专享"
                                android:textColor="#FFFFFF"
                                android:textSize="12sp"
                                android:visibility="@{viewModel.isVip ? View.GONE : View.VISIBLE}" />

                        </LinearLayout>
                        <!-- VIP状态下显示的布局 -->
                        <LinearLayout
                            android:id="@+id/layout_vip_info"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="16dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:visibility="@{viewModel.isVip ? View.VISIBLE : View.GONE}">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.highFrequencyCount}"
                                android:textColor="#333333"
                                android:textSize="16sp"
                                tools:text="300道" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:text="已学习"
                                android:textColor="#333333"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_learned_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.learnedCount}"
                                android:textColor="#4285F4"
                                android:textSize="14sp"
                                tools:text="0道" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:text="进度"
                                android:textColor="#333333"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_progress"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.progressPercent}"
                                android:textColor="#4285F4"
                                android:textSize="14sp"
                                tools:text="0%" />
                        </LinearLayout>

                        <!-- 非VIP状态下显示的布局 -->
                        <LinearLayout
                            android:id="@+id/layout_non_vip_info"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="16dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:visibility="@{viewModel.isVip ? View.GONE : View.VISIBLE}">


                            <TextView
                                android:id="@+id/tv_high_frequency_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="8dp"
                                android:text="@{viewModel.highFrequencyCount}"
                                android:textColor="#333333"
                                android:textSize="16sp"
                                tools:text="300道" />
                        </LinearLayout>
                    </LinearLayout>
                    <!-- 开始学习按钮 -->
                    <Button
                        android:id="@+id/btn_start_learning"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="16dp"
                        android:background="@drawable/bg_button_primary"
                        android:text="立即学习"
                        android:textColor="#FFFFFF"
                        android:textSize="16sp" />

                </LinearLayout>

                <!-- 高频考点列表 -->
                <LinearLayout
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#FFFFFF"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <View
                            android:layout_width="0dp"
                            android:layout_height="0.5dp"
                            android:layout_weight="1"
                            android:background="@color/gray" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="10dp"
                            android:layout_marginRight="10dp"
                            android:text="以下是高频考点"
                            android:textColor="#999999"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="0.5dp"
                            android:layout_weight="1"
                            android:background="@color/gray" />
                    </LinearLayout>

                    <!-- 知识点类别列表，这些会动态添加 -->
                    <LinearLayout
                        android:id="@+id/ll_knowledge_categories"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="vertical">

                        <!-- 动态添加的内容 -->
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </ScrollView>
        </LinearLayout>

        <!-- Loading 视图 -->
        <LinearLayout
            android:id="@+id/layout_loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#FFFFFF"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible">

            <!-- 标题栏 (在loading时也显示) -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#FFFFFF"
                android:paddingTop="30dp">

                <RelativeLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageButton
                        android:id="@+id/btn_back_loading"
                        android:layout_width="48dp"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="返回"
                        android:padding="12dp"
                        android:src="@drawable/back" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="高频考题"
                        android:textColor="#333333"
                        android:textSize="18sp"
                        android:textStyle="bold" />
                </RelativeLayout>
            </RelativeLayout>

            <!-- Loading 内容 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ProgressBar
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginBottom="16dp"
                    android:indeterminateTint="#333333" />

            </LinearLayout>
        </LinearLayout>

    </FrameLayout>
</layout>