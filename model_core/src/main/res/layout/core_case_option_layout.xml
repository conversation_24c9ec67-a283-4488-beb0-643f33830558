<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="model"
            type="com.zizhiguanjia.model_core.viewmodel.ExamCaseOptionViewModel" />

        <import type="android.view.View" />
    </data>
    <com.zizhiguanjia.model_core.view.NonFocusingScrollView
        android:id="@+id/sllvCaseMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:id="@+id/llMainCent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingTop="14dp"
            android:orientation="vertical">
            <com.zizhiguanjia.model_core.view.ExamMaterialsView
                android:id="@+id/exam_casequestion_top_emlv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="13dp"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp" />
            <View
                android:id="@+id/viewLine"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginBottom="11dp"
                android:background="#F4F7F9" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcyCaseQuestion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.zizhiguanjia.model_core.view.ExamMaterialsView
                android:id="@+id/emvCaseWt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="12dp" />

            <RelativeLayout
                android:id="@+id/rlCaseMsg"
                android:layout_width="match_parent"
                android:layout_height="113dp"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="13.5dp"
                android:layout_marginRight="12dp"
                android:background="@drawable/solid_radio_8">
<!--                android:background="@{model.lookEditObs?@drawable/core_case_question_edit_bg:@drawable/core_case_question_lookasy_yes_bg}"-->
<!--               >-->

                <EditText
                    android:id="@+id/etCaseMsg"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/llCountTv"
                    android:afterTextChanged="@{model::onMsgEditTextChanged}"
                    android:background="@null"
                    android:enabled="@{model.lookEditObs}"
                    android:gravity="top"
                    android:hint="请输入答案"
                    android:maxLength="200"
                    android:text="@{model.msgObs}"
                    android:textColor="#000000"
                    android:textSize="14sp"
                    android:textColorHint="#b0b0b0"
                    android:paddingLeft="19.5dp"
                    android:paddingTop="13.5dp"
                    android:paddingRight="19.5dp"/>

                <LinearLayout
                    android:id="@+id/llCountTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginRight="10dp"
                    android:layout_marginBottom="6dp">

                    <TextView
                        android:id="@+id/tvCaseCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{String.valueOf(model.msgObs.length())}"
                        android:textColor="#476BFF"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvCaseCountSum"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginRight="10dp"
                        android:layout_marginBottom="6dp"
                        android:text="/200"
                        android:textColor="#CDCDCF"
                        android:textSize="14sp" />
                </LinearLayout>

                <View
                    android:id="@+id/storeCaseMsg"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/hollow_radio_8"
                    />

            </RelativeLayout>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="16.5dp"
                android:layout_marginBottom="24.5dp">
                <TextView
                    android:id="@+id/tvLookAsy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:textColor="@drawable/core_exam_option_duo_confim_text_options"
                    android:background="@drawable/core_exam_option_duo_confim_bg_options"
                    android:onClick="@{model::onclick}"
                    android:paddingLeft="16dp"
                    android:paddingTop="5dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="5dp"
                    android:text="@{model.currentLookDes}"
                    android:textSize="14sp"
                    android:enabled="@{!model.lookAsyObs}" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility='@{model.currentLookDes.equals("确定") ? View.VISIBLE:View.GONE}'
                    android:textSize="14sp"
                    android:text="点击“确定”才能提交答案"
                    android:paddingHorizontal="5dp"
                    android:textColor="@{model.themeBean.confirmHintTextColor}"/>
            </androidx.appcompat.widget.LinearLayoutCompat>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="@{model.lookAsyObs?View.VISIBLE:View.GONE}">

                <LinearLayout
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="3dp"
                        android:layout_height="12dp"
                        android:background="@drawable/core_line_bg" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/tvAnswer"
                            android:layout_centerVertical="true"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:text="答案"
                            android:textColor="#333333"
                            android:textSize="14sp" />

                        <LinearLayout
                            android:id="@+id/llFaceBackMain"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:background="@drawable/core_exam_face_bg"
                            android:gravity="center"
                            android:visibility="gone"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingLeft="15.5dp"
                                android:paddingRight="15.5dp"
                                android:paddingBottom="4dp"
                                android:paddingTop="4dp"
                                android:text="纠错"
                                android:textColor="#999999"
                                android:textSize="12sp" />
                        </LinearLayout>
                        <LinearLayout
                            android:visibility="gone"
                            android:id="@+id/llShaperMain1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="5dp"
                            android:background="@drawable/core_exam_shaper_bg"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingLeft="11.5dp"
                            android:paddingTop="5dp"
                            android:paddingRight="5dp"
                            android:paddingBottom="7dp">

                            <ImageView
                                android:layout_width="13.5dp"
                                android:layout_height="13.5dp"
                                android:src="@drawable/core_exam_sharpe_img" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp"
                                android:text="考朋友"
                                android:textColor="@color/white"
                                android:textSize="12sp" />

                        </LinearLayout>
                    </RelativeLayout>
                </LinearLayout>

                <com.zizhiguanjia.model_core.view.ExamMaterialsView
                    android:id="@+id/emvCaseDn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="7dp"
                    android:layout_marginRight="12dp" />

            </LinearLayout>
            <View
                android:id="@+id/viewLineSkill"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="12dp"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/translucent" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="@{model.lookJqjqObs?View.VISIBLE:View.GONE}">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="3dp"
                        android:layout_height="12dp"
                        android:background="@drawable/core_exam_line_bg" />
                    <TextView
                        android:id="@+id/tvSkill"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:text="解题技巧"
                        android:textColor="#333333"
                        android:textSize="14sp" />
                </LinearLayout>
                <RelativeLayout
                    android:layout_marginBottom="11dp"
                    android:visibility="@{model.lookJqjqStateObs?View.GONE:View.VISIBLE}"
                    android:paddingBottom="8.5dp"
                    android:paddingTop="9dp"
                    android:paddingRight="4.5dp"
                    android:paddingLeft="8.5dp"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginLeft="12dp"
                    android:background="@drawable/core_exam_jtjq_bg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10dp"
                        android:layout_toLeftOf="@+id/tvLookJq"
                        android:textColor="#A1794C"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:text="@{model.lookJqjqTxtObs}"
                        android:textSize="@{model.textSizeBean.getSizeReduce(4)}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:id="@+id/tvLookJq"
                        android:layout_alignParentRight="true"
                        android:paddingLeft="12.5dp"
                        android:paddingTop="2.5dp"
                        android:paddingBottom="2.5dp"
                        android:paddingRight="12.5dp"
                        android:text="查看完整技巧"
                        android:textColor="#583218"
                        android:textSize="13sp"
                        android:background="@drawable/core_exam_jtjq_bt_bg"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </RelativeLayout>
                <TextView
                    android:id="@+id/tvSkillContent"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="11dp"
                    android:text="@{model.lookJqjqTxtObs}"
                    android:visibility="@{model.lookJqjqStateObs?View.VISIBLE:View.GONE}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp" />
                <View
                    android:id="@+id/viewLineSkillBottom"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="12dp"
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="@color/translucent" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24.5dp"
                android:orientation="vertical"
                android:visibility="@{model.lookAsyObs?View.VISIBLE:View.GONE}">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="3dp"
                        android:layout_height="12dp"
                        android:background="@drawable/core_exam_line_bg" />
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical">
                        <TextView
                            android:id="@+id/tvFaceBackMain1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            android:text="解析"
                            android:textSize="14sp" />

                        <LinearLayout
                            android:id="@+id/llFaceBackMain1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="5dp"
                            android:background="@drawable/core_exam_face_bg"
                            android:gravity="center"
                            android:orientation="horizontal"
                            >

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingLeft="15.5dp"
                                android:paddingRight="15.5dp"
                                android:paddingBottom="4dp"
                                android:paddingTop="4dp"
                                android:text="纠错"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <com.zizhiguanjia.model_core.view.ExamMaterialsView
                    android:id="@+id/emvCaseJx"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="7dp"
                    android:layout_marginRight="12dp" />
            </LinearLayout>
        </LinearLayout>
    </com.zizhiguanjia.model_core.view.NonFocusingScrollView>

</layout>