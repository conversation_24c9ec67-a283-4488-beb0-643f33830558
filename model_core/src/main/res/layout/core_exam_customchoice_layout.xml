<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="model"
            type="com.zizhiguanjia.model_core.viewmodel.CustomChoiceViewModel" />

        <import type="android.view.View" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.zizhiguanjia.model_core.view.ExamMaterialsView
                android:id="@+id/exam_question_top_emlv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="12dp" />

            <View
                android:id="@+id/viewLine"
                android:layout_width="match_parent"
                android:layout_height="10.5dp"
                android:layout_marginTop="13.5dp"
                android:layout_marginBottom="15dp"
                android:background="#F4F7F9" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="15.5dp"
                        android:layout_height="1.5dp"
                        android:layout_marginRight="6dp"
                        android:background="#D4D4D4" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="问题列表"
                        android:textColor="@{model.themeBean.contentTextColor}"
                        android:textSize="19sp" />

                    <View
                        android:layout_width="15.5dp"
                        android:layout_height="1.5dp"
                        android:layout_marginLeft="6dp"
                        android:background="#D4D4D4" />
                </LinearLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <com.zizhiguanjia.model_core.view.RecyClyMouthView
                        android:id="@+id/rcvQuestion"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="4dp"
                        android:layout_marginTop="22dp"
                        android:layout_marginBottom="12dp"
                        android:scrollbars="none" />
                </RelativeLayout>

                <androidx.cardview.widget.CardView
                    android:id="@+id/slCard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginBottom="12.5dp"
                    android:paddingBottom="10dp"
                    app:cardCornerRadius="5dp">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <com.zizhiguanjia.model_core.view.ExamMaterialsView
                            android:id="@+id/emvQuestionTags"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="12dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginRight="12dp" />

                        <com.zizhiguanjia.model_core.view.ExamOptionView
                            android:id="@+id/exam_question_option_eov"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp" />

                        <com.zizhiguanjia.model_core.view.ExamResultView
                            android:id="@+id/exam_question_result_erlt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:visibility="@{model.isConfimObs?View.VISIBLE:View.GONE}" />
                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</layout>