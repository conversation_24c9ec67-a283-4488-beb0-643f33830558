<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="type"
            type="java.lang.Integer" />
        <variable
            name="confim"
            type="java.lang.Boolean" />
        <variable
            name="model"
            type="com.zizhiguanjia.model_core.view.ExamOptionView" />
        <import type="android.view.View"/>
        <import type="com.zizhiguanjia.model_core.config.ExamQuestionTypeConfig"/>
    </data>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
       <RelativeLayout
           android:layout_width="match_parent"
           android:layout_height="match_parent">
           <androidx.recyclerview.widget.RecyclerView
               android:layout_marginRight="12dp"
               android:layout_marginLeft="12dp"
               android:id="@+id/exam_option_rcv"
               android:layout_width="match_parent"
               android:layout_height="match_parent"/>
       </RelativeLayout>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="@{type!=ExamQuestionTypeConfig.EXAM_QUESTION_DUO?View.GONE:View.VISIBLE}"
            android:gravity="center_vertical"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="12dp">

            <Button
                android:onClick="@{model::onClick}"
                android:visibility="@{type!=ExamQuestionTypeConfig.EXAM_QUESTION_DUO?View.GONE:View.VISIBLE}"
                android:layout_marginLeft="12dp"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="确定"
                android:textColor="@drawable/core_exam_option_duo_confim_text_options"
                android:textSize="14sp"
                android:background="@drawable/core_exam_option_duo_confim_bg_options"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:enabled="@{!confim}"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="@{type!=ExamQuestionTypeConfig.EXAM_QUESTION_DUO?View.GONE:View.VISIBLE}"
                android:textSize="14sp"
                android:text="点击“确定”才能提交答案"
                android:paddingHorizontal="5dp"
                android:textColor="@{model.themeBean.confirmHintTextColor}"/>

        </androidx.appcompat.widget.LinearLayoutCompat>

    </LinearLayout>
</layout>