<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_core.viewmodel.ExamViewModel" />
        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <LinearLayout
            android:orientation="vertical"
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:id="@+id/test1"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <include
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    layout="@layout/layout_common_loading_view"/>
            </LinearLayout>

<!--            <com.wb.lib_weiget.views.ProgressWheel-->
<!--                android:id="@+id/examShi"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent"/>-->
<!--            <com.wb.lib_weiget.views.MultipleStatusView xmlns:app="http://schemas.android.com/apk/res-auto"-->
<!--                android:id="@+id/msvExamQuestion"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent"-->
<!--                app:emptyView="@layout/layout_common_empty"-->
<!--                app:errorView="@layout/layout_common_error"-->
<!--                app:loadingView="@layout/public_loding_view"-->
<!--                app:noNetworkView="@layout/layout_common_network">-->
<!--                -->
<!--            </com.wb.lib_weiget.views.MultipleStatusView>-->
            <include
                android:visibility="@{model.questionTypeStateObs==1?View.VISIBLE:View.GONE}"
                android:id="@+id/icdExamViewContext"
                layout="@layout/core_exam_question_context_layout"
                app:model="@{model}" />
            <include
                android:id="@+id/icdExamViewCaseQuestionContext"
                android:visibility="@{model.questionTypeStateObs==2?View.VISIBLE:View.GONE}"
                app:model="@{model}"
                layout="@layout/core_casequestion_context_layout"/>
            <include
                android:visibility="@{model.questionTypeStateObs==3?View.VISIBLE:View.GONE}"
                app:model="@{model}"
                android:id="@+id/icdExamViewAlxzContext"
                layout="@layout/core_exam_alxz_layout"
                />
        </LinearLayout>
    </LinearLayout>
</layout>