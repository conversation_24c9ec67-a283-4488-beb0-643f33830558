<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.zizhiguanjia.model_core.viewmodel.ExamResultViewModel" />

        <import type="android.view.View" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="false">

        <!-- 滚动内容区域（包括成绩区域和其他内容） -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 成绩区域（沉浸式） -->
                <FrameLayout
                    android:id="@+id/fl_score_area"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:background="@drawable/result_bg"
                    android:paddingTop="60dp">  <!-- 为固定标题栏留出空间 -->

                    <!-- 成绩内容 -->
                    <LinearLayout
                        android:id="@+id/ll_score_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:visibility="gone">

                        <!-- 左侧圆形进度条 -->
                        <FrameLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">

                            <com.zizhiguanjia.model_core.view.CircleProgressView
                                android:id="@+id/circle_progress"
                                android:layout_width="70dp"
                                android:layout_height="70dp"
                                android:layout_gravity="center"
                                app:circle_progress="71" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:gravity="bottom"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{viewModel.score}"
                                    android:textColor="#FFFFFF"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    tools:text="71" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="分"
                                    android:textColor="#FFFFFF"
                                    android:textSize="12sp" />
                            </LinearLayout>
                        </FrameLayout>

                        <!-- 右侧文字信息 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text='@{String.format("恭喜你!获得%s分", viewModel.score)}'
                                android:textColor="#FFFFFF"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="成绩不错，努力学习提高通过率"
                                android:textColor="#E0E0E0"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>
                </FrameLayout>

                <!-- 统计数据区域 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="-25dp"
                    android:layout_marginRight="16dp"
                    android:background="@drawable/bg_white_radius"
                    android:elevation="2dp"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.totalQuestions}"
                            android:textColor="#FD6C02"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="40" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="总题量"
                            android:textColor="#999999"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="#E0E0E0" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.correctQuestions}"
                                android:textColor="#FD6C02"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                tools:text="2" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="道"
                                android:textColor="#333333"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="正确"
                            android:textColor="#999999"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="#E0E0E0" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.wrongQuestions}"
                                android:textColor="#FD6C02"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                tools:text="2" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="道"
                                android:textColor="#333333"
                                android:textSize="12sp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="错误"
                            android:textColor="#999999"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="#E0E0E0" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.correctRate}"
                            android:textColor="#FD6C02"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="25%" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="正确率"
                            android:textColor="#999999"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>


                <!-- 答题情况 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="16dp"
                    android:background="@drawable/bg_white_radius"
                    android:orientation="vertical"
                    android:padding="16dp">
                    <!-- 考试结果 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginBottom="20dp"
                        android:background="@drawable/bg_white_radius">
                        <!-- 右侧答题标识 -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="考试结果"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="right|center_vertical"
                            android:orientation="horizontal">

                            <!-- 答对标识 -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginRight="16dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <View
                                    android:layout_width="12dp"
                                    android:layout_height="12dp"
                                    android:background="@drawable/bg_exam_result_correct" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="4dp"
                                    android:text="答对"
                                    android:textSize="14sp" />
                            </LinearLayout>

                            <!-- 答错标识 -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <View
                                    android:layout_width="12dp"
                                    android:layout_height="12dp"
                                    android:background="@drawable/bg_exam_result_wrong" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="4dp"
                                    android:text="答错"
                                    android:textSize="14sp" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="单选题"
                        android:textColor="#333333"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_single_choice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="多选题"
                        android:textColor="#333333"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_multiple_choice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="判断题"
                        android:textColor="#333333"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_judgment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal" />
                </LinearLayout>

                <!-- 薄弱知识点 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginRight="16dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/bg_white_radius"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/star"
                            android:tint="#FF7E00" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:text="薄弱知识点，加强练习"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_weak_points"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <!-- 薄弱知识点列表，动态添加 -->
                        <!-- 示例项 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <View
                                android:layout_width="6dp"
                                android:layout_height="6dp"
                                android:background="@drawable/dot_red" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="8dp"
                                android:text="法律法规——建筑施工企业资质管理"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- 已掌握知识点 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/zan"
                            android:tint="#3163F6" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="8dp"
                            android:text="已掌握知识点，再接再厉"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_mastered_points"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <!-- 已掌握知识点列表，动态添加 -->
                        <!-- 示例项 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <CheckBox
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:button="@null"
                                android:checked="true"
                                android:enabled="false" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="8dp"
                                android:text="法律法规——建筑施工企业资质管理"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <!-- 底部按钮 -->
                <Button
                    android:id="@+id/btn_start_learning"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="16dp"
                    android:background="@drawable/bg_button_primary"
                    android:text="马上开始学习"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp" />
            </LinearLayout>
        </ScrollView>

        <!-- 固定的标题栏 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="30dp"
            android:paddingBottom="10dp">

            <!-- 返回按钮 -->
            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回"
                android:src="@drawable/back" />

            <!-- 标题 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="考试结果"
                android:textColor="#FFFFFF"
                android:textSize="18sp"
                android:textStyle="bold" />

        </FrameLayout>
    </FrameLayout>
</layout> 