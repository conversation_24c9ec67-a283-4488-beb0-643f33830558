<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_core.viewmodel.ExamCoreViewModel" />
    </data>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
       <include
           app:model="@{model}"
           android:id="@+id/llTopAction"
           layout="@layout/core_context_top"/>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.viewpager2.widget.ViewPager2
                android:layout_above="@+id/lnPreNextOptions"
                android:id="@+id/exam_viewpager2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                />
            <Button
                android:textSize="16sp"
                android:visibility="gone"
                android:layout_marginLeft="50dp"
                android:layout_marginRight="50dp"
                android:textColor="@color/white"
                android:background="@drawable/solid_max"
                android:backgroundTint="#3163F6"
                android:layout_centerInParent="true"
                android:id="@+id/btnCheckResult"
                android:layout_marginBottom="110dp"
                android:layout_alignParentBottom="true"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:text="查看结果"
                />
            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/lnPreNextOptions"
                android:layout_above="@+id/llBottomAction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone"
                android:background="@{model.themeBean.bgColor}"
                android:paddingVertical="8.5dp"
                android:gravity="center_horizontal"
                tools:visibility="visible">
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnPrePage"
                    android:layout_width="90dp"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@drawable/core_exam_options_color_enable"
                    android:background="@drawable/hollow_max"
                    android:paddingVertical="5dp"
                    android:gravity="center"
                    android:textSize="13sp"
                    android:layout_marginHorizontal="17dp"
                    android:minHeight="0dp"
                    android:textColor="@drawable/core_exam_options_color_enable"
                    android:text="上一题"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnNextPage"
                    android:layout_width="90dp"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@drawable/core_exam_options_color_enable"
                    android:background="@drawable/hollow_max"
                    android:paddingVertical="5dp"
                    android:gravity="center"
                    android:textSize="13sp"
                    android:layout_marginHorizontal="17dp"
                    android:minHeight="0dp"
                    android:textColor="@drawable/core_exam_options_color_enable"
                    android:text="下一题"/>


            </androidx.appcompat.widget.LinearLayoutCompat>

            <include
                android:id="@+id/llBottomAction"
                layout="@layout/core_context_bottom"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_alignParentBottom="true"
                app:model="@{model}" />
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@{model.themeBean.lineColor}"
                android:layout_above="@id/lnPreNextOptions"/>
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@{model.themeBean.lineColor}"
                android:layout_alignBottom="@id/lnPreNextOptions"/>
        </RelativeLayout>
    </LinearLayout>
</layout>