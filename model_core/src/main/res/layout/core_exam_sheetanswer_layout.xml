<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llContainerView"
    android:layout_width="match_parent"
    android:layout_height="500dp"
    android:background="@drawable/core_exam_sheet_bg"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/llTopView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:gravity="center_vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >
            <LinearLayout
                android:layout_marginTop="14dp"
                android:id="@+id/llSheetLeft"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivSheetLeft"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="19dp"
                    android:src="@drawable/core_exam_dtk" />

                <TextView
                    android:id="@+id/tvExamCurrentCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="2.5dp"
                    android:text="80"
                    android:textColor="#3163F6"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/tvExamCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="/80"
                    android:textColor="#666666"
                    android:textSize="15sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lnStateTip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginLeft="19dp"
                android:layout_marginRight="19dp"
                android:layout_toRightOf="@+id/llSheetLeft"
                android:gravity="right"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_marginTop="14dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical">

                    <View
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:background="@drawable/core_exam_single_right_bg" />

                    <TextView
                        android:id="@+id/tvTipSure"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2.5dp"
                        android:text="正确"
                        android:textColor="#333333"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_marginTop="14dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:gravity="center">

                    <View
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:background="@drawable/core_exam_single_error" />

                    <TextView
                        android:id="@+id/tvTipError"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2.5dp"
                        android:text="错误"
                        android:textColor="#333333"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_marginTop="14dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:gravity="center">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivTagUnDo"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:background="@drawable/core_exam_result_nor" />

                    <TextView
                        android:id="@+id/tvTipNone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2.5dp"
                        android:text="未填写"
                        android:textColor="#333333"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>

        </RelativeLayout>
        <View
            android:layout_marginTop="13dp"
            android:layout_marginBottom="14dp"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F4F4F4" />
    </LinearLayout>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <RelativeLayout
            android:layout_above="@+id/llBottomView"
            android:layout_below="@+id/llTopView"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:id="@+id/llTags"
                android:visibility="gone"
                android:layout_marginTop="13.5dp"
                android:layout_marginBottom="17.5dp"
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <View
                    android:layout_marginLeft="20dp"
                    android:background="@drawable/core_exam_line_bg"
                    android:layout_width="2.5dp"
                    android:layout_height="12dp"/>
                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_marginLeft="5.5dp"
                    android:textColor="#333333"
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/test1"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

            </LinearLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcvExamSheetLog"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/llTags" />
            <include
                android:layout_marginLeft="72dp"
                android:layout_marginRight="72dp"
                android:layout_marginBottom="23.5dp"
                android:id="@+id/bottom_icb"
                layout="@layout/core_sheet_bt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true" />
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>