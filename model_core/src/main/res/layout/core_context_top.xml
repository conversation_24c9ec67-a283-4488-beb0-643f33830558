<?xml version="1.0" encoding="utf-8" ?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.zizhiguanjia.model_core.viewmodel.ExamCoreViewModel" />
    </data>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@{model.bgColor}" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingLeft="15dp"
            android:paddingTop="13.5dp"
            android:paddingBottom="11.5sp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{model.numObs}"
                    android:textColor="#3163F6"
                    android:textSize="18sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text='@{"/"+model.countObs}'
                    android:textColor="@{model.themeBean.pageIndexSumColor}"
                    android:textSize="15sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvDownTimes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="倒计时"
                android:textColor="#000000"
                android:textSize="18.5sp"
                android:visibility="@{model.downTimeObs?View.VISIBLE:View.GONE}" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#F4F4F4" />
    </LinearLayout>
</layout>