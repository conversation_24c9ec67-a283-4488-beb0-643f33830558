<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="model"
            type="com.zizhiguanjia.model_core.viewmodel.ExamViewModel" />
        <import type="android.view.View" />
    </data>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <com.zizhiguanjia.model_core.view.ExamMaterialsView
                android:id="@+id/exam_question_top_emlv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="12.5dp"
                android:layout_marginRight="12dp" />

            <com.zizhiguanjia.model_core.view.ExamOptionView
                android:id="@+id/exam_question_option_eov"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp" />
            <com.zizhiguanjia.model_core.view.ExamResultView
                android:id="@+id/exam_question_result_erlt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="@{model.isConfimObs?View.VISIBLE:View.GONE}" />
        </LinearLayout>
    </ScrollView>
</layout>