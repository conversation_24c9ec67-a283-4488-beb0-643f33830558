<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="des"
            type="java.lang.String" />
        <variable
            name="postion"
            type="java.lang.Integer" />
        <variable
            name="select"
            type="java.lang.Integer" />
        <variable
            name="model"
            type="com.zizhiguanjia.model_core.adapter.ExamCaseQuestionHListAdapter" />
    </data>
    <LinearLayout
        android:id="@+id/lnBg"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/solid_radio_8">
        <TextView
            android:id="@+id/tvText"
            android:onClick="@{(view)->model.onclick(view,postion)}"
            android:textSize="14sp"
            android:text="@{des}"
            android:paddingRight="15.5dp"
            android:paddingLeft="15.5dp"
            android:paddingBottom="8.5dp"
            android:paddingTop="8.5dp"
            android:background="@drawable/hollow_radio_8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</layout>