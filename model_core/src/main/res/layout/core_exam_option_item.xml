<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="data"
            type="com.zizhiguanjia.model_core.model.ExamPackAccessDataBean.RecordsBean.QuestionsBean" />
        <variable
            name="reposity"
            type="com.zizhiguanjia.model_core.repository.ExamOptionRepository" />
        <variable
            name="postion"
            type="String" />
    </data>

    <RelativeLayout
        android:onClick="@{(view)->reposity.OnclickItem(view,postion)}"
        android:id="@+id/exam_option_main_rel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:background="@drawable/core_exam_option_select_error_bg"
        android:paddingLeft="12dp"
        android:paddingTop="13dp"
        android:paddingRight="18dp"
        android:paddingBottom="12.5dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@+id/exam_option_select_img"
            android:gravity="center_vertical">
            <com.zizhiguanjia.model_core.view.ExamOptionFlagsView
                xmlns:app="http://schemas.android.com/apk/res-auto"
                android:id="@+id/exam_option_flags_eof"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <com.zizhiguanjia.model_core.view.ExamMaterialsView
                android:id="@+id/exam_jx_emlv"
                android:layout_width="match_parent"
                android:layout_marginLeft="6.5dp"
                android:layout_height="wrap_content"
                 />
        </LinearLayout>

        <ImageView
            android:id="@+id/exam_option_select_img"
            android:layout_width="14.5dp"
            android:layout_height="10dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:src="@drawable/core_exam_error_img" />
    </RelativeLayout>
</layout>