<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ResourceName">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#f5f9fc"
        android:orientation="vertical">

        <!-- 顶部栏 -->
        <RelativeLayout
            paddingTop="36dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#ffffff"
            android:elevation="2dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_centerVertical="true"
                    android:contentDescription="返回"
                    android:padding="12dp"
                    android:src="@drawable/ic_back" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:text="摸底测评"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </RelativeLayout>
        </RelativeLayout>

        <!-- 内容区域 -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- 步骤指示器 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <!-- 步骤1 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:background="@drawable/circle_step_active"
                            android:gravity="center"
                            android:text="1"
                            android:textColor="#ffffff"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="测评科目"
                            android:textColor="#333333"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <!-- 步骤1和步骤2之间的连接线 -->
                    <View
                        android:layout_width="0dp"
                        android:layout_height="2dp"
                        android:layout_weight="0.5"
                        android:background="#e0e0e0" />

                    <!-- 步骤2 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:background="@drawable/circle_step_inactive"
                            android:gravity="center"
                            android:text="2"
                            android:textColor="#ffffff"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="参与测评"
                            android:textColor="#666666"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <!-- 步骤2和步骤3之间的连接线 -->
                    <View
                        android:layout_width="0dp"
                        android:layout_height="2dp"
                        android:layout_weight="0.5"
                        android:background="#e0e0e0" />

                    <!-- 步骤3 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:background="@drawable/circle_step_inactive"
                            android:gravity="center"
                            android:text="3"
                            android:textColor="#ffffff"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="定制计划"
                            android:textColor="#666666"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>

                <!-- 步骤1内容 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:text="第一步：确定您的测评科目"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <!-- 科目信息标签布局 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="#F5F9FC"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:padding="12dp">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="horizontal">

                                <!-- 科目标签 -->
                                <TextView
                                    android:id="@+id/tv_subject_info"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/bg_tag_blue"
                                    android:paddingHorizontal="12dp"
                                    android:paddingVertical="6dp"
                                    android:text="建筑安全三类人员"
                                    android:textColor="#4F7CF6"
                                    android:textSize="14sp" />

                                <!-- 地区标签 -->
                                <TextView
                                    android:id="@+id/tv_location"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="8dp"
                                    android:background="@drawable/bg_tag_blue"
                                    android:paddingHorizontal="12dp"
                                    android:paddingVertical="6dp"
                                    android:text="北京"
                                    android:textColor="#4F7CF6"
                                    android:textSize="14sp" />

                                <!-- 等级标签 -->
                                <TextView
                                    android:id="@+id/tv_level"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="8dp"
                                    android:background="@drawable/bg_tag_blue"
                                    android:paddingHorizontal="12dp"
                                    android:paddingVertical="6dp"
                                    android:text="A本"
                                    android:textColor="#4F7CF6"
                                    android:textSize="14sp" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- 步骤2内容 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:text="第二步：参与并完成测评"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:text="AI会从最高频的考点中随机抽取15道题，通过考试得分初步判断目前的知识点掌握程度，并找到薄弱点。"
                            android:textColor="#666666"
                            android:textSize="14sp" />

                        <!-- 题目信息表格 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:background="#f9f9f9"
                            android:orientation="vertical">

                            <!-- 表头 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="#f2f2f2"
                                android:orientation="horizontal"
                                android:padding="12dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="总计15道题"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:text="总分100分"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />
                            </LinearLayout>

                            <!-- 表格内容：单选题 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:padding="12dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="单选题5道"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:text="每题7分"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="right"
                                    android:text="35分"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />
                            </LinearLayout>

                            <!-- 表格内容：多选题 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="#f5f5f5"
                                android:orientation="horizontal"
                                android:padding="12dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="多选题3道"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:text="每题10分"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="right"
                                    android:text="30分"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />
                            </LinearLayout>

                            <!-- 表格内容：判断题 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:padding="12dp">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="判断题7道"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:text="每题5分"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="right"
                                    android:text="35分"
                                    android:textColor="#666666"
                                    android:textSize="14sp" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- 步骤3内容 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:text="第三步：定制学习计划"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="AI会根据您的情况个性化定制学习计划，告别题海战术，重点在于提分，考试通关"
                            android:textColor="#666666"
                            android:textSize="14sp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </LinearLayout>
        </ScrollView>

        <!-- 底部按钮 -->
        <Button
            android:id="@+id/btn_start_assessment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/btn_primary_blue"
            android:text="好的，请开始"
            android:textColor="#ffffff"
            android:textSize="16sp" />
    </LinearLayout>
</layout>