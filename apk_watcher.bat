@echo off
echo 开始监听APK文件生成...

:loop
if exist "app\build\outputs\baidu\debug\output-metadata.json" (
    echo 检测到APK文件已生成！
    mkdir "app\build\outputs\apk\baidu\debug" 2>nul
    copy "app\build\outputs\baidu\debug\*.apk" "app\build\outputs\apk\baidu\debug\" /Y
    copy "app\build\outputs\baidu\debug\output-metadata.json" "app\build\outputs\apk\baidu\debug\" /Y
    echo APK文件已复制到标准路径！
    exit /b 0
)
timeout /t 5 /nobreak > nul
goto loop 