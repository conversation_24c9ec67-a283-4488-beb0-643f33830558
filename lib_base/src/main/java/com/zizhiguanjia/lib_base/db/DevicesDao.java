package com.zizhiguanjia.lib_base.db;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.wb.lib_room.BaseDao;
import com.wb.lib_weiget.tabbar.Tab;
import com.zizhiguanjia.lib_base.tb.TableSetConfigInfo;

@Dao
public interface DevicesDao extends BaseDao<TableSetConfigInfo> {
    @Query("SELECT * FROM tableSettingConfigInfo")
    TableSetConfigInfo getSetConfig();
    @Insert
    void addSetConfig(TableSetConfigInfo tableSetConfigInfo);
    @Update
    void updataSetConfig(TableSetConfigInfo tableSetConfigInfo);
}
