package com.zizhiguanjia.lib_base.db;

import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.wb.lib_utils.AppUtils;
import com.zizhiguanjia.lib_base.tb.TableAccount;
import com.zizhiguanjia.lib_base.tb.TableCertificate;
import com.zizhiguanjia.lib_base.tb.TableErrorLog;
import com.zizhiguanjia.lib_base.tb.TableSetConfigInfo;
import com.zizhiguanjia.lib_base.tb.TableUserInfo;

@Database(entities = {TableSetConfigInfo.class, TableCertificate.class, TableAccount.class, TableUserInfo.class, TableErrorLog.class}, version = 11, exportSchema = false)
public abstract class AppDatabase extends RoomDatabase {
    private static AppDatabase INSTANCE;
    public abstract UserInfoDao userInfoDao();
    public abstract DevicesDao devicesDao();
    public abstract AccountDao accountDao();
    public abstract CertificateDao certificateDao();
    public abstract ErrorLogDao errorLogDao();
    public static AppDatabase getInstance() {
        if (INSTANCE == null) {
            synchronized (AppDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE =
                            Room.databaseBuilder(AppUtils.getApp(), AppDatabase.class, "aqykst.db")
                                    .allowMainThreadQueries() //room默认数据库的查询是不能在主线程中执行的，除非这样设置
                                    .fallbackToDestructiveMigration() //不想提供migration，而且希望更新版本之后清空数据库
                                    .build();
                }
            }
        }
        return INSTANCE;
    }
}

