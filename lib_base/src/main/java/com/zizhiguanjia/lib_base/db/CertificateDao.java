package com.zizhiguanjia.lib_base.db;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.wb.lib_room.BaseDao;
import com.zizhiguanjia.lib_base.tb.TableCertificate;
@Dao
public interface CertificateDao extends BaseDao<TableCertificate> {
    @Insert
    void addCertificate(TableCertificate tableCertificate);
    @Query("SELECT * FROM tableCertificate WHERE userAccount=:account")
    TableCertificate getCertificateByAccount(String account);
    @Query("SELECT isVip FROM tableCertificate WHERE userAccount=:account")
    boolean getCertificateStateByAccount(String account);
    @Update
    void updataCertificate(TableCertificate tableCertificate);
}
