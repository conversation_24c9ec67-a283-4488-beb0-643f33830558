package com.zizhiguanjia.lib_base.db;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.wb.lib_room.BaseDao;
import com.zizhiguanjia.lib_base.tb.TableAccount;
@Dao
public interface AccountDao extends BaseDao<TableAccount> {
    @Query("SELECT * FROM tableAccount ORDER BY lastTime DESC")
    TableAccount getAccountInfo();
    @Insert
    void addAccount(TableAccount tableAccount);
    @Update
    void updataAccount(TableAccount tableAccount);
}
