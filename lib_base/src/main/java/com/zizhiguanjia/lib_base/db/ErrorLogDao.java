package com.zizhiguanjia.lib_base.db;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.wb.lib_room.BaseDao;
import com.zizhiguanjia.lib_base.tb.TableErrorLog;
import com.zizhiguanjia.lib_base.tb.TableUserInfo;

@Dao
public interface ErrorLogDao extends BaseDao<TableErrorLog> {
    @Insert
    long addressErrorLog(TableErrorLog tableErrorLog);
    @Query("SELECT * FROM tableErrorLog")
    TableErrorLog getAllErrorLog();
    @Query("SELECT * FROM tableErrorLog WHERE id=:ids")
    TableErrorLog getErrorLogById(long ids);
    @Update
    void updateErrorLog(TableErrorLog tableErrorLog);
}
