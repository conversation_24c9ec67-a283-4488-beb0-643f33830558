package com.zizhiguanjia.lib_base.db;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.wb.lib_room.BaseDao;
import com.zizhiguanjia.lib_base.tb.TableUserInfo;
@Dao
public interface UserInfoDao extends BaseDao<TableUserInfo> {
    @Query("SELECT * FROM tableUserInfo WHERE Account=:account")
    TableUserInfo getUserInfoByAccount(String account);
    @Insert
    void addUserInfo(TableUserInfo tableUserInfo);
    @Update
    void updataUserInfo(TableUserInfo tableUserInfo);
}
