package com.zizhiguanjia.lib_base.tb;

import androidx.room.Embedded;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import java.io.Serializable;

@Entity(tableName = "tableSettingConfigInfo",foreignKeys=@ForeignKey(entity = TableErrorLog.class,parentColumns = "id",childColumns = "errorLogId",onDelete = ForeignKey.CASCADE),indices = {@Index(value = "id",unique = true),@Index(value = "errorLogId")})
public class TableSetConfigInfo implements Serializable {
    @PrimaryKey(autoGenerate = true)
    private long id;
    private String PayPath="http://www.zizhiguanjia.cn/m/pay";
    private String KeFuPath="https://www.zizhiguanjia.cn/openkefu.html";
    private String KeFuPhone="133-1645-7475";
    private String PageTimeDown="3";
    private String ServerTime=String.valueOf(System.currentTimeMillis());
    private String majId="0";
    private String majName="证书";
    private String cityCode="0";
    private String cityName="请选择地区";
    private String checkData="0";
    private int always_show=0;
    private String copyRight="Copyright@2019-2022\n北京京立方建设工程有限公司版权所有";
    private int reminderType;
    private String CodeSrc="https://zzgj.oss-cn-beijing.aliyuncs.com/images/kefu_vip.png";
    private String OpenLink="weixin://dl/business/?t=nMFgIzFkwtb";
    private String errorLogId;
    public String getErrorLogId() {
        return errorLogId;
    }
    public void setErrorLogId(String errorLogId) {
        this.errorLogId = errorLogId;
    }

    public String getCodeSrc() {
        return CodeSrc;
    }

    public void setCodeSrc(String codeSrc) {
        CodeSrc = codeSrc;
    }

    public String getOpenLink() {
        return OpenLink;
    }

    public void setOpenLink(String openLink) {
        OpenLink = openLink;
    }

    public int getReminderType() {
        return reminderType;
    }

    public void setReminderType(int reminderType) {
        this.reminderType = reminderType;
    }

    public int getAlways_show() {
        return always_show;
    }

    public void setAlways_show(int always_show) {
        this.always_show = always_show;
    }

    public String getCopyRight() {
        return copyRight;
    }

    public void setCopyRight(String copyRight) {
        this.copyRight = copyRight;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getPayPath() {
        return PayPath;
    }

    public void setPayPath(String payPath) {
        PayPath = payPath;
    }

    public String getKeFuPath() {
        return KeFuPath;
    }

    public void setKeFuPath(String keFuPath) {
        KeFuPath = keFuPath;
    }

    public String getKeFuPhone() {
        return KeFuPhone;
    }

    public void setKeFuPhone(String keFuPhone) {
        KeFuPhone = keFuPhone;
    }

    public String getPageTimeDown() {
        return PageTimeDown;
    }

    public void setPageTimeDown(String pageTimeDown) {
        PageTimeDown = pageTimeDown;
    }

    public String getServerTime() {
        return ServerTime;
    }

    public void setServerTime(String serverTime) {
        ServerTime = serverTime;
    }

    public String getCheckData() {
        return checkData;
    }

    public void setCheckData(String checkData) {
        this.checkData = checkData;
    }

    public String getMajId() {
        return majId;
    }

    public void setMajId(String majId) {
        this.majId = majId;
    }

    public String getMajName() {
        return majName;
    }

    public void setMajName(String majName) {
        this.majName = majName;
    }

    @Override
    public String toString() {
        return "TableSetConfigInfo{" +
                "id=" + id +
                ", PayPath='" + PayPath + '\'' +
                ", KeFuPath='" + KeFuPath + '\'' +
                ", KeFuPhone='" + KeFuPhone + '\'' +
                ", PageTimeDown='" + PageTimeDown + '\'' +
                ", ServerTime='" + ServerTime + '\'' +
                ", majId='" + majId + '\'' +
                ", majName='" + majName + '\'' +
                ", checkData='" + checkData + '\'' +
                '}';
    }
}
