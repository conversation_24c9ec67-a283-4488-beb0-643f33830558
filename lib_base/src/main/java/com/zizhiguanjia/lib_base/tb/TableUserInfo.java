package com.zizhiguanjia.lib_base.tb;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.SerializedName;
import com.zizhiguanjia.lib_base.msgconfig.LoginType;

import java.io.Serializable;
@Entity(tableName = "tableUserInfo")
public class TableUserInfo implements Serializable {
    @PrimaryKey(autoGenerate = true)
    private long id;
    private String UserId;
    private String ToKen;
    private String NickName;
    private String Headimgurl;
    private String Sex;
    private String Account;
    @SerializedName("IsVip")
    private boolean Vip;
    private String AccessToken;
    private int UserType;

    public int getUserType() {
        return UserType;
    }

    public void setUserType(int userType) {
        UserType = userType;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getUserId() {
        return UserId;
    }

    public void setUserId(String userId) {
        UserId = userId;
    }

    public String getToKen() {
        return ToKen;
    }

    public void setToKen(String toKen) {
        ToKen = toKen;
    }

    public String getNickName() {
        return NickName;
    }

    public void setNickName(String nickName) {
        NickName = nickName;
    }

    public String getHeadimgurl() {
        return Headimgurl;
    }

    public void setHeadimgurl(String headimgurl) {
        Headimgurl = headimgurl;
    }

    public String getSex() {
        return Sex;
    }

    public void setSex(String sex) {
        Sex = sex;
    }

    public String getAccount() {
        return Account;
    }

    public void setAccount(String account) {
        Account = account;
    }

    public boolean isVip() {
        return Vip;
    }

    public void setVip(boolean vip) {
        Vip = vip;
    }

    public String getAccessToken() {
        return AccessToken;
    }

    public void setAccessToken(String accessToken) {
        AccessToken = accessToken;
    }

    @Override
    public String toString() {
        return "TableUserInfo{" +
                "id=" + id +
                ", UserId='" + UserId + '\'' +
                ", ToKen='" + ToKen + '\'' +
                ", NickName='" + NickName + '\'' +
                ", Headimgurl='" + Headimgurl + '\'' +
                ", Sex='" + Sex + '\'' +
                ", Account='" + Account + '\'' +
                ", Vip=" + Vip +
                ", AccessToken='" + AccessToken + '\'' +
                ", UserType=" + UserType +
                '}';
    }
}
