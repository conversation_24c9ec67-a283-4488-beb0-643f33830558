package com.zizhiguanjia.lib_base.tb;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "tableAccount")
public class TableAccount {
    @PrimaryKey(autoGenerate = true)
    private long id;
    private String account;
    private long lastTime;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public long getLastTime() {
        return lastTime;
    }

    public void setLastTime(long lastTime) {
        this.lastTime = lastTime;
    }
}
