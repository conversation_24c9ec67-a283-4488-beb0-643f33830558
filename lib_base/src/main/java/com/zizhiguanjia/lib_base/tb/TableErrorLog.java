package com.zizhiguanjia.lib_base.tb;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.PrimaryKey;

@Entity(tableName = "tableErrorLog")
public class TableErrorLog {
    @PrimaryKey(autoGenerate = true)
    private long id;
    private String sendType;
    private String emailReceiver;
    private String emailSender;
    private String emailSendpassword;
    private String emailHost;
    private String emailPort;
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getSendType() {
        return sendType;
    }

    public void setSendType(String sendType) {
        this.sendType = sendType;
    }

    public String getEmailReceiver() {
        return emailReceiver;
    }

    public void setEmailReceiver(String emailReceiver) {
        this.emailReceiver = emailReceiver;
    }

    public String getEmailSender() {
        return emailSender;
    }

    public void setEmailSender(String emailSender) {
        this.emailSender = emailSender;
    }

    public String getEmailSendpassword() {
        return emailSendpassword;
    }

    public void setEmailSendpassword(String emailSendpassword) {
        this.emailSendpassword = emailSendpassword;
    }

    public String getEmailHost() {
        return emailHost;
    }

    public void setEmailHost(String emailHost) {
        this.emailHost = emailHost;
    }

    public String getEmailPort() {
        return emailPort;
    }

    public void setEmailPort(String emailPort) {
        this.emailPort = emailPort;
    }

    @NonNull
    @Override
    public String toString() {
        return "----->>>>"+sendType+"***"+emailSender;
    }
}
