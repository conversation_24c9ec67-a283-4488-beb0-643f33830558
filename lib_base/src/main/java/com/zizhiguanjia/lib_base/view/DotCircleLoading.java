package com.zizhiguanjia.lib_base.view;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.animation.LinearInterpolator;

import androidx.annotation.Nullable;

public class DotCircleLoading extends View implements ValueAnimator.AnimatorUpdateListener {

    private TextPaint mTextPaint;
    private int mTextSize = 14;
    private int mDotRadius = 0;
    private float mDotSpaceDegree = 0;
    private ValueAnimator mLoadAnimator = null;
    private boolean shouldReBuildAniamtor = false;
    private int headDotIndex = 0;

    @SuppressLint("Range")
    private int color = Color.parseColor("#027AFF");
    private int maxShowDotNum = 9;
    private boolean decreseColor = false;

    public DotCircleLoading(Context context) {
        this(context, null);
    }

    public DotCircleLoading(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DotCircleLoading(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        //设置此项true，否则无法滑动
        mTextPaint = initPaint();
        mDotRadius = (int) dpTopx(5);
        mDotSpaceDegree = (float) (Math.PI / 30);
        setClickable(true);
        setFocusable(true);
        setFocusableInTouchMode(true);
    }


    public void setDecreseColor(boolean decreseColor) {
        this.decreseColor = decreseColor;
    }

    public void setColor(int color) {
        this.color = color;
        if (mTextPaint == null) {
            mTextPaint = initPaint();
        }
        mTextPaint.setColor(color);

    }

    private TextPaint initPaint() {
        // 实例化画笔并打开抗锯齿
        TextPaint paint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        paint.setAntiAlias(true);
        paint.setDither(true);
        paint.setColor(color);
        paint.setStyle(Paint.Style.FILL);
        paint.setTextSize(dpTopx(mTextSize));
        return paint;
    }

    private float dpTopx(int dp) {
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, getResources().getDisplayMetrics());
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);

        if (heightMode != MeasureSpec.EXACTLY) {
            heightSize = getPaddingTop() + getPaddingBottom() + 320;
        }

        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);


        if (widthMode != MeasureSpec.EXACTLY) {
            widthSize = getPaddingTop() + getPaddingBottom() + 320;
        }

        setMeasuredDimension(widthSize, heightSize);

    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        shouldReBuildAniamtor = true;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        int width = getWidth();
        int height = getHeight();

        if (width == 0 || height == 0) return;

        int centerX = width / 2;
        int centerY = height / 2;
        int Radius = Math.min(width / 2, height / 2);

        if (Radius == 0) return;

        int restoreId = canvas.save();

        canvas.translate(centerX, centerY);

        float dotDegree = (float) (Math.abs(Math.asin(mDotRadius * 1.0f / Radius)) * 2);

        int dotNum = (int) (2 * Math.PI / (dotDegree + mDotSpaceDegree));  //造成精度损失

        float fixSpaceDotDgree = (float) ((2 * Math.PI - dotNum * (dotDegree + mDotSpaceDegree)) / dotNum); //精度弥补

        int PRadius = Radius - mDotRadius;

        if (maxShowDotNum > dotNum / 2) {
            maxShowDotNum = dotNum / 3;
        }
        int color = mTextPaint.getColor();
        int alphaCounter = 0;
        for (int i = headDotIndex; i > (headDotIndex - maxShowDotNum); i--) {
            int j = i;
            if (i < 0) {
                j = dotNum + i;
            }
            float netDotDegree = (mDotSpaceDegree + fixSpaceDotDgree + dotDegree) * (j - 1) + dotDegree / 2;
            int cx = (int) (PRadius * Math.cos(netDotDegree));
            int cy = (int) (PRadius * Math.sin(netDotDegree));
            int alpha = 255;
            if (decreseColor) {
                alpha = (int) (((maxShowDotNum - alphaCounter) * 1.0f / (maxShowDotNum)) * 255);
            }
            alphaCounter++;
            mTextPaint.setColor(argb(alpha, Color.red(color), Color.green(color), Color.blue(color)));
            canvas.drawCircle(cx, cy, mDotRadius, mTextPaint);

        }
        canvas.restoreToCount(restoreId);

        if (shouldReBuildAniamtor) {
            shouldReBuildAniamtor = false;
            startAnimation(dotNum);
        }
    }

    public static int argb(
            int alpha,
            int red,
            int green,
            int blue) {
        return (alpha << 24) | (red << 16) | (green << 8) | blue;
    }

    private void startAnimation(int dotNum) {

        if (mLoadAnimator != null) {
            mLoadAnimator.cancel();
        }
        ValueAnimator animator = ValueAnimator.ofInt(dotNum).setDuration(800);
        animator.setInterpolator(new LinearInterpolator());
        animator.setRepeatCount(ValueAnimator.INFINITE);
        animator.setRepeatMode(ValueAnimator.RESTART);
        animator.addUpdateListener(this);

        mLoadAnimator = animator;
        mLoadAnimator.start();
    }

    @Override
    public void onAnimationUpdate(ValueAnimator animation) {
        headDotIndex = (int) animation.getAnimatedValue();
        invalidate();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mLoadAnimator != null) {
            mLoadAnimator.cancel();
        }
        mLoadAnimator = null;
        shouldReBuildAniamtor = true;
    }

}