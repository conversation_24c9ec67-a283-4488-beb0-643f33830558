package com.zizhiguanjia.lib_base.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

/**
 * 功能作用：滑动状态设置轮播控件
 * 初始注释时间： 2023/11/29 17:06
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class ScrollSetViewPager extends ViewPager {
    private boolean mNoScroll = true;

    public ScrollSetViewPager(@NonNull Context context) {
        super(context);
    }

    public ScrollSetViewPager(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public void setNoScroll(boolean mNoScroll) {
        this.mNoScroll = mNoScroll;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mNoScroll) {
            return false;
        } else {
            return super.onTouchEvent(event);
        }
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        if (mNoScroll) {
            return false;
        } else {
            return super.onInterceptTouchEvent(event);
        }
    }
}
