package com.zizhiguanjia.lib_base.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.zizhiguanjia.lib_base.R;

import io.supercharge.shimmerlayout.ShimmerLayout;


public class ShimmerView extends LinearLayout {

    public ShimmerView(Context context) {
        this(context,null);
    }

    public ShimmerView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public ShimmerView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr,0);
    }

    public ShimmerView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }
    private void initView(){
        View mView= LayoutInflater.from(getContext()).inflate(R.layout.layout_common_loading_view,this);
        ShimmerLayout shimmerText = (ShimmerLayout) findViewById(R.id.shimmer_text);
        shimmerText.setShimmerColor(getContext().getResources().getColor(R.color.white));
        shimmerText.setShimmerAngle(20);
        shimmerText.startShimmerAnimation();
    }
}
