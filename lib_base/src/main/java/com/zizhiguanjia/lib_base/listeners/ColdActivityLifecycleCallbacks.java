package com.zizhiguanjia.lib_base.listeners;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.util.Log;
import com.zizhiguanjia.lib_base.DebugApplication;

public class ColdActivityLifecycleCallbacks implements Application.ActivityLifecycleCallbacks {
    private int mFinalCount;
    public Activity currentActivity;

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {

    }

    @Override
    public void onActivityStarted(Activity activity) {
        mFinalCount++;
        //如果mFinalCount ==1，说明是从后台到前台
        if (mFinalCount == 1) {
            // 进入前台 改为热启动
            DebugApplication.isColdLaunch = false;
        }
    }

    @Override
    public void onActivityResumed(Activity activity) {
        currentActivity = activity;
    }

    @Override
    public void onActivityPaused(Activity activity) {
        if (currentActivity == activity) {
            currentActivity = null;
        }
    }

    @Override
    public void onActivityStopped(Activity activity) {
        mFinalCount--;
        //如果mFinalCount ==0，说明是前台到后台
        if (mFinalCount == 0) {
        }
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(Activity activity) {

    }
}