package com.zizhiguanjia.lib_base.service;

import android.content.Context;

import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.base.IARouterService;

public interface CommonService  extends IARouterService {
    IFragment showCommonWebView();
    /**
     * html文本加载
     */
    IFragment showCommonWebViewHtmlTextLoad();
    IFragment showSetFragmentView();
    IFragment showFaceBackFragmentView();
    IFragment showPermissView();
    void upDataApp(Context mContext);
    void showCommonWebView(String routh,int payType,String url);
}
