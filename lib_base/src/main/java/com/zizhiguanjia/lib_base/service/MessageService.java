package com.zizhiguanjia.lib_base.service;

import android.app.Activity;
import android.content.Context;
import android.view.View;

import com.wb.lib_pop.code.BasePopupView;
import com.xuexiang.xupdate.entity.UpdateEntity;
import com.xuexiang.xupdate.proxy.IUpdateProxy;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.base.IARouterService;
import com.zizhiguanjia.lib_base.bean.OrderDialogBean;
import com.zizhiguanjia.lib_base.listeners.ExamGuilderListener;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.listeners.HomeFootListener;
import com.zizhiguanjia.lib_base.listeners.ILoginVipTs;
import com.zizhiguanjia.lib_base.listeners.MessageSuccessPayListener;
import com.zizhiguanjia.lib_base.listeners.OnChoiceTimeListenter;
import com.zizhiguanjia.lib_base.listeners.PaySuccessListen;
import com.zizhiguanjia.lib_base.listeners.SpeedListener;

public interface MessageService extends IARouterService {
    void openUpdateExam(String title,String subTitle,String contentTitle,String Content,String subContent,String ids);
//    void openPaySuccessDialog(Activity activity, PaySuccessListen paySuccessListen);
    void initPaySuccessDialog(Activity activity);
    void openNoPremissBuyDialog(Activity activity,boolean ised,String payRouthParams);
    void openSpeedDialog(Context activity, View attachView, SpeedListener speedListener);
    void openSpeedFullDialog(Context activity,View attachView, SpeedListener speedListener);
    void startCoupion(String url);
    void openGeneralCentDialog(Activity activity, String title, String msg, String cancel, String confim, boolean isHideCancel, boolean isBack, GeneralDialogListener generalDialogListener);
    void openGeneralCentDialog(Activity activity, String title, String msg, String cancel, String confim, boolean isHideCancel, boolean isBack, GeneralDialogListener generalDialogListener,int maxW,int maxH);
    void openBottomListDialog(Activity activity,String title,String msg,String cancel,String confirm,boolean isHind,boolean isBack,GeneralDialogListener generalDialogListener);
    void openBottomListDialog(Activity activity,String title,String msg,String cancel,String confirm,boolean isHind,boolean isBack,GeneralDialogListener generalDialogListener,int maxW,int maxH);
    void permissionsTips(Activity activity ,String title,String msg,String confim);
    BasePopupView openAccountPrivacyTipic(Activity activity, View mView);
    void openServiceTips(Activity activity);
    void openAppUpdataVersionTips(Activity activity, IUpdateProxy updateProxy, UpdateEntity updateEntity);
    void  openGuilder(Activity activity, int type, ExamGuilderListener examGuilderListener);
    void orderOpenDialog(OrderDialogBean orderDialogBean);
    void nextDialogTask();
    void openTimeChoiceDialog(Activity activity, OnChoiceTimeListenter onChoiceTimeListenter);
    void restCoupionState();
    void openCusomerDialog(Activity activity,String routh);
    void openLearningPlanDialog(Activity activity,String time,int type);
    void openGotoSubjectSelect(MessageSuccessPayListener messageSuccessPayListener,Activity activity);
    void openAccountLogOffSwithTs(Activity activity,String userToken);
    void openAccountLock(Activity activity);
    void openAccountVipTs(Activity activity, String certificateName, String cityName, String phone, String token, ILoginVipTs iLoginVipTs);
    void openHomeCertificateUpdata(Activity activity);
    void openSelectSubjectOrCity(Activity activity);
    void openOtherPayTs(Activity activity,String json);
    void openOffOnileService(Activity activity, String title, String subT, String url, HomeFootListener listener);
    void openPayFailServer(Activity activity, BaseFragment baseFragment);
    }
