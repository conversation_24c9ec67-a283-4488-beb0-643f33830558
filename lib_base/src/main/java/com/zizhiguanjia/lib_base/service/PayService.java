package com.zizhiguanjia.lib_base.service;

import android.app.Activity;

import com.zizhiguanjia.lib_base.base.IARouterService;
import com.zizhiguanjia.lib_base.listeners.PayInfoListener;

public interface PayService extends IARouterService {
    void getPayInfoByMajIds(String majId, PayInfoListener payInfoListener);
    void payOrder(String goodId, Activity activity,String payRouthParams);
    void nativeToPay(Activity mActivity,String appId,String Noncestr,String PackageX,String Partnerid,String Sign,String Prepayid,String Timestamp);
}
