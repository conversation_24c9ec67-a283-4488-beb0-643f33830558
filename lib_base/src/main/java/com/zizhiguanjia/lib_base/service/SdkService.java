package com.zizhiguanjia.lib_base.service;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import com.zizhiguanjia.lib_base.base.IARouterService;
import com.zizhiguanjia.lib_base.listeners.AutoOneKeyLoginListener;
import com.zizhiguanjia.lib_base.listeners.JpushAuthorizeListener;
import com.zizhiguanjia.lib_base.utils.ThirdPlatformOptionsUtils;

public interface SdkService extends IARouterService {
    void initJpushConfig(boolean debug);
    void startThridLogin(int name, ThirdPlatformOptionsUtils.ThirdPlatformOptionsCallback callback);
    void startOneKeylogin(AutoOneKeyLoginListener autoOneKeyLoginListener );
    void preOnekeyLogin();
    void removeAuthorize(int name, JpushAuthorizeListener jpushAuthorizeListener);
    void shaperWx(Activity activity,String questionId);
    boolean JpushOneKeyLoginInitConfig();
    void initHumSdk(Context context);
    void onPauseDou(Context context);
    void onResumeDou(Context context);
    void initOpenInstall(Context context);
    void getWakeUp(Intent intent,Context mContext);
    void getInstall(Context mContext);
    void registUserId(String uid);
    void deleteAlias(String uid);
    void userConfimYsyxJg(Context mContext,boolean comfig);
    void shareWechatByType(String url,int type);
    void clearBadgeNum();
    void countJpushMessage(String messageId);
    void startSdkWebview(Context context,String extraStr,String msgId);
}
