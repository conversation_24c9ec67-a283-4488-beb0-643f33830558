package com.zizhiguanjia.lib_base.service;

import com.zizhiguanjia.lib_base.base.IARouterService;
import com.zizhiguanjia.lib_base.tb.TableUserInfo;

public interface UserService extends IARouterService {
    int getUserTypeByAccount(String account);
    void addUserInfo(String json);
    boolean isBecomeVip();
    void passVip();
    TableUserInfo getUserInfo();
    void iniAppToken();
    void updataUserVipState(int type);
    void exitUser();
    void updataToken(String token);

}
