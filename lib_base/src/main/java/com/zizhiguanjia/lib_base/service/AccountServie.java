package com.zizhiguanjia.lib_base.service;

import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.base.IARouterService;

public interface AccountServie extends IARouterService {
    IFragment showUpdataPhone();
    IFragment showAccountLogin();
    boolean checkLoginState();
    void exitAccount();
    void initAccountInfo();
    void successLoginAccount(String json);
    String getCurrentLoginAccount();
    boolean isUserLogin();
    String getUserId();
    void exitSaveAccount();
    void exitAccountPostData();


    /**
     * 登录校监 未登录跳转登录 反正返回true
     * @return
     */
    boolean loginSuperintendent();
    /**
     * 登录验证
     * @param oneKeyLoginCheckDoubleClick 一键登录是否验证二次点击，防止其他地方的点击操作加了二次验证导致这里无法触发弹窗
     * @return true-已登录
     */
    boolean loginSuperintendent(boolean oneKeyLoginCheckDoubleClick);
    IFragment logOffAccountFragment();
    void openStaticAccountLock(int type,String token);
}
