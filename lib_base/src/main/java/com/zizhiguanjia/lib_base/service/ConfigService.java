package com.zizhiguanjia.lib_base.service;

import com.zizhiguanjia.lib_base.base.IARouterService;

import java.util.Map;

public interface ConfigService extends IARouterService {
    void initConfig();
    String getDafalutCertificata();
    String getSplanTimeDown();
    String getPayUrl();
    String getKfUrl();
    String getKfTel();
    int getAlwaysShow();
    String getCopyRight();
    boolean getAppUpdateType();
    String getWechatOpenUrl();
    String getCarshConfig();
    int  getReminderType();
}
