package com.zizhiguanjia.lib_base.service;

import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.base.IARouterService;
import com.zizhiguanjia.lib_base.listeners.CertificateDataListener;
import com.zizhiguanjia.lib_base.listeners.CertificateJsonByAddressListener;
import com.zizhiguanjia.lib_base.tb.TableCertificate;

import java.util.Map;

public interface CertificateService extends IARouterService {
    IFragment startChoseCertificateByAddress();
    IFragment startChoseCertificateZzByAddress();
    String initCertificate(boolean loginSuccess);
    void updateCertificate(String cityCode,String cityName,String majId,String majName,String des);
    boolean userChoiceCertificate();
    String exitAccountCertificate();
    TableCertificate getUserCertificate(String account);
    boolean getCertificateState(String account);
    void updataCertificate(TableCertificate certificateConfigBean);
    boolean isAqy();
    String getCertificateDes();
    void getCertificateJsonByAddress(String areaId, CertificateJsonByAddressListener certificateJsonByAddressListener);
    Map<String,String > getCurrentCertificate();
    void startCertificateActivity(String addressName,String addressId,boolean tgAddress);
    String getCurrentCertificateAddressName();
    boolean payTypeDialog();
}
