package com.zizhiguanjia.lib_base.service;

import com.zizhiguanjia.lib_base.base.IARouterService;
import com.zizhiguanjia.lib_base.listeners.ExamDataUploadListener;

public interface DataService extends IARouterService {
    boolean checkUserPremiss();
    boolean setUserPremiss(boolean userPremiss);
    String getExamSheetDataList(String json);
    boolean uploadRecolder(String json);
    void initExamConfig(String paperType,String paperValue,String currentTime);
    void userSubmit(boolean isSubmit);
    void userStartHandPaper(ExamDataUploadListener examDataUploadListener);
    void postAllQuestion();
    void uploadVideoCountData(String json);
    void uploadOrderState(String num);
}
