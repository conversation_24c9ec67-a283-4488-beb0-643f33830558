package com.zizhiguanjia.lib_base.service;

import com.zizhiguanjia.lib_base.base.IARouterService;

import java.util.Map;

public interface PointService extends IARouterService {
    void joinPointData(String key,boolean saveCache);
    void joinPointData(String key, Map<String,String> params, boolean saveCache);
    boolean isRegistPoinRouth(String routh);
    void addPoinRouth(String key);
    boolean checkPoinRouth(int routhType);
    void clernPointRouth(int routhType);
    void unRegistPoinRouth(String key,Map<String,String> params,boolean sendPost);
    void unRegistPoinRouth(String key,boolean sendPost);
    void cleanPaySuccessPoin(Map<String,String> params);
}
