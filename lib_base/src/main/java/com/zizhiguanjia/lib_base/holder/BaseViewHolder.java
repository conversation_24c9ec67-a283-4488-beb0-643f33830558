package com.zizhiguanjia.lib_base.holder;

import android.util.SparseArray;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.IdRes;

public class BaseViewHolder {
    private SparseArray<View> views;
    private View convertView;

    private BaseViewHolder(View view) {
        this.convertView = view;
        this.views = new SparseArray();
    }

    public static BaseViewHolder create(View view) {
        return new BaseViewHolder(view);
    }

    public <T extends View> T getView(@IdRes int viewId) {
        View view = (View)this.views.get(viewId);
        if (null == view) {
            view = this.convertView.findViewById(viewId);
            this.views.put(viewId, view);
        }

        return (T) view;
    }

    public void setText(int viewId, String text) {
        TextView textView = (TextView)this.getView(viewId);
        textView.setText(text);
    }

    public void setEditText(int viewId, String text) {
        EditText editText = (EditText)this.getView(viewId);
        editText.setText(text);
    }

    public void setTextColor(int viewId, int colorId) {
        TextView textView = (TextView)this.getView(viewId);
        textView.setTextColor(colorId);
    }

    public void setBackgroundResource(int viewId, int resId) {
        View view = this.getView(viewId);
        view.setBackgroundResource(resId);
    }

    public void setBackgroundColor(int viewId, int colorId) {
        View view = this.getView(viewId);
        view.setBackgroundColor(colorId);
    }

    public void setOnClickListener(int viewId, OnClickListener listener) {
        View view = this.getView(viewId);
        view.setOnClickListener(listener);
    }
}
