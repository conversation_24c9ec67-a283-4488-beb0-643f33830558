package com.zizhiguanjia.lib_base;

import android.app.Activity;
import android.app.Application;
import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.github.anzewei.parallaxbacklayout.ParallaxHelper;
import com.squareup.leakcanary.LeakCanary;
import com.tencent.mmkv.MMKV;
import com.wb.lib_image_loadcal.ImageManager;
import com.wb.lib_refresh_layout.SmartRefreshLayout;
import com.wb.lib_refresh_layout.api.DefaultRefreshFooterCreator;
import com.wb.lib_refresh_layout.api.DefaultRefreshHeaderCreator;
import com.wb.lib_refresh_layout.api.RefreshFooter;
import com.wb.lib_refresh_layout.api.RefreshHeader;
import com.wb.lib_refresh_layout.api.RefreshLayout;
import com.wb.lib_refresh_layout.footer.ClassicsFooter;
import com.wb.lib_refresh_layout.header.ClassicsHeader;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.listeners.ColdActivityLifecycleCallbacks;
import com.zizhiguanjia.lib_base.module.ModuleLifecycleConfig;

import androidx.multidex.MultiDex;
import me.jessyan.autosize.AutoSizeConfig;

public class DebugApplication extends Application {
    public static boolean tempCL=true;
    public static boolean isColdLaunch=true;
    public static Context mApplicationContext;
    private static final ColdActivityLifecycleCallbacks coldActivityLifecycleCallbacks = new ColdActivityLifecycleCallbacks();
    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(base);
    }
    @Override
    public void onCreate() {
        super.onCreate();
        mApplicationContext = this;
        if (AppUtils.isDebug()) {
            ARouter.openLog();     // 打印日志
            ARouter.openDebug();   // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
        }
        ARouter.init(this);
        MMKV.initialize(this);
        ImageManager.getInstance().init(this.getApplicationContext());
        AutoSizeConfig.getInstance().setLog(false).setUseDeviceSize(false);
        AppUtils.getApp().registerActivityLifecycleCallbacks(ParallaxHelper.getInstance());
        ModuleLifecycleConfig.getInstance().initModule(this);
        if (LeakCanary.isInAnalyzerProcess(this)) {
            return;
        }
        LogUtils.init().setDebug(BaseConfig.deBug);
//        LeakCanary.install(this);
        tempCL = true;
        this.registerActivityLifecycleCallbacks(coldActivityLifecycleCallbacks);
    }

    static {
        SmartRefreshLayout.setDefaultRefreshHeaderCreator(new DefaultRefreshHeaderCreator() {
            @Override
            public RefreshHeader createRefreshHeader(Context context, RefreshLayout layout) {
                layout.setPrimaryColorsId(R.color.resh_main_bg, R.color.resh_txt_bg);//全局设置主题颜色
                layout.setEnableOverScrollBounce(true);
                layout.setDragRate(0.4f);
                return new ClassicsHeader(context);
            }
        });
        SmartRefreshLayout.setDefaultRefreshFooterCreator(new DefaultRefreshFooterCreator() {
            @Override
            public RefreshFooter createRefreshFooter(Context context, RefreshLayout layout) {
                return new ClassicsFooter(context).setDrawableSize(20);
            }
        });
    }

    public static Activity getCurrentActivity() {
        return coldActivityLifecycleCallbacks.currentActivity;
    }

}