package com.zizhiguanjia.lib_base.emums;

/**
 * 功能作用：metaData数据key列表
 * 初始注释时间： 2024/1/2 12:24
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public enum MetaDataKeyEnum {
    /**
     * 应用包名
     */
    APPLICATION_ID("applicationIds"),
    /**
     * 平台渠道
     */
    APP_SEND_PLARFORM_CODE("APP_SEND_PLARFORM_CODE"),
    /**
     * 应用图标
     */
    APP_ICON("APP_ICON"),
    /**
     * 应用目标标记名称
     */
    TARGET_NAME("TARGET_NAME"),
    /**
     * 分享的图标
     */
    SHARE_IMAGE("SHARE_IMAGE"),
    /**
     * 微信AppId
     */
    WECHAT_APP_ID("WECHAT_APP_ID"),
    /**
     * 微信appSecret
     */
    WECHAT_APP_SECRET("WECHAT_APP_SECRET"),
    ;

    private final String mAgreementSource;

    MetaDataKeyEnum(String agreementSource) {
        mAgreementSource = agreementSource;
    }

    public String getAgreementSource() {
        return mAgreementSource;
    }
}
