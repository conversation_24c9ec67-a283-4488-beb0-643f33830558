package com.zizhiguanjia.lib_base.emums;

/**
 * 功能作用：支付类型枚举
 * 初始注释时间： 2024/7/23 10:27
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public enum PayTypeEnum {
    /**
     * 学习卡
     */
    LEARNING_CARD("25");

    private final String cardType;

    PayTypeEnum(String cardType) {
        this.cardType = cardType;
    }

    public String getCardType() {
        return cardType;
    }
}
