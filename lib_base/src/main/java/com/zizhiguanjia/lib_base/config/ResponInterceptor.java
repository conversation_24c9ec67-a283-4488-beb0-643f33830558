package com.zizhiguanjia.lib_base.config;

import com.alipay.tscenter.biz.rpc.vkeydfp.result.BaseResult;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.global.TokenInvalideException;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.httphelper.BaseHelper;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.UserMsgTypeConfig;
import com.zizhiguanjia.lib_base.utils.GZIPUtils;

import org.json.JSONObject;

import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.Charset;
import java.util.Map;
import java.util.Set;

import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;

public class ResponInterceptor implements Interceptor {
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request originalRequest = chain.request();
        Response response = chain.proceed(originalRequest);
        if (response.code() == 200) {
            MediaType mediaType = response.body().contentType();
            byte[] data = response.body().bytes();
            if (GZIPUtils.isGzip(response.headers())) {
                //请求头显示有gzip，需要解压
                data = GZIPUtils.uncompress(data);
            }
            return response.newBuilder()
                    .body(ResponseBody.create(mediaType, data))
                    .build();
        } else if (response.code() == 403 || response.code() == 401) {
            LogUtils.e("AppToken过期");
            MainThreadUtils.post(new Runnable() {
                @Override
                public void run() {
                    Bus.post(new MsgEvent(AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT));
                }
            });
        } else if (response.code() == 402) {
            return restBuild(chain,originalRequest,response);
        }
        return response;
    }
    private Response restBuild(Chain chain,Request originalRequest,Response response){
        String token = getNewToken();
        if (StringUtils.isEmpty(token)) {
            return response;
        } else {
            Response response1= null;
            try {
                response1 = getResponse2(chain, originalRequest,response, token);
                if (response1.code() == 200) {
                    MediaType mediaType = response1.body().contentType();
                    byte[] data = response1.body().bytes();
                    if (GZIPUtils.isGzip(response.headers())) {
                        //请求头显示有gzip，需要解压
                        data = GZIPUtils.uncompress(data);
                    }
                    return response1.newBuilder()
                            .body(ResponseBody.create(mediaType, data))
                            .build();
                }else if (response.code() == 403 || response.code() == 401) {
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            Bus.post(new MsgEvent(AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT));
                        }
                    });
                }else if (response.code() == 402) {
                    restBuild(chain,originalRequest,response1);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return response;
    }
    private Response getResponse2(Chain chain, Request request, Response response, String newToken) throws IOException {
        //重新请求
        Request.Builder builder = chain.request().newBuilder();
        builder.removeHeader("token");
        String token = (newToken != null && !newToken.isEmpty()) ? newToken : BaseConfig.getRequestToken();
        if(token != null && !token.isEmpty()){
            builder.addHeader("token",token);
        }else {
            builder.addHeader("tokenErrorType", "1111");
        }
        String oaId = com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getOaIdInfo();
        if(StringUtils.isEmpty(oaId)){
            com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getImeiInfo(info -> builder.addHeader("oaid",info));
        }else {
            builder.addHeader("oaid",oaId);
        }
        response.body().close();
        return chain.proceed(builder.build());
    }

    private String getNewToken() {
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("AccessToken", BaseConfig.refreahToken);
        RequestBody multiBody = multiBuilder.build();
        try {
            Response response = BaseHelper.getInstance().getBaseData(BaseAPI.VERSION_DES + "/Api/User/GetAccessToken", multiBody).execute();
            if (response.code() == 200) {
                MediaType mediaType = response.body().contentType();
                byte[] data = response.body().bytes();
                ResponseBody responseBody = ResponseBody.create(mediaType, data); //response.body();
                BufferedSource source = responseBody.source();
                source.request(Long.MAX_VALUE); // Buffer the entire body.
                Buffer buffer = source.buffer();
                Charset charset = Charset.forName("UTF8");
                String bodyString = buffer.clone().readString(charset);
                Type jsonType = new TypeToken<BaseData<Map>>() {
                }.getType();
                BaseData<Map<String, String>> baseData = new Gson().fromJson(bodyString, jsonType);
                String token = baseData.getResult().get("AccessToken");
                if (StringUtils.isEmpty(token)) {
                    return "";
                } else {
                    UserHelper.updataToken(token);
                }
                return token;

            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }
}
