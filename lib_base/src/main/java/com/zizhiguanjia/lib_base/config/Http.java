package com.zizhiguanjia.lib_base.config;

import com.wb.lib_network.AbstractHttp;
import com.wb.lib_utils.AppUtils;
import com.zizhiguanjia.lib_base.config.BaseAPI;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Cache;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import retrofit2.Converter;

public class Http extends AbstractHttp {
    private volatile static Http http;
    public static Http getInstance() {
        if (http == null) {
            synchronized (Http.class) {
                if (http == null) {
                    http = new Http();
                }
            }
        }
        return http;
    }
    @Override
    protected String baseUrl() {
        return BaseAPI.Base_Url_Api;
    }
    @Override
    protected Iterable<Interceptor> interceptors() {
        final List<Interceptor> interceptorList = new ArrayList<>();
        interceptorList.add(new BaseUrlInterceptor());
        interceptorList.add(new ResponInterceptor());
        return interceptorList;

    }

}