package com.zizhiguanjia.lib_base.config;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@IntDef({ENVIRONMENT.DEVELOPMENT, ENVIRONMENT.TEST, ENVIRONMENT.PRODUCTION})
@Retention(RetentionPolicy.SOURCE)
public @interface ENVIRONMENT {
    //开发
    int DEVELOPMENT = 0;
    //测试
    int TEST = 1;
    //生成
    int PRODUCTION = 2;
}
