package com.zizhiguanjia.lib_base.config;


import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.StringUtils;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class BaseUrlInterceptor implements Interceptor {
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request original = chain.request();
        Request.Builder requestBuilder = original.newBuilder();
        requestBuilder
                .addHeader("platform", "android")
                .addHeader("deviceId", "dsada"+DeviceUtils.getAndroidId(AppUtils.getApp()))
                .addHeader("packages", DeviceUtils.getAppPackageName())
                .addHeader("version", DeviceUtils.getAppVersionName(AppUtils.getApp()))
                .addHeader("source", "1")
                .addHeader("majorPid", BaseConfig.MAJOR_PID+"")
                .addHeader("Content-Encoding", "gzip")
                .addHeader("areaId", StringUtils.isEmpty(BaseConfig.cityCode) ? "" : BaseConfig.cityCode)
                .addHeader("appChannel", com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getAppChannel(AppUtils.getApp()))
                .addHeader("majorId", StringUtils.isEmpty(BaseConfig.majId) ? "" : BaseConfig.majId);
        String token = BaseConfig.getRequestToken();
        if (StringUtils.isEmpty(token)) {
            requestBuilder.addHeader("tokenErrorType", "2222");
        } else {
            requestBuilder.addHeader("token", token);
        }
        String oaId = com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getOaIdInfo();
        if(StringUtils.isEmpty(oaId)){
            com.zizhiguanjia.lib_base.utils.AppUtils.getInstance().getImeiInfo(info -> requestBuilder.addHeader("oaid",info));
        }else {
            requestBuilder.addHeader("oaid",oaId);
        }
        Request request = requestBuilder.build();
        return chain.proceed(request);

    }
}