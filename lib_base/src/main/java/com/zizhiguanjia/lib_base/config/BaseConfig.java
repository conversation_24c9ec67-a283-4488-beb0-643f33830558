package com.zizhiguanjia.lib_base.config;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Environment;
import com.wb.lib_utils.AppUtils;
import com.zizhiguanjia.lib_base.db.AppDatabase;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.msgconfig.UserType;
import com.zizhiguanjia.lib_base.tb.TableUserInfo;

public class BaseConfig {
    private volatile static BaseConfig config;
    public static BaseConfig getInstance() {
        if (config == null) {
            synchronized (BaseConfig.class) {
                if (config == null) {
                    config = new BaseConfig();
                }
            }
        }
        return config;
    }
    //存储位置
    public static final String DCMI_PATH = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).getAbsolutePath();
    public static int ENVIRONMENT_TYPE = ENVIRONMENT.PRODUCTION;
    public static boolean deBug=false;
    public static String WX_APP_ID = "";
    public static String WX_WAIT_ORDER = "";
    public static  int MAX_EXAM_PAGE=10;
    public static int MAX_POST_EXAM=5;
    public static boolean updateView=false;
    public static String SUCCESS_CODE="200";
    public static String UNBOUND_CODE="206";
    public static String majId="1101";
    public static long dialogTime=-1;
    public static String cityCode="-1";
    public static boolean finshPage=false;
    public static boolean APP_RESUM=false;
    private static String token="";
    public static  String source="1";
    public static  String clent="2";
    public static String refreahToken="";
    public static String messageId="";
    public static  String channelUID="";
    public static boolean paySuccessAutoClose=true;
    public static  String couponid="";
    public static int payWaitClass=-1;
    public static boolean payToast=false;
    public static boolean showCouponState=false;
    public static int APP_UPDATE_STATE=0;
    public static int APP_EXAM_DATE_STATE=0;
    public static int APP_COUPON_STATE=0;
    public static int MAJOR_PID=1100;
    public static int ORDER_BIND_TYPE;
    public static boolean isOpenLearnDialog=false;
    public static  boolean isPaySuccessDialog=false;
    public static int IsExamine=0;
    public static boolean currentPageMain=false;
    public  static  String getHttpBaseUrl(){
        if(ENVIRONMENT_TYPE==ENVIRONMENT.TEST){
            return "https://api.zizhiguanjia.cn/";
        }else if(ENVIRONMENT_TYPE==ENVIRONMENT.DEVELOPMENT){
            return "https://api.zizhiguanjia.cn/";
        }else if(ENVIRONMENT_TYPE==ENVIRONMENT.PRODUCTION){
            return "https://api.zizhiguanjia.cn/";
        }else {
            return "https://api.zizhiguanjia.cn/";
        }
    }
    public  static  String getHttpWebUrl(){
        if(ENVIRONMENT_TYPE==ENVIRONMENT.TEST){
            return "http://www.zizhiguanjia.cn/";
        }else if(ENVIRONMENT_TYPE==ENVIRONMENT.DEVELOPMENT){
            return "http://www.zizhiguanjia.cn/";
        }else if(ENVIRONMENT_TYPE==ENVIRONMENT.PRODUCTION){
            return "http://www.zizhiguanjia.cn/";
        }else {
            return "http://www.zizhiguanjia.cn/";
        }
    }

    public static String getRequestToken() {
        //原有逻辑迁移，同时BaseConfig.token为原有逻辑设置
        if (BaseConfig.token == null || BaseConfig.token.isEmpty()) {
            String account = AccountHelper.getCurrentLoginAccount();
            if (!account.equals(String.valueOf(UserType.USER_TYPE_VISITOR))) {
                TableUserInfo tableUserInfo = AppDatabase.getInstance().userInfoDao().getUserInfoByAccount(account);
                if (tableUserInfo != null) {
                    if (tableUserInfo.getUserType() != UserType.USER_TYPE_VISITOR) {
                        BaseConfig.token = tableUserInfo.getAccessToken();
                    }
                }
            }
        }
        //原有为空的情况下新逻辑赋值
        if (BaseConfig.token == null || BaseConfig.token.isEmpty()) {
            SharedPreferences preferences = AppUtils.getApp().getSharedPreferences("requestToken", Context.MODE_PRIVATE);
            BaseConfig.token = preferences.getString("requestToken", BaseConfig.token);
        }
        return BaseConfig.token;
    }

    public static void setRequestToken(String token) {
        BaseConfig.token = token;
        SharedPreferences preferences = AppUtils.getApp().getSharedPreferences("requestToken", Context.MODE_PRIVATE);
        preferences.edit().putString("requestToken", token).apply();
    }
}
