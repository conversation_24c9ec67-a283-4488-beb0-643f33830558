package com.zizhiguanjia.lib_base.helper;

import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.LoginRouterPath;
import com.zizhiguanjia.lib_base.service.LoginService;

public class LoginHelper {

    private static final LoginService SERVICE =
            (LoginService) ARouter.getInstance().build(LoginRouterPath.SERVICE).navigation();

    public static void start(Context context) {
        SERVICE.start(context);
    }

    public static IFragment mainPage(Context context) {
        return SERVICE.mainPage(context);
    }

    public static IFragment showPage(Context context) {
        return SERVICE.showPage(context);
    }

    public static boolean isLogin() {
        return SERVICE.isLogin();
    }
    public static void loginOut() {
        SERVICE.loginOut();
    }
}
