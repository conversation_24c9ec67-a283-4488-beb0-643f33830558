package com.zizhiguanjia.lib_base.helper;

import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.AccountRouterPath;
import com.zizhiguanjia.lib_base.service.AccountServie;

public class AccountHelper {
    private static final AccountServie SERVICE =
            (AccountServie) ARouter.getInstance().build(AccountRouterPath.SERVICE).navigation();
    public static void start(Context context){
         SERVICE.start(context);
    }
    public static IFragment showUpdataPhone(){
        return SERVICE.showUpdataPhone();
    }
    public static IFragment showAccountLogin(){
        return SERVICE.showAccountLogin();
    }
    public static IFragment goToLogOffAccount(){
        return SERVICE.logOffAccountFragment();
    }
    public static  boolean checkLoginState(){
        return SERVICE.checkLoginState();
    }
    public static void exitAccount(){
        SERVICE.exitAccount();
    }
    public static void initAccountInfo(){
        SERVICE.initAccountInfo();
    }
    public static void successLoginAccount(String json){
        SERVICE.successLoginAccount(json);
    }
    public static String getCurrentLoginAccount(){
        return SERVICE.getCurrentLoginAccount();
    }
    public static boolean isUserLogin(){
        return SERVICE.isUserLogin();
    }
    public static String getUserId(){
        return SERVICE.getUserId();
    }
    public static void exitSaveAccount(){
        SERVICE.exitSaveAccount();
    }
    public static void exitAccountPostData(){
        SERVICE.exitAccountPostData();
    }
    public static boolean loginSuperintendent(){
        return SERVICE.loginSuperintendent();
    }
    /**
     * 登录验证
     * @param oneKeyLoginCheckDoubleClick 一键登录是否验证二次点击，防止其他地方的点击操作加了二次验证导致这里无法触发弹窗
     * @return true-已登录
     */
    public static boolean loginSuperintendent(boolean oneKeyLoginCheckDoubleClick){
        return SERVICE.loginSuperintendent(oneKeyLoginCheckDoubleClick);
    }
    public static void openStaticAccountLock(int type, String token){
        SERVICE.openStaticAccountLock(type,token);
    }
}
