package com.zizhiguanjia.lib_base.helper;

import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.AccountRouterPath;
import com.zizhiguanjia.lib_base.constants.ConfigRouterPath;
import com.zizhiguanjia.lib_base.service.AccountServie;
import com.zizhiguanjia.lib_base.service.ConfigService;

public class ConfigHelper {
    private static final ConfigService SERVICE =
            (ConfigService) ARouter.getInstance().build(ConfigRouterPath.SERVICE).navigation();
    public static void initConfig(){
        SERVICE.initConfig();
    }
    public static String getSplanTimeDown(){
        return SERVICE.getSplanTimeDown();
    }
    public static String getDafalutCertificata(){
        return SERVICE.getDafalutCertificata();
    }
    public static String getPayUrl(){
        return SERVICE.getPayUrl();
    }
    public static String getKfUrl(){
        return SERVICE.getKfUrl();
    }
    public static String getKfTel(){
        return SERVICE.getKfTel();
    }
    public static int getAlwaysShow() {
        return SERVICE.getAlwaysShow();
    }

    public static String getCopyRight() {
        return SERVICE.getCopyRight();
    }
    public static boolean getAppUpdateType(){
        return SERVICE.getAppUpdateType();
    }
    public static String getWechatOpenUrl(){
        return SERVICE.getWechatOpenUrl();
    }
    public static String getCarshConfig(){
        return SERVICE.getCarshConfig();
    }
    public static int getReminderType(){
        return SERVICE.getReminderType();
    }
}
