package com.zizhiguanjia.lib_base.helper;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.constants.ExamRouterPath;
import com.zizhiguanjia.lib_base.service.ExamService;

public class ExamHelper {
//    private static final ExamService SERVICE =
//            (ExamService) ARouter.getInstance().build(ExamRouterPath.SERVICE).navigation();
//    public static IFragment showErrorListPage(){
//        return SERVICE.showErrorListPage();
//    }
//    public static  IFragment showSaveListPage(){
//        return SERVICE.showSaveListPage();
//    }
//    public static  IFragment showCommonListPage(){
//        return SERVICE.showCommonListPage();
//    }
//    public static IFragment showChapterMainPage(){
//        return SERVICE.showChapterMainPage();
//    }
//    public static IFragment showAutoTestExamPage(){
//        return SERVICE.showAutoTestExamPage();
//    }
//    public static   IFragment showExamView(){
//        return SERVICE.showExamView();
//    }
}
