package com.zizhiguanjia.lib_base.helper;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.ListRouterPath;
import com.zizhiguanjia.lib_base.service.ListServie;

public class ListHelper {
    private static final ListServie SERVICE =
            (ListServie) ARouter.getInstance().build(ListRouterPath.SERVICE).navigation();
    public static IFragment toPageExamResult(){
        return SERVICE.toPageExamResult();
    }
    public static IFragment toPageExamList(){
        return SERVICE.toPageExamList();
    }
    public static IFragment toPageExamDes(){
        return SERVICE.toPageExamDes();
    }
    public static IFragment toPageDocMajors(){
        return SERVICE.toPageDocMajors();
    }
    public static IFragment toPageExamAuto(){
        return SERVICE.toPageExamAuto();
    }
    public static IFragment showCommonListPage(){
        return SERVICE.showCommonListPage();
    }
}
