package com.zizhiguanjia.lib_base.helper;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.constants.SdkRouterPath;
import com.zizhiguanjia.lib_base.listeners.AutoOneKeyLoginListener;
import com.zizhiguanjia.lib_base.listeners.JpushAuthorizeListener;
import com.zizhiguanjia.lib_base.service.SdkService;
import com.zizhiguanjia.lib_base.utils.ThirdPlatformOptionsUtils;

import retrofit2.http.PUT;

public class SdkHelper {
    private static final SdkService SERVICE =
            (SdkService) ARouter.getInstance().build(SdkRouterPath.SERVICE).navigation();
    public static void initJpushConfig(boolean debug){
        SERVICE.initJpushConfig(debug);
    }
    public static void startThridLogin(int name, ThirdPlatformOptionsUtils.ThirdPlatformOptionsCallback callback){
        SERVICE.startThridLogin(name,callback);
    }
    public static  void startOneKeylogin(AutoOneKeyLoginListener autoOneKeyLoginListener ){
        SERVICE.startOneKeylogin(autoOneKeyLoginListener);
    }
    public static  void preOnekeyLogin(){
        SERVICE.preOnekeyLogin();
    }
    public static  void removeAuthorize(int name, JpushAuthorizeListener jpushAuthorizeListener){
        SERVICE.removeAuthorize(name,jpushAuthorizeListener);
    }
    public  static void shaperWx(Activity activity,String questionId){
        SERVICE.shaperWx(activity,questionId);
    }
    public static boolean JpushOneKeyLoginInitConfig(){
        return SERVICE.JpushOneKeyLoginInitConfig();
    }
    public static void initHumSdk(Context context){
        SERVICE.initHumSdk( context);
    }
    public static void onPauseDou(Context context){
        SERVICE.onPauseDou( context);
    }
    public static void onResumeDou(Context context){
        SERVICE.onResumeDou( context);
    }
    public static void initOpenInstall(Context context){
        SERVICE.initOpenInstall(context);
    }
    public static void getWakeUp(Intent intent,Context mContext){
        SERVICE.getWakeUp(intent,mContext);
    }
    public static  void getInstall(Context mContext){
        SERVICE.getInstall(mContext);
    }
    public static void registUserId(String uid){
        SERVICE.registUserId(uid);
    }
    public static void deleteAlias( String uid){
        SERVICE.deleteAlias(uid);
    }
    public static void userConfimYsyxJg(Context mContext,boolean comfig){
        SERVICE.userConfimYsyxJg(mContext,comfig);
    }
    public static void shareWechatByType(String url,int type){
        SERVICE.shareWechatByType(url,type);
    }
    public static void clearBadgeNum(){
        SERVICE.clearBadgeNum();
    }
    public static  void countJpushMessage(String messageId){
        SERVICE.countJpushMessage(messageId);
    }
    public static void  startSdkWebview(Context context,String extraStr,String msgId){
        SERVICE.startSdkWebview(context,extraStr,msgId);
    }
}
