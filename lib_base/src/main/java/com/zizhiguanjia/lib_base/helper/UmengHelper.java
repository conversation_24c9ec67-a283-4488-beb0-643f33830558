package com.zizhiguanjia.lib_base.helper;

import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.zizhiguanjia.lib_base.constants.SdkRouterPath;
import com.zizhiguanjia.lib_base.constants.UmengRouterPath;
import com.zizhiguanjia.lib_base.service.SdkService;
import com.zizhiguanjia.lib_base.service.UmengService;

import java.util.Map;

public class UmengHelper {
    private static final UmengService SERVICE =
            (UmengService) ARouter.getInstance().build(UmengRouterPath.SERVICE).navigation();
    public static  void preInitUmeng(String channel){
        SERVICE.preInitUmeng(channel);
    }
    public static void initUmeng(String channel){
        SERVICE.initUmeng(channel);
    }
    public static void onKillProcess(){
        SERVICE.onKillProcess();
    }
    public static void onEventObject(Context context, String eventID, Map<String,String> time ){
        SERVICE.onEventObject(context,eventID,time);
    }
}
