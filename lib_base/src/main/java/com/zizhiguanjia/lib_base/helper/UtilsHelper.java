package com.zizhiguanjia.lib_base.helper;

import android.app.Activity;
import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.PayRouterPath;
import com.zizhiguanjia.lib_base.constants.UtilsRouthPath;
import com.zizhiguanjia.lib_base.listeners.PayInfoListener;
import com.zizhiguanjia.lib_base.service.PayService;
import com.zizhiguanjia.lib_base.service.UtilsService;

public class UtilsHelper {
    private static final UtilsService SERVICE =
            (UtilsService) ARouter.getInstance().build(UtilsRouthPath.SERVICE).navigation();
    public static  void openMarkets(Activity activity){
        SERVICE.openMarkets(activity);
    }
}
