package com.zizhiguanjia.lib_base.helper;

import android.content.Context;
import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_utils.AppUtils;
import com.zizhiguanjia.lib_base.constants.HomeRoutherPath;
import com.zizhiguanjia.lib_base.service.HomeService;

public class HomeHelper {
    private static final HomeService SERVICE =
            (HomeService) ARouter.getInstance().build(HomeRoutherPath.SERVICE).navigation();
    public static void start(Context context){
        SERVICE.start(context);
    }
    public static IFragment startMessageFragment(){
        return SERVICE.startMessageFragment();
    }
    public static void startHomeActivityByRoute(String router){
        SERVICE.startHomeActivityByRoute(router);
    }
    public static IFragment mainPage(){
        return SERVICE.mainPage(AppUtils.getApp());
    }
    public static void start(Context context, String extraStr, String msgId){
        SERVICE.start(AppUtils.getApp(),extraStr,msgId);
    }
    public static IFragment gotoAvd(){
        return SERVICE.gotoAvd();
    }
}
