package com.zizhiguanjia.lib_base.helper;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.FixBugRouterPath;
import com.zizhiguanjia.lib_base.service.FixBugService;

public class FixBugHelper {
    private static final FixBugService SERVICE =
            (FixBugService) ARouter.getInstance().build(FixBugRouterPath.SERVICE).navigation();
    public static IFragment showFixBugMain(){
        return SERVICE.showFixBugMain();
    }
}
