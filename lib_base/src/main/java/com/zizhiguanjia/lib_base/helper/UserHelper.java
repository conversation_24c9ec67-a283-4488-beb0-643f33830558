package com.zizhiguanjia.lib_base.helper;

import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.UserRouterPath;
import com.zizhiguanjia.lib_base.service.UserService;
import com.zizhiguanjia.lib_base.tb.TableUserInfo;

public class UserHelper {
    private static final UserService SERVICE =
            (UserService) ARouter.getInstance().build(UserRouterPath.SERVICE).navigation();
    public static int getUserTypeByAccount(String account){
        return SERVICE.getUserTypeByAccount(account);
    }
    public static void addUserInfo(String json){
        SERVICE.addUserInfo(json);
    }
    public static void exitUser(){
        SERVICE.exitUser();
    }
    public static boolean isBecomeVip(){
        return SERVICE.isBecomeVip();
    }
    public static void passVip(){
        SERVICE.passVip();
    }
    public static void iniAppToken(){
        SERVICE.iniAppToken();
    }
    public static void updataUserVipState(int type){
        SERVICE.updataUserVipState(type);
    }
    public static IFragment mainPage(Context context){
        return SERVICE.mainPage(context);
    }
    public static void updataToken(String token){
        SERVICE.updataToken(token);
    }
    public static TableUserInfo getUserInfo(){
        return SERVICE.getUserInfo();
    }
    public static void start(Context context){
        SERVICE.start(context);
    }
}
