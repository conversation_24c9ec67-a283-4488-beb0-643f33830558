package com.zizhiguanjia.lib_base.helper;

import com.alibaba.android.arouter.launcher.ARouter;
import com.zizhiguanjia.lib_base.constants.VideoRouterPath;
import com.zizhiguanjia.lib_base.service.VideoService;

public class VideoHelper {
    private static final VideoService SERVICE =
            (VideoService) ARouter.getInstance().build(VideoRouterPath.SERVICE).navigation();

    public static void showVideoActivity(String type) {
        SERVICE.showVideoActivity(type);
    }
    public static void showVideoActivity(String type,String liveId) {
        SERVICE.showVideoActivity(type,liveId);
    }
}
