package com.zizhiguanjia.lib_base.helper;

import android.app.Activity;
import android.content.Context;
import android.view.View;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_pop.code.BasePopupView;
import com.xuexiang.xupdate.entity.UpdateEntity;
import com.xuexiang.xupdate.proxy.IUpdateProxy;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.bean.OrderDialogBean;
import com.zizhiguanjia.lib_base.constants.MessageRouterPath;
import com.zizhiguanjia.lib_base.listeners.ExamGuilderListener;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.listeners.HomeFootListener;
import com.zizhiguanjia.lib_base.listeners.ILoginVipTs;
import com.zizhiguanjia.lib_base.listeners.MessageSuccessPayListener;
import com.zizhiguanjia.lib_base.listeners.OnChoiceTimeListenter;
import com.zizhiguanjia.lib_base.listeners.SpeedListener;
import com.zizhiguanjia.lib_base.service.MessageService;

public class MessageHelper {
    private static final MessageService SERVICE =
            (MessageService) ARouter.getInstance().build(MessageRouterPath.SERVICE).navigation();
//    public static void openPaySuccessDialog(Activity activity, PaySuccessListen paySuccessListen){
//        SERVICE.openPaySuccessDialog(activity,paySuccessListen);
//    }
    public static void initPaySuccessDialog(Activity activity){
        SERVICE.initPaySuccessDialog(activity);
    }
    public static void openNoPremissBuyDialog(Activity activity,boolean ised,String payRouthParams){
        SERVICE.openNoPremissBuyDialog(activity,ised,payRouthParams);
    }
    public static void start(Context context){
        SERVICE.start(context);
    }
    public static void  openUpdateExam(String title,String subTitle,String contentTitle,String Content,String subContent,String ids){
        SERVICE.openUpdateExam(title, subTitle, contentTitle, Content, subContent, ids);
    }
    public static void openSpeedDialog(Context activity, View attachView,SpeedListener speedListener){
        SERVICE.openSpeedDialog(activity,attachView,speedListener);
    }
    public static void openSpeedFullDialog(Context activity,View attachView, SpeedListener speedListener){
        SERVICE.openSpeedFullDialog(activity,attachView,speedListener);
    }
    public static void  startCoupion(String url){
        SERVICE.startCoupion(url);
    }
    public static void restCoupionState(){
        SERVICE.restCoupionState();
    }
    public static void openGeneralCentDialog(Activity activity, String title, String msg, String cancel, String confim, boolean isHideCancel, boolean isBack, GeneralDialogListener generalDialogListener){
        SERVICE.openGeneralCentDialog(activity,title,msg,cancel,confim,isHideCancel,isBack,generalDialogListener);
    }
    public static void openGeneralCentDialog(Activity activity, String title, String msg, String cancel, String confim, boolean isHideCancel, boolean isBack, GeneralDialogListener generalDialogListener,int maxW,int maxH){
        SERVICE.openGeneralCentDialog(activity,title,msg,cancel,confim,isHideCancel,isBack,generalDialogListener,maxW,maxH);
    }
    public static void openBottomListDialog(Activity activity,String title,String msg,String cancel,String confirm,boolean isHind,boolean isBack,GeneralDialogListener generalDialogListener){
        SERVICE.openBottomListDialog(activity, title, msg, cancel, confirm, isHind, isBack, generalDialogListener);
    }
    public static void openBottomListDialog(Activity activity,String title,String msg,String cancel,String confirm,boolean isHind,boolean isBack,GeneralDialogListener generalDialogListener,int maxW,int maxH){
        SERVICE.openBottomListDialog(activity, title, msg, cancel, confirm, isHind, isBack, generalDialogListener, maxW, maxH);
    }
    public static void permissionsTips(Activity activity ,String title,String msg,String confim){
        SERVICE.permissionsTips(activity, title, msg, confim);
    }
    public static BasePopupView openAccountPrivacyTipic(Activity activity, View mView){
        return SERVICE.openAccountPrivacyTipic(activity, mView);
    }
    public static void  openServiceTips(Activity activity){
        SERVICE.openServiceTips(activity);
    }
    public static void  openAppUpdataVersionTips(Activity activity, IUpdateProxy updateProxy, UpdateEntity updateEntity){
        SERVICE.openAppUpdataVersionTips(activity, updateProxy, updateEntity);
    }
    public static void  openGuilder(Activity activity, int type, ExamGuilderListener examGuilderListener){
        SERVICE.openGuilder(activity, type, examGuilderListener);
    }
    public static void orderOpenDialog(OrderDialogBean a){
        SERVICE.orderOpenDialog(a);
    }
    public static void nextDialogTask(){
        SERVICE.nextDialogTask();
    }
    public static void openTimeChoiceDialog(Activity activity, OnChoiceTimeListenter onChoiceTimeListenter){
        SERVICE.openTimeChoiceDialog(activity,onChoiceTimeListenter);
    }
    public static void openCusomerDialog(Activity activity,String routh){
        SERVICE.openCusomerDialog(activity,routh);
    }
    public static void openLearningPlanDialog(Activity activity,String time,int type){
        SERVICE.openLearningPlanDialog(activity,time,type);
    }
    public static void openGotoSubject(MessageSuccessPayListener messageSuccessPayListener,Activity activity){
        SERVICE.openGotoSubjectSelect(messageSuccessPayListener,activity);
    }
    public static void openAccountLogOffSwithTs(Activity activity,String userToken){
        SERVICE.openAccountLogOffSwithTs( activity,userToken);
    }
    public static void openAccountLock(Activity activity){
        SERVICE.openAccountLock( activity);
    }
    public static void openAccountVipTs(Activity activity, String certificateName, String cityName, String phone, String token, ILoginVipTs iLoginVipTs){
        SERVICE.openAccountVipTs(activity,certificateName,cityName,phone,token,iLoginVipTs);
    }
    public static void openHomeCertificateUpdata(Activity activity){
        SERVICE.openHomeCertificateUpdata(activity);
    }
    public static void openSelectSubjectOrCity(Activity activity){
        SERVICE.openSelectSubjectOrCity(activity);
    }
    public static void openOtherPayTs(Activity activity,String json){
        SERVICE.openOtherPayTs(activity,json);
    }
    public static void  openOffOnileService(Activity activity, String title, String subT, String url, HomeFootListener listener){
        SERVICE.openOffOnileService(activity,title,subT,url,listener);
    }
    public static void openPayFailServer(Activity activity, BaseFragment baseFragment){
        SERVICE.openPayFailServer(activity,baseFragment);
    }
}
