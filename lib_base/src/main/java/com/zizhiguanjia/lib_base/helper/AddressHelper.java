package com.zizhiguanjia.lib_base.helper;

import android.content.Context;
import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.AccountRouterPath;
import com.zizhiguanjia.lib_base.constants.AddressRouterPath;
import com.zizhiguanjia.lib_base.service.AccountServie;
import com.zizhiguanjia.lib_base.service.AddressService;

public class AddressHelper {
    private static final AddressService SERVICE =
            (AddressService) ARouter.getInstance().build(AddressRouterPath.SERVICE).navigation();
    public static void start(Context context){
        SERVICE.start(context);
    }
    public static IFragment mainPage(Context context){
        return SERVICE.mainPage(context);
    }
}