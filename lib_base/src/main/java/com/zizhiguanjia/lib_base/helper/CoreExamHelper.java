package com.zizhiguanjia.lib_base.helper;

import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.CoreExamRouterPath;
import com.zizhiguanjia.lib_base.service.CoreExamService;

public class CoreExamHelper {
    private static final CoreExamService SERVICE =
            (CoreExamService) ARouter.getInstance().build(CoreExamRouterPath.SERVICE).navigation();
   public static IFragment mainPage(Context context){
        return SERVICE.mainPage(context);
    }
   public static IFragment courseDetailChildFinish(Context context){
        return SERVICE.courseDetailChildFinish(context);
    }
}
