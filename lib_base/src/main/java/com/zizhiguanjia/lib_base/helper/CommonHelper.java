package com.zizhiguanjia.lib_base.helper;

import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.CommonRouterPath;
import com.zizhiguanjia.lib_base.constants.DataRouterPath;
import com.zizhiguanjia.lib_base.service.CommonService;
import com.zizhiguanjia.lib_base.service.DataService;

public class CommonHelper {
    private static final CommonService SERVICE =
            (CommonService) ARouter.getInstance().build(CommonRouterPath.SERVICE).navigation();
    public static IFragment showCommonWeb(){
       return SERVICE.showCommonWebView();
    }
    public static IFragment showCommonWebViewHtmlTextLoad(){
        return SERVICE.showCommonWebViewHtmlTextLoad();
    }
    public static IFragment showSetFragmentView(){
        return SERVICE.showSetFragmentView();
    }
    public static IFragment showFaceBackFragmentView(){
        return SERVICE.showFaceBackFragmentView();
    }
    public static void upDataApp(Context mContext){
        SERVICE.upDataApp(mContext);
    }
    public static void start(Context context){
        SERVICE.start(context);
    }
    public static void showCommonWebView(String routh,int payType,String url){
        SERVICE.showCommonWebView(routh,payType,url);
    }
    public static IFragment showPermissView(){
        return SERVICE.showPermissView();
    }
}
