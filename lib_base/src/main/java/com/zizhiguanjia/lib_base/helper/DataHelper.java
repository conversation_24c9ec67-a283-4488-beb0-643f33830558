package com.zizhiguanjia.lib_base.helper;
import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.constants.DataRouterPath;
import com.zizhiguanjia.lib_base.listeners.ExamDataUploadListener;
import com.zizhiguanjia.lib_base.service.DataService;

public class DataHelper {
    private static final DataService SERVICE =
            (DataService) ARouter.getInstance().build(DataRouterPath.SERVICE).navigation();
    public static boolean checkUserPremiss(){
        return SERVICE.checkUserPremiss();
    }
    public static boolean setUserPremiss(boolean userPremiss){
        return SERVICE.setUserPremiss(userPremiss);
    }
    public static String getExamSheetDataList(String json){
        return SERVICE.getExamSheetDataList(json);
    }
    public static boolean uploadRecolder(String json){
        return SERVICE.uploadRecolder(json);
    }
    public static void initExamConfig(String paperType,String paperValue,String currentTime){
        SERVICE.initExamConfig(paperType,paperValue,currentTime);
    }
    public static void userSubmit(boolean isSubmit){
        SERVICE.userSubmit(isSubmit);
    }
    public static  void userStartHandPaper(ExamDataUploadListener examDataUploadListener){
        SERVICE.userStartHandPaper(examDataUploadListener);
    }
    public static  void postAllQuestion(int index){
        LogUtils.e("看看几次----"+index);
        SERVICE.postAllQuestion();
    }
    public static  void uploadVideoCountData(String json){
        SERVICE.uploadVideoCountData(json);
    }
    public static  void uploadOrderState(String num){
        SERVICE.uploadOrderState(num);
    }
}
