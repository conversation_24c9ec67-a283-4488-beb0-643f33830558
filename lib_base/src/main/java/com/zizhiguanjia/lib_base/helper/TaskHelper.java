package com.zizhiguanjia.lib_base.helper;
 
import com.alibaba.android.arouter.launcher.ARouter;
import com.zizhiguanjia.lib_base.constants.TaskRouterPath;
import com.zizhiguanjia.lib_base.listeners.CoupoinValidationListenter;
import com.zizhiguanjia.lib_base.service.TaskServie;
 
public class TaskHelper {
    private static final TaskServie SERVICE =
            (TaskServie) ARouter.getInstance().build(TaskRouterPath.SERVICE).navigation();
    public static void detectionCoupon(CoupoinValidationListenter coupoinValidationListenter){
        SERVICE.detectionCoupon(coupoinValidationListenter);
    }
}