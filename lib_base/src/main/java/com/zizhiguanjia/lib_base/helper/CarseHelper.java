package com.zizhiguanjia.lib_base.helper;

import com.alibaba.android.arouter.launcher.ARouter;
import com.zizhiguanjia.lib_base.constants.CarseRouthPath;
import com.zizhiguanjia.lib_base.service.CarseServie;

public class CarseHelper {
    private static final CarseServie SERVICE =
            (CarseServie) ARouter.getInstance().build(CarseRouthPath.SERVICE).navigation();
    public static void  initCarse(){
        SERVICE.initCarse();
    }
    public static void  uploadCarse(){
        SERVICE.checkUploadCarse();
    }
}
