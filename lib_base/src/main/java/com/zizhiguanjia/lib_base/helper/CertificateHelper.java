package com.zizhiguanjia.lib_base.helper;

import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.constants.CertificateRouterPath;
import com.zizhiguanjia.lib_base.listeners.CertificateDataListener;
import com.zizhiguanjia.lib_base.listeners.CertificateJsonByAddressListener;
import com.zizhiguanjia.lib_base.service.CertificateService;
import com.zizhiguanjia.lib_base.tb.TableCertificate;

import java.util.Map;

public class CertificateHelper {
    private static final CertificateService SERVICE =
            (CertificateService) ARouter.getInstance().build(CertificateRouterPath.SERVICE).navigation();
    public static String  initCertificate(boolean loginSuccess){
        return SERVICE.initCertificate(loginSuccess);
    }
    public static String exitAccountCertificate(){
        return SERVICE.exitAccountCertificate();
    }
    public static void updateCertificate(String cityCode,String cityName,String majId,String majName,int type,String dev){
        LogUtils.e("------>>>>更新了----->>>>"+type);
        SERVICE.updateCertificate(cityCode, cityName, majId, majName,dev);
    }
    public static TableCertificate getUserCertificate(String account){
        return SERVICE.getUserCertificate(account);
    }
    public static IFragment mainPage(Context context){
        return SERVICE.mainPage(context);
    }
    public static IFragment startChoseCertificateByAddress(){
        return SERVICE.startChoseCertificateByAddress();
    }
    public static void startCertificateActivity(String addressName, String addressId, boolean tgAddress){
         SERVICE.startCertificateActivity(addressName,addressId,tgAddress);
    }
    public static boolean userChoiceCertificate(){
        return SERVICE.userChoiceCertificate();
    }
    public static boolean getCertificateState(String account){
        return SERVICE.getCertificateState(account);
    }
    public static  void updataCertificate(TableCertificate certificateConfigBean){
        SERVICE.updataCertificate(certificateConfigBean);
    }
    public static void getCertificateJsonByAddress(String areaId, CertificateJsonByAddressListener certificateJsonByAddressListener){
        SERVICE.getCertificateJsonByAddress(areaId,certificateJsonByAddressListener);
    }
    public static String getCertificateDes(){
        return SERVICE.getCertificateDes();
    }
    public static Map<String,String> getCurrentCertificate(){
        return SERVICE.getCurrentCertificate();
    }
    public static IFragment startChoseCertificateZzByAddress(){
        return SERVICE.startChoseCertificateZzByAddress();
    }
    public static boolean isAqy(){
        return SERVICE.isAqy();
    }
    public static String getCurrentCertificateAddressName(){
        return SERVICE.getCurrentCertificateAddressName();
    }
    public static boolean getCurrentCertificateDialog(){
        return SERVICE.payTypeDialog();
    }
}
