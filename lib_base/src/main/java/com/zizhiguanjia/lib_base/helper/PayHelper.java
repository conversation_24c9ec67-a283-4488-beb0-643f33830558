package com.zizhiguanjia.lib_base.helper;

import android.app.Activity;
import android.content.Context;
import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.base.IARouterService;
import com.zizhiguanjia.lib_base.constants.PayRouterPath;
import com.zizhiguanjia.lib_base.listeners.PayInfoListener;
import com.zizhiguanjia.lib_base.service.PayService;

public class PayHelper {
    private static final PayService SERVICE =
            (PayService) ARouter.getInstance().build(PayRouterPath.SERVICE).navigation();
    public static IFragment toPageMain(Context context){
        return SERVICE.mainPage(context);
    }
    public static void getPayInfoByMajId(String majId, PayInfoListener payInfoListener){
        SERVICE.getPayInfoByMajIds(majId,payInfoListener);
    }
    public static void payOrder(String goodId, Activity activity,String payRouthParams){
        SERVICE.payOrder(goodId,activity,payRouthParams);
    }
    public static void nativeToPay(Activity mActivity,String appId,String Noncestr,String PackageX,String Partnerid,String Sign,String Prepayid,String Timestamp){
        SERVICE.nativeToPay(mActivity,appId,Noncestr,PackageX,Partnerid,Sign,Prepayid,Timestamp);
    }
}
