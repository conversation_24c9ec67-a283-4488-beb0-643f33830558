package com.zizhiguanjia.lib_base.helper;

import android.content.Context;

import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.constants.AccountRouterPath;
import com.zizhiguanjia.lib_base.constants.PointRouterPath;
import com.zizhiguanjia.lib_base.service.AccountServie;
import com.zizhiguanjia.lib_base.service.PointService;

import java.util.Map;

public class PointHelper {
    private static final PointService SERVICE =
            (PointService) ARouter.getInstance().build(PointRouterPath.SERVICE).navigation();
    public static  void joinPointData(String key,boolean saveCache){
        SERVICE.joinPointData(key,saveCache);
    }
    public static void joinPointData(String key, Map<String,String> params, boolean saveCache){
        SERVICE.joinPointData(key,params,saveCache);
    }
    public static boolean isRegistPoinRouth(String routh){
        return SERVICE.isRegistPoinRouth(routh);
    }
    public static  void addPoinRouth(String key){
        SERVICE.addPoinRouth(key);
    }
    public static boolean checkPoinRouth(int routhType){
        return SERVICE.checkPoinRouth(routhType);
    }
    public static void clernPointRouth(int routhType){
        SERVICE.clernPointRouth(routhType);
    }
    public static void unRegistPoinRouth(String key,Map<String,String> params,boolean sendPost){
        SERVICE.unRegistPoinRouth(key,params,sendPost);
    }
    public static void unRegistPoinRouth(String key,boolean sendPost){
        SERVICE.unRegistPoinRouth(key,sendPost);
    }
    public static void cleanPaySuccessPoin(Map<String,String> params){
        SERVICE.cleanPaySuccessPoin(params);
    }
}
