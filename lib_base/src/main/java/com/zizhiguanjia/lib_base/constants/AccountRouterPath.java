package com.zizhiguanjia.lib_base.constants;

public interface AccountRouterPath {
    /**
     * 模块一级路径
     */
    String MAIN_ROUTE = "/account";

    String SERVICE = MAIN_ROUTE + "/service";

    String MAIN_FRAGMENT = MAIN_ROUTE + "/main_fragment";
    /**
     * 主Activity
     */
    String MAIN_ACTIVITY = MAIN_ROUTE + "/main_activity";
    String MAIN_LOCK_ACTIVITY = MAIN_ROUTE + "/main_lock_activity";
    String MAIN_UPDATE_PHONE = MAIN_ROUTE + "/main_updata_phone";
    String MAIN_BIND_PHONE = MAIN_ROUTE + "/main_bind_phone";
    String MAIN_VERLOGIN_PHONE = MAIN_ROUTE + "/main_verlogin_phone";
    String MAIN_LOGOFF_ACCOUNT = MAIN_ROUTE + "/main_logoff_phone";
}
