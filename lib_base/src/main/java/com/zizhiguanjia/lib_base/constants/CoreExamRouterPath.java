package com.zizhiguanjia.lib_base.constants;

public interface CoreExamRouterPath {
    /**
     * 模块一级路径
     */
    String MAIN_ROUTE = "/core";
    String SERVICE = MAIN_ROUTE + "/service";
    String MAIN_FRAGMENT = MAIN_ROUTE + "/main_fragment";
    String MAIN_ACTIVITY = MAIN_ROUTE + "/main_activity";
    String COURSE_DETAIL_CHILD_FINISH = MAIN_ROUTE + "/childFinish";
    // 考试结果Fragment
    String EXAM_RESULT_FRAGMENT = MAIN_ROUTE + "/exam_result";
    
    // 高频考题Fragment
    String HIGH_FREQUENCY_FRAGMENT = MAIN_ROUTE + "/high_frequency";
    // 摸底测评开始页Fragment
    String BASELINE_ASSESSMENT_FRAGMENT = MAIN_ROUTE + "/baseline_assessment";
}
