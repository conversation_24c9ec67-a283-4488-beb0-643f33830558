package com.zizhiguanjia.lib_base.constants;

public interface MainRouterPath {
    /**
     * 模块一级路径
     */
    String MAIN_ROUTE = "/main";
    String SERVICE = MAIN_ROUTE + "/service";
    String MAIN_FRAGMENT = MAIN_ROUTE + "/main_fragment";
    String MAIN_FLUTTER_FRAGMENT = MAIN_ROUTE + "/main_flutter_fragment";
    String NOPREMISS_BACLPLAY_FLUTTER_FRAGMENT = MAIN_ROUTE + "/main_nopremiss_blackplay_fragment";
    String MAIN_COMMON_FLUTTER_FRAGMENT = MAIN_ROUTE + "/main_common_flutter_fragment";
    String MAIN_FLUTTER_ACTIVITY = MAIN_ROUTE + "/main_flutter_activity";
    String MAIN_COMMON_FLUTTER_ACTIVITY = MAIN_ROUTE + "/main_common_flutter_activity";
}
