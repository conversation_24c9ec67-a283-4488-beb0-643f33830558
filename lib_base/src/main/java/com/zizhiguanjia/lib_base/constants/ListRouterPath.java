package com.zizhiguanjia.lib_base.constants;

public interface ListRouterPath {
    /**
     * 模块一级路径
     */
    String MAIN_ROUTE = "/list";

    String SERVICE = MAIN_ROUTE + "/service";

    String MAIN_FRAGMENT = MAIN_ROUTE + "/main_fragment";
    /**
     * 主Activity
     */
    String MAIN_ACTIVITY = MAIN_ROUTE + "/main_activity";

    String MAIN_EXAM_RESULT = MAIN_ROUTE + "/main_result";
    String MAIN_EXAM_LIST = MAIN_ROUTE + "/main_list";
    String MAIN_EXAM_DES = MAIN_ROUTE + "/main_des";
    String MAIN_EXAM_AUTO = MAIN_ROUTE + "/main_auto";
    String MAIN_EXAM_COMMONLIST = MAIN_ROUTE + "/main_commonlist_fragment";
    String MAIN_EXAM_DOC_MAJORS = MAIN_ROUTE + "/docMajors";
}
