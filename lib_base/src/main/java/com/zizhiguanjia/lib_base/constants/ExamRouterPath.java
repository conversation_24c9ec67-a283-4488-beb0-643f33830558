package com.zizhiguanjia.lib_base.constants;

public interface ExamRouterPath {
    /**
     * 模块一级路径
     */
    String MAIN_ROUTE = "/exam";
    String SERVICE = MAIN_ROUTE + "/service";
    String MAIN_FRAGMENT = MAIN_ROUTE + "/main_fragment";
    String MAIN_FRAGMENT_SAVE = MAIN_ROUTE + "/main_save_fragment";
    String MAIN_FRAGMENT_EXAM = MAIN_ROUTE + "/main_exam_fragment";
    String MAIN_FRAGMENT_COMMONLIST = MAIN_ROUTE + "/main_commonlist_fragment";
    String MAIN_FRAGMENT_CHAPTERMAIN = MAIN_ROUTE + "/main_chaptermain_fragment";
    String MAIN_FRAGMENT_AUTOTESTEXAM = MAIN_ROUTE + "/main_chaptermain_autotestexam";
}
