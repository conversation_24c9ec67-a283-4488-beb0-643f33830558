package com.zizhiguanjia.lib_base.bean.theme;

/**
 * 功能作用：字体大小主题配置
 * 初始注释时间： 2024/6/26 18:58
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class ExamCoreThemeConfigThemeBean {
    /**
     * 是否默认
     */
    private Boolean def;
    /**
     * 名称
     */
    private String name;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 背景色
     */
    private String bgColor;
    /**
     * 底部容器背景颜色
     */
    private String bottomContainerBg;
    /**
     * 底部容器文本颜色
     */
    private String bottomContainerText;
    /**
     * 点击确定才能提交--文案颜色
     */
    private String confirmHintTextColor;
    /**
     * 问题内容文本颜色
     */
    private String contentTextColor;
    /**
     * 页面下标总数文本颜色
     */
    private String pageIndexSumColor;
    /**
     * 文字颜色
     */
    private String textColor;
    /**
     * 标题栏背景颜色
     */
    private String toolsBarBgColor;
    /**
     * 标题栏文本颜色
     */
    private String toolsBarTextColor;
    /**
     * 解析标题文本颜色
     */
    private String analysisTitleTextColor;
    /**
     * 分隔线颜色
     */
    private String lineColor;
    /**
     * 做错题目人员数量提示
     */
    private String errorPeopleCountTipTextColor;
    /**
     * 做错题目人员数量提示
     */
    private String errorPeopleCountTipBgColor;
    /**
     * 问题答案选择-背景-错误
     */
    private String questionAnswerSelectBgColorError;
    /**
     * 问题答案选择-背景-未做
     */
    private String questionAnswerSelectBgColorNo;
    /**
     * 问题答案选择-背景-正确
     */
    private String questionAnswerSelectBgColorSure;
    /**
     * 问题答案选择-图标背景-错误
     */
    private String questionAnswerSelectIconBgColorError;
    /**
     * 问题答案选择-图标背景-未做
     */
    private String questionAnswerSelectIconBgColorNo;
    /**
     * 问题答案选择-图标背景-正确
     */
    private String questionAnswerSelectIconBgColorSure;
    /**
     * 问题答案选择-图标文字-错误
     */
    private String questionAnswerSelectIconTextColorError;
    /**
     * 问题答案选择-图标文字-未做
     */
    private String questionAnswerSelectIconTextColorNo;
    /**
     * 问题答案选择-图标文字-正确
     */
    private String questionAnswerSelectIconTextColorSure;
    /**
     * 问题答案选择-内容文字-错误
     */
    private String questionAnswerSelectTextColorError;
    /**
     * 问题答案选择-内容文字-未做
     */
    private String questionAnswerSelectTextColorNo;
    /**
     * 问题答案选择-内容文字-正确
     */
    private String questionAnswerSelectTextColorSure;
    /**
     * 答案卡片-背景颜色
     */
    private String answerCardDialogPageBgColor;
    /**
     * 答案卡片-当前页码
     */
    private String answerCardDialogPageIndexCurrent;
    /**
     * 答案卡片-题目总数
     */
    private String answerCardDialogPageIndexSum;
    /**
     * 答题结果页面-卡片背景颜色
     */
    private String answerPageCardBgColor;
    /**
     * 问题答案选择-卡片背景颜色
     */
    private String questionAnswerSelectCardBgColor;
    /**
     * 答题结果页面-数量相关文本颜色
     */
    private String answerPageCountTextColor;
    /**
     * 答题结果页面-数量提示文本颜色
     */
    private String answerPageCountTipTextColor;
    /**
     * 答题结果页面-描述图标渲染颜色
     */
    private String answerPageDesIconTintColor;
    /**
     * 答题结果页面-描述文本颜色
     */
    private String answerPageDesTextColor;
    /**
     * 答题结果页面-百分比文本颜色
     */
    private String answerPagePercentColor;
    /**
     * 答题结果页面-答案大标题颜色
     */
    private String answerPageTitle;
    /**
     * 答案结果-item-错误背景色
     */
    private String answerResultItemBgColorError;
    /**
     * 答案结果-item-不可做背景色
     */
    private String answerResultItemBgColorLuck;
    /**
     * 答案结果-item-未做背景色
     */
    private String answerResultItemBgColorNo;
    /**
     * 答案结果-item-正确背景色
     */
    private String answerResultItemBgColorSure;
    /**
     * 答案结果-item-已做背景色
     */
    private String answerResultItemBgColorDoing;
    /**
     * 答案结果-item-错误边框色
     */
    private String answerResultItemStoreColorError;
    /**
     * 答案结果-item-不可做边框色
     */
    private String answerResultItemStoreColorLuck;
    /**
     * 答案结果-item-未做边框色
     */
    private String answerResultItemStoreColorNo;
    /**
     * 答案结果-item-已做边框色
     */
    private String answerResultItemStoreColorDoing;
    /**
     * 答案结果-item-正确边框色
     */
    private String answerResultItemStoreColorSure;
    /**
     * 答案结果-item-错误文本色
     */
    private String answerResultItemTextColorError;
    /**
     * 答案结果-item-不可做文本色
     */
    private String answerResultItemTextColorLuck;
    /**
     * 答案结果-item-未做文本色
     */
    private String answerResultItemTextColorNo;
    /**
     * 答案结果-item-已做文本色
     */
    private String answerResultItemTextColorDoing;
    /**
     * 答案结果-item-正确文本色
     */
    private String answerResultItemTextColorSure;
    /**
     * 答案结果-结果状态提示文本颜色
     */
    private String answerResultTipTextColor;
    /**
     * 答案结果-标题文本颜色
     */
    private String answerResultTitleTextColor;
    /**
     * 问题tab标题选中背景色
     */
    private String questionTabSelectBgColor;
    /**
     * 问题tab标题选中边框色
     */
    private String questionTabSelectStoreColor;
    /**
     * 问题tab标题选中文本色
     */
    private String questionTabSelectTextColor;
    /**
     * 问题tab标题未选中背景色
     */
    private String questionTabUnSelectBgColor;
    /**
     * 问题tab标题未选中边框色
     */
    private String questionTabUnSelectStoreColor;
    /**
     * 问题tab标题未选中文本色
     */
    private String questionTabUnSelectTextColor;

    public void setQuestionAnswerSelectCardBgColor(String questionAnswerSelectCardBgColor) {
        this.questionAnswerSelectCardBgColor = questionAnswerSelectCardBgColor;
    }

    public String getQuestionAnswerSelectCardBgColor() {
        return questionAnswerSelectCardBgColor;
    }

    public void setAnswerCardDialogPageBgColor(String answerCardDialogPageBgColor) {
        this.answerCardDialogPageBgColor = answerCardDialogPageBgColor;
    }

    public String getAnswerCardDialogPageBgColor() {
        return answerCardDialogPageBgColor;
    }

    public String getQuestionTabSelectBgColor() {
        return questionTabSelectBgColor;
    }

    public void setQuestionTabSelectBgColor(String questionTabSelectBgColor) {
        this.questionTabSelectBgColor = questionTabSelectBgColor;
    }

    public String getQuestionTabSelectStoreColor() {
        return questionTabSelectStoreColor;
    }

    public void setQuestionTabSelectStoreColor(String questionTabSelectStoreColor) {
        this.questionTabSelectStoreColor = questionTabSelectStoreColor;
    }

    public String getQuestionTabSelectTextColor() {
        return questionTabSelectTextColor;
    }

    public void setQuestionTabSelectTextColor(String questionTabSelectTextColor) {
        this.questionTabSelectTextColor = questionTabSelectTextColor;
    }

    public String getQuestionTabUnSelectBgColor() {
        return questionTabUnSelectBgColor;
    }

    public void setQuestionTabUnSelectBgColor(String questionTabUnSelectBgColor) {
        this.questionTabUnSelectBgColor = questionTabUnSelectBgColor;
    }

    public String getQuestionTabUnSelectStoreColor() {
        return questionTabUnSelectStoreColor;
    }

    public void setQuestionTabUnSelectStoreColor(String questionTabUnSelectStoreColor) {
        this.questionTabUnSelectStoreColor = questionTabUnSelectStoreColor;
    }

    public String getQuestionTabUnSelectTextColor() {
        return questionTabUnSelectTextColor;
    }

    public void setQuestionTabUnSelectTextColor(String questionTabUnSelectTextColor) {
        this.questionTabUnSelectTextColor = questionTabUnSelectTextColor;
    }

    public Boolean getDef() {
        return def;
    }

    public void setDef(Boolean def) {
        this.def = def;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getBgColor() {
        return bgColor;
    }

    public void setBgColor(String bgColor) {
        this.bgColor = bgColor;
    }

    public String getBottomContainerBg() {
        return bottomContainerBg;
    }

    public void setBottomContainerBg(String bottomContainerBg) {
        this.bottomContainerBg = bottomContainerBg;
    }

    public String getBottomContainerText() {
        return bottomContainerText;
    }

    public void setBottomContainerText(String bottomContainerText) {
        this.bottomContainerText = bottomContainerText;
    }

    public String getConfirmHintTextColor() {
        return confirmHintTextColor;
    }

    public void setConfirmHintTextColor(String confirmHintTextColor) {
        this.confirmHintTextColor = confirmHintTextColor;
    }

    public String getContentTextColor() {
        return contentTextColor;
    }

    public void setContentTextColor(String contentTextColor) {
        this.contentTextColor = contentTextColor;
    }

    public String getPageIndexSumColor() {
        return pageIndexSumColor;
    }

    public void setPageIndexSumColor(String pageIndexSumColor) {
        this.pageIndexSumColor = pageIndexSumColor;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getToolsBarBgColor() {
        return toolsBarBgColor;
    }

    public void setToolsBarBgColor(String toolsBarBgColor) {
        this.toolsBarBgColor = toolsBarBgColor;
    }

    public String getToolsBarTextColor() {
        return toolsBarTextColor;
    }

    public void setToolsBarTextColor(String toolsBarTextColor) {
        this.toolsBarTextColor = toolsBarTextColor;
    }

    public String getAnalysisTitleTextColor() {
        return analysisTitleTextColor;
    }

    public void setAnalysisTitleTextColor(String analysisTitleTextColor) {
        this.analysisTitleTextColor = analysisTitleTextColor;
    }

    public String getLineColor() {
        return lineColor;
    }

    public void setLineColor(String lineColor) {
        this.lineColor = lineColor;
    }

    public String getErrorPeopleCountTipTextColor() {
        return errorPeopleCountTipTextColor;
    }

    public void setErrorPeopleCountTipTextColor(String errorPeopleCountTipTextColor) {
        this.errorPeopleCountTipTextColor = errorPeopleCountTipTextColor;
    }

    public String getErrorPeopleCountTipBgColor() {
        return errorPeopleCountTipBgColor;
    }

    public void setErrorPeopleCountTipBgColor(String errorPeopleCountTipBgColor) {
        this.errorPeopleCountTipBgColor = errorPeopleCountTipBgColor;
    }

    public String getQuestionAnswerSelectBgColorError() {
        return questionAnswerSelectBgColorError;
    }

    public void setQuestionAnswerSelectBgColorError(String questionAnswerSelectBgColorError) {
        this.questionAnswerSelectBgColorError = questionAnswerSelectBgColorError;
    }

    public String getQuestionAnswerSelectBgColorNo() {
        return questionAnswerSelectBgColorNo;
    }

    public void setQuestionAnswerSelectBgColorNo(String questionAnswerSelectBgColorNo) {
        this.questionAnswerSelectBgColorNo = questionAnswerSelectBgColorNo;
    }

    public String getQuestionAnswerSelectBgColorSure() {
        return questionAnswerSelectBgColorSure;
    }

    public void setQuestionAnswerSelectBgColorSure(String questionAnswerSelectBgColorSure) {
        this.questionAnswerSelectBgColorSure = questionAnswerSelectBgColorSure;
    }

    public String getQuestionAnswerSelectIconBgColorError() {
        return questionAnswerSelectIconBgColorError;
    }

    public void setQuestionAnswerSelectIconBgColorError(String questionAnswerSelectIconBgColorError) {
        this.questionAnswerSelectIconBgColorError = questionAnswerSelectIconBgColorError;
    }

    public String getQuestionAnswerSelectIconBgColorNo() {
        return questionAnswerSelectIconBgColorNo;
    }

    public void setQuestionAnswerSelectIconBgColorNo(String questionAnswerSelectIconBgColorNo) {
        this.questionAnswerSelectIconBgColorNo = questionAnswerSelectIconBgColorNo;
    }

    public String getQuestionAnswerSelectIconBgColorSure() {
        return questionAnswerSelectIconBgColorSure;
    }

    public void setQuestionAnswerSelectIconBgColorSure(String questionAnswerSelectIconBgColorSure) {
        this.questionAnswerSelectIconBgColorSure = questionAnswerSelectIconBgColorSure;
    }

    public String getQuestionAnswerSelectIconTextColorError() {
        return questionAnswerSelectIconTextColorError;
    }

    public void setQuestionAnswerSelectIconTextColorError(String questionAnswerSelectIconTextColorError) {
        this.questionAnswerSelectIconTextColorError = questionAnswerSelectIconTextColorError;
    }

    public String getQuestionAnswerSelectIconTextColorNo() {
        return questionAnswerSelectIconTextColorNo;
    }

    public void setQuestionAnswerSelectIconTextColorNo(String questionAnswerSelectIconTextColorNo) {
        this.questionAnswerSelectIconTextColorNo = questionAnswerSelectIconTextColorNo;
    }

    public String getQuestionAnswerSelectIconTextColorSure() {
        return questionAnswerSelectIconTextColorSure;
    }

    public void setQuestionAnswerSelectIconTextColorSure(String questionAnswerSelectIconTextColorSure) {
        this.questionAnswerSelectIconTextColorSure = questionAnswerSelectIconTextColorSure;
    }

    public String getQuestionAnswerSelectTextColorError() {
        return questionAnswerSelectTextColorError;
    }

    public void setQuestionAnswerSelectTextColorError(String questionAnswerSelectTextColorError) {
        this.questionAnswerSelectTextColorError = questionAnswerSelectTextColorError;
    }

    public String getQuestionAnswerSelectTextColorNo() {
        return questionAnswerSelectTextColorNo;
    }

    public void setQuestionAnswerSelectTextColorNo(String questionAnswerSelectTextColorNo) {
        this.questionAnswerSelectTextColorNo = questionAnswerSelectTextColorNo;
    }

    public String getQuestionAnswerSelectTextColorSure() {
        return questionAnswerSelectTextColorSure;
    }

    public void setQuestionAnswerSelectTextColorSure(String questionAnswerSelectTextColorSure) {
        this.questionAnswerSelectTextColorSure = questionAnswerSelectTextColorSure;
    }

    public String getAnswerCardDialogPageIndexCurrent() {
        return answerCardDialogPageIndexCurrent;
    }

    public void setAnswerCardDialogPageIndexCurrent(String answerCardDialogPageIndexCurrent) {
        this.answerCardDialogPageIndexCurrent = answerCardDialogPageIndexCurrent;
    }

    public String getAnswerCardDialogPageIndexSum() {
        return answerCardDialogPageIndexSum;
    }

    public void setAnswerCardDialogPageIndexSum(String answerCardDialogPageIndexSum) {
        this.answerCardDialogPageIndexSum = answerCardDialogPageIndexSum;
    }

    public String getAnswerPageCardBgColor() {
        return answerPageCardBgColor;
    }

    public void setAnswerPageCardBgColor(String answerPageCardBgColor) {
        this.answerPageCardBgColor = answerPageCardBgColor;
    }

    public String getAnswerPageCountTextColor() {
        return answerPageCountTextColor;
    }

    public void setAnswerPageCountTextColor(String answerPageCountTextColor) {
        this.answerPageCountTextColor = answerPageCountTextColor;
    }

    public String getAnswerPageCountTipTextColor() {
        return answerPageCountTipTextColor;
    }

    public void setAnswerPageCountTipTextColor(String answerPageCountTipTextColor) {
        this.answerPageCountTipTextColor = answerPageCountTipTextColor;
    }

    public String getAnswerPageDesIconTintColor() {
        return answerPageDesIconTintColor;
    }

    public void setAnswerPageDesIconTintColor(String answerPageDesIconTintColor) {
        this.answerPageDesIconTintColor = answerPageDesIconTintColor;
    }

    public String getAnswerPageDesTextColor() {
        return answerPageDesTextColor;
    }

    public void setAnswerPageDesTextColor(String answerPageDesTextColor) {
        this.answerPageDesTextColor = answerPageDesTextColor;
    }

    public String getAnswerPagePercentColor() {
        return answerPagePercentColor;
    }

    public void setAnswerPagePercentColor(String answerPagePercentColor) {
        this.answerPagePercentColor = answerPagePercentColor;
    }

    public String getAnswerPageTitle() {
        return answerPageTitle;
    }

    public void setAnswerPageTitle(String answerPageTitle) {
        this.answerPageTitle = answerPageTitle;
    }

    public String getAnswerResultItemBgColorError() {
        return answerResultItemBgColorError;
    }

    public void setAnswerResultItemBgColorError(String answerResultItemBgColorError) {
        this.answerResultItemBgColorError = answerResultItemBgColorError;
    }

    public String getAnswerResultItemBgColorLuck() {
        return answerResultItemBgColorLuck;
    }

    public void setAnswerResultItemBgColorLuck(String answerResultItemBgColorLuck) {
        this.answerResultItemBgColorLuck = answerResultItemBgColorLuck;
    }

    public String getAnswerResultItemBgColorNo() {
        return answerResultItemBgColorNo;
    }

    public void setAnswerResultItemBgColorNo(String answerResultItemBgColorNo) {
        this.answerResultItemBgColorNo = answerResultItemBgColorNo;
    }

    public String getAnswerResultItemBgColorDoing() {
        return answerResultItemBgColorDoing;
    }

    public void setAnswerResultItemBgColorDoing(String answerResultItemBgColorDoing) {
        this.answerResultItemBgColorDoing = answerResultItemBgColorDoing;
    }

    public String getAnswerResultItemBgColorSure() {
        return answerResultItemBgColorSure;
    }

    public void setAnswerResultItemBgColorSure(String answerResultItemBgColorSure) {
        this.answerResultItemBgColorSure = answerResultItemBgColorSure;
    }

    public String getAnswerResultItemStoreColorError() {
        return answerResultItemStoreColorError;
    }

    public void setAnswerResultItemStoreColorError(String answerResultItemStoreColorError) {
        this.answerResultItemStoreColorError = answerResultItemStoreColorError;
    }

    public String getAnswerResultItemStoreColorLuck() {
        return answerResultItemStoreColorLuck;
    }

    public void setAnswerResultItemStoreColorLuck(String answerResultItemStoreColorLuck) {
        this.answerResultItemStoreColorLuck = answerResultItemStoreColorLuck;
    }

    public String getAnswerResultItemStoreColorNo() {
        return answerResultItemStoreColorNo;
    }

    public void setAnswerResultItemStoreColorNo(String answerResultItemStoreColorNo) {
        this.answerResultItemStoreColorNo = answerResultItemStoreColorNo;
    }

    public String getAnswerResultItemStoreColorDoing() {
        return answerResultItemStoreColorDoing;
    }

    public void setAnswerResultItemStoreColorDoing(String answerResultItemStoreColorDoing) {
        this.answerResultItemStoreColorDoing = answerResultItemStoreColorDoing;
    }

    public String getAnswerResultItemStoreColorSure() {
        return answerResultItemStoreColorSure;
    }

    public void setAnswerResultItemStoreColorSure(String answerResultItemStoreColorSure) {
        this.answerResultItemStoreColorSure = answerResultItemStoreColorSure;
    }

    public String getAnswerResultItemTextColorError() {
        return answerResultItemTextColorError;
    }

    public void setAnswerResultItemTextColorError(String answerResultItemTextColorError) {
        this.answerResultItemTextColorError = answerResultItemTextColorError;
    }

    public String getAnswerResultItemTextColorLuck() {
        return answerResultItemTextColorLuck;
    }

    public void setAnswerResultItemTextColorLuck(String answerResultItemTextColorLuck) {
        this.answerResultItemTextColorLuck = answerResultItemTextColorLuck;
    }

    public String getAnswerResultItemTextColorNo() {
        return answerResultItemTextColorNo;
    }

    public void setAnswerResultItemTextColorNo(String answerResultItemTextColorNo) {
        this.answerResultItemTextColorNo = answerResultItemTextColorNo;
    }

    public String getAnswerResultItemTextColorDoing() {
        return answerResultItemTextColorDoing;
    }

    public void setAnswerResultItemTextColorDoing(String answerResultItemTextColorDoing) {
        this.answerResultItemTextColorDoing = answerResultItemTextColorDoing;
    }

    public String getAnswerResultItemTextColorSure() {
        return answerResultItemTextColorSure;
    }

    public void setAnswerResultItemTextColorSure(String answerResultItemTextColorSure) {
        this.answerResultItemTextColorSure = answerResultItemTextColorSure;
    }

    public String getAnswerResultTipTextColor() {
        return answerResultTipTextColor;
    }

    public void setAnswerResultTipTextColor(String answerResultTipTextColor) {
        this.answerResultTipTextColor = answerResultTipTextColor;
    }

    public String getAnswerResultTitleTextColor() {
        return answerResultTitleTextColor;
    }

    public void setAnswerResultTitleTextColor(String answerResultTitleTextColor) {
        this.answerResultTitleTextColor = answerResultTitleTextColor;
    }
}
