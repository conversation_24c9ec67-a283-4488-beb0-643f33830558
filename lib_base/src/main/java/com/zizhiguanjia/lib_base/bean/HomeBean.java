package com.zizhiguanjia.lib_base.bean;

import java.util.ArrayList;
import java.util.List;

public class HomeBean {
    private int MajorId;
    private int AreaId;
    private String AreaName;
    private int TotalNumber;
    private int AnswerNumber;
    private int PaperType;
    private String PaperTitle;
    private int CorrectNumber;
    private String CorrectPercent;
    private String QuestionMakingProgress;
    private boolean IsVip;
    private int StudyMinutes;
    private int IsExamine;
    private int OrderBindType;
    private int RemainingQuestionsCount;
    private int liveVieoType;
    private boolean IsShowAds;

    public String getAreaName() {
        return AreaName;
    }

    public void setAreaName(String areaName) {
        AreaName = areaName;
    }

    public boolean isShowAds() {
        return IsShowAds;
    }

    public void setShowAds(boolean showAds) {
        IsShowAds = showAds;
    }


    public int getOrderBindType() {
        return OrderBindType;
    }

    public void setOrderBindType(int orderBindType) {
        OrderBindType = orderBindType;
    }



    public int getLiveVieoType() {
        return liveVieoType;
    }

    public void setLiveVieoType(int liveVieoType) {
        this.liveVieoType = liveVieoType;
    }




    public int getMajorId() {
        return MajorId;
    }

    public void setMajorId(int MajorId) {
        this.MajorId = MajorId;
    }

    public int getAreaId() {
        return AreaId;
    }

    public void setAreaId(int AreaId) {
        this.AreaId = AreaId;
    }

    public int getTotalNumber() {
        return TotalNumber;
    }

    public void setTotalNumber(int TotalNumber) {
        this.TotalNumber = TotalNumber;
    }

    public int getAnswerNumber() {
        return AnswerNumber;
    }

    public void setAnswerNumber(int AnswerNumber) {
        this.AnswerNumber = AnswerNumber;
    }

    public int getPaperType() {
        return PaperType;
    }

    public void setPaperType(int PaperType) {
        this.PaperType = PaperType;
    }

    public String getPaperTitle() {
        return PaperTitle;
    }

    public void setPaperTitle(String PaperTitle) {
        this.PaperTitle = PaperTitle;
    }

    public int getCorrectNumber() {
        return CorrectNumber;
    }

    public void setCorrectNumber(int CorrectNumber) {
        this.CorrectNumber = CorrectNumber;
    }

    public String getCorrectPercent() {
        return CorrectPercent;
    }

    public void setCorrectPercent(String CorrectPercent) {
        this.CorrectPercent = CorrectPercent;
    }

    public String getQuestionMakingProgress() {
        return QuestionMakingProgress;
    }

    public void setQuestionMakingProgress(String QuestionMakingProgress) {
        this.QuestionMakingProgress = QuestionMakingProgress;
    }

    public boolean isIsVip() {
        return IsVip;
    }

    public void setIsVip(boolean IsVip) {
        this.IsVip = IsVip;
    }

    public int getStudyMinutes() {
        return StudyMinutes;
    }

    public void setStudyMinutes(int StudyMinutes) {
        this.StudyMinutes = StudyMinutes;
    }

    public int getIsExamine() {
        return IsExamine;
    }

    public void setIsExamine(int IsExamine) {
        this.IsExamine = IsExamine;
    }

    public int getRemainingQuestionsCount() {
        return RemainingQuestionsCount;
    }

    public void setRemainingQuestionsCount(int RemainingQuestionsCount) {
        this.RemainingQuestionsCount = RemainingQuestionsCount;
    }


}
