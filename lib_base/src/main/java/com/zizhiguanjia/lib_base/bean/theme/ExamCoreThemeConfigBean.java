package com.zizhiguanjia.lib_base.bean.theme;

import java.util.List;

/**
 * 功能作用：主题配置字体大小实体
 * 初始注释时间： 2024/6/26 18:58
 * 创建人：王亮（<PERSON>ren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class ExamCoreThemeConfigBean {
    /**
     * 字体大小配置
     */
    private  List<ExamCoreThemeConfigTextSizeBean> textList;

    /**
     * 主题配置
     */
    private List<ExamCoreThemeConfigThemeBean> themeList;

    public List<ExamCoreThemeConfigTextSizeBean> getTextList() {
        return textList;
    }

    public void setTextList(List<ExamCoreThemeConfigTextSizeBean> textList) {
        this.textList = textList;
    }

    public List<ExamCoreThemeConfigThemeBean> getThemeList() {
        return themeList;
    }

    public void setThemeList(List<ExamCoreThemeConfigThemeBean> themeList) {
        this.themeList = themeList;
    }
}
