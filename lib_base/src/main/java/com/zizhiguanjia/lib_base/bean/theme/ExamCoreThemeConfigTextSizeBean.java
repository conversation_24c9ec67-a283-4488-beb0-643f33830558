package com.zizhiguanjia.lib_base.bean.theme;

/**
 * 功能作用：主题配置字体大小实体
 * 初始注释时间： 2024/6/26 18:58
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class ExamCoreThemeConfigTextSizeBean {
    /**
     * 是否默认
     */
    private Boolean def;
    /**
     * 名称
     */
    private String name;
    /**
     * 大小，字号
     */
    private Float size;
    /**
     * 类型
     */
    private Integer type;

    public Boolean getDef() {
        return def;
    }

    public void setDef(<PERSON>ole<PERSON> def) {
        this.def = def;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Float getSize() {
        return size;
    }
    public Float getSizeReduce(float reduce) {
        return size - reduce;
    }

    public void setSize(Float size) {
        this.size = size;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
