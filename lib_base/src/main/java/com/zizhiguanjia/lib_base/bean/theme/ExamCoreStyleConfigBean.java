package com.zizhiguanjia.lib_base.bean.theme;

import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.SPUtils;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.utils.AppUtils;

import java.util.Objects;

/**
 * 功能作用：答题界面风格样式
 * 初始注释时间： 2024/6/27 21:53
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 * 1、2、7、8、9、11 不显示背题以及自动跳转
 * 1、2、7、11 ,不显示对错统计
 * 1-历年真题、2-模拟真题、11-模拟考核、7-模拟考试，不管对错自动跳转下一题
 * 非vip背题模式自动关闭，同时不做记录，切换到vip还原
 *
 * <AUTHOR>
 */
public class ExamCoreStyleConfigBean {
    /**
     * 字体大小类型
     */
    private Integer textSizeType;
    /**
     * 主题
     */
    private Integer themeType;
    /**
     * 显示上一题下一题
     */
    private boolean showPreNextButtons = false;
    /**
     * 是否是背题模式
     */
    private boolean showMemoryModel = false;
    /**
     * 是否自动跳转下一题
     */
    private boolean autoJumpNext = true;

    private ExamCoreStyleConfigBean() {

    }

    public Integer getTextSizeType() {
        return textSizeType;
    }

    public void setTextSizeType(String paperType, String title, Integer textSizeType) {
        this.textSizeType = textSizeType;
        saveConfigInfoToStore(paperType, title);
    }

    public Integer getThemeType() {
        return themeType;
    }

    public void setThemeType(String paperType, String title, Integer themeType) {
        this.themeType = themeType;
        saveConfigInfoToStore(paperType, title);
    }

    public boolean isShowPreNextButtons() {
        return showPreNextButtons;
    }

    public void setShowPreNextButtons(String paperType, String title, boolean showPreNextButtons) {
        this.showPreNextButtons = showPreNextButtons;
        saveConfigInfoToStore(paperType, title);
    }

    public boolean isShowMemoryModel() {
        return showMemoryModel;
    }

    public void setShowMemoryModel(String paperType, String title, boolean showMemoryModel) {
        this.showMemoryModel = showMemoryModel;
        saveConfigInfoToStore(paperType, title);
    }

    public boolean isAutoJumpNext() {
        return autoJumpNext;
    }

    public void setAutoJumpNext(String paperType, String title, boolean autoJumpNext) {
        this.autoJumpNext = autoJumpNext;
        saveConfigInfoToStore(paperType, title);
    }

    /**
     * 从缓存中获取配置信息
     *
     * @return 配置信息
     */
    public static ExamCoreStyleConfigBean getConfigInfoByStore(String paperType, String title) {
        ExamCoreStyleConfigBean configBean = getConfigOriginInfoByStore();
        //根据条件进行初始数据处理
        //1、2、7、8、9、11 不显示背题以及自动跳转
        if (ExamPaperTypeConfig.isExaminationAndAnalysis(Integer.parseInt(paperType),title)) {
            configBean.autoJumpNext = false;
            configBean.showMemoryModel = false;
        }
        //1-历年真题、2-模拟真题、11-模拟考核、7-模拟考试，不管对错自动跳转下一题
        if (ExamPaperTypeConfig.isAnswerTopIc(Integer.parseInt(paperType), title)) {
            configBean.autoJumpNext = true;
        }
        //非vip的时候禁用背题
        if (!UserHelper.isBecomeVip()) {
            configBean.showMemoryModel = false;
        }
        return configBean;
    }

    /**
     * 保存配置信息到缓存中
     */
    public void saveConfigInfoToStore(String paperType, String title) {
        //复制当前数据
        ExamCoreStyleConfigBean currentInfo = copy();
        //获取初始换成数据
        ExamCoreStyleConfigBean configInfoByStore = getConfigOriginInfoByStore();
        //将不进行修改的数据进行缓存处理，使用旧数据更新
        //1、2、7、8、9、11 不显示背题以及自动跳转
        if (ExamPaperTypeConfig.isExaminationAndAnalysis(Integer.parseInt(paperType),title)) {
            currentInfo.autoJumpNext = configInfoByStore.autoJumpNext;
            currentInfo.showMemoryModel = configInfoByStore.showMemoryModel;
        }
        //1-历年真题、2-模拟真题、11-模拟考核、7-模拟考试，不管对错自动跳转下一题
        if (ExamPaperTypeConfig.isAnswerTopIc(Integer.parseInt(paperType), title)) {
            currentInfo.autoJumpNext = configInfoByStore.autoJumpNext;
        }
        //非vip的时候禁用背题
        if (!UserHelper.isBecomeVip()) {
            currentInfo.showMemoryModel = configInfoByStore.showMemoryModel;
        }
        SPUtils.setParam("ExamCoreFragmentStyleConfig_" + (UserHelper.getUserInfo() == null? "" : UserHelper.getUserInfo().getUserId()), GsonUtils.newInstance().GsonToString(currentInfo));
    }

    /**
     * 获取配置的原始信息
     *
     * @return 原始信息
     */
    private static ExamCoreStyleConfigBean getConfigOriginInfoByStore() {
        ExamCoreStyleConfigBean configBean = GsonUtils.newInstance().getBean(SPUtils.getParam("ExamCoreFragmentStyleConfig_" +(UserHelper.getUserInfo() == null? "" : UserHelper.getUserInfo().getUserId()), "").toString(),
                ExamCoreStyleConfigBean.class);
        if (configBean == null) {
            return new ExamCoreStyleConfigBean();
        }
        return configBean;
    }

    /**
     * 获取主题配置信息
     */
    public ExamCoreThemeConfigThemeBean getThemeBeanInfo() {
        String themeConfigData = AppUtils.getInstance().readFromAssets("CoreThemeConfig.json");
        ExamCoreThemeConfigBean themeConfigBean = com.wb.lib_network.utils.GsonUtils.gsonToBean(themeConfigData, ExamCoreThemeConfigBean.class);
        if (themeConfigBean != null) {
            for (ExamCoreThemeConfigThemeBean configThemeBean : themeConfigBean.getThemeList()) {
                if (getThemeType() != null) {
                    if (Objects.equals(configThemeBean.getType(), getThemeType())) {
                        return configThemeBean;
                    }
                } else if (configThemeBean.getDef()) {
                    return configThemeBean;
                }
            }
        }
        return null;
    }

    /**
     * 获取主题配置信息
     */
    public ExamCoreThemeConfigTextSizeBean getTextSizeBeanInfo() {
        String themeConfigData = AppUtils.getInstance().readFromAssets("CoreThemeConfig.json");
        ExamCoreThemeConfigBean themeConfigBean = com.wb.lib_network.utils.GsonUtils.gsonToBean(themeConfigData, ExamCoreThemeConfigBean.class);
        if (themeConfigBean != null) {
            for (ExamCoreThemeConfigTextSizeBean configTextSizeBean : themeConfigBean.getTextList()) {
                if (getTextSizeType() != null) {
                    if (Objects.equals(configTextSizeBean.getType(), getTextSizeType())) {
                        return configTextSizeBean;
                    }
                } else if (configTextSizeBean.getDef()) {
                    return configTextSizeBean;
                }
            }
        }
        return null;
    }

    /**
     * 复制数据
     */
    public ExamCoreStyleConfigBean copy() {
        ExamCoreStyleConfigBean bean = new ExamCoreStyleConfigBean();
        bean.showMemoryModel = showMemoryModel;
        bean.textSizeType = textSizeType;
        bean.themeType = themeType;
        bean.showPreNextButtons = showPreNextButtons;
        bean.autoJumpNext = autoJumpNext;
        return bean;
    }

}
