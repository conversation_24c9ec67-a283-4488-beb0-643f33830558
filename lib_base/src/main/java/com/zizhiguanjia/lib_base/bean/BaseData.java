package com.zizhiguanjia.lib_base.bean;

import com.wb.lib_utils.interfaces.IData;
import com.wb.lib_utils.utils.log.LogUtils;

public class BaseData<T> implements IData<T>  {
    public String Code;
    public String Message;
    public T Data;
    @Override
    public String getCode() {
        return Code;
    }

    @Override
    public String getMsg() {
        return Message;
    }

    @Override
    public T getResult() {
        return Data;
    }

    @Override
    public boolean isSuccess() {
        return "200".equals(Code)||"206".equals(Code)?true:false;
    }

    @Override
    public String toString() {
        return "BaseData{" +
                "code='" + Code + '\'' +
                ", msg='" + Message + '\'' +
                ", data=" + Data +
                '}';
    }
}