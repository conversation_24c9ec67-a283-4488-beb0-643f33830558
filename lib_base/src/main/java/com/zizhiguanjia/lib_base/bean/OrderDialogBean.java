package com.zizhiguanjia.lib_base.bean;

import android.app.Activity;

import com.xuexiang.xupdate.entity.PromptEntity;
import com.xuexiang.xupdate.entity.UpdateEntity;
import com.xuexiang.xupdate.proxy.IUpdateProxy;

public class OrderDialogBean {
    private Activity activity;
    private IUpdateProxy updateProxy;
    private UpdateEntity promptEntity;
    private int index;
    private String tips;

    private String title;
    private String subTitle;
    private String contentTitle;
    private String Content;
    private String subContent;
    private String ids;

    public Activity getActivity() {
        return activity;
    }

    public void setActivity(Activity activity) {
        this.activity = activity;
    }

    public IUpdateProxy getUpdateProxy() {
        return updateProxy;
    }

    public void setUpdateProxy(IUpdateProxy updateProxy) {
        this.updateProxy = updateProxy;
    }

    public UpdateEntity getPromptEntity() {
        return promptEntity;
    }

    public void setPromptEntity(UpdateEntity promptEntity) {
        this.promptEntity = promptEntity;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getContentTitle() {
        return contentTitle;
    }

    public void setContentTitle(String contentTitle) {
        this.contentTitle = contentTitle;
    }

    public String getContent() {
        return Content;
    }

    public void setContent(String content) {
        Content = content;
    }

    public String getSubContent() {
        return subContent;
    }

    public void setSubContent(String subContent) {
        this.subContent = subContent;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }
}
