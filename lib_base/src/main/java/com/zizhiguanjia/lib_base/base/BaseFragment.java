package com.zizhiguanjia.lib_base.base;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.DefaultLifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_arch.action.ArchAction;
import com.wb.lib_arch.action.BundleAction;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_arch.base.Java8Observer;
import com.wb.lib_arch.common.ArchConfig;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_arch.delegate.FragmentDelegate;
import com.wb.lib_arch.utils.AppManager;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.subsciber.SimpleThrowableAction;
import com.wb.lib_utils.action.AnimAction;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.action.ClickAction;
import com.wb.lib_weiget.action.HandlerAction;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.bean.HomeBean;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.ClientShaperPlamTypeConfig;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.lib_base.httphelper.BaseHelper;
import com.zizhiguanjia.lib_base.listeners.JpushAuthorizeListener;
import com.zizhiguanjia.lib_base.listeners.MessageSuccessPayListener;
import com.zizhiguanjia.lib_base.listeners.PaySuccessListen;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;

import java.io.IOException;
import java.lang.ref.WeakReference;
import java.lang.reflect.Type;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 懒加载
 *
 * <AUTHOR>
 */
public abstract class BaseFragment extends Fragment
        implements IFragment, ArchAction, BundleAction, HandlerAction, ClickAction, AnimAction {

    protected final String TAG = getClass().getCanonicalName();
    private FragmentDelegate mFragmentDelegate = getBaseDelegate();

    protected WeakReference<Context> mContext;
    protected WeakReference<AppCompatActivity> mActivity;
    protected View mRootView;

    private Java8Observer java8Observer;

    @Nullable
    @Override
    public Context getContext() {
        return mContext.get();
    }


    public BaseFragment() {
        java8Observer = new Java8Observer(TAG);
        getLifecycle().addObserver(java8Observer);
        getLifecycle().addObserver(mFragmentDelegate);
    }
    private void iniPublic(){
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                AppManager instance = AppManager.getInstance();
                if(msgEvent.getCode()== PayMsgTypeConfig.PAY_MSG_TS_BASE){
                    if(instance.currentFragment().getClass().toString().contains("CommonWebviewFragment")){
                        LogUtils.e("开始弹出老师----->>>>1"+BaseConfig.paySuccessAutoClose);
                        if(!NoDoubleClickUtils.isDoubleClick()){
                            LogUtils.e("开始弹出老师----->>>>11"+BaseConfig.paySuccessAutoClose);
                            if(BaseConfig.isPaySuccessDialog){
                                RxJavaUtils.delay(1, new Consumer<Long>() {
                                    @Override
                                    public void accept(Long aLong) throws Exception {
                                        if(BaseConfig.paySuccessAutoClose){
                                            Bus.post(new MsgEvent(PayMsgTypeConfig.PAY_MSG_TS));
                                        }else {
                                            MessageHelper.openGotoSubject(new MessageSuccessPayListener() {
                                                @Override
                                                public void GotoSelectSubject() {
                                                    startFragment(CertificateHelper.startChoseCertificateZzByAddress());
                                                }
                                            },instance.currentFragment().getActivity());
                                        }
                                        BaseConfig.paySuccessAutoClose=true;
                                    }
                                });
                            }

                        }
                    }else {
                        if(BaseConfig.isPaySuccessDialog){
                            MessageHelper.openGotoSubject(new MessageSuccessPayListener() {
                                @Override
                                public void GotoSelectSubject() {
                                    startFragment(CertificateHelper.startChoseCertificateZzByAddress());
                                }
                            },instance.currentFragment().getActivity());
                        }
                        LogUtils.e("开始弹出老师----->>>>2"+BaseConfig.isPaySuccessDialog);
                    }
                }else if(msgEvent.getCode()==0x4545445){
                    LogUtils.e("开始关闭了---->>>>"+isMain());
                    clearAllFragment(false);
                }else if(msgEvent.getCode()== AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT){
                    clearAllFragment(true);
                }
            }
        });
    }
    public void clearAllFragment(boolean isTs){
        if(!NoDoubleClickUtils.isDoubleClick()){
            List<Fragment> fragments=AppManager.getInstance().getFragmentList();
            if(fragments==null||fragments.size()==0)return;
            for(Fragment fragment : fragments){
                LogUtils.e("开始关闭----->>>>>>"+isMain()+"----"+fragment.getClass().getName()+"-----"+TAG);
                if (!fragment.getClass().getName().contains("HomeFragment")){
//                    fragment.getActivity().finish();
                    SdkHelper.removeAuthorize(ClientShaperPlamTypeConfig.Wx, new JpushAuthorizeListener() {
                        @Override
                        public void userGetAuthorize(String name, String openId, String imageAva, boolean auth, String unionId) {
                        }

                        @Override
                        public void userRemoveAuthrize(boolean success) {
//                            navigator.showLoadingView(false, "");
                            AccountHelper.exitAccount();
                            ToastUtils.normal(success ? "账号退出成功" : "账号退出失败", Gravity.CENTER);
                        }
                    });
                }else {
                    Bus.post(new MsgEvent(0x98885555,isTs?"1":"0"));
                }
            }
        }

    }
    public <B extends ViewDataBinding> B getBinding() {
        DataBindingUtil.bind(mRootView);
        return DataBindingUtil.getBinding(mRootView);
    }

    @Override
    public Bundle initArguments() {
        return mFragmentDelegate.initArguments();
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new DefaultLifecycleObserver() {
            @Override
            public void onCreate(@NonNull LifecycleOwner owner) {
                mContext = new WeakReference<>(context);
                mActivity = new WeakReference<>((AppCompatActivity) getContext());
                owner.getLifecycle().removeObserver(this);
            }
        });
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        mRootView = mFragmentDelegate.onCreateView(inflater, container, savedInstanceState);
        iniPublic();
        return mRootView;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        mFragmentDelegate.setUserVisibleHint(isVisibleToUser);
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        mFragmentDelegate.onHiddenChanged(hidden);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mFragmentDelegate.onConfigurationChanged(newConfig);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        removeCallbacks();
        mFragmentDelegate = null;
        mContext.clear();
        mContext = null;
        mRootView = null;
        mActivity.clear();
        mActivity = null;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        getLifecycle().removeObserver(java8Observer);
    }

    @Nullable
    @Override
    public Bundle getBundle() {
        return initArguments();
    }

    @Override
    public <V extends View> V findViewById(int id) {
        return mRootView.findViewById(id);
    }

    /**
     * 返回.
     */
    public void finish() {
        mFragmentDelegate.finish();
    }
//    private boolean isMain=false;
    public boolean isMain(){
        return false;
    }

    public <T extends IFragment> void startFragment(T targetFragment) {
        try {
            Bundle bundle = initArguments();
            Intent intent = new Intent(mActivity.get(), ContainerActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.putExtra(ArchConfig.FRAGMENT, targetFragment.getClass().getCanonicalName());
            intent.putExtra(ArchConfig.BUNDLE, bundle);
            mActivity.get().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private int resultCode;
    private Intent result;

    public void setResultCode(int resultCode) {
        setResultCode(resultCode, null);
    }

    public void setResultCode(int resultCode, Intent result) {
        this.resultCode = resultCode;
        this.result = result;
    }

    public int getResultCode() {
        return resultCode;
    }

    public Intent getResult() {
        return result;
    }
}
