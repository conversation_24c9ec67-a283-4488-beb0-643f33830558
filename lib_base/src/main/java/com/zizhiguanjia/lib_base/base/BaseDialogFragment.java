package com.zizhiguanjia.lib_base.base;
import android.app.Dialog;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.zizhiguanjia.lib_base.holder.BaseViewHolder;

public abstract class BaseDialogFragment extends DialogFragment {
    @LayoutRes
    protected int mLayoutResId;
    private BaseDialogFragment.ViewConvertListener viewConvertListener;
    private boolean mOutCancel = true;
    private boolean mBgTransparent = true;
    private int mWidth;
    private int mHeight;
    private int mGravity = 17;

    public BaseDialogFragment() {
    }

    protected abstract int setUpLayoutId();

    protected boolean useEventBus() {
        return false;
    }

    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.mLayoutResId = this.setUpLayoutId();
    }

    @Nullable
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(this.setUpLayoutId(), (ViewGroup)null);
        this.convertView(BaseViewHolder.create(view), this);
        return view;
    }

    @NonNull
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        Window window = dialog.getWindow();
        if (null != window) {
            window.requestFeature(1);
        }

        return dialog;
    }

    public void onStart() {
        super.onStart();
        Window window = this.getDialog().getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = this.mWidth;
            params.height = this.mHeight;
            params.gravity = this.mGravity;
            window.setAttributes(params);
            if (this.mBgTransparent) {
                window.setBackgroundDrawable(new ColorDrawable(0));
            }
        }

        this.setCancelable(this.mOutCancel);
    }
    public BaseDialogFragment show(FragmentManager manager) {
        FragmentTransaction transaction = manager.beginTransaction();
        transaction.add(this, String.valueOf(System.currentTimeMillis()));
        transaction.commitAllowingStateLoss();
        transaction.show(this);
        return this;
    }

    public BaseDialogFragment setDialogSize(int width, int height) {
        this.mWidth = width;
        this.mHeight = height;
        return this;
    }

    public BaseDialogFragment setGravity(int gravity) {
        this.mGravity = gravity;
        return this;
    }

    public BaseDialogFragment setOutCancel(boolean outCancel) {
        this.mOutCancel = outCancel;
        return this;
    }

    public BaseDialogFragment setBgTransparent(boolean transparent) {
        this.mBgTransparent = transparent;
        return this;
    }

    public BaseDialogFragment setConvertViewListener(BaseDialogFragment.ViewConvertListener listener) {
        this.viewConvertListener = listener;
        return this;
    }

    protected void convertView(BaseViewHolder holder, BaseDialogFragment baseDialog) {
        if (null != this.viewConvertListener) {
            this.viewConvertListener.convertView(holder, baseDialog);
        }

    }

    public void onDestroy() {
        super.onDestroy();

    }

    public void onDestroyView() {
        super.onDestroyView();
    }

    public interface ViewConvertListener {
        void convertView(BaseViewHolder var1, BaseDialogFragment var2);
    }
}