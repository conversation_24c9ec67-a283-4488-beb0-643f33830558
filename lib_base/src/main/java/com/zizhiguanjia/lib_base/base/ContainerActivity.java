package com.zizhiguanjia.lib_base.base;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.util.Patterns;
import android.view.WindowManager;
import android.widget.FrameLayout;

import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_arch.common.ArchConfig;
import com.wb.lib_utils.utils.ClipboardUtils;
import com.zizhiguanjia.lib_base.helper.MessageHelper;

import java.lang.ref.WeakReference;
import java.util.regex.Matcher;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

/**
 * Description: 盛装Fragment的一个容器(代理)Activity
 * 普通界面只需要编写Fragment,使用此Activity盛装,这样就不需要每个界面都在AndroidManifest中注册一遍
 */
@BindRes(isContainer = true)
public class ContainerActivity extends BaseActivity {

    private WeakReference<Fragment> mFragment;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        final FrameLayout mFrameLayout = new FrameLayout(this);
        mFrameLayout.setId(android.R.id.content);
        setContentView(mFrameLayout);
    }

    public Fragment initBaseFragment() {
        return null;
    }

    private boolean isMains;

    public boolean isMain() {
        return false;
    }

    @SuppressLint("SourceLockedOrientationActivity")
    @Override
    public void initView(Bundle savedInstanceState) {
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
//        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        try {
            Intent intent = getIntent();

            if (intent == null) {
                throw new RuntimeException("you must provide a page info to display");
            }
            isMains = isMain();
            mFragment = new WeakReference<>(initBaseFragment());
            if (mFragment.get() != null) {
                if (intent.getBundleExtra(ArchConfig.BUNDLE) != null) {
                    mFragment.get().setArguments(intent.getBundleExtra(ArchConfig.BUNDLE));
                }
                setRootFragment((IFragment) mFragment.get(), android.R.id.content);
                return;
            }

            String fragmentName = intent.getStringExtra(ArchConfig.FRAGMENT);
            if (fragmentName == null || fragmentName.isEmpty()) {
                if(initBaseFragment() != null) {
                    mFragment = new WeakReference<>(initBaseFragment());
                    if (intent.getBundleExtra(ArchConfig.BUNDLE) != null) {
                        mFragment.get().setArguments(intent.getBundleExtra(ArchConfig.BUNDLE));
                    }
                    setRootFragment((IFragment) mFragment.get(), android.R.id.content);
                    return;
                }
                
                // 如果是HomeActivity或其他自定义Activity，允许它们自己管理Fragment
                if (isMain()) {
                    return;
                }
                
                throw new IllegalArgumentException("can not find page fragmentName");
            }

            Class<?> fragmentClass = Class.forName(fragmentName);

            Fragment fragment = (Fragment) fragmentClass.newInstance();

            if (intent.getBundleExtra(ArchConfig.BUNDLE) != null) {
                fragment.setArguments(intent.getBundleExtra(ArchConfig.BUNDLE));
            }

            setRootFragment((IFragment) fragment, android.R.id.content);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 清除所有打开得activity
     *
     * @param isMain
     */
    public void cleanAllActivity(boolean isMain) {
//        try {
//            Bus.post(new MsgEvent(999999999));
//        }catch (Exception e){
//
//        }

    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        mFragment.clear();
    }

//    @Override
//    protected void onRestart() {
//        super.onRestart();
//        if (!isDoubleClick()) {
//            MainThreadUtils.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    checkCouponid();
//                }
//            },1000L);
//        }
//    }

    private final static int SPACE_TIME = 2000;//2次点击的间隔时间，单位ms

    private static long lastClickTime;

    public synchronized  boolean isDoubleClick() {
        long currentTime = System.currentTimeMillis();
        boolean isClick;
        if (currentTime - lastClickTime > SPACE_TIME) {
            isClick = false;
        } else {
            isClick = true;
        }
        lastClickTime = currentTime;
        return isClick;
    }
    private void checkCouponid() {
        try {
            String txt = ClipboardUtils.getText(this).toString();
                String url=matcherData(txt);
                if(url==null||url.isEmpty())return;
                if(url.startsWith("http")||url.startsWith("https")){
                    //优惠卷监听
                    MessageHelper.startCoupion(url);
                }
        } catch (Exception e) {
        }
    }
    private String matcherData(String tpis){
        Matcher matcher = Patterns.WEB_URL.matcher(tpis);
        if (matcher.find()){
            return matcher.group();
        }
        return null;
    }
}
