package com.zizhiguanjia.lib_base.msgconfig;

import androidx.annotation.IntDef;
import androidx.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
@StringDef({PointRouthType.APP_CHAPTER_ROUTH,PointRouthType.APP_EXAM_ROUTH,PointRouthType.APP_MAIN,PointRouthType.APP_POINTER_HOME_START})
@Retention(RetentionPolicy.SOURCE)
public @interface PointRouthType {
    //首页路径
    String APP_MAIN="APP_MAIN";
    //章节路径
    String APP_CHAPTER_ROUTH="APP_CHAPTER";
    //考试路径
    String APP_EXAM_ROUTH="APP_EXAM";
    //首页----开始---章节
    String APP_POINTER_HOME_START="APP_POINTER_HOME_START";

}
