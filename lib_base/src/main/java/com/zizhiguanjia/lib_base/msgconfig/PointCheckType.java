package com.zizhiguanjia.lib_base.msgconfig;

import androidx.annotation.IntDef;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
@IntDef({PointCheckType.CHECK_MAIN_CHAPTER_EXAM,
        PointCheckType.CHECK_MAIN_CHAPER,
        PointCheckType.CHECK_MAIN_HOMEBT_CHAPER,
        PointCheckType.CHECK_MAIN_CHAPER_TO_HOMEBT,
        PointCheckType.CHECK_MAIN_HOMEBT_CHAPER_EXAM,
        PointCheckType.CHECK_EXAM_CLEAL})
@Retention(RetentionPolicy.SOURCE)
public @interface PointCheckType {
    //首页---章节---考试
    int CHECK_MAIN_CHAPTER_EXAM=0x900001;
    //首页---章节
    int CHECK_MAIN_CHAPER=0x900002;
    //首页---开始---章节
    int CHECK_MAIN_HOMEBT_CHAPER=0x900003;
    //首页---开始---章节--答题
    int CHECK_MAIN_HOMEBT_CHAPER_EXAM=0x900005;
    //从章节列表回退首页
    int CHECK_MAIN_CHAPER_TO_HOMEBT=0x900004;
    //退出考试清理
    int CHECK_EXAM_CLEAL=0x900006;
}
