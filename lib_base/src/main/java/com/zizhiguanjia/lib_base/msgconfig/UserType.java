package com.zizhiguanjia.lib_base.msgconfig;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@IntDef({UserType.USER_TYPE_VISITOR,
        UserType.USER_TYPE_COMMON,
        UserType.USER_TYPE_VIP,
        UserType.USER_TYPE_ADMIN
})
@Retention(RetentionPolicy.SOURCE)
public @interface UserType {
    int USER_TYPE_VISITOR = 1;
    int USER_TYPE_COMMON = 2;
    int USER_TYPE_VIP = 3;
    int USER_TYPE_ADMIN = 4;
}
