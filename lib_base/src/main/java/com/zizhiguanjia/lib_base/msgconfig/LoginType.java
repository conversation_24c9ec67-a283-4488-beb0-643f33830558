package com.zizhiguanjia.lib_base.msgconfig;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
@IntDef({LoginType.LOGIN_TYPE_ACCOUNT,
        LoginType.LOGIN_TYPE_ONEKEY,
        LoginType.LOGIN_TYPE_WX,
        LoginType.LOGIN_TYPE_VISITOR,
        LoginType.LOGIN_TYPE_OTHER
})
@Retention(RetentionPolicy.SOURCE)
public @interface LoginType {
    int LOGIN_TYPE_ACCOUNT=1;
    int LOGIN_TYPE_ONEKEY=2;
    int LOGIN_TYPE_WX=3;
    int LOGIN_TYPE_VISITOR=4;
    int LOGIN_TYPE_OTHER=5;
}
