package com.zizhiguanjia.lib_base.utils;

import android.annotation.SuppressLint;
import android.app.AlarmManager;
import android.provider.Settings.Global;
import android.provider.Settings.SettingNotFoundException;
import android.text.format.DateUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.wb.lib_image_loadcal.utils.Utils;
import com.wb.lib_utils.AppConfig;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.StringTokenizer;
import java.util.TimeZone;
import java.util.regex.Pattern;

import cn.hutool.core.date.DateUtil;

public final class TimeUtils {
    @SuppressLint({"SimpleDateFormat"})
    private static final DateFormat DEFAULT_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final String DATE_PATTERN_1 = "HH:mm";
    private static final String DATE_PATTERN_2 = "yyyyMMdd";
    public static final String DATE_PATTERN_3 = "yyyy-MM-dd";
    private static final String DATE_PATTERN_4 = "yyyyMMddHHmmss";
    private static final String DATE_PATTERN_5 = "yyyy年MM月dd日";
    public static final String DATE_PATTERN_6 = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_PATTERN_7 = "yyyyMM";
    private static final String DATE_PATTERN_8 = "yyyyMMddHHmmssSSS";
    private static final String DATE_PATTERN_9 = "yyyyMMdd_HHmmssSSS";
    private static final String DATE_PATTERN_11 = "HH";
    public static final String DATE_PATTERN_12 = "HH:mm:ss";
    private static final int TIME_MAX_COUNT = 10;
    private static final int DAY_OF_WEEK_MAX = 7;
    private static final DateFormat SHORT_FORMAT;
    private static final DateFormat LONG_FORMAT;
    private static final DateFormat LONG_FORMAT2;
    private static final DateFormat LONG_FORMAT_HOUR;
    private static final DateFormat STRING_FORMAT;

    private TimeUtils() {
        throw new UnsupportedOperationException("无法初始化！");
    }

    @SuppressLint({"SimpleDateFormat"})
    public static String millis2String(long millis, String strFormat) {
        return millis2String(millis, (DateFormat)(new SimpleDateFormat(strFormat)));
    }

    @SuppressLint({"SimpleDateFormat"})
    public static String millis2String(long millis) {
        return millis2String(millis, DEFAULT_FORMAT);
    }

    private static String millis2String(long millis, @NonNull DateFormat dateFormat) {
        return dateFormat.format(new Date(millis));
    }

    public static String date2String(Date date) {
        return date2String(date, DEFAULT_FORMAT);
    }

    public static String date2String(Date date, @NonNull DateFormat format) {
        return format.format(date);
    }

    public static long string2Millis(String strTime) {
        return string2Millis(strTime, DEFAULT_FORMAT);
    }

    public static long string2Millis(String strTime, @NonNull DateFormat dateFormat) {
        try {
            return dateFormat.parse(strTime).getTime();
        } catch (ParseException var3) {
            var3.printStackTrace();
            return -1L;
        }
    }

    public static Date string2Date(String time) {
        return string2Date(time, DEFAULT_FORMAT);
    }

    public static Date string2Date(String time, @NonNull DateFormat format) {
        try {
            return format.parse(time);
        } catch (ParseException var3) {
            var3.printStackTrace();
            return null;
        }
    }

    public static String getDayOfWeek() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(System.currentTimeMillis()));
        int day = cal.get(7);
        int transferDay = day - 1;
        if (transferDay == 0) {
            transferDay = 7;
        }

        return String.valueOf(transferDay);
    }

    public static boolean isValidDate(String str, String pattern) {
        boolean convertSuccess = true;
        SimpleDateFormat format = new SimpleDateFormat(pattern, Locale.CHINA);

        try {
            format.setLenient(false);
            format.parse(str);
        } catch (ParseException var5) {
            var5.printStackTrace();
            convertSuccess = false;
        }

        return convertSuccess;
    }

    public static boolean checkDoorAuthority(String input) {
        String regex = "([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$";
        return Pattern.matches(regex, input);
    }

    public static String getStringHour(long time) {
        SimpleDateFormat format = new SimpleDateFormat("HH:mm", Locale.CHINA);
        return format.format(time);
    }

    public static String getStringDay(long time) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy年MM月dd日", Locale.CHINA);
        return format.format(time);
    }

    public static String getShortStringDay(long time) {
        return SHORT_FORMAT.format(time);
    }

    public static String getShortStringHour(long time) {
        return LONG_FORMAT_HOUR.format(time);
    }

    public static String getCurrentTimeFormat() {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss", Locale.CHINA);
        return format.format(System.currentTimeMillis());
    }

    public static String getCurrentTimeFormat2() {
        return LONG_FORMAT.format(System.currentTimeMillis());
    }

    public static String getCurrentTimeRecordImg() {
        return LONG_FORMAT2.format(System.currentTimeMillis());
    }

    public static String getUsbRegisterFailTime() {
        return STRING_FORMAT.format(System.currentTimeMillis());
    }

    public static String getStringCurrentTime() {
        int hour = getHourOfDay();
        int minute = getMinute();
        int second = getSecond();
        String strHour;
        if (hour < 10) {
            strHour = "0" + hour;
        } else {
            strHour = "" + hour;
        }

        String strMinute;
        if (minute < 10) {
            strMinute = "0" + minute;
        } else {
            strMinute = "" + minute;
        }

        String strSecond;
        if (second < 10) {
            strSecond = "0" + second;
        } else {
            strSecond = "" + second;
        }

        return strHour + ":" + strMinute + ":" + strSecond;
    }

    private static int getHourOfDay() {
        return Calendar.getInstance().get(11);
    }

    public static int getHour() {
        return Calendar.getInstance().get(10);
    }

    private static int getMinute() {
        return Calendar.getInstance().get(12);
    }

    private static int getSecond() {
        return Calendar.getInstance().get(13);
    }

    public static long getStartTimeOfDay() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(11, 0);
        todayStart.set(12, 0);
        todayStart.set(13, 0);
        todayStart.set(14, 0);
        return todayStart.getTime().getTime();
    }

    public static long getEndTimeOfDay() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(11, 23);
        todayEnd.set(12, 59);
        todayEnd.set(13, 59);
        todayEnd.set(14, 999);
        return todayEnd.getTime().getTime();
    }

    public static boolean setSystemTime(String datetime) {
        try {
            setAutoDateTime(0);
            setAutoTimeZone(0);
            setTimeZone(getDefaultTimeZone());
            String[] strTime1 = datetime.split(" ");
            if (strTime1.length != 2) {
                return false;
            } else {
                String strDate = strTime1[0];
                String strTime = strTime1[1];
                String[] dateArr = strDate.split("-");
                String[] timeArr = strTime.split(":");
                if (dateArr.length == 3 && timeArr.length == 3) {
                    int y = Integer.parseInt(dateArr[0]);
                    int m = Integer.parseInt(dateArr[1]);
                    int d = Integer.parseInt(dateArr[2]);
                    int h = Integer.parseInt(dateArr[0]);
                    int min = Integer.parseInt(dateArr[1]);
                    int s = Integer.parseInt(dateArr[2]);
                    setSysDate(y, m, d);
                    setSysTime(h, min, s);
                    return true;
                } else {
                    return false;
                }
            }
        } catch (Exception var12) {
            return false;
        }
    }

    public static void setAutoDateTime(int checked) {
        Global.putInt(AppConfig.getInstance().getApp().getContentResolver(), "auto_time", checked);
    }

    public static boolean isTimeZoneAuto() {
        try {
            return Global.getInt(AppConfig.getInstance().getApp().getContentResolver(), "auto_time_zone") > 0;
        } catch (SettingNotFoundException var1) {
            var1.printStackTrace();
            return false;
        }
    }

    public static void setAutoTimeZone(int checked) {
        Global.putInt(AppConfig.getInstance().getApp().getContentResolver(), "auto_time_zone", checked);
    }

    public static void setSysDate(int year, int month, int day) {
        Calendar c = Calendar.getInstance();
        c.set(1, year);
        c.set(2, month);
        c.set(5, day);
        long when = c.getTimeInMillis();
        if (when / 1000L < 2147483647L) {
            @SuppressLint("WrongConstant") AlarmManager alarmManager = (AlarmManager)AppConfig.getInstance().getApp().getSystemService("alarm");
            if (null != alarmManager) {
                alarmManager.setTime(when);
            }
        }

    }

    public static void setSysTime(int hour, int minute, int second) {
        Calendar c = Calendar.getInstance();
        c.set(11, hour);
        c.set(12, minute);
        c.set(13, second);
        c.set(14, 0);
        long when = c.getTimeInMillis();
        if (when / 1000L < 2147483647L) {
            @SuppressLint("WrongConstant") AlarmManager alarmManager = (AlarmManager)AppConfig.getInstance().getApp().getSystemService("alarm");
            if (null != alarmManager) {
                alarmManager.setTime(when);
            }
        }

    }

    public static void setTimeZone(String timeZone) {
        Calendar now = Calendar.getInstance();
        TimeZone tz = TimeZone.getTimeZone(timeZone);
        now.setTimeZone(tz);
    }

    public static String getDefaultTimeZone() {
        return TimeZone.getDefault().getDisplayName();
    }

    public static String formatDateStampString() {
        long when = System.currentTimeMillis();
        int format_flags = 526870;
        return DateUtils.formatDateTime(AppConfig.getInstance().getApp(), when, format_flags);
    }

    public static String formatSimpleDateStampString() {
        long when = System.currentTimeMillis();
        int format_flags = 133652;
        return DateUtils.formatDateTime(AppConfig.getInstance().getApp(), when, format_flags);
    }

    public static String formatHourMinString() {
        long when = System.currentTimeMillis();
        int format_flags = 526849;
        return DateUtils.formatDateTime(AppConfig.getInstance().getApp(), when, format_flags);
    }

    public static boolean compareTime(String time1, String time2) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");

        try {
            Date date1 = dateFormat.parse(time1);
            Date date2 = dateFormat.parse(time2);
            return date2.getTime() > date1.getTime();
        } catch (Exception var5) {
            return false;
        }
    }

    public static boolean compareDate(String input1, String input2) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        try {
            Date date1 = dateFormat.parse(input1);
            Date date2 = dateFormat.parse(input2);
            return date2.getTime() > date1.getTime();
        } catch (Exception var5) {
            return false;
        }
    }

    public static boolean compareDate2(String input1, String input2) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        try {
            Date date1 = dateFormat.parse(input1);
            Date date2 = dateFormat.parse(input2);
            return date1.getTime() <= date2.getTime();
        } catch (Exception var5) {
            return false;
        }
    }

    static {
        SHORT_FORMAT = new SimpleDateFormat("yyyyMMdd", Locale.CHINA);
        LONG_FORMAT = new SimpleDateFormat("yyyyMMddHHmmssSSS", Locale.CHINA);
        LONG_FORMAT2 = new SimpleDateFormat("yyyyMMdd_HHmmssSSS", Locale.CHINA);
        LONG_FORMAT_HOUR = new SimpleDateFormat("HH", Locale.CHINA);
        STRING_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
    }
    /**
     *
     * @param nowDate   要比较的时间
     * @return   true在时间段内，false不在时间段内
     * @throws Exception
     */
    public static boolean hourMinuteBetween(String time) {
        try {
            String timeStr = TimeUtils.formatHourMinString();
            String[] strings=convertStrToArray2(time);
            String startDate=strings[0];
            String endDate=strings[1];
            SimpleDateFormat format = new SimpleDateFormat("HH:mm");
            Date now = format.parse(timeStr);
            Date start = format.parse(startDate);
            Date end = format.parse(endDate);

            long nowTime = now.getTime();
            long startTime = start.getTime();
            long endTime = end.getTime();
            return nowTime >= startTime && nowTime <= endTime;
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }
    }
    public static String[] convertStrToArray2(String str){
        StringTokenizer st = new StringTokenizer(str,"-");
        String[] strArray = new String[st.countTokens()];
        int i=0;
        while(st.hasMoreTokens()){
            strArray[i++] = st.nextToken().trim();
        }
        return strArray;
    }
}