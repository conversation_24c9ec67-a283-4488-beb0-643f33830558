package com.zizhiguanjia.lib_base.utils;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.view.Gravity;

import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelmsg.SendAuth;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXMiniProgramObject;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.xiaomi.mipush.sdk.Constants;
import com.zizhiguanjia.lib_base.DebugApplication;
import com.zizhiguanjia.lib_base.emums.MetaDataKeyEnum;

import java.util.HashMap;
import java.util.Map;

import cn.hutool.core.lang.UUID;

/**
 * 功能作用：第三方平台操作工具类
 * 初始注释时间： 2024/1/15 16:01
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class ThirdPlatformOptionsUtils {
    private static volatile ThirdPlatformOptionsUtils optionsInstance;

    private ThirdPlatformOptionsUtils() {
    }

    public static ThirdPlatformOptionsUtils getInstance() {
        if (optionsInstance == null) {
            synchronized (ThirdPlatformOptionsUtils.class) {
                if (optionsInstance == null) {
                    optionsInstance = new ThirdPlatformOptionsUtils();
                }
            }
        }
        return optionsInstance;
    }

    /**
     * 是第三方app和微信通信的openApi接口
     */
    private IWXAPI mWechatApi;

    /**
     * 微信的appid
     */
    private final String mWechatAppId = AppUtils.getInstance().getAppMetaData(MetaDataKeyEnum.WECHAT_APP_ID);

    /**
     * 回调集合
     */
    private final Map<String, ThirdPlatformOptionsCallback> mCallbackMap = new HashMap<>();

    //标题
    private final String SHAPER_TITLE = AppUtils.getInstance().getAppMetaData(MetaDataKeyEnum.TARGET_NAME) + "考证考级，就用" +
            AppUtils.getInstance().getAppName();
    //内容
    private final String SHAPER_TEXT = "精编题目畅快刷，智能组卷解题技巧，助力考试无忧，考不过全额退";
    //超链接
    private final String SHAPER_URL = "https://docs.jiguang.cn/jshare/client/Android/android_api/";
    //封面图片
    private final String SHAPER_ImagePath = AppUtils.getInstance().getAppMetaData(MetaDataKeyEnum.SHARE_IMAGE);
    //小程序路径
    private final String SHAPER_MiniProgramPath = "/pages/share/index?data=";
    //小程序原始ID
    private final String SHAPER_MiniProgramUserName = "gh_1c91e78fbc80";
    //小程序原始ID
    private final String SHAPER_MiniProgramUserName2 = "gh_2bb8301f983e";

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    public void init() {
        // 通过WXAPIFactory工厂，获取IWXAPI的实例
        mWechatApi = WXAPIFactory.createWXAPI(DebugApplication.mApplicationContext, mWechatAppId, true);
        // 将应用的appId注册到微信
        mWechatApi.registerApp(mWechatAppId);
        //建议动态监听微信启动广播进行注册到微信
        DebugApplication.mApplicationContext.registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                // 将该app注册到微信
                mWechatApi.registerApp(Constants.APP_ID);
            }
        }, new IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP));
    }

    public void handleWechatIntent(Intent intent, IWXAPIEventHandler handler) {
        mWechatApi.handleIntent(intent, handler);
    }

    /**
     * 微信网页分享
     *
     * @param shareUrl 分享链接
     * @param target   目标 SendMessageToWX.Req
     */
    public void shareWechatWebpage(String shareUrl, int target) {
        if (!mWechatApi.isWXAppInstalled()) {
            ToastUtils.normal("请先安装微信客户端", Gravity.CENTER);
            return;
        }
        WXWebpageObject webpageObject = new WXWebpageObject();
        webpageObject.webpageUrl = shareUrl;
        sendWechatSendReq(getWechatMessage(webpageObject), target, new ThirdPlatformOptionsCallback() {
            /**
             * 分享成功
             */
            @Override
            public void onShareSuccess() {
                ToastUtils.normal("分享成功", Gravity.CENTER);
            }

            /**
             * 分享失败
             */
            @Override
            public void onShareFail() {
                ToastUtils.normal("分享失败", Gravity.CENTER);
            }

            /**
             * 分享取消
             */
            @Override
            public void onShareCancel() {
                ToastUtils.normal("分享取消", Gravity.CENTER);
            }
        });
    }

    /**
     * 微信小程序分享
     *
     * @param questionId 路径id
     */
    public void shareWechatMiniProgram(String questionId) {
        if (!mWechatApi.isWXAppInstalled()) {
            ToastUtils.normal("请先安装微信客户端", Gravity.CENTER);
            return;
        }

        WXMiniProgramObject miniProgramObj = new WXMiniProgramObject();
        miniProgramObj.webpageUrl = SHAPER_URL; // 兼容低版本的网页链接
        miniProgramObj.miniprogramType = WXMiniProgramObject.MINIPTOGRAM_TYPE_RELEASE;// 正式版:0，测试版:1，体验版:2
        miniProgramObj.userName = SHAPER_MiniProgramUserName2;     // 小程序原始id
        miniProgramObj.path = SHAPER_MiniProgramPath + questionId;            //小程序页面路径；对于小游戏，可以只传入 query 部分，来实现传参效果，如：传入 "?foo=bar"

        WXMediaMessage message = getWechatMessage(miniProgramObj);
        message.setThumbImage(getShareMiniProgramImage());
        sendWechatSendReq(message, SendMessageToWX.Req.WXSceneSession, new ThirdPlatformOptionsCallback() {
            /**
             * 分享成功
             */
            @Override
            public void onShareSuccess() {
                ToastUtils.normal("分享成功", Gravity.CENTER);
            }

            /**
             * 分享失败
             */
            @Override
            public void onShareFail() {
                ToastUtils.normal("分享失败", Gravity.CENTER);
            }

            /**
             * 分享取消
             */
            @Override
            public void onShareCancel() {
                ToastUtils.normal("分享取消", Gravity.CENTER);
            }
        });
    }

    /**
     * 微信登录获取code
     *
     * @param callback 微信登录获取code
     */
    public void wechatLogin(ThirdPlatformOptionsCallback callback) {
        // send oauth request
        SendAuth.Req req = new SendAuth.Req();
        req.scope = "snsapi_userinfo"; // 只能填 snsapi_userinfo
        req.state = "wechat_sdk_demo_test";
        req.transaction = UUID.fastUUID().toString(true);
        mCallbackMap.put(req.transaction, callback);
        mWechatApi.sendReq(req);
    }

    /**
     * wxEntry的回调
     *
     * @param baseResp 回调信息
     */
    public void onWxEntryRespCallback(BaseResp baseResp) {
        MainThreadUtils.post(() -> {
            ThirdPlatformOptionsCallback callback;
            if (baseResp.transaction != null && mCallbackMap.containsKey(baseResp.transaction)) {
                callback = mCallbackMap.get(baseResp.transaction);
                if (callback != null) {
                    if (baseResp.getType() == ConstantsAPI.COMMAND_SENDAUTH) {
                        //微信登录
                        SendAuth.Resp authResp = (SendAuth.Resp) baseResp;
                        callback.onWechatLoginRepCode(authResp.code);
                    } else {
                        //分享处理
                        switch (baseResp.errCode) {
                            case BaseResp.ErrCode.ERR_OK://发送成功
                                callback.onShareSuccess();
                                break;
                            case BaseResp.ErrCode.ERR_USER_CANCEL://发送取消
                                callback.onShareCancel();
                                break;
                            case BaseResp.ErrCode.ERR_AUTH_DENIED://发送被拒绝
                            case BaseResp.ErrCode.ERR_UNSUPPORT://不支持错误
                            default://发送返回
                                callback.onShareFail();
                                break;
                        }
                    }
                }
                //移除回调
                mCallbackMap.remove(baseResp.transaction);
            }
        });
    }

    /**
     * 发送微信消息
     *
     * @param message 消息
     * @param target  目标类型,SendMessageToWX.Req
     */
    private void sendWechatSendReq(WXMediaMessage message, int target, ThirdPlatformOptionsCallback callback) {
        //构造一个Req
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.message = message;
        req.transaction = UUID.fastUUID().toString(true);
        req.scene = target;//SendMessageToWX.Req
        mCallbackMap.put(req.transaction, callback);
        mWechatApi.sendReq(req);
    }

    /**
     * 获取微信消息构造体
     *
     * @param object 数据
     * @return 构造体
     */
    private WXMediaMessage getWechatMessage(WXMediaMessage.IMediaObject object) {
        WXMediaMessage message = new WXMediaMessage();
        message.title = SHAPER_TITLE;
        message.description = SHAPER_TEXT;
        message.setThumbImage(getAppLogoBitmap());
        message.mediaObject = object;
        return message;
    }

    /**
     * 获取logo图片位图
     *
     * @return 应用位图
     */
    private Bitmap getAppLogoBitmap() {
        Resources resources = DebugApplication.mApplicationContext.getResources();
        String logoPath = AppUtils.getInstance().getAppMetaData(MetaDataKeyEnum.APP_ICON);
        int id = resources.getIdentifier(logoPath.substring(logoPath.indexOf("/") + 1), "mipmap",
                AppUtils.getInstance().getAppMetaData(MetaDataKeyEnum.APPLICATION_ID));
        return BitmapFactory.decodeResource(DebugApplication.mApplicationContext.getResources(), id);
    }

    /**
     * 获取分享的小程序图片
     *
     * @return 图片信息
     */
    private Bitmap getShareMiniProgramImage() {
        Resources resources = DebugApplication.mApplicationContext.getResources();
        String image = AppUtils.getInstance().getAppMetaData(MetaDataKeyEnum.SHARE_IMAGE);
        image = image.substring(0, image.indexOf("."));
        int id = resources.getIdentifier(image, "mipmap", AppUtils.getInstance().getAppMetaData(MetaDataKeyEnum.APPLICATION_ID));
        return BitmapFactory.decodeResource(DebugApplication.mApplicationContext.getResources(), id);
    }

    /**
     * 操作回调
     */
    public abstract static class ThirdPlatformOptionsCallback {
        /**
         * 分享成功
         */
        public void onShareSuccess() {}

        /**
         * 分享失败
         */
        public void onShareFail() {}

        /**
         * 分享取消
         */
        public void onShareCancel() {}

        /**
         * 微信登录响应code信息
         *
         * @param code code信息
         */
        public void onWechatLoginRepCode(String code) {}
    }
}
