package com.zizhiguanjia.lib_base.utils;

import android.annotation.SuppressLint;
import android.os.Environment;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

public final class FileUtils {
    private FileUtils() {
        throw new UnsupportedOperationException("无法初始化！");
    }

    public static File getFileByPath(String path) {
        return isSpace(path) ? null : new File(path);
    }

    public static boolean isFileExists(String filePath) {
        return isFileExists(getFileByPath(filePath));
    }

    public static boolean isFileExists(File file) {
        return file != null && file.exists();
    }

    public static boolean isDir(String filePath) {
        return isDir(getFileByPath(filePath));
    }

    public static boolean isDir(File file) {
        return file != null && file.exists() && file.isDirectory();
    }

    public static boolean isFile(File file) {
        return file != null && file.exists() && file.isFile();
    }

    public static boolean isFile(String filePath) {
        return isFile(getFileByPath(filePath));
    }

    public static boolean createDirOrDirExist(File file) {
        boolean var10000;
        label25: {
            if (file != null) {
                if (file.exists()) {
                    if (file.isDirectory()) {
                        break label25;
                    }
                } else if (file.mkdirs()) {
                    break label25;
                }
            }

            var10000 = false;
            return var10000;
        }

        var10000 = true;
        return var10000;
    }

    public static boolean createDirOrDirExist(String filePath) {
        return createDirOrDirExist(getFileByPath(filePath));
    }

    public static boolean createFileOrFileExist(String filePath) {
        return createFileOrFileExist(getFileByPath(filePath));
    }

    public static boolean createFileOrFileExist(File file) {
        if (file == null) {
            return false;
        } else if (file.exists()) {
            return file.isFile();
        } else if (!createDirOrDirExist(file.getParentFile())) {
            return false;
        } else {
            try {
                return file.createNewFile();
            } catch (Exception var2) {
                var2.printStackTrace();
                return false;
            }
        }
    }

    public static boolean createFileByDeleteOldFile(File file) {
        if (file == null) {
            return false;
        } else if (file.exists() && !file.delete()) {
            return false;
        } else if (!createDirOrDirExist(file.getParentFile())) {
            return false;
        } else {
            try {
                return file.createNewFile();
            } catch (IOException var2) {
                return false;
            }
        }
    }

    public static boolean delete(File file) {
        if (file == null) {
            return false;
        } else {
            return file.isDirectory() ? deleteDir(file) : deleteFile(file);
        }
    }

    public static boolean delete(String path) {
        File file = getFileByPath(path);
        if (file == null) {
            return false;
        } else {
            return file.isDirectory() ? deleteDir(file) : deleteFile(file);
        }
    }

    public static boolean deleteDir(String path) {
        return deleteDir(getFileByPath(path));
    }

    public static boolean deleteDir(File dir) {
        if (null == dir) {
            return false;
        } else if (!dir.exists()) {
            return true;
        } else if (!dir.isDirectory()) {
            return false;
        } else {
            File[] files = dir.listFiles();
            if (files != null && files.length > 0) {
                File[] var2 = files;
                int var3 = files.length;

                for(int var4 = 0; var4 < var3; ++var4) {
                    File file = var2[var4];
                    if (file.isFile()) {
                        if (!file.delete()) {
                            return false;
                        }
                    } else if (file.isDirectory() && !deleteDir(file)) {
                        return false;
                    }
                }
            }

            return dir.delete();
        }
    }

    public static boolean deleteFile(String srcFilePath) {
        return deleteFile(getFileByPath(srcFilePath));
    }

    public static boolean deleteFile(File file) {
        return file != null && (!file.exists() || file.isFile() && file.delete());
    }

    private static boolean isSpace(String s) {
        if (s == null) {
            return true;
        } else {
            int i = 0;

            for(int len = s.length(); i < len; ++i) {
                if (!Character.isWhitespace(s.charAt(i))) {
                    return false;
                }
            }

            return true;
        }
    }

    public static long getFileLength(File file) {
        return !isFile(file) ? -1L : file.length();
    }

    public static long getFileLength(String filePath) {
        return getFileLength(getFileByPath(filePath));
    }

    public static long getDirLength(File dir) {
        if (!isDir(dir)) {
            return -1L;
        } else {
            long len = 0L;
            File[] files = dir.listFiles();
            if (files != null && files.length != 0) {
                File[] var4 = files;
                int var5 = files.length;

                for(int var6 = 0; var6 < var5; ++var6) {
                    File file = var4[var6];
                    if (file.isDirectory()) {
                        len += getDirLength(file);
                    } else {
                        len += file.length();
                    }
                }
            }

            return len;
        }
    }

    public static String getFileSize(File file) {
        long len = getFileLength(file);
        return len == -1L ? "" : byte2FitMemorySize(len);
    }

    public static String getFileSize(String filePath) {
        return getFileSize(getFileByPath(filePath));
    }

    public static String getDirSize(File dir) {
        long len = getDirLength(dir);
        return len == -1L ? "" : byte2FitMemorySize(len);
    }

    public static String getDirSize(String dirPath) {
        return getDirSize(getFileByPath(dirPath));
    }

    @SuppressLint({"DefaultLocale"})
    public static String byte2FitMemorySize(long byteNum) {
        if (byteNum < 0L) {
            return "shouldn't be less than zero!";
        } else if (byteNum < 1024L) {
            return String.format("%.3fB", (double)byteNum);
        } else if (byteNum < 1048576L) {
            return String.format("%.3fKB", (double)byteNum / 1024.0D);
        } else {
            return byteNum < 1073741824L ? String.format("%.3fMB", (double)byteNum / 1048576.0D) : String.format("%.3fGB", (double)byteNum / 1.073741824E9D);
        }
    }

    public static List<String> readFileByLine(String filePath) {
        return readFileByLine(getFileByPath(filePath));
    }

    public static List<String> readFileByLine(File file) {
        return readFileByLine(file, 0, 2147483647, (String)null);
    }

    public static List<String> readFileByLine(File file, int st, int end, String charsetName) {
        if (!isFileExists(file)) {
            return null;
        } else if (st > end) {
            return null;
        } else {
            BufferedReader reader = null;

            Object var6;
            try {
                int curLine = 1;
                ArrayList<String> list = new ArrayList();
                if (isSpace(charsetName)) {
                    reader = new BufferedReader(new InputStreamReader(new FileInputStream(file)));
                } else {
                    reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), charsetName));
                }

                String line;
                for(; (line = reader.readLine()) != null && curLine <= end; ++curLine) {
                    if (st <= curLine && curLine <= end) {
                        list.add(line);
                    }
                }

                ArrayList<String> var8 = list;
                return var8;
            } catch (IOException var18) {
                var18.printStackTrace();
                var6 = null;
            } finally {
                try {
                    if (reader != null) {
                        reader.close();
                    }
                } catch (IOException var17) {
                    var17.printStackTrace();
                }

            }

            return (List)var6;
        }
    }

    public static List<String> readFileByLine(BufferedInputStream bis, int st, int end) {
        if (st > end) {
            return null;
        } else {
            BufferedReader reader = null;

            Object var5;
            try {
                int curLine = 1;
                ArrayList<String> list = new ArrayList();

                String line;
                for(reader = new BufferedReader(new InputStreamReader(bis)); (line = reader.readLine()) != null && curLine <= end; ++curLine) {
                    if (st <= curLine && curLine <= end) {
                        list.add(line);
                    }
                }

                ArrayList var7 = list;
                return var7;
            } catch (IOException var17) {
                var17.printStackTrace();
                var5 = null;
            } finally {
                try {
                    if (reader != null) {
                        reader.close();
                    }

                    if (bis != null) {
                        bis.close();
                    }
                } catch (IOException var16) {
                    var16.printStackTrace();
                }

            }

            return (List)var5;
        }
    }

    public static ArrayList<String> getImageFileList(String strPath) {
        ArrayList<String> allFile = new ArrayList();
        File dir = new File(strPath);
        File[] files = dir.listFiles();
        ArrayList<String> wechats = new ArrayList();
        if (files == null) {
            return null;
        } else {
            File[] var7 = files;
            int var8 = files.length;

            for(int var9 = 0; var9 < var8; ++var9) {
                File file = var7[var9];
                if (file.isDirectory()) {
                    getImageFileList(file.getAbsolutePath());
                } else {
                    String filename = file.getName();
                    int j = filename.lastIndexOf(".");
                    String suf = filename.substring(j + 1);
                    if ("jpg".equalsIgnoreCase(suf) || "png".equalsIgnoreCase(suf)) {
                        wechats.add(file.getAbsolutePath());
                        allFile.add(file.getAbsolutePath());
                    }
                }
            }

            return allFile;
        }
    }

    public static long getSdcardAvailableSize() {
        File file = Environment.getExternalStorageDirectory();
        return file.getUsableSpace();
    }

    public static List<File> getFileList(File[] oriFile) {
        List<File> fileList = new ArrayList();
        File[] var2 = oriFile;
        int var3 = oriFile.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            File file = var2[var4];
            if (file.isDirectory()) {
                File[] childFileArr = file.listFiles();
                Collections.addAll(fileList, childFileArr);
            } else if (file.isFile()) {
                Collections.addAll(fileList, new File[]{file});
            }
        }

        return fileList;
    }

    public static List<String> getFilePathList(File[] oriFile) {
        List<File> fileList = new ArrayList();
        File[] var2 = oriFile;
        int var3 = oriFile.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            File file = var2[var4];
            if (file.isDirectory()) {
                File[] childFileArr = file.listFiles();
                Collections.addAll(fileList, childFileArr);
            } else if (file.isFile()) {
                Collections.addAll(fileList, new File[]{file});
            }
        }

        List<String> fileListString = new ArrayList();
        Iterator var8 = fileList.iterator();

        while(var8.hasNext()) {
            File file = (File)var8.next();
            fileListString.add(file.getAbsolutePath());
        }

        return fileListString;
    }

    public static boolean write(File file, String content) {
        FileOutputStream outputStream = null;

        try {
            outputStream = new FileOutputStream(file);
            outputStream.write(content.getBytes());
            outputStream.flush();
            boolean var3 = true;
            return var3;
        } catch (IOException var13) {
            var13.printStackTrace();
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (Exception var12) {
                var12.printStackTrace();
            }

        }

        return false;
    }

    public static boolean writeContent(String content, String filePath) {
        BufferedWriter bw = null;

        try {
            File file = new File(filePath);
            bw = new BufferedWriter(new FileWriter(file, true));
            bw.write(content);
            bw.flush();
            boolean var4 = true;
            return var4;
        } catch (Exception var14) {
            var14.printStackTrace();
        } finally {
            try {
                if (bw != null) {
                    bw.close();
                }
            } catch (Exception var13) {
                var13.printStackTrace();
            }

        }

        return false;
    }

    public static boolean copyFile(File file, InputStream is) {
        BufferedOutputStream os = null;

        boolean var4;
        try {
            os = new BufferedOutputStream(new FileOutputStream(file));
            byte[] data = new byte[8192];

            int len;
            while((len = is.read(data, 0, 8192)) != -1) {
                os.write(data, 0, len);
            }

            boolean var5 = true;
            return var5;
        } catch (IOException var19) {
            var19.printStackTrace();
            var4 = false;
        } finally {
            try {
                is.close();
            } catch (IOException var18) {
                var18.printStackTrace();
            }

            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException var17) {
                var17.printStackTrace();
            }

        }

        return var4;
    }

    public static boolean rename(String filePath, String newName) {
        return rename(getFileByPath(filePath), newName);
    }

    public static boolean rename(File file, String newName) {
        if (file == null) {
            return false;
        } else if (!file.exists()) {
            return false;
        } else if (isSpace(newName)) {
            return false;
        } else if (newName.equals(file.getName())) {
            return true;
        } else {
            File newFile = new File(file.getParent() + File.separator + newName);
            return !newFile.exists() && file.renameTo(newFile);
        }
    }
}