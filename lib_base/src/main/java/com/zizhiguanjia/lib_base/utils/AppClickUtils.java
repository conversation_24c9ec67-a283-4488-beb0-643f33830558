package com.zizhiguanjia.lib_base.utils;

import android.os.SystemClock;

public class AppClickUtils {
    private static AppClickUtils appClickUtils;
    private final static int COUNTS = 3;//点击次数
    private final static long DURATION = 3 * 1000;//规定有效时间
    private long[] mHits = new long[COUNTS];
    public static AppClickUtils getInstance(){
        synchronized (AppClickUtils.class){
            if(appClickUtils==null){
                return appClickUtils=new AppClickUtils();
            }
        }
        return appClickUtils;
    }
    public boolean exitAfterMany() {
        System.arraycopy(mHits, 1, mHits, 0, mHits.length - 1);
        mHits[mHits.length - 1] = SystemClock.uptimeMillis();//System.currentTimeMillis()
        if ((mHits[mHits.length - 1] - mHits[0] <= DURATION)) {
            return true;
        }
        return false;
    }
}