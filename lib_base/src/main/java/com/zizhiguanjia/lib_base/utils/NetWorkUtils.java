package com.zizhiguanjia.lib_base.utils;

import android.view.Gravity;

import com.wb.lib_utils.utils.NetworkUtils;
import com.wb.lib_utils.utils.ToastUtils;

/**
 * 网络检测工具类
 */
public class NetWorkUtils {
    private static NetWorkUtils netWorkUtils;
    public static NetWorkUtils getInstance() {
        if (netWorkUtils == null) {
            synchronized (NetWorkUtils.class) {
                return netWorkUtils = new NetWorkUtils();
            }
        }
        return netWorkUtils;
    }
    public boolean checkNetWork(){
        if(NetworkUtils.isAvailable()){
            return true;
        }else{
            ToastUtils.normal("当前无网络！", Gravity.CENTER);
            return false;
        }
    }
}
