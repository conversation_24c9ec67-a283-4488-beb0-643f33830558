package com.zizhiguanjia.lib_base.utils;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import androidx.core.app.ActivityCompat;

import com.github.anzewei.parallaxbacklayout.ParallaxHelper;
import com.huawei.hms.ads.identifier.AdvertisingIdClient;
import com.wb.lib_utils.permission.PermissionListener;
import com.wb.lib_utils.permission.PermissionUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.lib_base.DebugApplication;
import com.zizhiguanjia.lib_base.emums.MetaDataKeyEnum;
import com.zizhiguanjia.lib_base.helper.MessageHelper;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

public class AppUtils {
    private static AppUtils appUtils;

    public static AppUtils getInstance() {
        if (appUtils == null) {
            synchronized (AppUtils.class) {
                return appUtils = new AppUtils();
            }
        }
        return appUtils;
    }

    public String getAppMetaData(Context context) {
        String key = "JPUSH_CHANNEL";
        if (context == null || TextUtils.isEmpty(key)) {
            return null;
        }
        String channelNumber = null;
        try {
            PackageManager packageManager = context.getPackageManager();
            if (packageManager != null) {
                ApplicationInfo applicationInfo = packageManager.getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);
                if (applicationInfo != null) {
                    if (applicationInfo.metaData != null) {
                        channelNumber = applicationInfo.metaData.getString(key);
                    }
                }
            }
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return "1";
        }
        try {
            if (channelNumber.equals("guanfang")) {
                return "1";
            }
        } catch (Exception e) {
            return "1";
        }

        return channelNumber;
        //        String key="JPUSH_CHANNEL";
        //        if (context == null || TextUtils.isEmpty(key)) {
        //            return "1";
        //        }
        //        String channelNumber = null;
        //        try {
        //            PackageManager packageManager = context.getPackageManager();
        //            if (packageManager != null) {
        //                ApplicationInfo applicationInfo = packageManager.getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);
        //                if (applicationInfo != null) {
        //                    if (applicationInfo.metaData != null) {
        //                        try {
        //                            channelNumber = String.valueOf(applicationInfo.metaData.getInt(key));
        //                            if(channelNumber==null||channelNumber.isEmpty()){
        //                                return "1";
        //                            }
        //                        }catch (Exception e){
        //                            channelNumber = applicationInfo.metaData.getString(key);
        //                            if(channelNumber==null||channelNumber.isEmpty()){
        //                                return "1";
        //                            }
        //                        }
        //
        //                    }
        //                }
        //            }
        //        } catch (PackageManager.NameNotFoundException e) {
        //            e.printStackTrace();
        //            return "1";
        //        }
        //        return channelNumber;
    }

    /**
     * 获取App打包渠道
     */
    public String getAppChannel(Context context) {
        String data = getAppMetaData(MetaDataKeyEnum.APP_SEND_PLARFORM_CODE);
        return data == null || data.isEmpty() ? "1" : data;
    }

    /**
     * 获取App应用名称
     */
    public String getAppName() {
        return DebugApplication.mApplicationContext.getString(DebugApplication.mApplicationContext.getApplicationInfo().labelRes);
    }

    /**
     * 获取MetaData数据
     */
    public String getAppMetaData(MetaDataKeyEnum keyEnum) {
        String channelNumber = null;
        try {
            PackageManager packageManager = DebugApplication.mApplicationContext.getPackageManager();
            if (packageManager != null) {
                ApplicationInfo applicationInfo = packageManager.getApplicationInfo(DebugApplication.mApplicationContext.getPackageName(),
                        PackageManager.GET_META_DATA);
                if (applicationInfo.metaData != null) {
                    channelNumber = String.valueOf(applicationInfo.metaData.get(keyEnum.getAgreementSource()));
                }
            }
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return "";
        }
        return channelNumber;
    }

    /**
     * 从assets文件夹中读取文件内容
     */
    public String readFromAssets(String fileName) {
        AssetManager assetManager = DebugApplication.mApplicationContext.getAssets();
        StringBuilder stringBuilder = new StringBuilder();

        try (InputStream inputStream = assetManager.open(fileName); BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {

            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            return "";
        }

        return stringBuilder.toString();
    }

    /**
     * 获取oaId信息
     */
    public String getOaIdInfo() {
        try {
            AdvertisingIdClient.Info info = AdvertisingIdClient.getAdvertisingIdInfo(DebugApplication.mApplicationContext);
            if (null != info) {
                return StringUtils.isEmpty(info.getId()) ? "" : info.getId();
            }
        } catch (IOException ignored) {
        }
        return "";
    }

    @SuppressLint("HardwareIds")
    public void getImeiInfo(GetImeiInfoCallback callback) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10 及以上无法获取 IMEI
            callback.result("");
            return;
        }
        Context applicationContext = DebugApplication.mApplicationContext;
        if (ActivityCompat.checkSelfPermission(applicationContext, android.Manifest.permission.READ_PHONE_STATE)
                != PackageManager.PERMISSION_GRANTED) {
            // 没有权限，返回空或提示外部处理权限申请
            PermissionUtils.with(DebugApplication.getCurrentActivity()).addPermissions(Manifest.permission.READ_PHONE_STATE)
                    .setPermissionsCheckListener(new PermissionListener() {
                        @Override
                        public void permissionRequestSuccess() {
                            getImeiInfo(callback);
                        }

                        @Override
                        public void permissionRequestFail(String[] grantedPermissions, String[] deniedPermissions, String[] forceDeniedPermissions) {
                            callback.result(null);
                        }
                    })
                    .createConfig()
                    .setForceAllPermissionsGranted(false)
                    .setForceDeniedPermissionTips("请前往设置->应用->【" + this.getAppName() + "】->权限中打开相关权限，否则功能无法正常运行！")
                    .buildConfig()
                    .startCheckPermission();
        }else {
            TelephonyManager tm = (TelephonyManager) applicationContext.getSystemService(Context.TELEPHONY_SERVICE);
            if (tm != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    callback.result(StringUtils.isEmpty(tm.getImei()) ? "" : tm.getImei());// API 26+
                } else {
                    callback.result(StringUtils.isEmpty(tm.getDeviceId()) ? "" : tm.getDeviceId()); // 已废弃，但兼容老版本
                }
            } else {
                callback.result("");
            }
        }
    }

    public static interface GetImeiInfoCallback {
        public void result(String info);
    }
}
