package com.zizhiguanjia.lib_base.utils;

import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.wb.lib_image_loadcal.ImageManager;
import com.zizhiguanjia.lib_base.R;

import androidx.databinding.BindingAdapter;
import androidx.databinding.ObservableField;
import androidx.databinding.adapters.ListenerUtil;

public class BindingAdapters {

    @BindingAdapter("android:src")
    public static void setSrc(ImageView view, Bitmap bitmap) {
        view.setImageBitmap(bitmap);
    }

    @BindingAdapter("android:src")
    public static void setSrc(ImageView view, int resId) {
        view.setImageResource(resId);
    }

    /**
     * 网络图片
     *
     * @param imageView
     * @param url
     * @param placeholder 占位
     * @param error       错误
     */
    @BindingAdapter(value = {"url", "placeholder", "error"}, requireAll = false)
    public static void loadImage(ImageView imageView, String url, int placeholder, int error) {
        if (placeholder == 0) {
            placeholder = Color.BLACK;
        }
        if (error == 0) {
            error = Color.BLACK;
        }
        ImageManager.getInstance().displayImage(url, imageView, placeholder, error);
    }

    /**
     * 防止多次点击的OnClick实现
     * <p>
     * <EditText
     * android:onTextChanged="@{(str)-> xxx.onTextChanged(str)}" />
     *
     * @param editText
     * @param listener
     */
    @BindingAdapter("android:onTextChanged")
    public static void setTextChangedListener(EditText editText, final OnTextChangedListener listener) {
        if (listener != null) {
            TextWatcher textWatcher = new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    listener.onTextChanged(s.toString());
                }

                @Override
                public void afterTextChanged(Editable s) {

                }
            };
            TextWatcher oldWatcher = ListenerUtil.trackListener(editText, textWatcher, R.id.textWatcher);
            if (oldWatcher != null) {
                editText.removeTextChangedListener(oldWatcher);
            } else {
                editText.addTextChangedListener(textWatcher);
            }
        }
    }

    public interface OnTextChangedListener {
        void onTextChanged(String text);
    }


    @BindingAdapter("android:textColor")
    public static void setTextColor(TextView view, String color) {
        if(color != null){
            view.setTextColor(Color.parseColor(color));
        }
    }

    @BindingAdapter("android:background")
    public static void setBackground(View view, ObservableField<Integer> color) {
        if (color != null && color.get() != null) {
            view.setBackgroundColor(color.get());
        }
    }

    @BindingAdapter("android:background")
    public static void setBackground(View view, String color) {
        view.setBackgroundColor(Color.parseColor(color));
    }

    @BindingAdapter("android:backgroundTint")
    public static void setBackgroundTint(View view, String color) {
        view.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(color)));
    }

    @BindingAdapter("android:tint")
    public static void setBackgroundTintList(ImageView view, String color) {
        view.setImageTintList(ColorStateList.valueOf(Color.parseColor(color)));
    }
}