package com.zizhiguanjia.lib_base.utils;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;


/**
 * 功能作用：加解密工具类
 * 初始注释时间： 2024/7/6 19:07
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class EncryptDecryptOptions {

    private String privateKey = "-----BEGIN PRIVATE KEY-----\n" + "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJXyA7VG/ahXq3nd\n" +
            "txw/xVS+XWxmI/uIJsiBBHkeA9h/oeoQzaqBqZ/q8tyyus7MN4erKigakZT1zATE\n" +
            "YWHypxmbbxh2qni0X1/fjlzs8qafI2oLrGJ+XgGi712biaD4a1DrQrVmpAa87+uq\n" +
            "uCi22BMXictyq5IzRWnik6aiPEhHAgMBAAECgYBKJdtfkTNuQ6wwH0ulVIOoXFqm\n" +
            "d/vjLni/Pc2Qdx8/uB3BOX36ixPuWKiwKlHmYWY0WJgILNtA5it1UAc4AF5QsZaO\n" +
            "Wat3g7EJphHcz0z7VSPrxbLdwfJv8+utO5gDjlWz/ayq08X852dVY+1SHxjibfHp\n" +
            "EC4J0gksBvkTbn2RQQJBAMeMnbAydNraWdZ/QEDq4QMPKP3YOZbUEt5zfvm91mmH\n" +
            "a/co2c0pbMXao7S999Et6ZhnYL0PI/f7C6+I61YBhr0CQQDAXRO0IMwuo1CMaYUv\n" +
            "pl5ljGEUqhu41jFzUFHXR7jQSU2Da6CSlZR85ScCyMnbBjleHN0TFR2oh55XJhNk\n" +
            "3g1TAkEAtYhym6wLKl2hhMTWyzfh40qTW/qsyU28rKodRU0yFWahDYQYP9ADqCqy\n" +
            "6uY24BEIhSDmnJd9fUbg5puEnAVfcQJAHU7RDrOkiEKHpiz5hs4BOdWOgPh96sZv\n" +
            "mFMXdQZPaSSR88D1IQA7NGAxdmIjJwSmvukE4cBo2WF4RWzerZKLUwJAcyUiwEOl\n" +
            "SUuBSGcMg4gDQPStgALZJxoW65zbaZ+pFLRDPmRPCiv/hLsbeJ1I5nOEyjwAVTcN\n" + "mVODW8bq3qtJwg==\n" + "-----END PRIVATE KEY-----".replace(
            "-----BEGIN PRIVATE KEY-----", "").replace("-----END PRIVATE KEY-----", "").replaceAll("\\s", "");
    /**
     * 换成的aesKey信息
     */
    private String saveAesKey;

    /**
     * 设置AesKey信息
     */
    public void setAesKey(String key) {
        if (key == null || key.isEmpty()) {
            saveAesKey = null;
            return;
        }
        try {
            saveAesKey = rsaDecrypt(key, stringToPrivateKey(privateKey));
        } catch (Exception ignore) {
            saveAesKey = key;
        }
    }

    public String decryptData(String json) {
        if (saveAesKey == null || saveAesKey.isEmpty()) {
            return json;
        }
        try {
            json = aesDecrypt(json, this.saveAesKey);
        } catch (Exception ignore) {
        }
        return json;
    }

    // 从Base64编码的字符串恢复私钥
    private PrivateKey stringToPrivateKey(String keyString) throws Exception {
        byte[] decodedKey = Base64.getDecoder().decode(keyString.replace("-----BEGIN PRIVATE KEY-----", "").replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", ""));
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
        return KeyFactory.getInstance("RSA").generatePrivate(keySpec);
    }

    // 使用私钥解密字符串数据
    private String rsaDecrypt(String encryptedData, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    // 使用AES密钥和指定的IV解密字符串数据
    private String aesDecrypt(String encryptedData, String aesKey) throws Exception {
        byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
        SecretKeySpec keySpec = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }
}
