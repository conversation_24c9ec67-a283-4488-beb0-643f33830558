package com.zizhiguanjia.lib_base.module;

public class ModuleLifecycleReflexs {
    private static final String APP_ID = "com.zizhiguanjia";
    private static final String COMMON_INIT =APP_ID+".model_common.ModuleInit";
    private static final String DATA_INIT = APP_ID+".model_data.ModuleInit";
    private static final String INIT_INIT = APP_ID+".model_init.ModuleInit";
    private static final String LOGIN_INIT = APP_ID+".model_account.ModuleInit";
    private static final String PAY_INIT = APP_ID+".model_pay.ModuleInit";
    private static final String USER_INIT = APP_ID+".model_user.ModuleInit";
    private static final String POINT_INIT = APP_ID+".model_point.ModuleInit";
    private static final String CONFIG_INIT = APP_ID+".model_config.ModuleInit";
    private static final String ACCOUNT_INIT = APP_ID+".model_account.ModuleInit";
    private static final String ADDRESS_INIT = APP_ID+".model_address.ModuleInit";
    private static final String CERTIFICATE_INIT = APP_ID+".model_certificate.ModuleInit";
    private static final String CORE_INIT = APP_ID+".model_core.ModuleInit";
    private static final String FIXBUG_INIT = APP_ID+".model_fixbug.ModuleInit";
    private static final String LIST_INIT = APP_ID+".model_list.ModuleInit";
//    private static final String MAIN_INIT = APP_ID+".model_main.ModuleInit";
    private static final String SDK_INIT = APP_ID+".model_sdk.ModuleInit";
    private static final String UMENG_INIT = APP_ID+".model_umeng.ModuleInit";
    private static final String VIDEO_INIT = APP_ID+".model_video.ModuleInit";
    private static final String CARSE_INIT = APP_ID+".model_carse.ModuleInit";
    private static final String HOME_INIT = APP_ID+".model_home.ModuleInit";
    private static final String TASK_INIT = APP_ID+".model_task.ModuleInit";
    private static final String UTILS = APP_ID+".model_utils.ModuleInit";
    public static String[] initModuleNames = {
            COMMON_INIT,
            CARSE_INIT,
            DATA_INIT,
            INIT_INIT,
            LOGIN_INIT,
            PAY_INIT,
            USER_INIT,
            POINT_INIT,
            CONFIG_INIT,
            ACCOUNT_INIT,
            ADDRESS_INIT,
            CERTIFICATE_INIT,
            CORE_INIT,
            FIXBUG_INIT,
            LIST_INIT,
//            MAIN_INIT,
            SDK_INIT,
            UMENG_INIT,
            VIDEO_INIT,
            HOME_INIT,
            TASK_INIT,
            UTILS,
    };
}