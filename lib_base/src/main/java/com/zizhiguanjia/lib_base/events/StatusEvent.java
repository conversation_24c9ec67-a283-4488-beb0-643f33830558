package com.zizhiguanjia.lib_base.events;

import androidx.lifecycle.LifecycleOwner;

public class StatusEvent extends SingleLiveEvent<StatusEvent.HttpStatus> {

    public void observe(LifecycleOwner owner, final StatusEvent.StatusObserver observer) {
        super.observe(owner, t -> {
            if (t != null) {
                observer.onStatusChanged(t);
            }
        });
    }

    public interface StatusObserver {
        void onStatusChanged(HttpStatus status);
    }

    /**
     * 状态
     */
    public enum HttpStatus {
        LOADING,
        SUCCESS,
        FAILURE,
        ERROR
    }
}
