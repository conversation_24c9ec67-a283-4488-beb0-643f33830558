package com.zizhiguanjia.lib_base.events;

import androidx.lifecycle.LifecycleOwner;

import cn.hutool.core.util.ObjectUtil;

public class MessageEvent extends SingleLiveEvent<String> {

    public void observe(LifecycleOwner owner, final MessageObserver observer) {
        super.observe(owner, t -> {
            if (ObjectUtil.isNotNull(t)) {
                observer.onMessage(t);
            }
        });
    }

    public interface MessageObserver {
        void onMessage(String message);
    }
}
