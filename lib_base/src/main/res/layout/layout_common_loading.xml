<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <io.supercharge.shimmerlayout.ShimmerLayout
        android:id="@+id/shimmer_text"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >

       <LinearLayout
           android:background="@color/white"
           android:orientation="vertical"
           android:layout_width="match_parent"
           android:layout_height="match_parent">
           <LinearLayout
               android:paddingLeft="12.5dp"
               android:layout_marginLeft="10dp"
               android:layout_marginTop="10dp"
               android:layout_marginRight="10dp"
               android:orientation="vertical"
               android:background="@drawable/loading_bg"
               android:layout_width="match_parent"
               android:layout_height="106dp">
                <View
                    android:layout_marginTop="18dp"
                    android:layout_width="114.5dp"
                    android:layout_height="18dp"
                    android:background="#E0E0E0"/>
               <View
                   android:layout_marginTop="16.5dp"
                   android:layout_width="75dp"
                   android:layout_height="16dp"
                   android:background="#E9E9E9"/>
               <View
                   android:layout_marginTop="6dp"
                   android:layout_width="209.5dp"
                   android:layout_height="16dp"
                   android:background="#E9E9E9"/>
           </LinearLayout>
           <LinearLayout
               android:paddingLeft="12.5dp"
               android:layout_marginLeft="10dp"
               android:layout_marginTop="10dp"
               android:layout_marginRight="10dp"
               android:orientation="vertical"
               android:background="@drawable/loading_bg"
               android:layout_width="match_parent"
               android:layout_height="84dp">
               <View
                   android:layout_marginTop="18dp"
                   android:layout_width="114.5dp"
                   android:layout_height="18dp"
                   android:background="#E0E0E0"/>
               <View
                   android:layout_marginTop="16.5dp"
                   android:layout_width="75dp"
                   android:layout_height="16dp"
                   android:background="#E9E9E9"/>
           </LinearLayout>
           <LinearLayout
               android:paddingLeft="12.5dp"
               android:layout_marginLeft="10dp"
               android:layout_marginTop="10dp"
               android:layout_marginRight="10dp"
               android:orientation="vertical"
               android:background="@drawable/loading_bg"
               android:layout_width="match_parent"
               android:layout_height="84dp">
               <View
                   android:layout_marginTop="18dp"
                   android:layout_width="114.5dp"
                   android:layout_height="18dp"
                   android:background="#E0E0E0"/>
               <View
                   android:layout_marginTop="16.5dp"
                   android:layout_width="75dp"
                   android:layout_height="16dp"
                   android:background="#E9E9E9"/>
           </LinearLayout>
           <LinearLayout
               android:paddingLeft="12.5dp"
               android:layout_marginLeft="10dp"
               android:layout_marginTop="10dp"
               android:layout_marginRight="10dp"
               android:orientation="vertical"
               android:background="@drawable/loading_bg"
               android:layout_width="match_parent"
               android:layout_height="106dp">
               <View
                   android:layout_marginTop="18dp"
                   android:layout_width="114.5dp"
                   android:layout_height="18dp"
                   android:background="#E0E0E0"/>
               <View
                   android:layout_marginTop="16.5dp"
                   android:layout_width="75dp"
                   android:layout_height="16dp"
                   android:background="#E9E9E9"/>
               <View
                   android:layout_marginTop="6dp"
                   android:layout_width="209.5dp"
                   android:layout_height="16dp"
                   android:background="#E9E9E9"/>
           </LinearLayout>
           <LinearLayout
               android:paddingLeft="12.5dp"
               android:layout_marginLeft="10dp"
               android:layout_marginTop="10dp"
               android:layout_marginRight="10dp"
               android:orientation="vertical"
               android:background="@drawable/loading_bg"
               android:layout_width="match_parent"
               android:layout_height="106dp">
               <View
                   android:layout_marginTop="18dp"
                   android:layout_width="114.5dp"
                   android:layout_height="18dp"
                   android:background="#E0E0E0"/>
               <View
                   android:layout_marginTop="16.5dp"
                   android:layout_width="75dp"
                   android:layout_height="16dp"
                   android:background="#E9E9E9"/>
               <View
                   android:layout_marginTop="6dp"
                   android:layout_width="209.5dp"
                   android:layout_height="16dp"
                   android:background="#E9E9E9"/>
           </LinearLayout>
           <LinearLayout
               android:paddingLeft="12.5dp"
               android:layout_marginLeft="10dp"
               android:layout_marginTop="10dp"
               android:layout_marginRight="10dp"
               android:orientation="vertical"
               android:background="@drawable/loading_bg"
               android:layout_width="match_parent"
               android:layout_height="84dp">
               <View
                   android:layout_marginTop="18dp"
                   android:layout_width="114.5dp"
                   android:layout_height="18dp"
                   android:background="#E0E0E0"/>
               <View
                   android:layout_marginTop="16.5dp"
                   android:layout_width="75dp"
                   android:layout_height="16dp"
                   android:background="#E9E9E9"/>
           </LinearLayout>
           <LinearLayout
               android:paddingLeft="12.5dp"
               android:layout_marginLeft="10dp"
               android:layout_marginTop="10dp"
               android:layout_marginRight="10dp"
               android:orientation="vertical"
               android:background="@drawable/loading_bg"
               android:layout_width="match_parent"
               android:layout_height="84dp">
               <View
                   android:layout_marginTop="18dp"
                   android:layout_width="114.5dp"
                   android:layout_height="18dp"
                   android:background="#E0E0E0"/>
               <View
                   android:layout_marginTop="16.5dp"
                   android:layout_width="75dp"
                   android:layout_height="16dp"
                   android:background="#E9E9E9"/>
           </LinearLayout>
       </LinearLayout>
    </io.supercharge.shimmerlayout.ShimmerLayout>
</LinearLayout>