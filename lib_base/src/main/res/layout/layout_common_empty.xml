<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/empty_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/empty_retry_view"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_centerInParent="true"
        android:src="@drawable/nodata"
        />

    <TextView
        style="@style/MultipleStatusView.Content"
        android:id="@+id/empty_view_tv"
        android:layout_below="@id/empty_retry_view"
        android:text="@string/empty_view_hint"/>
</RelativeLayout>