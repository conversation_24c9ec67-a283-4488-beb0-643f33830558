<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.wb.lib_refresh_layout.SmartRefreshLayout
            android:id="@+id/refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.wb.lib_refresh_layout.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>

            <com.wb.lib_refresh_layout.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

        </com.wb.lib_refresh_layout.SmartRefreshLayout>

        <!--<com.yyxnb.widget.EmptyView
            android:id="@+id/empty_view"
            android:layout_width="match_parent"
            android:layout_marginTop="100dp"
            android:layout_height="match_parent"/>-->
    </FrameLayout>
</layout>