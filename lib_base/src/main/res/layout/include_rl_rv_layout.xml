<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:skin="http://schemas.android.com/android/skin"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ResourceName">

    <data>
    </data>

    <com.wb.lib_refresh_layout.SmartRefreshLayout
        android:id="@+id/mRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorBackground"
        app:srlEnableOverScrollBounce="true"
        skin:enable="true">

        <com.wb.lib_weiget.status.StatusView
            android:id="@+id/mStatusView"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </com.wb.lib_weiget.status.StatusView>

    </com.wb.lib_refresh_layout.SmartRefreshLayout>
</layout>