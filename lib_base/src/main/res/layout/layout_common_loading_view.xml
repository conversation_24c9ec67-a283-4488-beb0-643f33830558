<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <io.supercharge.shimmerlayout.ShimmerLayout
        android:id="@+id/shimmer_text"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="#F4F7F9" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <View
                    android:layout_width="52dp"
                    android:layout_height="16dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="17dp"
                    android:layout_marginRight="12dp"
                    android:background="#F0F0F0" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="31dp"
                    android:layout_marginRight="12dp"
                    android:gravity="center_vertical">

                    <View
                        android:layout_width="38dp"
                        android:layout_height="21dp"
                        android:background="#F0F0F0" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="16dp"
                        android:layout_marginLeft="6dp"
                        android:background="#F0F0F0" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="16dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="12dp"
                    android:background="#F0F0F0" />

                <View
                    android:layout_width="175dp"
                    android:layout_height="16dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="7dp"
                    android:layout_marginRight="12dp"
                    android:background="#F0F0F0" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="124dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="11dp"
                    android:layout_marginRight="12dp"
                    android:background="#F0F0F0" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="11dp"
                    android:layout_marginRight="12dp"
                    android:background="#F6F6F6"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="11.5dp"
                        android:background="@drawable/layout_content_load_yuan_bg" />

                    <View
                        android:layout_width="75dp"
                        android:layout_height="16dp"
                        android:layout_marginLeft="7dp"
                        android:background="#E0E0E0" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="11dp"
                    android:layout_marginRight="12dp"
                    android:background="#F6F6F6"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="11.5dp"
                        android:background="@drawable/layout_content_load_yuan_bg" />

                    <View
                        android:layout_width="75dp"
                        android:layout_height="16dp"
                        android:layout_marginLeft="7dp"
                        android:background="#E0E0E0" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="11dp"
                    android:layout_marginRight="12dp"
                    android:background="#F6F6F6"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="11.5dp"
                        android:background="@drawable/layout_content_load_yuan_bg" />

                    <View
                        android:layout_width="75dp"
                        android:layout_height="16dp"
                        android:layout_marginLeft="7dp"
                        android:background="#E0E0E0" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="11dp"
                    android:layout_marginRight="12dp"
                    android:background="#F6F6F6"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="11.5dp"
                        android:background="@drawable/layout_content_load_yuan_bg" />

                    <View
                        android:layout_width="75dp"
                        android:layout_height="16dp"
                        android:layout_marginLeft="7dp"
                        android:background="#E0E0E0" />
                </LinearLayout>

                <View
                    android:layout_width="65dp"
                    android:layout_height="18dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="34.5dp"
                    android:background="#DFDFDF" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="13dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="75dp"
                        android:layout_height="16dp"
                        android:background="#F0F0F0" />

                    <View
                        android:layout_width="75dp"
                        android:layout_height="16dp"
                        android:layout_marginLeft="17.5dp"
                        android:background="#F0F0F0" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10.5dp"
                    android:layout_marginTop="14dp"
                    android:background="#F0F0F0" />

                <View
                    android:layout_width="65.5dp"
                    android:layout_height="18dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="14dp"
                    android:background="#DFDFDF" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="18dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginRight="12dp"
                    android:background="#F0F0F0" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="18dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginRight="12dp"
                    android:background="#F0F0F0" />

                <View
                    android:layout_width="164dp"
                    android:layout_height="18dp"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginBottom="45dp"
                    android:background="#F0F0F0" />
            </LinearLayout>
        </LinearLayout>
    </io.supercharge.shimmerlayout.ShimmerLayout>
</LinearLayout>