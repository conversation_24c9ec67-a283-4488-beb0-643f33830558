<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_gravity="center"
    android:gravity="center"
    android:padding="10dp"
    android:background="@drawable/_popup_loading_bg"
    android:orientation="horizontal"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.wb.lib_pop.widget.LoadingView
        android:layout_marginStart="3dp"
        android:layout_marginEnd="3dp"
        android:layout_width="20dp"
        android:layout_height="20dp" />
    <TextView
        android:id="@+id/tv_title"
        android:maxLines="1"
        android:visibility="gone"
        android:ellipsize="end"
        android:textSize="15sp"
        android:textColor="#EEEEEE"
        android:paddingStart="5dp"
        android:paddingEnd="14dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
</LinearLayout>