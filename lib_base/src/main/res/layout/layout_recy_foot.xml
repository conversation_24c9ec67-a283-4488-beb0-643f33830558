<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:layout_below="@+id/top1"
        android:id="@+id/llBottomView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginBottom="14dp"
            android:background="#F4F4F4" />

        <LinearLayout
            android:layout_marginBottom="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginLeft="15dp"
            android:id="@+id/llRestartExam"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/core_exam_sheet_post_bg"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="11dp"
            android:paddingBottom="11dp">

            <ImageView
                android:id="@+id/imgRest"
                android:layout_width="23dp"
                android:layout_height="22dp"
                android:src="@drawable/core_exam_icon_cxlx" />

            <TextView
                android:layout_marginLeft="8dp"
                android:id="@+id/tvReStartExam"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="重新练习"
                android:textColor="#3163F6"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>