<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.zizhiguanjia.lib_base">
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <application
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="N">
        <!-- 适配华为（huawei）刘海屏 -->
        <meta-data
            android:name="android.notch_support"
            android:value="true" />
        <!-- 适配小米（xiaomi）刘海屏 -->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />
        <!-- 允许绘制到oppo、vivo刘海屏机型的刘海区域 -->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />
        <!-- 适配 Android 7.0 文件意图 -->
        <provider
            android:name=".provider.MyProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">

            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <provider
            android:name=".provider.MyProvider"
            android:authorities="${applicationId}.fileprovider.myprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/register_face_paths" />
        </provider>
        <meta-data
            android:name="design_width_in_dp"
            android:value="375" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="667" />
        <activity
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustResize"
            android:name=".base.ContainerActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
            android:label="${APP_NAME}"
            android:theme="@style/BaseAppTheme" >
        </activity>
        <activity
            android:screenOrientation="portrait"
            android:name="com.wb.lib_pop.util.Permission$PermissionActivity"/>
    </application>
</manifest>