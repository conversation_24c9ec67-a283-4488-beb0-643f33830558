package com.zizhiguanjia.model_message.api;

import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.model_message.bean.CityPickerBean;

import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface MessageApi {
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/exam/AddUserExamTip")
    Observable<BaseData> addUserExamTip(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/examCoupon/CollectionCoupon")
    Observable<BaseData> lingCoupon(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Common/IgnoreAPPVersionUpgrade")
    Observable<BaseData> ignoreUpdata(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/common/GetAreas")
    Observable<BaseData<CityPickerBean>> getCityList(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/common/GetMajorsByAreaId")
    Observable<BaseData<CityPickerBean>> getSecondConstruction(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/User/UpdateOrder")
    Observable<BaseData> bindSubject(@FieldMap Map<String,String> params);
}
