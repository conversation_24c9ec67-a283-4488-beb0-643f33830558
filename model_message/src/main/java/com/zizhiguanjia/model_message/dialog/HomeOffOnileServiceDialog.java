package com.zizhiguanjia.model_message.dialog;

import android.content.Context;
import android.graphics.Color;
import android.text.Html;
import android.text.SpannableStringBuilder;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.listeners.HomeFootListener;
import com.zizhiguanjia.lib_base.utils.SpannableStringUtils;
import com.zizhiguanjia.model_message.R;

import cn.hutool.core.util.PhoneUtil;

public class HomeOffOnileServiceDialog extends CenterPopupView implements View.OnClickListener {
    private TextView messageTv,titleTv,onOnlieTv;
    private String titleStr,subStr,urlStr;
    private ImageView closeOffImg;
    private HomeFootListener homeFootListener;
    public HomeOffOnileServiceDialog(@NonNull Context context, String titleStr, String subStr, String urlStr, HomeFootListener homeFootListener) {
        super(context);
        this.titleStr = titleStr;
        this.homeFootListener=homeFootListener;
        this.subStr = subStr;
        this.urlStr = urlStr;
    }
    @Override
    protected int initLayoutResId() {
        return R.layout.message_offonline_service_layout;
    }

    @Override
    protected int getMaxWidth() {
        return DpUtils.dp2px(getContext(),293);
    }

    @Override
    protected void init() {
        super.init();
        String str="<font color='#1F3C64'>工作时间：</font>"+"<font color='#FF6600'>"+subStr+"</font>"+"<br>"+ "<font color='#1F3C64'>如有疑问，请点击“在线客服”留言</font>";
//        String c="工作时间:<font color='#1F3C64'>工作日8:30-17:30</font><br/>如有疑问，请点击“在线客服”留言";
        messageTv.setText(Html.fromHtml(str));
        titleTv.setText(titleStr);
    }

    @Override
    protected void initView() {
        super.initView();
        messageTv=this.findViewById(R.id.messgaeTv);
        titleTv=this.findViewById(R.id.titleTv);
        closeOffImg=this.findViewById(R.id.closeOffImg);
        onOnlieTv=this.findViewById(R.id.onOnlieTv);
        closeOffImg.setOnClickListener(this);
        onOnlieTv.setOnClickListener(this);
    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(),325);
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.closeOffImg){
            dismiss();
        }else if(v.getId()==R.id.onOnlieTv){
            homeFootListener.onToUrl(urlStr);
            dismiss();
        }
    }
}
