package com.zizhiguanjia.model_message.dialog;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.contrarywind.adapter.WheelAdapter;
import com.contrarywind.listener.OnItemSelectedListener;
import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.bean.CityPickerData;
import com.zizhiguanjia.model_message.bean.ParentSubjectsBean;
import com.zizhiguanjia.model_message.listenter.IHomeSelectCity;
import com.zizhiguanjia.model_message.view.WheelView;

import java.util.List;

import androidx.annotation.NonNull;

public class HomeSelectCityDialog extends CenterPopupView implements View.OnClickListener {
    private WheelView city1;
    private List<?> cityPickerDataLists;
    private int currentPostion;
    private TextView messageBt;
    private IHomeSelectCity iHomeSelectCity;
    private ImageView messageClose;
    private int type;
    private TextView tvTitle;

    public HomeSelectCityDialog(@NonNull Context context, List<?> cityPickerDataLists, IHomeSelectCity iHomeSelectCity, int type) {
        super(context);
        this.cityPickerDataLists = cityPickerDataLists;
        this.iHomeSelectCity = iHomeSelectCity;
        this.type = type;
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
    }

    @Override
    protected int initLayoutResId() {
        return R.layout.message_select_city_layout;
    }

    @Override
    protected int getMaxWidth() {
        return DpUtils.dp2px(getContext(), 310);
    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(), 230);
    }

    @Override
    protected void init() {
        super.init();
        initSfData();
    }

    private void initSfData() {
        city1.setAdapter(new ArrayWheelCityAdapter(cityPickerDataLists));
        city1.setCurrentItem(0);
        city1.setCyclic(false);
        city1.setAlphaGradient(true);
        city1.setGravity(Gravity.CENTER);
        city1.setIsOptions(true);
        city1.setDividerType(WheelView.DividerType.FILL);
        city1.setItemsVisibleCount(3);
        city1.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                currentPostion = index;
            }
        });
        tvTitle.setText(type == 1 ? "请选择相应的城市" : type == 2 ?"请选择相应的证书" : type == 3 ? "请选择相应的分类" : "");
    }

    @Override
    protected void initView() {
        super.initView();
        city1 = this.findViewById(R.id.messageWheelCity);
        messageBt = this.findViewById(R.id.messageBt);
        messageClose = this.findViewById(R.id.messageClose);
        tvTitle = this.findViewById(R.id.tvTitle);
        messageBt.setOnClickListener(this);
        messageClose.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.messageBt) {
            this.dismiss();
            Object select = cityPickerDataLists.get(currentPostion);
            if (select instanceof CityPickerData) {
                iHomeSelectCity.onGetUserCity(((CityPickerData) select).getName(), ((CityPickerData) select).getId(), type);
            } else if (select instanceof ParentSubjectsBean.Item) {
                iHomeSelectCity.onGetUserCity(((ParentSubjectsBean.Item) select).getName(), ((ParentSubjectsBean.Item) select).getId(), type);
            }
        } else if (v.getId() == R.id.messageClose) {
            this.dismiss();
        }
    }

    class ArrayWheelCityAdapter<T> implements WheelAdapter {
        private List<?> items;

        public ArrayWheelCityAdapter(List<CityPickerData> items) {
            this.items = items;
        }

        @Override
        public int getItemsCount() {
            return items.size();
        }

        @Override
        public Object getItem(int index) {
            if (index >= 0 && index < items.size()) {
                Object select = items.get(index);
                if (select instanceof CityPickerData) {
                    return ((CityPickerData) select).getName();
                } else if (select instanceof ParentSubjectsBean.Item) {
                    return ((ParentSubjectsBean.Item) select).getName();
                }
            }
            return "";
        }

        @Override
        public int indexOf(Object o) {
            return items.indexOf(o);
        }

    }
}
