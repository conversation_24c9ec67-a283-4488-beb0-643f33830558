package com.zizhiguanjia.model_message.dialog;
import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.xuexiang.xupdate.entity.UpdateEntity;
import com.xuexiang.xupdate.proxy.IUpdateProxy;
import com.xuexiang.xupdate.service.OnFileDownloadListener;
import com.xuexiang.xupdate.widget.NumberProgressBar;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.viewmodel.MessageVideModel;
import java.io.File;
public class AppUpdataVersionDialog extends CenterPopupView implements View.OnClickListener{
    private IUpdateProxy updateProxy;
    private TextView updataAppTv, titleTv, desTv;
    private UpdateEntity updateEntity;
    private Context mContext;
    private NumberProgressBar numberPb;
    private TextView updataDesAppTv;
    private LinearLayout closeImg;

    public AppUpdataVersionDialog(@NonNull Context context, IUpdateProxy updateProxy, UpdateEntity updateEntity) {
        super(context);
        this.bindLayoutId = 0;
        this.mContext = context;
        this.updateProxy = updateProxy;
        this.updateEntity = updateEntity;
        addInnerContent();
    }

    @Override
    protected int initLayoutResId() {
        return bindLayoutId != 0 ? bindLayoutId : R.layout.message_app_updata_version;
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        BaseConfig.APP_UPDATE_STATE=1;
        updataAppTv = findViewById(R.id.tvUpdataApp);
        numberPb = this.findViewById(R.id.nbpbProgress);
        updataAppTv.setOnClickListener(this);
        updataDesAppTv = this.findViewById(R.id.tvUpdataDesApp);
        closeImg = this.findViewById(R.id.imgClose);
        titleTv = this.findViewById(R.id.tvTitle);
        desTv = this.findViewById(R.id.tvDes);
        closeImg.setOnClickListener(this);
        initData();
    }

    private void initData() {
        titleTv.setText("是否升级到" + updateEntity.getVersionName() + "版本？");
        desTv.setText(updateEntity.getUpdateContent());
        closeImg.setVisibility(updateEntity.isIgnorable()?VISIBLE:GONE);
    }

    @Override
    protected int getMaxWidth() {
        return DpUtils.dp2px(getContext(), 261);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tvUpdataApp) {
            PointHelper.joinPointData("a_statistical_forcedUpdate",false);
            startAppUpdata();
        } else if (v.getId() == R.id.imgClose) {
            BaseConfig.APP_UPDATE_STATE=0;
            MessageHelper.nextDialogTask();
            igUpdata();
            PointHelper.joinPointData("a_statistical_ignoreUpdate",false);
            KvUtils.save("app_updata_version",updateEntity.getVersionCode());
            updateProxy.cancelDownload();
            this.dismiss();
        }
    }
    private void igUpdata(){
        MessageVideModel messageVideModel=new MessageVideModel();
        messageVideModel.ignoreUpdata(updateEntity.getVersionName());
    }
    private void startAppUpdata() {
        updateProxy.startDownload(updateEntity, new OnFileDownloadListener() {
            @Override
            public void onStart() {
            }

            @Override
            public void onProgress(float progress, long total) {
                int pro=(int) (progress * 100);
                numberPb.setProgress(pro==99?100:pro);
                updataDesAppTv.setVisibility(VISIBLE);
                updataAppTv.setVisibility(GONE);
            }

            @Override
            public boolean onCompleted(File file) {
                updataDesAppTv.setVisibility(GONE);
                updataAppTv.setVisibility(VISIBLE);
                return true;
            }

            @Override
            public void onError(Throwable throwable) {
                updataDesAppTv.setVisibility(GONE);
                updataAppTv.setVisibility(VISIBLE);
            }
        });
    }

    @Override
    protected void doAfterShow() {
        BaseConfig.updateView=true;
        super.doAfterShow();
    }

    @Override
    public void dismiss() {
        BaseConfig.updateView=false;
        super.dismiss();
    }
}
