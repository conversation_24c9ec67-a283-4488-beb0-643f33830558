package com.zizhiguanjia.model_message.dialog;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.listeners.ILoginVipTs;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.utils.SpannableStringUtils;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.bean.LoginBean;
import com.zizhiguanjia.model_message.helper.MessageHelper;

import java.io.IOException;
import java.lang.reflect.Type;

import cn.hutool.core.util.PhoneUtil;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

public class AccountVipTsDialog extends CenterPopupView implements View.OnClickListener {
    private TextView mContextTv,tvOldLogin,tvVipLogin;
    private String certificateName,cityName,phone,token;
    private Activity mActivity;
    private LoadingPopupView loadingPopupView;
    private ILoginVipTs iLoginVipTs;
    public AccountVipTsDialog(@NonNull Activity context, String certificateName, String cityName, String phone,String token,ILoginVipTs iLoginVipTs) {
        super(context);
        this.iLoginVipTs=iLoginVipTs;
        this.mActivity = context;
        this.certificateName = certificateName;
        this.cityName = cityName;
        this.token=token;
        this.phone = phone;
        addInnerContent();
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
    }

    @Override
    protected int initLayoutResId() {
        return R.layout.message_vipts_account;
    }

    @Override
    protected int getMaxWidth() {
        return DpUtils.dp2px(getContext(),310);
    }

    @Override
    protected void init() {
        super.init();
        SpannableStringBuilder textFrontColorSp = SpannableStringUtils.getBuilder("您已经开通过").setBold()
                .setForegroundColor(Color.parseColor("#000000"))
                .append(cityName).setForegroundColor(Color.parseColor("#007AFF")).setBold()
                .append("的题库，开通手机号为").setForegroundColor(Color.parseColor("#000000")).setBold()
                .append(PhoneUtil.hideBetween(phone)+"\n\n").setForegroundColor(Color.parseColor("#007AFF")).setBold()
                .append("其他手机号没有VIP权限，登录后不能免费刷题。\n如果您需要继续学习，请用VIP权限绑定的手机号来登录。").setForegroundColor(Color.parseColor("#000000"))
                .create();
        mContextTv.setText(textFrontColorSp);
    }

    @Override
    protected void initView() {
        super.initView();
        mContextTv=this.findViewById(R.id.vContextTop);
        loadingPopupView=new PopupManager.Builder(mActivity).asLoading();
        loadingPopupView.setTitle("登录中...");
        tvOldLogin=this.findViewById(R.id.tvOldLogin);
        tvVipLogin=this.findViewById(R.id.tvVipLogin);
        tvOldLogin.setOnClickListener(this);
        tvVipLogin.setOnClickListener(this);
    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(),363);
    }
    private void postSwitchDrivce(){
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("userToken",token);
        RequestBody multiBody=multiBuilder.build();
        loadingPopupView.show();
        MessageHelper.getInstance().postHttps(BaseAPI.VERSION_DES + "/API/User/TokenLogin", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        loadingPopupView.dismiss();
                        ToastUtils.normal("服务器繁忙，请稍后尝试~");
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        loadingPopupView.dismiss();
                        if(response.code()==200) {
                            try {
                                String json = response.body().string();
                                Type jsonType = new TypeToken<BaseData<LoginBean>>() {}.getType();
                                BaseData<LoginBean> commonExamBeanBaseData= new Gson().fromJson(json,jsonType);
                                if(commonExamBeanBaseData.isSuccess()){
                                    ToastUtils.normal(commonExamBeanBaseData.Message, Gravity.CENTER);
                                    String jsonStr=com.wb.lib_network.utils.GsonUtils.gsonString(commonExamBeanBaseData.Data);
                                    AccountHelper.successLoginAccount(jsonStr);
                                    Bus.post(new MsgEvent(AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS));
                                    Bus.post(new MsgEvent(0x999));
                                    dismiss();
                                    LogUtils.e("看看解析的数据----->>>>"+commonExamBeanBaseData.getResult().getAccount());
                                }else {
                                    ToastUtils.normal(commonExamBeanBaseData.Message,Gravity.CENTER);
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                                ToastUtils.normal("服务器繁忙，请稍后尝试！", Gravity.CENTER);
                            }
                        }else {
                            ToastUtils.normal("服务器繁忙，请稍后尝试！", Gravity.CENTER);
                        }
                    }
                });
            }
        });
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.tvOldLogin){
            postSwitchDrivce();
        }else if(v.getId()==R.id.tvVipLogin){
            iLoginVipTs.onGoTel(phone);
            Bus.post(new MsgEvent(0x999));
            dismiss();
        }
    }
}
