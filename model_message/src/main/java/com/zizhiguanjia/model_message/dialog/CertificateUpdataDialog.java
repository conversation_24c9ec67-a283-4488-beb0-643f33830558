package com.zizhiguanjia.model_message.dialog;

import android.app.Activity;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.ViewModelStoreOwner;

import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.msgconfig.CertificateMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.bean.CityPickerBean;
import com.zizhiguanjia.model_message.bean.CityPickerData;
import com.zizhiguanjia.model_message.factory.CustomViewModelFactory;
import com.zizhiguanjia.model_message.listenter.IHomeSelectCity;
import com.zizhiguanjia.model_message.manager.MessageManager;
import com.zizhiguanjia.model_message.navigator.CertificateUpdataNavigator;
import com.zizhiguanjia.model_message.viewmodel.CerticicateUpdataViewModel;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Editor;
import io.reactivex.functions.Consumer;

public class CertificateUpdataDialog extends BottomPopupView implements ViewModelStoreOwner, CertificateUpdataNavigator, View.OnClickListener, IHomeSelectCity {
    private CerticicateUpdataViewModel examViewModel;
    private ViewModelStore mViewModelStore;
    private TextView tvCityName;
    private TextView tvCertificateName;
    private LinearLayout tvUpdataCertificate;
    private String currentCityCode,currentCertificateIds;
    private TextView saveTv,cancelTv;
    private LoadingPopupView loadingPopupView;
    private List<CityPickerData> lists;
    private LinearLayout updataCityTv;
    private Activity mActivity;
    private List<CityPickerData> certificateLists;
    public CertificateUpdataDialog(@NonNull Activity context) {
        super(context);
        this.mActivity=context;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        mViewModelStore = new ViewModelStore();
        examViewModel = new ViewModelProvider(this, new CustomViewModelFactory()).get(CerticicateUpdataViewModel.class);
        loadingPopupView=new PopupManager.Builder(mActivity).asLoading("请稍等....");
    }
    @Override
    public void showOrHindLoading(boolean b) {
        if(loadingPopupView==null)return;
        if(b){
            if(loadingPopupView.isShow()){
                return;
            }
            loadingPopupView.show();
        }else {
            if(loadingPopupView.isShow()){
                loadingPopupView.dismiss();
            }
        }

    }

    @Override
    public void showErrorToast(String msg) {
        ToastUtils.normal(msg, Gravity.CENTER);
    }

    @Override
    public void fillCertificate(List<CityPickerData> cityPickerDataLis) {
        if(certificateLists==null||certificateLists.size()==0)return;
        List<CityPickerData> cityPickerData=ListUtil.filter(cityPickerDataLis, new Editor<CityPickerData>() {
            @Override
            public CityPickerData edit(CityPickerData cityPickerData) {
                LogUtils.e("-----请>>>>"+cityPickerData.getId()+"*****"+currentCertificateIds);
                if(cityPickerData.getId().equals(currentCertificateIds)){
                    return cityPickerData;
                }
                return null;
            }
        });
        if(cityPickerData==null||cityPickerData.size()==0){
            certificateLists.clear();
            certificateLists.addAll(cityPickerDataLis);
            currentCertificateIds=null;
            tvCertificateName.setText("请选择");
        }
    }

    @Override
    protected int initLayoutResId() {
        return R.layout.message_certificate_updata_layout;
    }

    @Override
    protected int getMaxWidth() {
        return DpUtils.dp2px(getContext(),375);
    }

    @Override
    protected void init() {
        super.init();
        lists=new ArrayList<>();
        certificateLists=new ArrayList<>();
    }

    @Override
    protected void initView() {
        super.initView();
        updataCityTv=this.findViewById(R.id.tvUpdataCity);
        tvCityName=this.findViewById(R.id.tvCityName);
        tvUpdataCertificate=this.findViewById(R.id.tvUpdataCertificate);
        tvCertificateName=this.findViewById(R.id.tvCertificateName);
        saveTv=this.findViewById(R.id.tvSave);
        cancelTv=this.findViewById(R.id.tvCancle);
        examViewModel.initParams(this);
        initListenter();
    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(),281);
    }

    @NonNull
    @Override
    public ViewModelStore getViewModelStore() {
        return mViewModelStore;
    }
    @Override
    protected void onDetachedFromWindow() {
        mViewModelStore.clear();
        super.onDetachedFromWindow();
    }

    @Override
    public void openSelectCityDialog(List<CityPickerData> cityPickerData,boolean open) {
        lists.clear();
        lists.addAll(cityPickerData);
        if (open){
            openUserSelectCityDialog();
        }
    }

    @Override
    public void initListenter() {
        updataCityTv.setOnClickListener(this);
        tvUpdataCertificate.setOnClickListener(this);
        cancelTv.setOnClickListener(this);
        saveTv.setOnClickListener(this);
    }

    @Override
    public void openUserSelectCityDialog() {
        MessageManager.getInstance().openSelectSubjectOrCity(mActivity,lists,this,1);
    }

    @Override
    public void OpenUserSelectCertificateDialog() {
        MessageManager.getInstance().openSelectSubjectOrCity(mActivity,certificateLists,this,2);
    }

    @Override
    public void getCertificateListByCity(List<CityPickerData> Records,boolean open,boolean resh) {
        certificateLists.clear();
        certificateLists.addAll(Records);
        if (open){
            OpenUserSelectCertificateDialog();
        }

    }

    @Override
    public void initCurrentCertificate(String cityName, String cityCode, String certificateName, String ids) {
        currentCertificateIds=ids;
        currentCityCode=cityCode;
        tvCityName.setText(cityName);
        tvCertificateName.setText(certificateName);
        examViewModel.getCeitificateByCityIds(currentCertificateIds,false,false);
    }

    @Override
    public void bindSuccess() {
        CertificateHelper.updateCertificate(currentCityCode,tvCityName.getText().toString().trim(),currentCertificateIds,tvCertificateName.getText().toString().trim(),90,"");
        RxJavaUtils.delay(1, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        Bus.post(new MsgEvent(CertificateMsgTypeConfig.CERTIFICATE_MSG_BIND_SUCCESS));
                        Bus.post(new MsgEvent(0x4545445));
                        dismiss();
                    }
                });
            }
        });
    }
    @Override
    public void onClick(View v) {
        if(!MessageManager.getInstance().isDoubleClick()){
            if(v.getId()==R.id.tvUpdataCity){
                LogUtils.e("看看----->>>>"+lists.size());
                if(lists==null||lists.size()==0){
                examViewModel.getCertificateCityPostData(true);
                }else {
                    openUserSelectCityDialog();
                }
            }else if(v.getId()==R.id.tvUpdataCertificate){
                if(certificateLists==null||certificateLists.size()==0){
                    examViewModel.getCeitificateByCityIds(currentCertificateIds,true,false);
                }else {
                    OpenUserSelectCertificateDialog();
                }
            }else if(v.getId()==R.id.tvSave){
                examViewModel.binSubject(currentCityCode,currentCertificateIds);
            }else if(v.getId()==R.id.tvCancle){
                this.dismiss();
            }
        }

    }

    @Override
    public void onGetUserCity(String name, String ids,int type) {
        if(type==1){
            currentCityCode=ids;
            tvCityName.setText(name);
            examViewModel.getCeitificateByCityIds(currentCityCode,false,true);
        }else {
            currentCertificateIds=ids;
            tvCertificateName.setText(name);
        }

    }
}
