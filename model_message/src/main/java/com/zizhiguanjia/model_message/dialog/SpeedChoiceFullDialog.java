package com.zizhiguanjia.model_message.dialog;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_pop.code.AttachPopupView;
import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.task.RxAsyncTask;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.FileUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.listeners.SpeedListener;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.bean.SpeedBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.functions.Consumer;

public class SpeedChoiceFullDialog extends AttachPopupView {
    private LinearLayout video_quality_wrapper_area;
    private List<SpeedBean> speedBeanList;
    private SpeedListener speedListener;
    public SpeedChoiceFullDialog(@NonNull Context context, SpeedListener speedListener) {
        super(context);
        this.speedListener=speedListener;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return  R.layout.message_fullspeed;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        speedBeanList=new ArrayList<>();
        video_quality_wrapper_area=findViewById(R.id.video_quality_wrapper_area);
        initConfigData();
        RxJavaUtils.delay(4, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                dismiss();
            }
        });
    }
    private void initConfigData(){
        String jsonDes="speedconfig.json";
        RxJavaUtils.executeAsyncTask(new RxAsyncTask<String, List<SpeedBean>>(jsonDes) {
            @Override
            public void doInUIThread(List<SpeedBean> userListDataConfigBeans) {
                speedBeanList.clear();
                LogUtils.e("倍速大小----->>>"+userListDataConfigBeans.size());
                if(userListDataConfigBeans==null||userListDataConfigBeans.size()==0)return;
                speedBeanList.addAll(userListDataConfigBeans);
                addViewSpeed(speedBeanList);
            }
            @Override
            public List<SpeedBean> doInIOThread(String s) {
                try {
                    String json = FileUtils.getFromAssets(AppUtils.getApp(), s);
                    LogUtils.e("倍速"+json);
                    List<SpeedBean> userCertificateBeans = GsonUtils.jsonToList(json, SpeedBean.class);
                    return userCertificateBeans;
                } catch (Exception e) {
                    return null;
                }
            }
        });
    }
    private void addViewSpeed(List<SpeedBean> userListDataConfigBeans){
//        String ids=KvUtils.get("videoSpeed", "4");
        String json=KvUtils.get("videoSpeed","");
        String ids="";
        if(json==null||json.isEmpty()){
            ids="4";
        }else {
            Map<String,String> jsonMap= GsonUtils.gsonToMaps(json);
            ids=jsonMap.get("idsSpeed");
        }
        for (int j = 0; j < userListDataConfigBeans.size(); j++) {
            LogUtils.e("倍速生产---->>>"+j);
            SpeedBean speedBean=userListDataConfigBeans.get(j);
            String key = speedBean.getTitle();
            TextView clarityItem = (TextView) View.inflate(getContext(), R.layout.message_lzyout_speed_item, null);
            clarityItem.setText(key);
            clarityItem.setTag(j);
            clarityItem.setOnClickListener(mQualityListener);
            video_quality_wrapper_area.addView(clarityItem, j);
            if (Integer.parseInt(ids) == speedBean.getIds()) {
                clarityItem.setTextColor(Color.parseColor("#007AFF"));
            }else{
                clarityItem.setTextColor(Color.parseColor("#ffffff"));
            }
        }

    }
    OnClickListener mQualityListener = v1 -> {
        int index = (int) v1.getTag();
        SpeedBean speedBean=speedBeanList.get(index);
        Map<String,String> map=new HashMap<>();
        map.put("idsSpeed",speedBean.getIds()+"");
        map.put("titleSpeed",speedBean.getTitle());
        KvUtils.save("videoSpeed", com.wb.lib_utils.utils.GsonUtils.newInstance().GsonToString(map));
        dismiss();
        if(speedListener==null)return;
        speedListener.onSpeed(Float.parseFloat(speedBean.getSpeed()),speedBean.getTitle());
    };
}
