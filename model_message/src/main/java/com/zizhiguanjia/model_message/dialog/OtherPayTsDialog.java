package com.zizhiguanjia.model_message.dialog;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.text.SpannableStringBuilder;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.utils.SpannableStringUtils;
import com.zizhiguanjia.model_message.R;

import java.util.Map;

import cn.hutool.core.util.PhoneUtil;

public class OtherPayTsDialog extends CenterPopupView {
    private TextView msgTv;
    private String mJsonMap;
    private TextView contextTv;
    private TextView freeBuyTv;
    private ImageView otherCloseImage;
    public OtherPayTsDialog(@NonNull Context context,String jsonMap) {
        super(context);
        this.mJsonMap=jsonMap;
        addInnerContent();
    }

    @Override
    protected int initLayoutResId() {
        return R.layout.message_other_payts_layout;
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
    }

    @Override
    protected int getMaxWidth() {
        return DpUtils.dp2px(getContext(),293);
    }

    @Override
    protected void init() {
        super.init();
        Map<String,String> param= GsonUtils.newInstance().getBean(mJsonMap,Map.class);
        SpannableStringBuilder textFrontColorSp = SpannableStringUtils.getBuilder(param.get("MajorName")).setBold().setForegroundColor(Color.parseColor("#6F5020"))
                .append(param.get("Content")).setForegroundColor(Color.parseColor("#333333"))
                .create();
        msgTv.setText(textFrontColorSp);
        contextTv.setText(param.get("SubContent"));
        //WXLink
        freeBuyTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(param==null|| StringUtils.isEmpty(param.get("WXLink"))){
                    ToastUtils.normal("无效的链接~");
                }else {
                    dismiss();
                    String uri=param.get("WXLink");
                    LogUtils.e("*******"+uri);
                    Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(uri));
                    getContext().startActivity(it);
                }
            }
        });
        otherCloseImage.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

    @Override
    protected void initView() {
        super.initView();
        msgTv=this.findViewById(R.id.msgTv);
        freeBuyTv=this.findViewById(R.id.freeBuyTv);
        contextTv=this.findViewById(R.id.contextTv);
        otherCloseImage=this.findViewById(R.id.otherCloseImage);

    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(),400);
    }
}
