package com.zizhiguanjia.model_message.dialog;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.wb.lib_arch.common.Bus;
import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.listeners.MessageSuccessPayListener;
import com.zizhiguanjia.lib_base.utils.SpannableStringUtils;
import com.zizhiguanjia.model_message.R;

public class AqySuccessPayTsDialog extends CenterPopupView {
    private TextView selectDesTv,goToKmTv,selectMsgTv,goToUpdateSubjectTv,goToKmTv1;
    private MessageSuccessPayListener messageSuccessPayListener;
    private LinearLayout btMain;
    private Activity mActivity;
    public AqySuccessPayTsDialog(@NonNull Activity context, MessageSuccessPayListener messageSuccessPayListener) {
        super(context);
        this.messageSuccessPayListener=messageSuccessPayListener;
        this.mActivity=context;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return bindLayoutId != 0 ? bindLayoutId : R.layout.message_aqy_success_pay_ts_layout;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
    }

    @Override
    protected void init() {
        super.init();
    }

    @Override
    protected void initView() {
        super.initView();
        selectDesTv=this.findViewById(R.id.selectDesTv);
        goToUpdateSubjectTv=this.findViewById(R.id.goToUpdateSubjectTv);
        selectMsgTv=this.findViewById(R.id.selectMsgTv);
        goToKmTv=this.findViewById(R.id.goToKmTv);
        goToKmTv1=this.findViewById(R.id.goToKmTv1);
        btMain=this.findViewById(R.id.btMain);
        if(CertificateHelper.getCurrentCertificateDialog()){
            goToUpdateSubjectTv.setVisibility(VISIBLE);
            String ids=CertificateHelper.getCertificateDes();
            String newStr=ids.replace("·","");
            selectDesTv.setText(ids);
            selectMsgTv.setText(getSpanner());
            goToKmTv1.setText("确认开通"+newStr);
            goToKmTv1.setVisibility(VISIBLE);
            goToKmTv.setVisibility(GONE);
            RelativeLayout.LayoutParams params=new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
            params.bottomMargin=DpUtils.dp2px(getContext(),10);
            btMain.setLayoutParams(params);

            goToUpdateSubjectTv.setText(getBottomMsg());

        }else {
            goToUpdateSubjectTv.setVisibility(GONE);
            if(BaseConfig.payWaitClass==1){
                selectDesTv.setText("单科打包套餐");
            }else {
                selectDesTv.setText("三科打包套餐");

            }
            selectMsgTv.setText("选择科目后即可畅快刷题");
            goToKmTv.setText("去选择科目");
            goToKmTv1.setVisibility(GONE);
            goToKmTv.setVisibility(VISIBLE);
            RelativeLayout.LayoutParams params=new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
            params.bottomMargin=DpUtils.dp2px(getContext(),35);
            btMain.setLayoutParams(params);
        }

        goToKmTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if(CertificateHelper.getCurrentCertificateDialog())return;
                messageSuccessPayListener.GotoSelectSubject();
            }
        });
        goToKmTv1.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if(CertificateHelper.getCurrentCertificateDialog())return;
                messageSuccessPayListener.GotoSelectSubject();
            }
        });

        goToUpdateSubjectTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                MessageHelper.openHomeCertificateUpdata(mActivity);
//                MessageHelper.openSelectSubjectOrCity(mActivity);
            }
        });
    }
    private SpannableStringBuilder getBottomMsg(){
        SpannableStringBuilder textFrontColorSp = SpannableStringUtils.getBuilder("买错了")
                .setForegroundColor(Color.parseColor("#999999")).setUnderline()
                .append("，").setForegroundColor(Color.parseColor("#999999"))
                .append("更换科目").setForegroundColor(Color.parseColor("#999999")).setUnderline()
                .create();
        return textFrontColorSp;
    }
    private SpannableStringBuilder getSpanner(){
        SpannableStringBuilder textFrontColorSp = SpannableStringUtils.getBuilder("赠送您")
                .setForegroundColor(Color.parseColor("#666666"))
                .append("二级建造师全科题库").setForegroundColor(Color.parseColor("#D52930"))
                .append("，切换到二建科目后即可学习。").setForegroundColor(Color.parseColor("#666666"))
                .create();
        return textFrontColorSp;
    }
    @Override
    protected int getMaxWidth() {
        return DpUtils.dp2px(getContext(),265);
    }

    @Override
    protected int getMaxHeight() {
        if(CertificateHelper.getCurrentCertificateDialog()){
            return DpUtils.dp2px(getContext(),400);
        }else {
            return DpUtils.dp2px(getContext(),348);
        }
    }
}
