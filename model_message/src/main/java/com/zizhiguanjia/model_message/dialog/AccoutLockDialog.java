package com.zizhiguanjia.model_message.dialog;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.utils.SpannableStringUtils;
import com.zizhiguanjia.model_message.R;

public class AccoutLockDialog extends CenterPopupView {
    private TextView msgTv;
    private TextView lookTv;
    public AccoutLockDialog(@NonNull Context context) {
        super(context);
        addInnerContent();
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
    }

    @Override
    protected int initLayoutResId() {
        return R.layout.message_lock_account;
    }

    @Override
    protected int getMaxWidth() {
        return DpUtils.dp2px(getContext(),310);
    }

    @Override
    protected void init() {
        super.init();
        SpannableStringBuilder textFrontColorSp = SpannableStringUtils.getBuilder("您的账号已经切换过1次登录设备，系统再次检测到您的登录设备有变化，为了保障数据安全，系统将限制您的账号")
                .setForegroundColor(Color.parseColor("#000000"))
                .append("在新设备上登录，请继续在原设备上使用。").setForegroundColor(Color.parseColor("#000000")).setBold()
                .create();
        msgTv.setText(textFrontColorSp);
    }

    @Override
    protected void initView() {
        super.initView();
        msgTv=this.findViewById(R.id.tvMsg);
        lookTv=this.findViewById(R.id.tvLook);
        lookTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                Bus.post(new MsgEvent(0x999));
            }
        });
    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(),274);
    }
}
