package com.zizhiguanjia.model_message.dialog;
import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_pop.code.AttachPopupView;
import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.task.RxAsyncTask;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.FileUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.lib_base.listeners.SpeedListener;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.adapter.SpeedAdapter;
import com.zizhiguanjia.model_message.bean.SpeedBean;
import java.util.List;
import java.util.Map;

import io.reactivex.functions.Consumer;

public class SpeedChoiceDialog extends BottomPopupView implements View.OnClickListener {
    private RecyclerView mRecyclerView;
    private SpeedAdapter mAdapter;
    private SpeedListener speedListener;
    private TextView tvCommonCancel;
    public SpeedChoiceDialog(@NonNull Context context,SpeedListener speedListener) {
        super(context);
        this.speedListener=speedListener;
        addInnerContent();
    }

    @Override
    protected int initLayoutResId() {
        return  R.layout.message_speed_layout;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        mRecyclerView=this.findViewById(R.id.messageSpeedRcy);
        tvCommonCancel=this.findViewById(R.id.tvCommonCancel);
        String json=KvUtils.get("videoSpeed","");
        String ids="";
        if(json==null||json.isEmpty()){
            ids="4";
        }else {
            Map<String,String> jsonMap= GsonUtils.gsonToMaps(json);
            ids=jsonMap.get("idsSpeed");
        }
        mAdapter=new SpeedAdapter(new SpeedListener() {
            @Override
            public void onSpeed(float speed,String title) {
                speedListener.onSpeed(speed,title);
                dismiss();
            }
        }, ids);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mRecyclerView.setAdapter(mAdapter);
        initConfigData();
        tvCommonCancel.setOnClickListener(this);
        RxJavaUtils.delay(4, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                dismiss();
            }
        });
    }
    private void initConfigData(){
        String jsonDes="speedconfig.json";
        RxJavaUtils.executeAsyncTask(new RxAsyncTask<String, List<SpeedBean>>(jsonDes) {
            @Override
            public void doInUIThread(List<SpeedBean> userListDataConfigBeans) {
                mAdapter.setDataItems(userListDataConfigBeans);
            }
            @Override
            public List<SpeedBean> doInIOThread(String s) {
                try {
                    String json = FileUtils.getFromAssets(AppUtils.getApp(), s);
                    LogUtils.e("倍速"+json);
                    List<SpeedBean> userCertificateBeans = GsonUtils.jsonToList(json, SpeedBean.class);
                    return userCertificateBeans;
                } catch (Exception e) {
                    return null;
                }
            }
        });
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.tvCommonCancel){
            dismiss();
        }
    }
}
