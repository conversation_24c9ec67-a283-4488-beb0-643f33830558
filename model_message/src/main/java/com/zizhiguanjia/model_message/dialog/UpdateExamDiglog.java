package com.zizhiguanjia.model_message.dialog;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.listeners.IUpdateExamListenter;
import com.zizhiguanjia.model_message.R;

public class UpdateExamDiglog extends CenterPopupView implements View.OnClickListener {
    private TextView mUpdataTitleTv,mUpdataSubTitleTv,mUpdataConTitleTv,mUpdataContentTitleTv,mUpdataSubContentTitleTv,mUpdatePostTv;
    private ImageView mUpdataCloseImg;
    private IUpdateExamListenter updateExamListenter;
    private String title,subTitle,contentTitle,Content,subContent,ids;
    public UpdateExamDiglog(@NonNull Context context,String title,String subTitle,String contentTitle,String Content,String subContent,String ids,IUpdateExamListenter iUpdateExamListenter) {
        super(context);
        this.title=title;
        this.subTitle=subTitle;
        this.contentTitle=contentTitle;
        this.Content=Content;
        this.subContent=subContent;
        this.ids=ids;
        this.updateExamListenter=iUpdateExamListenter;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return bindLayoutId != 0 ? bindLayoutId : R.layout.message_updateexam_layout;
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
    }

    @Override
    protected void initView() {
        super.initView();
        BaseConfig.APP_EXAM_DATE_STATE=1;
        mUpdataTitleTv=this.findViewById(R.id.updataTitleTv);
        mUpdataSubTitleTv=this.findViewById(R.id.updataSubTitleTv);
        mUpdataConTitleTv=this.findViewById(R.id.updataConTitleTv);
        mUpdataContentTitleTv=this.findViewById(R.id.updataContentTitleTv);
        mUpdataSubContentTitleTv=this.findViewById(R.id.updataSubContentTitleTv);
        mUpdatePostTv=this.findViewById(R.id.updatePostTv);
        mUpdataCloseImg=this.findViewById(R.id.updataCloseImg);
        initData();
        initListent();
    }
    private void initListent(){
        mUpdataCloseImg.setOnClickListener(this);
        mUpdatePostTv.setOnClickListener(this);
    }
    private void initData(){
        if(title==null||title.isEmpty()){
            mUpdataTitleTv.setVisibility(GONE);
        }else {
            mUpdataTitleTv.setVisibility(VISIBLE);
            mUpdataTitleTv.setText(title);
        }
        if(subTitle==null||subTitle.isEmpty()){
            mUpdataSubTitleTv.setVisibility(GONE);
        }else {
            mUpdataSubTitleTv.setVisibility(VISIBLE);
            mUpdataSubTitleTv.setText(subTitle);
        }
        if(contentTitle==null||contentTitle.isEmpty()){
            mUpdataConTitleTv.setVisibility(GONE);
        }else {
            mUpdataConTitleTv.setVisibility(VISIBLE);
            mUpdataConTitleTv.setText(contentTitle);
        }
        if(Content==null||Content.isEmpty()){
            mUpdataContentTitleTv.setVisibility(GONE);
        }else {
            mUpdataContentTitleTv.setVisibility(VISIBLE);
            mUpdataContentTitleTv.setText(Content);
        }
        if(subContent==null||subContent.isEmpty()){
            mUpdataSubContentTitleTv.setVisibility(GONE);
        }else {
            mUpdataSubContentTitleTv.setVisibility(VISIBLE);
            mUpdataSubContentTitleTv.setText(subContent);
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.updataCloseImg) {
            if(updateExamListenter==null)return;
            updateExamListenter.OnUserUpdateExam(ids,2);
            BaseConfig.APP_EXAM_DATE_STATE=0;
            this.dismiss();
        }else  if (v.getId() == R.id.updatePostTv) {
            if(updateExamListenter==null)return;
            BaseConfig.APP_EXAM_DATE_STATE=0;
            updateExamListenter.OnUserUpdateExam(ids,1);
            this.dismiss();
        }
    }
}
