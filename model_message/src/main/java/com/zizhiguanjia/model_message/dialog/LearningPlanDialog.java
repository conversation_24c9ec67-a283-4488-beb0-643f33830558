package com.zizhiguanjia.model_message.dialog;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.wb.lib_pop.code.CenterPopupView;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.model_message.R;

public class LearningPlanDialog extends CenterPopupView implements View.OnClickListener {
    private String time;
    private TextView timeTv,cydcTV,jxxxTV,tvpass;
    private int type;
    private View message1,message2;
    public LearningPlanDialog(@NonNull Context context,String time,int type) {
        super(context);
        this.time=time;
        this.type=type;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return bindLayoutId != 0 ? bindLayoutId : R.layout.message_learning_layout;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        BaseConfig.isOpenLearnDialog=true;
    }

    @Override
    protected void initView() {
        super.initView();
        timeTv=this.findViewById(R.id.tvTime);
        cydcTV=this.findViewById(R.id.tvcydc);
        jxxxTV=this.findViewById(R.id.tvjxxx);
        tvpass=this.findViewById(R.id.tvpass);
        message1=this.findViewById(R.id.message1);
        message2=this.findViewById(R.id.message2);
        cydcTV.setOnClickListener(this);
        jxxxTV.setOnClickListener(this);
    }

    @Override
    protected void init() {
        super.init();
        if(type==1){
            message1.setVisibility(VISIBLE);
            message2.setVisibility(GONE);
            timeTv.setText(time);
        }else {
            tvpass.setText(time+"%");
            message1.setVisibility(GONE);
            message2.setVisibility(VISIBLE);
        }
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.tvcydc){
            this.dismiss();
            BaseConfig.isOpenLearnDialog=false;
            MessageHelper.openCusomerDialog((Activity) getContext(),"1");
        }else if(v.getId()==R.id.tvjxxx){
            BaseConfig.isOpenLearnDialog=false;
            this.dismiss();
        }
    }
}
