//package com.zizhiguanjia.model_message.dialog;
//
//import android.content.Context;
//import android.content.Intent;
//import android.net.Uri;
//import android.view.Gravity;
//import android.view.View;
//import android.widget.ImageView;
//import android.widget.TextView;
//
//import androidx.annotation.NonNull;
//
//import com.wb.lib_image_loadcal.ImageManager;
//import com.wb.lib_network.utils.GsonUtils;
//import com.wb.lib_pop.code.CenterPopupView;
//import com.wb.lib_utils.utils.StringUtils;
//import com.wb.lib_utils.utils.ToastUtils;
//import com.zizhiguanjia.lib_base.config.PayRouthConfig;
//import com.zizhiguanjia.lib_base.helper.CommonHelper;
//import com.zizhiguanjia.lib_base.helper.ConfigHelper;
//import com.zizhiguanjia.lib_base.listeners.PaySuccessListen;
//import com.zizhiguanjia.model_message.R;
//
//import java.util.Map;
//
//public class PaySuccessDialog extends CenterPopupView {
//    private ImageView messageQrCodeImage,messageCloseImg;
//    private TextView addTeacherTv;
//    private String link;
//    private PaySuccessListen paySuccessListen;
//    public PaySuccessDialog(@NonNull Context context, PaySuccessListen paySuccessListen) {
//        super(context);
//        this.bindLayoutId = 0;
//        this.paySuccessListen=paySuccessListen;
//        addInnerContent();
//    }
//
//    @Override
//    protected int initLayoutResId() {
//        return bindLayoutId != 0 ? bindLayoutId : R.layout.message_pay_success_layout;
//    }
//    @Override
//    protected void initPopupContent() {
//        super.initPopupContent();
//        messageQrCodeImage=this.findViewById(R.id.messageQrCodeImage);
//        messageCloseImg=this.findViewById(R.id.messageCloseImg);
//        addTeacherTv=this.findViewById(R.id.addTeacherTv);
//        initData();
//        messageCloseImg.setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                dismiss();
//            }
//        });
//        addTeacherTv.setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if(link==null||link.isEmpty()){
//                    ToastUtils.normal("链接无效！", Gravity.CENTER);
//                    return;
//                }
//                if(checkSechemValid(link)){
//                    Intent it = new Intent(Intent.ACTION_VIEW, Uri.parse(link));
//                    getContext().startActivity(it);
//                }else {
//                    toWebNoPremiss(link);
//                }
//                dismiss();
//            }
//        });
//    }
//    public void toWebNoPremiss(String state){
//        paySuccessListen.paySunncess(1,state);
//    }
//    public boolean checkSechemValid(String uri){
//        if(uri.startsWith("http")||uri.startsWith("https")){
//            return false;
//        }else {
//            return true;
//        }
//    }
//    private void initData(){
//        String json= ConfigHelper.getWechatOpenUrl();
//        if(json==null||json.isEmpty())return;
//        Map<String,String> jspn= GsonUtils.gsonToMaps(json);
//        String imags=jspn.get("weChatImage");
//        link=jspn.get("weChatLink");
//        if(!StringUtils.isEmpty(imags)){
//            ImageManager.getInstance().displayImage(imags,messageQrCodeImage);
//        }
//
//    }
//}
