package com.zizhiguanjia.model_message.dialog;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.view.MotionEvent;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.listeners.ExamGuilderListener;
import com.zizhiguanjia.model_message.R;

public class GuilderDialog extends CenterPopupView {
    private int type;
    private ExamGuilderListener call;
    private ImageView imageView;
    private TextView dexTitleTv;
    private float x1 = 0;
    private float x2 = 0;
    private float y1 = 0;
    private float y2 = 0;

    public GuilderDialog(@NonNull Context context, ExamGuilderListener call, int types) {
        super(context);
        addInnerContent();
        this.call = call;
        this.type = types;
    }
    @Override
    protected int initLayoutResId() {
        return R.layout.message_guilder_layout;
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        imageView = this.findViewById(R.id.center_img);
        dexTitleTv = this.findViewById(R.id.tvDesTitle);
        if (type == 1) {
            dexTitleTv.setText("左右滑动屏幕，\n可以查看上一题下一题哦~");
        } else {
            dexTitleTv.setText("再试一次");
        }
        removeLeft1();
    }

    private void removeLeft1() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(imageView, "translationX", 0, DpUtils.dp2px(getContext(), -84));
        animator.setDuration(1000);
        animator.start();
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                removeRight1();
            }
        });
    }

    private void removeRight1() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(imageView, "translationX", DpUtils.dp2px(getContext(), -84), 0);
        animator.setDuration(1000);
        animator.start();
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                removeLeft();
            }
        });
    }

    private void removeLeft() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(imageView, "translationX", 0, DpUtils.dp2px(getContext(), 84));
        animator.setDuration(1000);
        animator.start();
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                removeRight();
            }
        });
    }

    private void removeRight() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(imageView, "translationX", DpUtils.dp2px(getContext(), 84), 0);
        animator.setDuration(1000);
        animator.start();
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                removeLeft1();
            }
        });
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            //当手指按下的时候
            x1 = event.getX();
            y1 = event.getY();
        }
        if (event.getAction() == MotionEvent.ACTION_UP) {
            //当手指离开的时候
            KvUtils.save("exam_guilder_again3", type == 1 ? 2 : 3);
            dismiss();
        }
        return super.onTouchEvent(event);
    }
}
