package com.zizhiguanjia.model_message.dialog;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.contrarywind.adapter.WheelAdapter;
import com.contrarywind.listener.OnItemSelectedListener;
import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.listeners.OnChoiceTimeListenter;
import com.zizhiguanjia.lib_base.utils.TimeUtils;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.helper.MessageHelper;
import com.zizhiguanjia.model_message.view.WheelView;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

public class TimeSelectDialog extends CenterPopupView implements View.OnClickListener {
    private WheelView mWheelView1,mWheelView2,mWheelView3;
    private String currentYear,currentMouther,currentDay;
    private ImageView messageClose;
    private TextView messageBt;
    private OnChoiceTimeListenter onChoiceTimeListenter;
    public TimeSelectDialog(@NonNull Context context, OnChoiceTimeListenter onChoiceTimeListenter) {
        super(context);
        this.onChoiceTimeListenter=onChoiceTimeListenter;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return bindLayoutId != 0 ? bindLayoutId : R.layout.message_timechoices_layout;
    }

    @Override
    protected void initView() {
        super.initView();
        mWheelView1=this.findViewById(R.id.messageWheelTime1);
        mWheelView2=this.findViewById(R.id.messageWheelTime2);
        mWheelView3=this.findViewById(R.id.messageWheelTime3);
        messageClose=this.findViewById(R.id.messageClose);
        messageBt=this.findViewById(R.id.messageBt);
        messageClose.setOnClickListener(this);
        messageBt.setOnClickListener(this);
    }
    private void initYearData(){
        currentYear=getYearsStr()+"";
        List<String> years=getYears();

        mWheelView1.setAdapter(new ArrayWheelAdapter(years));
        mWheelView1.setCurrentItem(0);
        mWheelView1.setCyclic(false);
        mWheelView1.setAlphaGradient(true);
        mWheelView1.setDividerType(WheelView.DividerType.LEFT);
        mWheelView1.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                currentYear=years.get(index);
                initMouther();
            }
        });
    }
    private void initMouther(){
        currentMouther=getMouthsStr()+"";
        List<String> mouthers=getMouthers();
        mWheelView2.setAdapter(new ArrayWheelAdapter(mouthers));
        mWheelView2.setAlphaGradient(true);
        mWheelView2.setCyclic(false);
        mWheelView2.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                LogUtils.e("看看选择的2---->>>"+index);
                currentMouther=mouthers.get(index);
                initDays();
            }
        });
        int index=mouthers.indexOf(currentMouther);
        LogUtils.e("看看寻找---->>>"+index);
        mWheelView2.setCurrentItem(index);
    }

    private void initDays(){
        if(currentDay==null||currentDay.isEmpty()){
            currentDay=getDaysStr()+"";
        }
        List<String> days=getDays(Integer.parseInt(currentMouther));
        mWheelView3.setAdapter(new ArrayWheelAdapter(days));
        mWheelView3.setAlphaGradient(true);
        mWheelView3.setCyclic(false);
        mWheelView3.setDividerType(WheelView.DividerType.RIGHT);
        mWheelView3.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                LogUtils.e("看看选择的3---->>>"+index);
                currentDay=days.get(index);
            }
        });
        int index=days.indexOf(String.valueOf(currentDay));
        mWheelView3.setCurrentItem(index);
    }
    private List<String> getYears(){
        List<String> years=new ArrayList<>();
        for(int i=0;i<10;i++){
            years.add((getYearsStr()+i)+"");
        }
        return years;
    }
    private List<String> getMouthers(){
        if(currentYear.equals(getYearsStr()+"")){
            //今年
            List<String> years=new ArrayList<>();
            for(int i=Integer.parseInt(currentMouther);i<13;i++){
                years.add((i)+"");
            }
            return years;
        }else {
            List<String> years=new ArrayList<>();
            for(int i=0;i<12;i++){
                years.add((i+1)+"");
            }
            return years;
        }

    }
    private List<String> getDays(int mouther){
        List<String> years=new ArrayList<>();
        int max=0;
        if(mouther==2){
            max=28;
        }else if(mouther==1||mouther==3||mouther==5||mouther==7||mouther==8||mouther==10||mouther==12){
            max=31;
        }else {
            max=30;
        }
        years.clear();
        if(currentYear.equals(getYearsStr()+"")&&currentMouther.equals(getMouthsStr()+"")){
            for(int i=getDaysStr();i<max+1;i++){
                years.add((i)+"");
            }
            return years;
        }else {
            for(int i=0;i<max;i++){
                years.add((i+1)+"");
            }
            return years;
        }
    }

    private int getYearsStr(){
        Calendar cal = Calendar.getInstance();
        Date date=new Date();//现在的日期
        cal.setTime(date);
        Integer year=cal.get(Calendar.YEAR);//获取年
        return year;
    }
    private int getMouthsStr(){
        Calendar cal = Calendar.getInstance();
        Date date=new Date();//现在的日期
        cal.setTime(date);
        Integer year=cal.get(Calendar.MONTH)+1;//获取年
        LogUtils.e("获取到的*----越-"+year);
        return year;
    }
    private int getDaysStr(){
        Calendar cal = Calendar.getInstance();
        Date date=new Date();//现在的日期
        cal.setTime(date);
        Integer year=cal.get(Calendar.DAY_OF_MONTH);//获取年
        return year;
    }
    @Override
    protected void init() {
        super.init();
        initYearData();
        initMouther();
        initDays();
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
    }

    @Override
    protected int getMaxWidth() {
        return DpUtils.dp2px(getContext(),310);
    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(),381);
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.messageClose){
            postHttps();
            this.dismiss();
        }else if(v.getId()==R.id.messageBt){
            if(onChoiceTimeListenter==null)return;
            onChoiceTimeListenter.getChoiceTime(currentYear,currentMouther,currentDay);
            this.dismiss();
        }
    }
    private void postHttps(){
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("time",TimeUtils.getCurrentTimeRecordImg());
        RequestBody multiBody=multiBuilder.build();
        MessageHelper.getInstance().postHttps(BaseAPI.VERSION_DES + "/API/User/CloseDateBox", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {

            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String json=response.body().string();
                LogUtils.e("请求看了"+json);
            }
        });
    }
}

class ArrayWheelAdapter<T> implements WheelAdapter{
    private List<T> items;

    public ArrayWheelAdapter(List<T> items) {
        this.items = items;
    }

    @Override
    public int getItemsCount() {
        return items.size();
    }

    @Override
    public Object getItem(int index) {
        if(index>=0&&index<items.size()){
            return items.get(index);
        }
        return "";
    }

    @Override
    public int indexOf(Object o) {
        return items.indexOf(o);
    }

}
