package com.zizhiguanjia.model_message.dialog;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.text.Html;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.utils.SpannableStringUtils;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.bean.FeedBackBean;
import com.zizhiguanjia.model_message.bean.LoginBean;
import com.zizhiguanjia.model_message.helper.MessageHelper;
import com.zizhiguanjia.model_message.listenter.CommonDialogListener;

import java.io.IOException;
import java.lang.reflect.Type;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

public class AccountLogOffSwitchDialog extends CenterPopupView implements View.OnClickListener {
    private TextView msgTv;
    private String userToken;
    private TextView postCancelTv,postSwitchTv;
    private LoadingPopupView loadingPopupView;
    private Activity mActivity;
    public AccountLogOffSwitchDialog(@NonNull Context context,String token) {
        super(context);
        this.userToken=token;
        this.mActivity= (Activity) context;
        addInnerContent();
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
    }

    @Override
    protected int initLayoutResId() {
        return R.layout.message_account_logoffswitch_layour;
    }

    @Override
    protected int getMaxWidth() {
        return DpUtils.dp2px(getContext(),310);
    }

    @Override
    protected void initView() {
        super.initView();
        msgTv=this.findViewById(R.id.tvMsg);
        postSwitchTv=this.findViewById(R.id.tvPostSwitch);
        postCancelTv=this.findViewById(R.id.tvPostCancel);
        postCancelTv.setOnClickListener(this);
        postSwitchTv.setOnClickListener(this);
        loadingPopupView=new PopupManager.Builder(mActivity).asLoading();
        loadingPopupView.setTitle("正在切换...");
    }

    @Override
    protected void init() {
        super.init();
        SpannableStringBuilder textFrontColorSp = SpannableStringUtils.getBuilder("检测到您的登录设备有变化，为了保障数据安全，请您确认以下信息：\n\n")
                .setForegroundColor(Color.parseColor("#000000"))
                .append("1）当前设备为您本人使用，以后作为常用登录设备，原登录设备作废，请选择“").setForegroundColor(Color.parseColor("#000000")).setBold()
                .append("切换设备").setForegroundColor(Color.parseColor("#007AFF")).setBold()
                .append("”，即可登录；\n" + "2）如您对上述问题无法确认，请选择“").setForegroundColor(Color.parseColor("#000000")).setBold()
                .append("取消").setForegroundColor(Color.parseColor("#007AFF")).setBold()
                .append("”，继续用原设备登录。\n\n").setForegroundColor(Color.parseColor("#000000")).setBold()
                .append("重要提示：").setForegroundColor(Color.parseColor("#E02020")).setBold()
                .append("您仅有1次机会可切换至新设备登录，请谨慎选择。").setForegroundColor(Color.parseColor("#E02020"))
                .create();
    msgTv.setText(textFrontColorSp);
    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(),445);
    }

    @Override
    public void onClick(View v) {
        if (v.getId()==R.id.tvPostCancel){
        }else if(R.id.tvPostSwitch==v.getId()){
            postSwitchDrivce();
        }
        Bus.post(new MsgEvent(0x999));
        dismiss();
    }
    private void postSwitchDrivce(){
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("userToken",userToken);
        RequestBody multiBody=multiBuilder.build();
        loadingPopupView.show();
        MessageHelper.getInstance().postHttps(BaseAPI.VERSION_DES + "/API/User/SwitchingDevice", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        loadingPopupView.dismiss();
                        ToastUtils.normal("服务器繁忙，请稍后尝试~");
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        loadingPopupView.dismiss();
                        if(response.code()==200) {
                            try {
                                String json = response.body().string();
                                Type jsonType = new TypeToken<BaseData<LoginBean>>() {}.getType();
                                BaseData<LoginBean> commonExamBeanBaseData= new Gson().fromJson(json,jsonType);
                                if(commonExamBeanBaseData.isSuccess()){
                                    ToastUtils.normal(commonExamBeanBaseData.Message,Gravity.CENTER);
                                    String jsonStr=com.wb.lib_network.utils.GsonUtils.gsonString(commonExamBeanBaseData.Data);
                                    AccountHelper.successLoginAccount(jsonStr);
                                    Bus.post(new MsgEvent(AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS));
                                    LogUtils.e("看看解析的数据----->>>>"+commonExamBeanBaseData.getResult().getAccount());
                                }else {
                                    ToastUtils.normal(commonExamBeanBaseData.Message,Gravity.CENTER);
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                                ToastUtils.normal("服务器繁忙，请稍后尝试！", Gravity.CENTER);
                            }
                        }else {
                            ToastUtils.normal("服务器繁忙，请稍后尝试！", Gravity.CENTER);
                        }
                    }
                });
            }
        });
    }
}
