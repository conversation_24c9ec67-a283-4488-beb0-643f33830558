package com.zizhiguanjia.model_message.dialog;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.wb.lib_pop.code.CenterPopupView;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.listenter.DialogCommonListener;

public class CoupoinDialog extends CenterPopupView implements View.OnClickListener {
    private DialogCommonListener dialogCommonListener;
    private ImageView backImage;
    private TextView tvLing;
    private String ids;
    public CoupoinDialog(@NonNull Context context,DialogCommonListener dialogCommonListener,String ids) {
        super(context);
        this.ids=ids;
        this.dialogCommonListener=dialogCommonListener;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return bindLayoutId != 0 ? bindLayoutId : R.layout.message_coupoin;
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
    }
    @Override
    protected void initView() {
        super.initView();
        LogUtils.e("看看对话框----10000->>>>"+BaseConfig.showCouponState);
        BaseConfig.showCouponState=true;
        backImage=this.findViewById(R.id.imgBack);
        tvLing=this.findViewById(R.id.lingTv);
        initListener();
    }
    private void initListener(){
        backImage.setOnClickListener(this);
        tvLing.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.imgBack){
            if(dialogCommonListener==null)return;
            dialogCommonListener.onCommonBack();
        }else if(v.getId()==R.id.lingTv){
            if(dialogCommonListener==null)return;
            dialogCommonListener.onLing(ids);
        }
    }
}
