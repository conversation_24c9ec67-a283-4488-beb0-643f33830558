package com.zizhiguanjia.model_message.dialog;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_utils.permission.PermissionListener;
import com.wb.lib_utils.permission.PermissionUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.model_message.R;

public class ServiceDialog extends BottomPopupView implements View.OnClickListener{
    private TextView mCommonCallTelTv,mCommonCancelTv;
    public ServiceDialog(@NonNull Context context) {
        super(context);
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return R.layout.message_service_tel_layout;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        mCommonCallTelTv=this.findViewById(R.id.tvCommonCallTel);
        mCommonCancelTv=this.findViewById(R.id.tvCommonCancel);
        mCommonCancelTv.setOnClickListener(this);
        mCommonCallTelTv.setOnClickListener(this);
        initData();
    }
    private void initData(){
        mCommonCallTelTv.setText("拨打："+ConfigHelper.getKfTel());
    }
    @Override
    public void onClick(View v) {
        if(v.getId()== R.id.tvCommonCallTel){
            DeviceUtils.dial(getContext(), ConfigHelper.getKfTel());
//            Activity activity= (Activity) getContext();
//            PermissionUtils.with(activity).addPermissions(Manifest.permission.CALL_PHONE)
//                    .setPermissionsCheckListener(new PermissionListener() {
//                        @Override
//                        public void permissionRequestSuccess() {
//                            DeviceUtils.callPhone(getContext(), ConfigHelper.getKfTel());
//                            dismiss();
//                        }
//                        @Override
//                        public void permissionRequestFail(String[] grantedPermissions, String[] deniedPermissions, String[] forceDeniedPermissions) {
//
//                            MessageHelper.permissionsTips(activity,"温馨提示",
//                                    "请前往设置->应用->【" + PermissionUtils.getAppName(activity) + "】->权限中打开打电话权限，否则功能无法正常运行！","确定");
//                        }
//                    })
//                    .createConfig()
//                    .setForceAllPermissionsGranted(false)
//                    .setForceDeniedPermissionTips("请前往设置->应用->【" + PermissionUtils.getAppName(getContext()) + "】->权限中打开相关权限，否则功能无法正常运行！")
//                    .buildConfig()
//                    .startCheckPermission();

        }else if(v.getId()==R.id.tvCommonCancel){
            dismiss();
        }
    }
}
