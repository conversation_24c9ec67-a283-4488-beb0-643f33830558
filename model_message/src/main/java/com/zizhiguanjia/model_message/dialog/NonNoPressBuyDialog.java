package com.zizhiguanjia.model_message.dialog;

import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.helper.PayHelper;
import com.zizhiguanjia.lib_base.listeners.PayInfoListener;
import com.zizhiguanjia.model_message.R;

import androidx.annotation.NonNull;

public class NonNoPressBuyDialog extends BottomPopupView implements View.OnClickListener {
    private ImageView imgClose,imgUpVip;
    private String goodsId;
    private Activity mActivity;
    private TextView pricesTv,timesTv,edTv;
    private ImageView imgGuild;
    private boolean ed;
    private String  payRouthParams;
    private long time;
    public NonNoPressBuyDialog(@NonNull Context context ,Activity activity,boolean ised,String payRouthParams) {
        super(context);
        this.ed=ised;
        this.payRouthParams=payRouthParams;
        this.mActivity=activity;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return R.layout.message_core_exam_nobuy_vip;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        imgGuild=this.findViewById(R.id.imgGuild);
        imgClose=this.findViewById(R.id.imgClose);
        imgUpVip=this.findViewById(R.id.imgUpVip);
        timesTv=this.findViewById(R.id.timesTv);
        pricesTv=this.findViewById(R.id.pricesTv);
        edTv=this.findViewById(R.id.edTv);
        removeLeft1();
        if(!ed){
            edTv.setText("试用额度已用完，升级题库畅快刷");
        }else {
            edTv.setText("额，没有权限哦，升级题库畅快刷");
        }
        imgClose.setOnClickListener(this);
        imgUpVip.setOnClickListener(this);
        PayHelper.getPayInfoByMajId(BaseConfig.majId, new PayInfoListener() {
            @Override
            public void onPayInfo(boolean state, String price, String times, String GoodsId,String json,int goodType, int majorType) {
                LogUtils.e("-----"+state+price+times+GoodsId);
                goodsId=GoodsId;
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        String prices;
                        try {
                            if (Integer.parseInt(price) < 99) {
                                prices=(String.valueOf(Double.parseDouble(price) / 100) );
                            } else {
                                prices=(String.valueOf(Integer.parseInt(price) / 100));
                            }
                        }catch (Exception e){
                            prices="0.00";
                        }
                        pricesTv.setText(prices);
                        timesTv.setText(times+"有效期");
                    }
                });
            }
        });
    }

    @Override
    public void onClick(View v) {
        LogUtils.e("点击了");
        if(v.getId()==R.id.imgUpVip){
            if(StringUtils.isEmpty(goodsId))return;
            PayHelper.payOrder(goodsId,mActivity,payRouthParams);
//            Bus.post(new MsgEvent(PayMsgTypeConfig.PAY_MSG_SUCCESS));
            this.dismiss();
        }else if(v.getId()==R.id.imgClose){
            this.dismiss();
        }
    }
    private void removeLeft1() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(imgGuild, "translationX", 0,6);
        animator.setDuration(500);
        animator.setRepeatCount(-1);
        animator.setRepeatMode(ObjectAnimator.REVERSE);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(imgGuild, "translationY",   6,12);
        animator1.setDuration(500);
        animator1.setRepeatCount(-1);
        animator1.setRepeatMode(ObjectAnimator.REVERSE);
        animator1.start();
    }

    @Override
    protected void doAfterShow() {
        time=System.currentTimeMillis();
        super.doAfterShow();
    }

    @Override
    public void dismiss() {
        BaseConfig.dialogTime=System.currentTimeMillis()-time;
        super.dismiss();
    }
}
