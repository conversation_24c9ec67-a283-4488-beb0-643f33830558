package com.zizhiguanjia.model_message.dialog;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.lib_base.helper.UtilsHelper;
import com.zizhiguanjia.lib_base.utils.TimeUtils;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.adapter.CustomerAdapter;
import com.zizhiguanjia.model_message.bean.FeedBackBean;
import com.zizhiguanjia.model_message.helper.MessageHelper;
import com.zizhiguanjia.model_message.listenter.CustomerScoresListenter;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

public class CustomerDialog extends BottomPopupView implements View.OnClickListener,TextWatcher,CustomerScoresListenter {
    private RecyclerView mRecyclerView;
    private CustomerAdapter adapter;
    private TextView postDatasTv;
    private View scourceLayout,scourceSuccessLayout,facebackLayout;
    private EditText etInputBack;
    private ImageView imgClose;
    private String FeedbackScoreId;
    private String routh;
    public CustomerDialog(@NonNull Context context,String routh) {
        super(context);
        this.routh=routh;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return R.layout.message_customer_layout;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
    }

    @Override
    protected void initView() {
        super.initView();
        mRecyclerView=this.findViewById(R.id.messageMydRcy);
        postDatasTv=this.findViewById(R.id.tvPostDatas);
        scourceLayout=this.findViewById(R.id.message_scour_layout);
        scourceSuccessLayout=this.findViewById(R.id.message_scour_success_layout);
        facebackLayout=this.findViewById(R.id.message_faceback_layout);
        etInputBack=this.findViewById(R.id.etInputBack);
        imgClose=this.findViewById(R.id.imgClose);
        postDatasTv.setOnClickListener(this);
        etInputBack.addTextChangedListener(this);
        imgClose.setOnClickListener(this);
        if(routh.equals("2")){
            postPoionHttps();
        }
    }

    @Override
    protected void init() {
        super.init();
        mRecyclerView.setLayoutManager(new GridLayoutManager(getContext(),10));
        adapter=new CustomerAdapter(this);
        mRecyclerView.setAdapter(adapter);
        List<String> des=new ArrayList<>();
        for(int i=1;i<11;i++){
            des.add(String.valueOf(i));
        }
        adapter.setDataItems(des);
        setState(1,"");
    }
    @Override
    protected int getMaxWidth() {
        return DpUtils.getScreenWidth(getContext());
    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(),300);
    }

    @Override
    public void onClick(View v) {
        if(v.getId()==R.id.tvPostDatas){
            checkDatas();
        }else if(v.getId()==R.id.imgClose){
            this.dismiss();
        }
    }
    private void checkDatas(){
        int state= (int) postDatasTv.getTag();
        LogUtils.e("填写的des"+state);
        if(state==1){
            if(adapter==null||adapter.getCurSelectOption()==-1){
                ToastUtils.normal("请选择评分~", Gravity.CENTER);
                return;
            }
            postAddFeedbackScoreHttps();
        }else if(state==2){
            this.dismiss();
            postGotoMarketyHttps();
            UtilsHelper.openMarkets((Activity) getContext());
        }else if(state==3){
            String des=etInputBack.getText().toString().trim();
            if(StringUtils.isEmpty(des)){
                ToastUtils.normal("请填写反馈信息~", Gravity.CENTER);
                return;
            }
            if(StringUtils.isEmpty(FeedbackScoreId)){
                ToastUtils.normal("主键Id异常，请重新尝试~", Gravity.CENTER);
                return;
            }
            postAddFeedbackHttps();
        }
    }
    private void postPoionHttps(){
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("entrance",routh);
        RequestBody multiBody=multiBuilder.build();
        MessageHelper.getInstance().postHttps(BaseAPI.VERSION_DES + "/API/Common/AddScoreFrameRecord", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
            }
        });
    }
    private void postGotoMarketyHttps(){
        if(StringUtils.isEmpty(FeedbackScoreId)){
            LogUtils.e("无效的*---->>>FeedbackScoreId");
            return;
        }
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("feedbackScoreId",FeedbackScoreId);
        multiBuilder.addFormDataPart("refferPage",routh);
        RequestBody multiBody=multiBuilder.build();
        MessageHelper.getInstance().postHttps(BaseAPI.VERSION_DES + "/API/Common/ClickMarket", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {

            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {

            }
        });
    }
    private void setState(int state,String ids){
        if(state==1){
            scourceLayout.setVisibility(VISIBLE);
            scourceSuccessLayout.setVisibility(GONE);
            facebackLayout.setVisibility(GONE);
            postDatasTv.setText("提交信息");
        }else if(state==2){
            this.FeedbackScoreId=ids;
            scourceLayout.setVisibility(View.GONE);
            scourceSuccessLayout.setVisibility(VISIBLE);
            facebackLayout.setVisibility(GONE);
            postDatasTv.setText("去应用商店");
        }else if(state==3){
            this.FeedbackScoreId=ids;
            scourceLayout.setVisibility(View.GONE);
            scourceSuccessLayout.setVisibility(GONE);
            facebackLayout.setVisibility(VISIBLE);
            postDatasTv.setText("提交反馈");
        }
        updateState(state);
    }
    private void updateState(int state){
        if(state==1){
            if(adapter==null||adapter.getCurSelectOption()==-1){
                postDatasTv.setBackgroundResource(R.drawable.message_login_nor_bg);
            }else {
                postDatasTv.setBackgroundResource(R.drawable.message_login_yes_bg);
            }
            postDatasTv.setTag(1);
        }else if(state==2){
            postDatasTv.setBackgroundResource(R.drawable.message_login_yes_bg);
            postDatasTv.setTag(2);
        }else if(state==3){
            postDatasTv.setTag(3);
            postDatasTv.setBackgroundResource(R.drawable.message_login_nor_bg);
        }
    }
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        if(count>0){
            postDatasTv.setBackgroundResource(R.drawable.message_login_yes_bg);
        }else {
            postDatasTv.setBackgroundResource(R.drawable.message_login_nor_bg);
        }
    }

    @Override
    public void afterTextChanged(Editable s) {
    }
    private void postAddFeedbackScoreHttps(){
        int score=adapter.getCurSelectOption()+1;
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("score", String.valueOf(score));
        multiBuilder.addFormDataPart("entrance", routh);
        RequestBody multiBody=multiBuilder.build();
        MessageHelper.getInstance().postHttps(BaseAPI.VERSION_DES + "/API/Common/AddFeedbackScore", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                ToastUtils.normal("服务器繁忙，请稍后尝试！",Gravity.CENTER);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        if(response.code()==200) {
                            BaseData<FeedBackBean> baseData = null;
                            try {
                                String json=response.body().string();
                                Type jsonType = new TypeToken<BaseData<FeedBackBean>>() {
                                }.getType();
                                baseData = new Gson().fromJson(json, jsonType);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            if(score>=7){
                                //去应用市场
                                setState(2,baseData==null?"":baseData.getResult().getFeedbackScoreId());
                            }else {
                                //评论
                                setState(3,baseData==null?"":baseData.getResult().getFeedbackScoreId());
                            }
                        }else {
                            ToastUtils.normal("服务器繁忙，请稍后尝试！",Gravity.CENTER);
                        }
                    }
                });
            }
        });
    }
    private void postAddFeedbackHttps(){
        String des=etInputBack.getText().toString().trim();
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("type", "20");
        multiBuilder.addFormDataPart("content",des);
        multiBuilder.addFormDataPart("questionId",FeedbackScoreId);
        multiBuilder.addFormDataPart("refferPage",routh);
        RequestBody multiBody=multiBuilder.build();
        MessageHelper.getInstance().postHttps(BaseAPI.VERSION_DES + "/API/Common/AddFeedback", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                ToastUtils.normal("服务器繁忙，请稍后尝试！",Gravity.CENTER);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
               MainThreadUtils.post(new Runnable() {
                   @Override
                   public void run() {
                       if(response.code()==200) {
                           ToastUtils.normal("反馈成功~",Gravity.CENTER);
                           dismiss();
                       }else {
                           ToastUtils.normal("服务器繁忙，请稍后尝试！",Gravity.CENTER);
                       }
                   }
               });
            }
        });
    }

    @Override
    public void onSelectScores(String des) {
        postDatasTv.setBackgroundResource(R.drawable.message_login_yes_bg);
    }
}
