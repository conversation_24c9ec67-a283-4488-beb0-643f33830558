package com.zizhiguanjia.model_message.dialog;

import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Context;
import android.graphics.Paint;
import android.text.TextPaint;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.task.RxAsyncTask;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.FileUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.lib_base.helper.PayHelper;
import com.zizhiguanjia.lib_base.listeners.PayInfoListener;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.adapter.NoPremissEjBuyDetails;
import com.zizhiguanjia.model_message.adapter.NoPresmissBuyAdapter;
import com.zizhiguanjia.model_message.adapter.NoPresmissEjBuyAdapter;
import com.zizhiguanjia.model_message.bean.GoodsInfoBean;
import com.zizhiguanjia.model_message.bean.NoPremissBuyBean;
import com.zizhiguanjia.model_message.listenter.BuySelectListener;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.collection.ListUtil;

public class NopreMissBuyEjDialog extends BottomPopupView implements View.OnClickListener, BuySelectListener {
    private ImageView imgClose,imgUpVip;
//    private String goodsId;
    private Activity mActivity;
    private TextView pricesTv,timesTv;
    private ImageView imgGuild;
    private boolean ed;
    private long time;
    private String payRouthParams;
    private RecyclerView mRecyclerView;
    private RecyclerView mGoodsRecyclerView;
    private NoPresmissEjBuyAdapter mAdapter;
    private NoPremissEjBuyDetails noPremissEjBuyDetails;
    private List<GoodsInfoBean> goodsInfoBeans=new ArrayList<>();
    private String mPrice,mTimes,mGoodsId,mJsonList;
    public NopreMissBuyEjDialog(@NonNull Context context, Activity activity, boolean ised, String payRouthParams,String price, String times, String GoodsId,String jsonList,int goodType) {
        super(context);
        this.ed=ised;
        this.payRouthParams=payRouthParams;
        this.mActivity=activity;
        this.mPrice=price;
        this.mTimes=times;
        this.mGoodsId=GoodsId;
        this.mJsonList=jsonList;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return R.layout.message_nopremiss_ej_layout;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        BaseConfig.payToast=true;
        imgGuild=this.findViewById(R.id.imgGuild);
        imgClose=this.findViewById(R.id.imgClose);
        imgUpVip=this.findViewById(R.id.imgUpVip);
        timesTv=this.findViewById(R.id.timesTv);
        pricesTv=this.findViewById(R.id.pricesTv);
        mGoodsRecyclerView=this.findViewById(R.id.premissGoodsRvs);
        mRecyclerView=this.findViewById(R.id.premissRvs);
        removeLeft1();
        mRecyclerView.setLayoutManager(new GridLayoutManager(getContext(),4));
        mAdapter=new NoPresmissEjBuyAdapter();
        mRecyclerView.setAdapter(mAdapter);
        imgClose.setOnClickListener(this);
        imgUpVip.setOnClickListener(this);

        mGoodsRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        noPremissEjBuyDetails=new NoPremissEjBuyDetails(this);
        mGoodsRecyclerView.setAdapter(noPremissEjBuyDetails);
        String prices;
        try {
            if (Integer.parseInt(mPrice) < 99) {
                prices=(String.valueOf(Double.parseDouble(mPrice) / 100) );
            } else {
                prices=(String.valueOf(Integer.parseInt(mPrice) / 100));
            }
        }catch (Exception e){
            prices="0.00";
        }
        pricesTv.setText(prices);
        timesTv.setText(mTimes+"有效期");
        if(!StringUtils.isEmpty(mJsonList)){
            List<GoodsInfoBean> lists=GsonUtils.jsonToList(mJsonList,GoodsInfoBean.class);
            LogUtils.e("商品大小"+lists.size());
            if(!lists.isEmpty()){
                goodsInfoBeans.clear();
                goodsInfoBeans.addAll(lists);
                noPremissEjBuyDetails.setDataItems(goodsInfoBeans);
            }
        }
        initConfigData();
    }
    @Override
    public void onClick(View v) {
        LogUtils.e("点击了");
        if(v.getId()==R.id.imgUpVip){
            if(StringUtils.isEmpty(mGoodsId))return;
            PayHelper.payOrder(mGoodsId,mActivity,payRouthParams);
            BaseConfig.payToast=false;
            this.dismiss();
        }else if(v.getId()==R.id.imgClose){
            BaseConfig.payToast=false;
            this.dismiss();
        }
    }
    private void initConfigData(){
//        String jsonDes="premissconfig_ej.json";
        String jsonDes="newpremissconfig.json";
        RxJavaUtils.executeAsyncTask(new RxAsyncTask<String, List<NoPremissBuyBean>>(jsonDes) {
            @Override
            public void doInUIThread(List<NoPremissBuyBean> userListDataConfigBeans) {
                mAdapter.setDataItems(userListDataConfigBeans);
            }
            @Override
            public List<NoPremissBuyBean> doInIOThread(String s) {
                try {
                    String json = FileUtils.getFromAssets(AppUtils.getApp(), s);
                    List<NoPremissBuyBean> userCertificateBeans = GsonUtils.jsonToList(json, NoPremissBuyBean.class);
                    return userCertificateBeans;
                } catch (Exception e) {
                    return null;
                }
            }
        });
    }
    private void removeLeft1() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(imgGuild, "translationX", 0,6);
        animator.setDuration(500);
        animator.setRepeatCount(-1);
        animator.setRepeatMode(ObjectAnimator.REVERSE);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(imgGuild, "translationY",   6,12);
        animator1.setDuration(500);
        animator1.setRepeatCount(-1);
        animator1.setRepeatMode(ObjectAnimator.REVERSE);
        animator1.start();
    }

    @Override
    protected void doAfterShow() {
        time=System.currentTimeMillis();
        super.doAfterShow();
    }

    @Override
    public void dismiss() {
        BaseConfig.dialogTime=System.currentTimeMillis()-time;
        super.dismiss();
    }

    @Override
    public void onSelectGoodsInfo(GoodsInfoBean goods) {
        LogUtils.e("------->>>>goodsId"+mGoodsId);
        if(goodsInfoBeans==null||goodsInfoBeans.size()==0)return;
        mGoodsId=goods.getGoodsId();
        LogUtils.e("------->>>>goodsId"+mGoodsId);
        String prices;
        try {
            if (Integer.parseInt(goods.getPrice()) < 99) {
                prices=(String.valueOf(Double.parseDouble(goods.getPrice()) / 100) );
            } else {
                prices=(String.valueOf(Integer.parseInt(goods.getPrice()) / 100));
            }
        }catch (Exception e){
            prices="0.00";
        }
        pricesTv.setText(prices);
        for(GoodsInfoBean goodsInfoBean:goodsInfoBeans){
            if(goodsInfoBean.getGoodsId().equals(goods.getGoodsId())){
                goodsInfoBean.setSelect(true);
            }else {
                goodsInfoBean.setSelect(false);
            }
        }
        noPremissEjBuyDetails.notifyDataSetChanged();
    }

    @Override
    protected int getMaxHeight() {
        return DpUtils.dp2px(getContext(),455);
    }
}
