package com.zizhiguanjia.model_message.navigator;

import com.zizhiguanjia.model_message.bean.CityPickerData;

import java.util.List;

public interface CertificateUpdataNavigator {
    void openSelectCityDialog(List<CityPickerData> cityPickerData,boolean open);
    void initListenter();
    void openUserSelectCityDialog();
    void OpenUserSelectCertificateDialog();
    void getCertificateListByCity(List<CityPickerData> Records,boolean open,boolean reshTv);
    void initCurrentCertificate(String cityName,String cityCode,String certificateName,String ids);
    void bindSuccess();
    void showOrHindLoading(boolean b);
    void showErrorToast(String msg);
    void fillCertificate(List<CityPickerData> cityPickerDataLists);
}
