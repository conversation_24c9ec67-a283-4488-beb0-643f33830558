package com.zizhiguanjia.model_message.manager;
import android.app.Activity;
import android.os.Message;

import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.OrderDialogBean;
import com.zizhiguanjia.lib_base.helper.MessageHelper;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;

public class DialogManager {
    private static DialogManager dialogManager;
    private Queue<OrderDialogBean> queue=new ConcurrentLinkedQueue<>();
    private int lastIndex=0;
    public static DialogManager getInstance(){
        if(dialogManager==null){
            synchronized (DialogManager.class){
                dialogManager=new DialogManager();
            }
        }
        return dialogManager;
    }
    private boolean canshow(){
        return queue.size()<3;
    }

    /**
     * 将对话框添加到队列中
     * @param orderDialogBean
     */
    public void pushToQueue(OrderDialogBean orderDialogBean){
        if(orderDialogBean==null)return;
        if(orderDialogBean.getIndex()<=0)return;
        LogUtils.e("看看弹窗----->>>"+orderDialogBean.getIndex());
        queue.add(orderDialogBean);
        boolean jj=canshow();
        LogUtils.e("看看弹窗----->>>"+jj);
        if(jj){
            nextDialogTask();
        }
    }
    public void nextDialogTask(){
        if(queue==null)return;
        if(queue.isEmpty())return;
        queueScheduling();
        OrderDialogBean a=queue.element();
        if(a==null||a.getIndex()<=0||lastIndex==a.getIndex()){
            //任务空
            LogUtils.e("看看对话框----->>>>yes"+lastIndex+"**"+a.getIndex());
        }else {
            LogUtils.e("看看对话框----->>>>no");
            openAboutDialog(a);
        }
    }
    public void restPageState(){
        lastIndex=-1;
        removeTopTask();
    }
    private void openAboutDialog(OrderDialogBean a){
        lastIndex=a.getIndex();
        LogUtils.e("看看对话框----->>>>"+a.getIndex());
        if(a.getIndex()==1){
            //升级app
            MessageHelper.openAppUpdataVersionTips(a.getActivity(),a.getUpdateProxy(),a.getPromptEntity());
        }else if(a.getIndex()==2){
            //升级题库
            MessageHelper.openUpdateExam(a.getTitle(),a.getSubTitle(),a.getContentTitle(),a.getContent(),a.getSubContent(),a.getIds());
        }else if(a.getIndex()==3){
            //优惠卷
            MessageHelper.startCoupion(a.getTips());
        }
    }
    /**
     * 队列排序
     */
    private void queueScheduling(){
        List<OrderDialogBean> list=new ArrayList<>();
        for(OrderDialogBean map:queue){
            list.add(map);
        }
        ListUtil.sortByProperty(list,"index");
        queue.clear();
        for(OrderDialogBean a:list){
            queue.add(a);
        }
    }
    private void removeTopTask() {
        lastIndex=-1;
        LogUtils.e("清楚----1>>>"+lastIndex);
        queue.poll(); //出栈
    }
    public void nextTask(){
        LogUtils.e("清楚---2->>>"+lastIndex);
        removeTopTask();
        nextDialogTask();
    }
}
