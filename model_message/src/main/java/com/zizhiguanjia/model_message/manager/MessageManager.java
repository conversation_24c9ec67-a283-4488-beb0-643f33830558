package com.zizhiguanjia.model_message.manager;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.provider.Settings;
import android.view.View;

import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.animator.PopupAnimation;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_pop.enums.PopupPosition;
import com.wb.lib_pop.interfaces.OnCancelListener;
import com.wb.lib_pop.interfaces.OnClickOutsideListener;
import com.wb.lib_pop.interfaces.OnConfirmListener;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.xuexiang.xupdate.entity.UpdateEntity;
import com.xuexiang.xupdate.proxy.IUpdateProxy;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.helper.AddressHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.listeners.ExamGuilderListener;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.listeners.HomeFootListener;
import com.zizhiguanjia.lib_base.listeners.ILoginVipTs;
import com.zizhiguanjia.lib_base.listeners.MessageSuccessPayListener;
import com.zizhiguanjia.lib_base.listeners.OnChoiceTimeListenter;
import com.zizhiguanjia.lib_base.listeners.SpeedListener;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.model_message.dialog.AccountLogOffSwitchDialog;
import com.zizhiguanjia.model_message.dialog.AccountShowPremissDialog;
import com.zizhiguanjia.model_message.dialog.AccountVipTsDialog;
import com.zizhiguanjia.model_message.dialog.AccoutLockDialog;
import com.zizhiguanjia.model_message.dialog.AppUpdataVersionDialog;
import com.zizhiguanjia.model_message.dialog.AqySuccessPayTsDialog;
import com.zizhiguanjia.model_message.dialog.BottomListDialog;
import com.zizhiguanjia.model_message.dialog.CertificateUpdataDialog;
import com.zizhiguanjia.model_message.dialog.CustomerDialog;
import com.zizhiguanjia.model_message.dialog.GeneralCentDialog;
import com.zizhiguanjia.model_message.dialog.GuilderDialog;
import com.zizhiguanjia.model_message.dialog.HomeOffOnileServiceDialog;
import com.zizhiguanjia.model_message.dialog.HomeSelectCityDialog;
import com.zizhiguanjia.model_message.dialog.LearningPlanDialog;
import com.zizhiguanjia.model_message.dialog.OtherPayTsDialog;
import com.zizhiguanjia.model_message.dialog.ServiceDialog;
import com.zizhiguanjia.model_message.dialog.SpeedChoiceDialog;
import com.zizhiguanjia.model_message.dialog.SpeedChoiceFullDialog;
import com.zizhiguanjia.model_message.dialog.TimeSelectDialog;
import com.zizhiguanjia.model_message.listenter.IHomeSelectCity;

import java.util.List;

import io.reactivex.functions.Consumer;

public class MessageManager {
    private static MessageManager messageManager;
    private BasePopupView againPayPopu;
    private boolean isOpenLearn=false;
    public static MessageManager getInstance() {
        if (messageManager == null) {
            synchronized (MessageManager.class) {
                return messageManager = new MessageManager();
            }
        }
        return messageManager;
    }
    public void openGuilder(Activity activity, int type, ExamGuilderListener examGuilderListener){
        new PopupManager.Builder(activity).dismissOnTouchOutside(false)
                .dismissOnBackPressed(false)
                .maxHeight(DeviceUtils.getScreenHeight(activity))
                .maxWidth(DeviceUtils.getScreenHeight(activity))
                .asCustom(new GuilderDialog(activity, examGuilderListener, type)).show();
    }
    public void openTimeChoiceDialog(Activity activity, OnChoiceTimeListenter onChoiceTimeListenter){
        new PopupManager.Builder(activity).dismissOnTouchOutside(false)
                .dismissOnBackPressed(false)
                .asCustom(new TimeSelectDialog(activity,onChoiceTimeListenter)).show();
    }
    public void openAppUpdataVersionTips(Activity activity,IUpdateProxy updateProxy, UpdateEntity updateEntity){
        new PopupManager.Builder(activity).dismissOnBackPressed(false).dismissOnTouchOutside(false).asCustom(new AppUpdataVersionDialog(activity,updateProxy,updateEntity)).show();
    }
    public void openServiceTips(Activity activity){
        new PopupManager.Builder(activity).asCustom(new ServiceDialog(activity)).show();
    }
    public BasePopupView openAccountPrivacyTipic(Activity activity,View mView){
        BasePopupView userPreMissDialog = new PopupManager.Builder(activity)
                .dismissOnTouchOutside(false)
                .dismissOnBackPressed(false)
                .isClickThrough(true)
                .offsetX(-DpUtils.dp2px(activity, 10))
                .atView(mView)
                .hasStatusBarShadow(false)
                .hasShadowBg(false)
                .popupAnimation(PopupAnimation.ScaleAlphaFromCenter)
                .maxHeight(DpUtils.dp2px(AppUtils.getApp(), 60))
                .maxWidth(DpUtils.dp2px(AppUtils.getApp(), 218))
                .popupPosition(PopupPosition.Bottom)
                .asCustom(new AccountShowPremissDialog(activity));
        return userPreMissDialog;
    }
    public void permissionsTips(Activity activity, String title, String msg, String confim) {
        new AlertDialog.Builder(activity)
                .setTitle(title)
                .setMessage(msg)
                .setCancelable(false)
                .setPositiveButton(confim, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        openSettingPage(activity);
                    }
                }).show();
    }

    private void openSettingPage(Context context) {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", context.getPackageName(), null);
        intent.setData(uri);
        context.startActivity(intent);
    }

    public void openBottomListDialog(Activity activity, String title, String msg, String cancel, String confirm, boolean isHind, boolean isBack, GeneralDialogListener generalDialogListener, int maxW, int maxH) {
        BottomListDialog popupView = new BottomListDialog(activity);
        popupView.setTitleContent(title, msg, null);
        popupView.setCancelText(cancel);
        popupView.setConfirmText(confirm);
        popupView.isHideCancel = isHind;
        popupView.setListener(new OnConfirmListener() {
            @Override
            public void onConfirm() {
                generalDialogListener.onConfim();
            }
        }, new OnCancelListener() {
            @Override
            public void onCancel() {
                generalDialogListener.onCancel();
            }
        });
        againPayPopu = new PopupManager.Builder(activity).maxWidth(DpUtils.dp2px(activity, maxW)).maxHeight(DpUtils.dp2px(activity, maxH)).dismissOnTouchOutside(isBack).dismissOnBackPressed(isBack).asCustom(popupView);
        if (againPayPopu == null) return;
        if (againPayPopu.isShow()) return;
        againPayPopu.show();
    }

    /**
     * 打开中间
     *
     * @param activity
     * @param title
     * @param msg
     * @param cancel
     * @param confim
     * @param isHideCancel
     * @param isBack
     */
    private boolean isShow=false;
    public void openGeneralCentDialog(Activity activity, String title, String msg, String cancel, String confim, boolean isHideCancel, boolean isBack, GeneralDialogListener generalDialogListener, int maxW, int maxH) {
        if(!isShow){
            GeneralCentDialog popupView = new GeneralCentDialog(activity);
            popupView.setTitleContent(title, msg, null);
            popupView.setCancelText(cancel);
            popupView.setConfirmText(confim);
            popupView.isHideCancel = isHideCancel;
            popupView.setListener(new OnConfirmListener() {
                @Override
                public void onConfirm() {
                    isShow = false;
                    generalDialogListener.onConfim();
                }
            }, new OnCancelListener() {
                @Override
                public void onCancel() {
                    isShow = false;
                    generalDialogListener.onCancel();
                }
            }, new OnClickOutsideListener() {
                @Override
                public void onClickOutside() {
                    isShow = false;
                    generalDialogListener.onDismiss();
                }
            });
            new PopupManager.Builder(activity)
                    .dismissOnTouchOutside(isBack)
                    .dismissOnBackPressed(isBack)
                    .dismissOnTouchOutside(false)
                    .maxHeight(DpUtils.dp2px(activity, 185))
                    .maxWidth(DpUtils.dp2px(activity, 290))
                    .asCustom(popupView).show();
            isShow=true;
        }

    }

    public void openSpeedFullDialog(Context activity, View attachView, SpeedListener speedListener) {
        if (!isDoubleClick()) {
            BasePopupView userPreMissDialog = new PopupManager.Builder(activity)
                    .isClickThrough(true)
                    .atView(attachView)
                    .hasStatusBarShadow(false)
                    .hasShadowBg(false)
                    .offsetY(DpUtils.dp2px(activity, -60))
                    .popupAnimation(PopupAnimation.ScaleAlphaFromCenter)
                    .popupPosition(PopupPosition.Top)
                    .asCustom(new SpeedChoiceFullDialog(activity, speedListener));
            userPreMissDialog.show();
        }
    }

    public void openSpeedDialog(Context activity, View attachView, SpeedListener speedListener) {

        if (!isDoubleClick()) {
            new PopupManager.Builder(activity).asCustom(new SpeedChoiceDialog(activity, speedListener)).show();
        }
    }
    public void openCusomerDialog(Context activity,String routh) {

        if (!isDoubleClick()) {
            new PopupManager.Builder(activity).dismissOnTouchOutside(false)
                    .dismissOnTouchOutside(false).asCustom(new CustomerDialog(activity,routh)).show();
        }
    }
    public void openLearningPlanDialog(Activity activity,String time,int type){
        if (!isDoubleClick()) {
            RxJavaUtils.delay(1, new Consumer<Long>() {
                @Override
                public void accept(Long aLong) throws Exception {
                    LogUtils.e("开启通知了5-----");
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            new PopupManager.Builder(activity).dismissOnTouchOutside(false)
                                    .dismissOnTouchOutside(false).asCustom(new LearningPlanDialog(activity,time,type)).show();
                        }
                    });
                }
            });

        }
    }
    public void openGotoSubject(MessageSuccessPayListener messageSuccessPayListener,Activity activity){
        if (!isDoubleClick()) {
            RxJavaUtils.delay(1, new Consumer<Long>() {
                @Override
                public void accept(Long aLong) throws Exception {
                    LogUtils.e("开启通知了5-----");
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            new PopupManager.Builder(activity)
                                    .dismissOnTouchOutside(false)
                                    .dismissOnTouchOutside(false)
                                    .asCustom(new AqySuccessPayTsDialog(activity,messageSuccessPayListener)).show();
                        }
                    });
                }
            });

        }
    }

//    public synchronized void openPaySuccessDialog(Activity activity, PaySuccessListen paySuccessListen) {
//        if (!isDoubleClick()) {
//            againPayPopu = new PopupManager.Builder(activity)
//                    .dismissOnTouchOutside(false)
//                    .dismissOnBackPressed(false)
//                    .asCustom(new  PaySuccessDialog(activity, paySuccessListen));
//            LogUtils.e("开始调用得======前>>>>" + againPayPopu.isShow() + "***" + againPayPopu.popupStatus.toString());
//            if (againPayPopu.popupStatus == PopupStatus.Showing) return;
//            againPayPopu.show();
//            LogUtils.e("开始调用得==============>中" + againPayPopu.popupStatus.toString());
//            LogUtils.e("开始调用得======后>>>>" + againPayPopu.isShow());
//        } else {
//            LogUtils.e("开始调用得======前>>>>无效");
//        }
//
//    }

    private final int SPACE_TIME = 2 * 1000;//2次点击的间隔时间，单位ms
    private long lastClickTime;

    public synchronized boolean isDoubleClick() {
        long currentTime = System.currentTimeMillis();

        boolean isClick;
        if (currentTime - lastClickTime > SPACE_TIME) {
            isClick = false;

        } else {
            isClick = true;

        }
        lastClickTime = currentTime;
        return isClick;

    }
    public void openAccountLogOffSwithTs(Activity activity,String userToken){
        if (!isDoubleClick()) {
            new PopupManager.Builder(activity).
                    dismissOnTouchOutside(false)
                    .dismissOnBackPressed(false)
                    .asCustom(new AccountLogOffSwitchDialog(activity,userToken)).show();
        }
    }
    public void openAccountLock(Activity activity){
        if (!isDoubleClick()) {
                     new PopupManager.Builder(activity)
                             .dismissOnTouchOutside(false)
                    .dismissOnBackPressed(false)
                    .asCustom(new AccoutLockDialog(activity))
                    .show();
        }
    }
    public void openAccountVipTs(Activity activity, String certificateName, String cityName, String phone, String token, ILoginVipTs iLoginVipTs){
        if (!isDoubleClick()) {
            new PopupManager.Builder(activity)
                    .dismissOnTouchOutside(false)
                    .dismissOnBackPressed(false)
                    .asCustom(new AccountVipTsDialog(activity,certificateName,cityName,phone,token,iLoginVipTs))
                    .show();
        }
    }
    public void openHomeCertificateUpdata(Activity activity){
        if (!isDoubleClick()) {
            new PopupManager.Builder(activity).dismissOnTouchOutside(false)
                    .dismissOnTouchOutside(false).asCustom(new CertificateUpdataDialog(activity)).show();
        }
    }
    public void openSelectSubjectOrCity(Activity activity, List<?> cityPickerData, IHomeSelectCity iHomeSelectCity,int type){
        new PopupManager.Builder(activity).dismissOnTouchOutside(false)
                .dismissOnTouchOutside(false).asCustom(new HomeSelectCityDialog(activity,cityPickerData,iHomeSelectCity,type)).show();
    }
    public void openOtherPayTs(Activity activity,String jsonStr){
        new PopupManager.Builder(activity).dismissOnTouchOutside(false)
                .dismissOnTouchOutside(false).asCustom(new OtherPayTsDialog(activity,jsonStr)).show();
    }
    public void openOffOnileService(Activity activit, String title, String subT, String url, HomeFootListener listener){
        new PopupManager.Builder(activit).dismissOnTouchOutside(false)
                .dismissOnTouchOutside(false).asCustom(new HomeOffOnileServiceDialog(activit,title,subT,url,listener)).show();
    }
    public void openPayFailServer(Activity activity, BaseFragment fragment){
        if(CertificateHelper.getCurrentCertificateDialog()){
            MessageHelper.openBottomListDialog(activity, "您取消了支付，是因为", "", "我再想想", "科目选错了，我要换科目", false, false, new GeneralDialogListener() {
                @Override
                public void onCancel() {
                    PointHelper.joinPointData(PointerMsgType.POINTER_A_PAYFAILURE_WZXXBT, false);
                    openYwView(activity,fragment);
                }

                @Override
                public void onConfim() {
                    PointHelper.joinPointData(PointerMsgType.POINTER_A_PAYFAILURE_XCLBT, false);
                    fragment.initArguments().putBoolean("isSave", false);
                    fragment.startFragment(AddressHelper.mainPage(activity));
                }

                /**
                 *
                 */
                @Override
                public void onDismiss() {

                }
            });
        }else {
            MessageHelper.openGeneralCentDialog(activity, "您取消了支付，是有什么疑惑吗？\n可以主动咨询客服哦", "",
                    "暂时不需要", "联系客服", false, true, new GeneralDialogListener() {
                        @Override
                        public void onCancel() {

                        }
                        @Override
                        public void onConfim() {
//                            fragment.initArguments().putString("routh", "home");
                            fragment.initArguments().putString("url", ConfigHelper.getKfUrl());
                            fragment.initArguments().putInt("payType", 1);
                            fragment.startFragment(CommonHelper.showCommonWeb());
                        }

                        /**
                         *
                         */
                        @Override
                        public void onDismiss() {

                        }
                    });
        }
    }
    private void openYwView(Activity activity,BaseFragment baseFragment) {
        MessageHelper.openGeneralCentDialog(activity,
                "是有什么疑问吗？\n点击“联系客服”详细咨询哟",
                "",
                "暂时不需要",
                "联系客服",
                false,
                false,
                new GeneralDialogListener() {
                    @Override
                    public void onCancel() {
                    }
                    @Override
                    public void onConfim() {
                        baseFragment.initArguments().putString("url", ConfigHelper.getKfUrl());
                        baseFragment.initArguments().putInt("payType", 1);
                        baseFragment.startFragment(CommonHelper.showCommonWeb());
                    }

                    /**
                     *
                     */
                    @Override
                    public void onDismiss() {

                    }
                });
    }
}
