package com.zizhiguanjia.model_message.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class LoginBean implements Parcelable {

    /**
     * UserId : 4622
     * ToKen : q0oPJJ28zYmP4N1uRd1h/AUdy5QT/1TGTRTJKA9ApWsHs3VIj4mvTT3qTU5nAh
     * NickName : 学员2611
     * Headimgurl : 4622.jpeg
     * Sex : 1
     * IsNewUser : false
     * Account : ***********
     * IsVip : false
     * tick
     */

    private int UserId;
    private String ToKen;
    private String NickName;
    private String Headimgurl;
    private int Sex;
    private boolean IsNewUser;
    private String Account;
    private boolean IsVip;
    private String majorid;
    private String AccessToken;
    private int SwitchingCount;
    private int LoginState;

    public int getLoginState() {
        return LoginState;
    }

    public void setLoginState(int loginState) {
        LoginState = loginState;
    }

    public int getSwitchingCount() {
        return SwitchingCount;
    }

    public void setSwitchingCount(int switchingCount) {
        SwitchingCount = switchingCount;
    }

    public boolean isNewUser() {
        return IsNewUser;
    }

    public void setNewUser(boolean newUser) {
        IsNewUser = newUser;
    }

    public boolean isVip() {
        return IsVip;
    }

    public void setVip(boolean vip) {
        IsVip = vip;
    }

    public String getMajorid() {
        return majorid;
    }

    public void setMajorid(String majorid) {
        this.majorid = majorid;
    }

    public String getAccessToken() {
        return AccessToken;
    }

    public void setAccessToken(String accessToken) {
        AccessToken = accessToken;
    }

    public String getMajId() {
        return majorid;
    }

    public void setMajId(String majId) {
        this.majorid = majId;
    }

    public int getUserId() {
        return UserId;
    }

    public void setUserId(int UserId) {
        this.UserId = UserId;
    }

    public String getToKen() {
        return ToKen;
    }

    public void setToKen(String ToKen) {
        this.ToKen = ToKen;
    }

    public String getNickName() {
        return NickName;
    }

    public void setNickName(String NickName) {
        this.NickName = NickName;
    }

    public String getHeadimgurl() {
        return Headimgurl;
    }

    public void setHeadimgurl(String Headimgurl) {
        this.Headimgurl = Headimgurl;
    }

    public int getSex() {
        return Sex;
    }

    public void setSex(int Sex) {
        this.Sex = Sex;
    }

    public boolean isIsNewUser() {
        return IsNewUser;
    }

    public void setIsNewUser(boolean IsNewUser) {
        this.IsNewUser = IsNewUser;
    }

    public String getAccount() {
        return Account;
    }

    public void setAccount(String Account) {
        this.Account = Account;
    }

    public boolean isIsVip() {
        return IsVip;
    }

    public void setIsVip(boolean IsVip) {
        this.IsVip = IsVip;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.UserId);
        dest.writeString(this.ToKen);
        dest.writeString(this.NickName);
        dest.writeString(this.Headimgurl);
        dest.writeInt(this.Sex);
        dest.writeByte(this.IsNewUser ? (byte) 1 : (byte) 0);
        dest.writeString(this.Account);
        dest.writeByte(this.IsVip ? (byte) 1 : (byte) 0);
    }

    public LoginBean() {
    }

    protected LoginBean(Parcel in) {
        this.UserId = in.readInt();
        this.ToKen = in.readString();
        this.NickName = in.readString();
        this.Headimgurl = in.readString();
        this.Sex = in.readInt();
        this.IsNewUser = in.readByte() != 0;
        this.Account = in.readString();
        this.IsVip = in.readByte() != 0;
    }

    public static final Parcelable.Creator<LoginBean> CREATOR = new Parcelable.Creator<LoginBean>() {
        @Override
        public LoginBean createFromParcel(Parcel source) {
            return new LoginBean(source);
        }

        @Override
        public LoginBean[] newArray(int size) {
            return new LoginBean[size];
        }
    };
}
