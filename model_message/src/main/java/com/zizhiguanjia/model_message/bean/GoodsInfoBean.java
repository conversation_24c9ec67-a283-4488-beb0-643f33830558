package com.zizhiguanjia.model_message.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class GoodsInfoBean implements Parcelable {
    private String GoodsName;
    private String Desc;
    private String Info;
    private String Price;
    private boolean Select;
    private String GoodsId;
    private String ShowInfo;
    private String Duration;
    private String TimeOutDate;
    public String getGoodsName() {
        return GoodsName;
    }

    public void setGoodsName(String goodsName) {
        GoodsName = goodsName;
    }

    public String getDesc() {
        return Desc;
    }

    public void setDesc(String desc) {
        Desc = desc;
    }

    public String getInfo() {
        return Info;
    }

    public void setInfo(String info) {
        Info = info;
    }

    public String getPrice() {
        return Price;
    }

    public void setPrice(String price) {
        Price = price;
    }

    public boolean isSelect() {
        return Select;
    }

    public void setSelect(boolean select) {
        Select = select;
    }

    public String getGoodsId() {
        return GoodsId;
    }

    public void setGoodsId(String goodsId) {
        GoodsId = goodsId;
    }

    public String getShowInfo() {
        return ShowInfo;
    }

    public void setShowInfo(String showInfo) {
        ShowInfo = showInfo;
    }

    public String getDuration() {
        return Duration;
    }

    public void setDuration(String duration) {
        Duration = duration;
    }

    public String getTimeOutDate() {
        return TimeOutDate;
    }

    public void setTimeOutDate(String timeOutDate) {
        TimeOutDate = timeOutDate;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.GoodsName);
        dest.writeString(this.Desc);
        dest.writeString(this.Info);
        dest.writeString(this.Price);
        dest.writeByte(this.Select ? (byte) 1 : (byte) 0);
        dest.writeString(this.GoodsId);
        dest.writeString(this.ShowInfo);
        dest.writeString(this.Duration);
        dest.writeString(this.TimeOutDate);
    }

    public void readFromParcel(Parcel source) {
        this.GoodsName = source.readString();
        this.Desc = source.readString();
        this.Info = source.readString();
        this.Price = source.readString();
        this.Select = source.readByte() != 0;
        this.GoodsId = source.readString();
        this.ShowInfo = source.readString();
        this.Duration = source.readString();
        this.TimeOutDate = source.readString();
    }

    public GoodsInfoBean() {
    }

    protected GoodsInfoBean(Parcel in) {
        this.GoodsName = in.readString();
        this.Desc = in.readString();
        this.Info = in.readString();
        this.Price = in.readString();
        this.Select = in.readByte() != 0;
        this.GoodsId = in.readString();
        this.ShowInfo = in.readString();
        this.Duration = in.readString();
        this.TimeOutDate = in.readString();
    }

    public static final Parcelable.Creator<GoodsInfoBean> CREATOR = new Parcelable.Creator<GoodsInfoBean>() {
        @Override
        public GoodsInfoBean createFromParcel(Parcel source) {
            return new GoodsInfoBean(source);
        }

        @Override
        public GoodsInfoBean[] newArray(int size) {
            return new GoodsInfoBean[size];
        }
    };
}
