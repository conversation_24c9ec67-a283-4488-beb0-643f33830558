package com.zizhiguanjia.model_message.viewmodel;

import android.view.Gravity;

import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_network.BaseViewModel;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.msgconfig.CommonMsgTypeConfig;
import com.zizhiguanjia.model_message.api.MessageApi;

import java.util.HashMap;
import java.util.Map;

public class MessageVideModel extends BaseViewModel {
    private MessageApi mApi= Http.getInstance().create(MessageApi.class);

    public void ignoreUpdata(String version){
        Map<String,String> params=new HashMap<>();
        params.put("version",version);
        launchOnlyResult(mApi.ignoreUpdata(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
            }
            @Override
            public void error(String msg) {
            }
        });
    }


    public void OnUserUpdateExam(String ids,boolean isToast) {
        Map<String,String> params=new HashMap<>();
        params.put("tipId",ids);
        launchOnlyResult(mApi.addUserExamTip(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                if(data.Message==null||data.Message.isEmpty())return;
                Bus.post(new MsgEvent(CommonMsgTypeConfig.COMMON_UPDATA_EXAM_SUCCESS));
                if(isToast){
                    ToastUtils.normal(data.Message, Gravity.CENTER);
                }

            }
            @Override
            public void error(String msg) {
                if(msg==null||msg.isEmpty())return;
                if(isToast){
                    ToastUtils.normal(msg, Gravity.CENTER);
                }
            }
        });
    }
}
