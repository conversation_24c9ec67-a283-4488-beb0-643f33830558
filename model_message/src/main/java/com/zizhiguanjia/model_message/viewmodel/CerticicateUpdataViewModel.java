package com.zizhiguanjia.model_message.viewmodel;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.model_message.api.MessageApi;
import com.zizhiguanjia.model_message.bean.CityPickerBean;
import com.zizhiguanjia.model_message.bean.CityPickerData;
import com.zizhiguanjia.model_message.listenter.ICerticicateUpdata;
import com.zizhiguanjia.model_message.navigator.CertificateUpdataNavigator;

import java.util.HashMap;
import java.util.Map;
import java.util.StringTokenizer;

public class CerticicateUpdataViewModel extends CommonViewModel implements ICerticicateUpdata {
    private MessageApi mApi=new Http().create(MessageApi.class);
    private CertificateUpdataNavigator navigator;
    @Override
    public void initParams(CertificateUpdataNavigator certificateUpdataNavigator) {
        this.navigator=certificateUpdataNavigator;
        String des=CertificateHelper.getCertificateDes();
        LogUtils.e("des"+des);
        String[] address=convertStrToArray2(des);
        LogUtils.e("address"+address.toString());
        navigator.initCurrentCertificate(address[0], BaseConfig.cityCode,address[1],BaseConfig.majId);
        getCertificateCityPostData(false);
    }
    public  String[] convertStrToArray2(String str) {
        StringTokenizer st;
        if(str.contains("·")){
            st= new StringTokenizer(str, "·");
        }else {
            st= new StringTokenizer(str, " ");
        }
        String[] strArray = new String[st.countTokens()];
        int i = 0;
        while (st.hasMoreTokens()) {
            strArray[i++] = st.nextToken().trim();
        }
        return strArray;
    }
    @Override
    public void getCertificateCityPostData(boolean open) {
        launchOnlyResult(mApi.getCityList(new HashMap<>()), new OnHandleException<BaseData<CityPickerBean>>() {
            @Override
            public void success(BaseData<CityPickerBean> data) {
                if(data.getResult()==null||data.getResult().getRecords()==null||data.getResult().getRecords().size()==0)
                    return;
                navigator.openSelectCityDialog(data.getResult().getRecords(),open);
            }

            @Override
            public void error(String msg) {

            }
        });
    }
    @Override
    public void getCeitificateByCityIds(String ids,boolean open,boolean resh) {
        Map<String,String> params=new HashMap<>();
        params.put("areaId",ids);
        launchOnlyResult(mApi.getSecondConstruction(params), new OnHandleException<BaseData<CityPickerBean>>() {
            @Override
            public void success(BaseData<CityPickerBean> data) {
                if(data.getResult()==null||data.getResult().getRecords()==null||data.getResult().getRecords().size()==0)
                    return;
                navigator.getCertificateListByCity(data.Data.getRecords(),open,resh);
                if(resh){
                    navigator.fillCertificate(data.Data.getRecords());
                }
            }
            @Override
            public void error(String msg) {

            }
        });
    }

    @Override
    public void binSubject(String code, String ids) {
        Map<String,String> params=new HashMap<>();
        params.put("areaId",code);
        params.put("majorId",ids);
        navigator.showOrHindLoading(true);
        launchOnlyResult(mApi.bindSubject(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                navigator.showErrorToast(data.getMsg());
                navigator.showOrHindLoading(false);
                navigator.bindSuccess();
            }
            @Override
            public void error(String msg) {
                navigator.showErrorToast(msg);
                navigator.showOrHindLoading(false);
            }
        });
    }
}
