package com.zizhiguanjia.model_message.viewmodel;

import android.util.Patterns;

import com.wb.lib_network.BaseViewModel;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_message.api.MessageApi;
import com.zizhiguanjia.model_message.navigator.CouponNavigator;

import java.net.URLDecoder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class CouponViewModel extends BaseViewModel {
    private MessageApi mApi=new Http().create(MessageApi.class);
    private CouponNavigator navigatorn;
    public void initParams(String tips,CouponNavigator couponNavigator){
        this.navigatorn=couponNavigator;
        toDialog(tips);
    }
    private void toDialog(String tips){
        navigatorn.openCouponView(tips);
    }
    public void lingCoupon(String ids){
//        navigatorn.hindLoading();
        navigatorn.lingSuccess();
//        navigatorn.showMsg(data.Message);
//        Map<String,String> params=new HashMap<>();
//        params.put("couponid",ids);
//        navigatorn.showLoading("领取中....");
//        launchOnlyResult(mApi.lingCoupon(params), new OnHandleException<BaseData>() {
//            @Override
//            public void success(BaseData data) {
//
//            }
//
//            @Override
//            public void error(String msg) {
//                navigatorn.hindLoading();
//                navigatorn.showMsg(msg);
//                navigatorn.clearCopy();
//            }
//        });
    }
}
