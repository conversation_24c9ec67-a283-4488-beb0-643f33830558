package com.zizhiguanjia.model_message.adapter;

import android.view.View;

import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_adapter.ItemDelegate;
import com.wb.lib_adapter.MultiItemTypeAdapter;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.bean.GoodsInfoBean;
import com.zizhiguanjia.model_message.databinding.MessageEjbuySelectItemBinding;
import com.zizhiguanjia.model_message.databinding.MessageEjbuySelectNorItemBinding;
import com.zizhiguanjia.model_message.listenter.BuySelectListener;

public class NoPremissEjBuyDetails extends MultiItemTypeAdapter<GoodsInfoBean> {
    public NoPremissEjBuyDetails(BuySelectListener buySelectListener) {
        addItemDelegate(new ItemDelegate<GoodsInfoBean>() {
            private MessageEjbuySelectItemBinding binding;
            @Override
            public int layoutId() {
                return R.layout.message_ejbuy_select_item;
            }

            @Override
            public boolean isThisType(GoodsInfoBean item, int position) {
//                return item.isSelect()?true:false;
                LogUtils.e("----->>>名称2"+item.getGoodsName());
                return item.getGoodsName().contains("打包")?true:false;
            }

            @Override
            public void convert(BaseViewHolder holder, GoodsInfoBean item, int position) {
                binding=holder.getBinding();
                binding.setBean(item);
                binding.selectRel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        buySelectListener.onSelectGoodsInfo(item);
                    }
                });
                if(item.isSelect()){
                    binding.rightSelectImg.setImageResource(R.drawable.message_buy_select);
                }else {
                    binding.rightSelectImg.setImageResource(R.drawable.message_buy_nor);
                }
            }

        });
        addItemDelegate(new ItemDelegate<GoodsInfoBean>() {
            private MessageEjbuySelectNorItemBinding itemBinding;
            @Override
            public int layoutId() {
                return R.layout.message_ejbuy_select_nor_item;
            }

            @Override
            public boolean isThisType(GoodsInfoBean item, int position) {
//                return item.isSelect()?false:true;
                LogUtils.e("----->>>名称1"+item.getGoodsName());
                return item.getGoodsName().contains("打包")?false:true;
            }

            @Override
            public void convert(BaseViewHolder holder, GoodsInfoBean item, int position) {
                itemBinding=holder.getBinding();
                itemBinding.setBean(item);
                if(item.isSelect()){
                    itemBinding.selectIma.setImageResource(R.drawable.message_buy_select);
                }else {
                    itemBinding.selectIma.setImageResource(R.drawable.message_buy_nor);
                }
                itemBinding.selectNorRel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        buySelectListener.onSelectGoodsInfo(item);
                    }
                });
            }
        });
    }

}
