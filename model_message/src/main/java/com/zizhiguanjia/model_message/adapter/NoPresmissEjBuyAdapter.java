package com.zizhiguanjia.model_message.adapter;

import android.graphics.Paint;
import android.text.TextPaint;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.bean.NoPremissBuyBean;
import com.zizhiguanjia.model_message.databinding.MessageNopremissbuyEjItemBinding;

public class NoPresmissEjBuyAdapter extends BaseAdapter<NoPremissBuyBean> {
    private MessageNopremissbuyEjItemBinding messageNopremissEjLayoutBinding;
    public NoPresmissEjBuyAdapter() {
        super(R.layout.message_nopremissbuy_ej_item);
    }

    @Override
    protected void bind(BaseViewHolder holder, NoPremissBuyBean item, int position) {
        messageNopremissEjLayoutBinding=holder.getBinding();
        messageNopremissEjLayoutBinding.setData(item);
        if(item.getResId()==1){
            messageNopremissEjLayoutBinding.messageDesImg.setImageResource(R.drawable.meaasge_wxst);
        }else if(item.getResId()==2){
            messageNopremissEjLayoutBinding.messageDesImg.setImageResource(R.drawable.meaasge_zbkc);
        }else if(item.getResId()==3){
            messageNopremissEjLayoutBinding.messageDesImg.setImageResource(R.drawable.meaasge_qcdx);
        }else if(item.getResId()==4){
            messageNopremissEjLayoutBinding.messageDesImg.setImageResource(R.drawable.meaasge_mnks);
        }else if(item.getResId()==5){
            messageNopremissEjLayoutBinding.messageDesImg.setImageResource(R.drawable.kbgtk);
        }else if(item.getResId()==6){
            messageNopremissEjLayoutBinding.messageDesImg.setImageResource(R.drawable.one);
        }
        TextPaint paint = messageNopremissEjLayoutBinding.titleTv.getPaint();
        paint.setStrokeWidth(1.0f);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);
    }
}
