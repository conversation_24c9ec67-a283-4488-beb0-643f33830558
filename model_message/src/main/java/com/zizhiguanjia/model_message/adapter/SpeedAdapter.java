package com.zizhiguanjia.model_message.adapter;

import android.graphics.Color;
import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.listeners.SpeedListener;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.bean.SpeedBean;
import com.zizhiguanjia.model_message.databinding.MessageSpeedItemBinding;

import java.util.HashMap;
import java.util.Map;

public class SpeedAdapter extends BaseAdapter<SpeedBean> {
    private MessageSpeedItemBinding messageSpeedLayoutBinding;
    private SpeedListener speedListener;
    private String selectIds="";
    public SpeedAdapter(SpeedListener speedListener,String ids) {
        super(R.layout.message_speed_item);
        this.speedListener=speedListener;
        this.selectIds=ids;
    }

    @Override
    protected void bind(BaseViewHolder holder, SpeedBean item, int position) {
        messageSpeedLayoutBinding=holder.getBinding();
        messageSpeedLayoutBinding.setModel(this);
        messageSpeedLayoutBinding.setBean(item);
        if(selectIds==null||selectIds.isEmpty()){
            messageSpeedLayoutBinding.tvTitle.setTextColor(Color.parseColor("#666666"));
        }else {
            if(Integer.parseInt(selectIds)==item.getIds()){
                messageSpeedLayoutBinding.tvTitle.setTextColor(Color.parseColor("#007AFF"));
            }else{
                messageSpeedLayoutBinding.tvTitle.setTextColor(Color.parseColor("#666666"));
            }
        }
        if(position<getData().size()){
            messageSpeedLayoutBinding.lineView.setVisibility(View.VISIBLE);
        }else {
            messageSpeedLayoutBinding.lineView.setVisibility(View.GONE);
        }

    }

    public void onClick(View view, String spedd,int ids){
        if(speedListener==null)return;
        selectIds=String.valueOf(ids);
        Map<String,String> map=new HashMap<>();
        map.put("idsSpeed",ids+"");
        map.put("titleSpeed",spedd+"X");
        KvUtils.save("videoSpeed", GsonUtils.newInstance().GsonToString(map));
        notifyDataSetChanged();
        speedListener.onSpeed(Float.parseFloat(spedd),spedd+"X");
    }
}
