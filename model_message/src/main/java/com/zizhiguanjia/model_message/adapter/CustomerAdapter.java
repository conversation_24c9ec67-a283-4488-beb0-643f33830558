package com.zizhiguanjia.model_message.adapter;

import android.graphics.Color;
import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.databinding.MessageCustomerItemBinding;
import com.zizhiguanjia.model_message.listenter.CustomerScoresListenter;

public class CustomerAdapter extends BaseAdapter<String> {
    private MessageCustomerItemBinding binding;
    private int lasteSelectOption = -1;
    private int curSelectOption = -1;
    private CustomerScoresListenter customerScoresListenter;
    public int getCurSelectOption() {
        return curSelectOption;
    }

    public CustomerAdapter(CustomerScoresListenter customerScoresListenter) {
        super(R.layout.message_customer_item);
        this.customerScoresListenter=customerScoresListenter;
    }

    private void select(int postion) {
        lasteSelectOption = new Integer(curSelectOption);
        curSelectOption = postion;
        if (lasteSelectOption != -1) {
            notifyItemChanged(lasteSelectOption);
        }
        if (curSelectOption != -1) {
            notifyItemChanged(curSelectOption);
        }
    }

    @Override
    protected void bind(BaseViewHolder holder, String item, int position) {
        binding = holder.getBinding();
        binding.setDes(item);
        if (curSelectOption == position) {
            binding.tvDes.setBackgroundResource(R.drawable.messsage_customer_yes_bg);
            binding.tvDes.setTextColor(Color.parseColor("#ffffff"));
        } else {
            binding.tvDes.setTextColor(Color.parseColor("#666666"));
            binding.tvDes.setBackgroundResource(R.drawable.messsage_customer_nor_bg);
        }
        binding.tvDes.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                customerScoresListenter.onSelectScores(position+"");
                select(position);
            }
        });
    }
}
