package com.zizhiguanjia.model_message.ui;

import android.content.ClipboardManager;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.KeyEvent;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.base.ContainerActivity;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.constants.MessageRouterPath;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.model_message.CouponFragment;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.dialog.CoupoinDialog;
import com.zizhiguanjia.model_message.listenter.DialogCommonListener;
import com.zizhiguanjia.model_message.navigator.CouponNavigator;
import com.zizhiguanjia.model_message.viewmodel.CouponViewModel;

@Route(path = MessageRouterPath.MAIN_COUPION_ACTIVITY)
public class CouponActivity extends ContainerActivity  {
    @Autowired(name = "tips")
    public String tips;

    @Override
    public Fragment initBaseFragment() {
        ARouter.getInstance().inject(this);
        return CouponFragment.newInstance(tips);
    }
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            return true;
        } else {
            return super.dispatchKeyEvent(event);
        }
    }
    @Override
    public boolean isMain() {
        return true;
    }

}
