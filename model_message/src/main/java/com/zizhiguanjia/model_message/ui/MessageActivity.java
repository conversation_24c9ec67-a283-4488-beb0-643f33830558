package com.zizhiguanjia.model_message.ui;

import android.os.Bundle;
import android.widget.Button;

import androidx.annotation.Nullable;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.zizhiguanjia.lib_base.base.ContainerActivity;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.constants.MessageRouterPath;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.listeners.IUpdateExamListenter;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.dialog.UpdateExamDiglog;
import com.zizhiguanjia.model_message.viewmodel.MessageVideModel;

@Route(path = MessageRouterPath.MAIN_ACTIVITY)
public class MessageActivity extends ContainerActivity {
    private Button messageBt;
    @Autowired(name = "title")
    public String title;
    @Autowired(name = "subTitle")
    public String subTitle;
    @Autowired(name = "contentTitle")
    public String contentTitle;
    @Autowired(name = "Content")
    public String Content;
    @Autowired(name = "subContent")
    public String subContent;
    @Autowired(name = "ids")
    public String ids;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ARouter.getInstance().inject(this);
        setContentView(R.layout.message_test);
        openDia();
    }
    private void openDia(){
        UpdateExamDiglog popupView = new
                UpdateExamDiglog(getContext(), title, subTitle, contentTitle, Content, subContent, ids, new IUpdateExamListenter() {
            @Override
            public void OnUserUpdateExam(String ids,int type) {
                finish();
                MessageVideModel messageVideModel=new MessageVideModel();
                messageVideModel.OnUserUpdateExam(ids,type==1?true:false);
//                MainHelper.queryCouponInfo();
                MessageHelper.nextDialogTask();
            }
        });
        BasePopupView againPayPopu = new PopupManager
                .Builder(getContext()).dismissOnTouchOutside(false)
                .hasStatusBar(false).hasNavigationBar(false).
                        dismissOnBackPressed(false).asCustom(popupView);
        againPayPopu.show();
    }
    @Override
    public boolean isMain() {
        return true;
    }
}
