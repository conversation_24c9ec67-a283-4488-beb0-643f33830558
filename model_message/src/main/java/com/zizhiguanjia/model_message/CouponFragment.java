package com.zizhiguanjia.model_message;

import android.content.ClipboardManager;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;

import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.model_message.dialog.CoupoinDialog;
import com.zizhiguanjia.model_message.listenter.DialogCommonListener;
import com.zizhiguanjia.model_message.navigator.CouponNavigator;
import com.zizhiguanjia.model_message.viewmodel.CouponViewModel;

public class CouponFragment extends BaseFragment implements DialogCommonListener, CouponNavigator {
    @BindViewModel
    CouponViewModel model;
    private LoadingPopupView loadingPopupView;
    private String tips;
    public static CouponFragment newInstance(String tips){
        CouponFragment couponFragment=new CouponFragment();
        Bundle args = new Bundle();
        args.putString("tips", tips);
        couponFragment.setArguments(args);
        return couponFragment;
    }
    @Override
    public int initLayoutResId() {
        return R.layout.message_test;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        loadingPopupView=new PopupManager.Builder(getActivity()).asLoading();
        tips=getArguments().getString("tips","");
        LogUtils.e("tips----->>>"+tips);
        model.initParams(tips,this);
    }
    @Override
    public void onCommonBack() {
        MessageHelper.nextDialogTask();
        finish();
    }

    @Override
    public void onLing(String ids) {
        model.lingCoupon(ids);
    }

    @Override
    public void showLoading(String msg) {
        loadingPopupView.setTitle(msg);
        if(loadingPopupView.isShow())return;
        loadingPopupView.show();
    }

    @Override
    public void hindLoading() {
        if(loadingPopupView==null)return;;
        if(loadingPopupView.isShow()) loadingPopupView.dismiss();

    }

    @Override
    public void showMsg(String msg) {
        if(msg==null||msg.isEmpty())return;
        ToastUtils.normal(msg, Gravity.CENTER);
    }

    @Override
    public void lingSuccess() {
        clearCopy();
        initArguments().putString("routh", "home");
        initArguments().putString("url", ConfigHelper.getPayUrl());
        initArguments().putInt("payType", 1);
        initArguments().putString("payRouthParams", PayRouthConfig.PAY_COUPON_PAY);
        startFragment(CommonHelper.showCommonWeb());
    }

    @Override
    public void openCouponView(String ids) {
        LogUtils.e("看看对话框----99999->>>>"+BaseConfig.showCouponState);
        CoupoinDialog popupView = new CoupoinDialog(getContext(),this,ids);
        BasePopupView againPayPopu = new PopupManager
                .Builder(getContext())
                .dismissOnTouchOutside(false)
                .hasStatusBar(false)
                .hasNavigationBar(false)
                .dismissOnBackPressed(false)
                .asCustom(popupView);
        againPayPopu.show();
    }

    @Override
    public void clearCopy() {
        MessageHelper.nextDialogTask();
        BaseConfig.showCouponState=false;
        BaseConfig.couponid="";
        ClipboardManager manager = (ClipboardManager) AppUtils.getApp().getSystemService(Context.CLIPBOARD_SERVICE);
        if (manager != null) {
            try {
                manager.setPrimaryClip(manager.getPrimaryClip());
                manager.setText(null);
            } catch (Exception e) {
            }
        }
        finish();
    }

    @Override
    public void onDestroy() {
        MessageHelper.restCoupionState();
        super.onDestroy();
    }
}
