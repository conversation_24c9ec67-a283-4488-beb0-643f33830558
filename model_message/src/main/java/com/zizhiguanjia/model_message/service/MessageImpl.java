package com.zizhiguanjia.model_message.service;

import android.app.Activity;
import android.content.Context;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_arch.utils.AppManager;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.xuexiang.xupdate.entity.UpdateEntity;
import com.xuexiang.xupdate.proxy.IUpdateProxy;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.bean.OrderDialogBean;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.constants.MessageRouterPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.PayHelper;
import com.zizhiguanjia.lib_base.listeners.ExamGuilderListener;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.listeners.HomeFootListener;
import com.zizhiguanjia.lib_base.listeners.ILoginVipTs;
import com.zizhiguanjia.lib_base.listeners.MessageSuccessPayListener;
import com.zizhiguanjia.lib_base.listeners.OnChoiceTimeListenter;
import com.zizhiguanjia.lib_base.listeners.PayInfoListener;
import com.zizhiguanjia.lib_base.listeners.SpeedListener;
import com.zizhiguanjia.lib_base.service.MessageService;
import com.zizhiguanjia.model_message.dialog.NoPremissBuyDialog;
import com.zizhiguanjia.model_message.manager.DialogManager;
import com.zizhiguanjia.model_message.manager.MessageManager;

@Route(path = MessageRouterPath.SERVICE)
public class MessageImpl implements MessageService {
    @Override
    public void start(Context context) {
        ARouterUtils.navActivity(MessageRouterPath.MAIN_ACTIVITY);
    }

    @Override
    public IFragment mainPage(Context context) {
        return null;
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {
    }

    @Override
    public void openUpdateExam(String title, String subTitle, String contentTitle, String Content, String subContent, String ids) {
        LogUtils.e("看看路由传参---11111--"+ids);
        ARouter.getInstance().build(MessageRouterPath.MAIN_ACTIVITY)
                .withString("title",title)
                .withString("subTitle",subTitle)
                .withString("contentTitle",contentTitle)
                .withString("Content",Content)
                .withString("subContent",subContent)
                .withString("ids",ids).navigation();
    }

//    @Override
//    public void openPaySuccessDialog(Activity activity, PaySuccessListen paySuccessListen) {
//        initPaySuccessDialog(activity);
//      MessageManager.getInstance().openPaySuccessDialog(activity,paySuccessListen);
//    }

    @Override
    public void initPaySuccessDialog(Activity activity) {
//        MessageManager.getInstance().initPa/**/ySuccessDialog(activity);
    }
    private LoadingPopupView loadingPopupView;
    @Override
    public void openNoPremissBuyDialog(Activity activity,boolean ised,String payRouthParams) {
        loadingPopupView=new PopupManager.Builder(activity).asLoading("请稍等....");
        if(!loadingPopupView.isShow()){
            loadingPopupView.show();
        }
        PayHelper.getPayInfoByMajId(BaseConfig.majId, new PayInfoListener() {
            @Override
            public void onPayInfo(boolean state, String price, String times, String GoodsId,String json,int goodType, int majorType) {
                LogUtils.e("-----"+state+price+times+GoodsId);
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        if(loadingPopupView.isShow())loadingPopupView.dismiss();
                        if(state){
                            if(StringUtils.isEmpty(GoodsId)){
//                                MessageHelper.openOtherPayTs(activity);
                                MessageManager.getInstance().openOtherPayTs(activity,json);
                            }else {
//                                if(CertificateHelper.getCurrentCertificateDialog()){
                                    new PopupManager.Builder(activity)
                                            .maxHeight(DpUtils.dp2px(activity,436/825))
                                            .asCustom(new NoPremissBuyDialog(activity,activity,ised, payRouthParams,price,times,GoodsId,goodType,majorType))
                                            .show();
//                                }else {
//                                    new PopupManager.Builder(activity)
//                                            .maxHeight(DpUtils.dp2px(activity,436/825))
//                                            .asCustom(new NopreMissBuyEjDialog(activity,activity,ised, payRouthParams,price,times,GoodsId,json,goodType))
//                                            .show();
//                                }
                            }
                        }else {
                            ToastUtils.normal(price);
                        }
                    }
                });
            }
        });
    }

    @Override
    public void openSpeedDialog(Context activity, View attachView, SpeedListener speedListener) {
        MessageManager.getInstance().openSpeedDialog(activity,attachView,speedListener);
    }

    @Override
    public void openSpeedFullDialog(Context activity,View attachView, SpeedListener speedListener ) {
        MessageManager.getInstance().openSpeedFullDialog(activity,attachView,speedListener);
    }

    @Override
    public void startCoupion(String url) {
        if(!AccountHelper.isUserLogin())return;
        AppManager instance = AppManager.getInstance();
        if(instance.currentFragment().getClass().toString().contains("CouponFragment")){
            LogUtils.e("看看对话框----00001->>>>");
        }else {
            LogUtils.e("看看对话框----00000->>>>");
            ARouter.getInstance().build(MessageRouterPath.MAIN_COUPION_ACTIVITY)
                    .withString("tips",url)
                    .navigation();
        }
    }

    @Override
    public void openGeneralCentDialog(Activity activity, String title, String msg, String cancel, String confim, boolean isHideCancel, boolean isBack, GeneralDialogListener generalDialogListener) {
        MessageManager.getInstance().openGeneralCentDialog(activity,title,msg,cancel,confim,isHideCancel,isBack,generalDialogListener,DpUtils.dp2px(activity, 290),DpUtils.dp2px(activity, 185));
    }

    @Override
    public void openGeneralCentDialog(Activity activity, String title, String msg, String cancel, String confim, boolean isHideCancel, boolean isBack, GeneralDialogListener generalDialogListener, int maxW, int maxH) {
        MessageManager.getInstance().openGeneralCentDialog(activity,title,msg,cancel,confim,isHideCancel,isBack,generalDialogListener,maxW,maxH);
    }

    @Override
    public void openBottomListDialog(Activity activity, String title, String msg, String cancel, String confirm, boolean isHind, boolean isBack, GeneralDialogListener generalDialogListener) {
        MessageManager.getInstance().openBottomListDialog(activity, title, msg, cancel, confirm, isHind, isBack, generalDialogListener,270,182);
    }

    @Override
    public void openBottomListDialog(Activity activity, String title, String msg, String cancel, String confirm, boolean isHind, boolean isBack, GeneralDialogListener generalDialogListener, int maxW, int maxH) {
        MessageManager.getInstance().openBottomListDialog(activity, title, msg, cancel, confirm, isHind, isBack, generalDialogListener,maxW,maxH);
    }

    @Override
    public void permissionsTips(Activity activity, String title, String msg, String confim) {
        MessageManager.getInstance().permissionsTips(activity, title, msg, confim);
    }

    @Override
    public BasePopupView openAccountPrivacyTipic(Activity activity, View mView) {
        return MessageManager.getInstance().openAccountPrivacyTipic(activity,mView);
    }

    @Override
    public void openServiceTips(Activity activity) {
        MessageManager.getInstance().openServiceTips(activity);
    }

    @Override
    public void openAppUpdataVersionTips(Activity activity, IUpdateProxy updateProxy, UpdateEntity updateEntity) {
        MessageManager.getInstance().openAppUpdataVersionTips(activity, updateProxy, updateEntity);
    }

    @Override
    public void openGuilder(Activity activity, int type, ExamGuilderListener examGuilderListener) {
        MessageManager.getInstance().openGuilder(activity, type, examGuilderListener);
    }

    @Override
    public void orderOpenDialog(OrderDialogBean orderDialogBean) {
        DialogManager.getInstance().pushToQueue(orderDialogBean);
    }

    @Override
    public void nextDialogTask() {
        DialogManager.getInstance().nextTask();
    }

    @Override
    public void openTimeChoiceDialog(Activity activity, OnChoiceTimeListenter OnChoiceTimeListenter) {
        MessageManager.getInstance().openTimeChoiceDialog(activity,OnChoiceTimeListenter);
    }

    @Override
    public void restCoupionState() {
        DialogManager.getInstance().restPageState();
    }

    @Override
    public void openCusomerDialog(Activity activity,String routh) {
        MessageManager.getInstance().openCusomerDialog(activity,routh);
    }

    @Override
    public void openLearningPlanDialog(Activity activity,String time,int type) {
        MessageManager.getInstance().openLearningPlanDialog(activity,time,type);
    }

    @Override
    public void openGotoSubjectSelect(MessageSuccessPayListener messageSuccessPayListener,Activity activity) {
        MessageManager.getInstance().openGotoSubject(messageSuccessPayListener,activity);
    }

    @Override
    public void openAccountLogOffSwithTs(Activity activity,String userToken) {
        MessageManager.getInstance().openAccountLogOffSwithTs(activity,userToken);
    }

    @Override
    public void openAccountLock(Activity activity) {
        MessageManager.getInstance().openAccountLock(activity);
    }

    @Override
    public void openAccountVipTs(Activity activity, String certificateName, String cityName, String phone, String token, ILoginVipTs iLoginVipTs) {
        MessageManager.getInstance().openAccountVipTs(activity,certificateName,cityName,phone,token,iLoginVipTs);
    }

    @Override
    public void openHomeCertificateUpdata(Activity activity) {
        MessageManager.getInstance().openHomeCertificateUpdata(activity);
    }

    @Override
    public void openSelectSubjectOrCity(Activity activity) {
//        MessageManager.getInstance().openSelectSubjectOrCity(activity);
    }

    @Override
    public void openOtherPayTs(Activity activity,String json) {
        MessageManager.getInstance().openOtherPayTs(activity,json);
    }

    @Override
    public void openOffOnileService(Activity activity, String title, String subT, String url, HomeFootListener listener) {
        MessageManager.getInstance().openOffOnileService(activity,title,subT,url,listener);
    }

    @Override
    public void openPayFailServer(Activity activity, BaseFragment baseFragment) {
        MessageManager.getInstance().openPayFailServer(activity,baseFragment);
    }
}
