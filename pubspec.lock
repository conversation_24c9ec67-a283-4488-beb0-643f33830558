# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.8.2"
  auto_size_text:
    dependency: "direct main"
    description:
      name: auto_size_text
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.1"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.15.0"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.5"
  dio:
    dependency: "direct main"
    description:
      name: dio
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.3.3"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_page_indicator:
    dependency: transitive
    description:
      name: flutter_page_indicator
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.0.3"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.7.0"
  flutter_swiper:
    dependency: "direct main"
    description:
      name: flutter_swiper
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.6"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  get:
    dependency: "direct main"
    description:
      name: get
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.6.5"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.2"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.12.11"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.7.0"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.8.0"
  pull_to_refresh:
    dependency: "direct main"
    description:
      name: pull_to_refresh
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sleek_circular_slider:
    dependency: "direct main"
    description:
      name: sleek_circular_slider
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.8.1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.10.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.3"
  text_scroll:
    dependency: "direct main"
    description:
      name: text_scroll
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.1.2"
  transformer_page_view:
    dependency: transitive
    description:
      name: transformer_page_view
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.1.6"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.0"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
sdks:
  dart: ">=2.15.0 <3.0.0"
  flutter: ">=2.0.0"
