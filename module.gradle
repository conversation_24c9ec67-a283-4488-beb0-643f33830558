import java.util.regex.Pattern
//先加载local.properties文件
Properties localProperties = new Properties()
//加载签名文件
def keystorePropertiesFile = rootProject.file("signing.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
try {
    def localFile = project.rootProject.file('local.properties')
    if (localFile != null && localFile.exists()) {
        localProperties.load(localFile.newDataInputStream())
    }
} catch (Exception ignored) {
    println("local.properties not found")
}
//读取build.gradle中的设置
def runAsApp = ext.has('runAsApp')
//是否library
def isLibrary = ext.has('isLibrary')
ext.isLibrary = isLibrary
if (runAsApp) {
    runAsApp = ext.runAsApp
} else if (ext.has('mainApp') && ext.mainApp) { //ext.mainApp为true时，代表以app方式运行
    runAsApp = true
} else {
    //build.gradle中没有配置runAsApp，且ext.mainApp=false或未配置
    //再从local.properties中读取配置，例如： demo_component_a=true
    //注：如果采用local.properties读取配置，每次修改需要重新同步(Sync Project)一下
    runAsApp = 'true' == localProperties.getProperty(project.name)
}
//设置到ext中，供module的build.gradle使用（例如用于设置sourceSets配置）
ext.runAsApp = runAsApp
if (runAsApp) {
    apply plugin: 'com.android.application'
} else {
    apply plugin: 'com.android.library'
}

//对组件库的依赖格式： addComponent dependencyName [, realDependency]
// 使用示例见demo/build.gradle
//  dependencyName: 组件库的名称，推荐直接使用使用module的名称
//  realDependency(可选): 组件库对应的实际依赖，可以是module依赖，也可以是maven依赖
//    如果未配置realDependency，将自动依赖 project(":$dependencyName")
//    realDependency可以为如下2种中的一种:
//      module依赖 : project(':demo_component_b') //如果module名称跟dependencyName相同，可省略(推荐)
//      maven依赖  : 'com.billy.demo:demoB:1.1.0' //如果使用了maven私服，请使用此方式

ext.addComponent = { dependencyName, realDependency = null ->
    def curModuleIsBuildingApk = false //当前task是否为给本module打apk包
    def taskNames = project.gradle.startParameter.taskNames
    def regex = "((.*:)?${project.name.toUpperCase()}:)?((ASSEMBLE)|(INSTALL)|((BUILD)?TINKER)|(RESGUARD)).*"
    def taskBuildApkPattern = Pattern.compile(regex)
    for (String task : taskNames) {
        if (taskBuildApkPattern.matcher(task.toUpperCase()).matches()) {
            curModuleIsBuildingApk = true
            break
        }
    }
    //不是在为本app module打apk包，不添加对组件的依赖
    if (!curModuleIsBuildingApk)
        return
    def componentProject = rootProject.subprojects.find { it.name == dependencyName }
    def app  //dependencyName指定的module是否为配置为以application方式编译
    if (componentProject && componentProject.ext.has('runAsApp')) {
        //兼容以前的ext.runAsApp=true的配置方式，runAsApp的优先级高
        app = componentProject.ext.runAsApp
    } else if (componentProject && componentProject.ext.has('mainApp') && componentProject.ext.mainApp) {
        //仅ext.mainApp为true时，确定为application方式编译，若为false，则读取local.properties中的配置
        app = true
    } else {
        //local.properties中配置为true代表该module以application方式编译
        app = 'true' == localProperties.getProperty(dependencyName)
    }
    if (!app) {
        def dependencyMode = 'api'
        if (realDependency) {
            //通过参数传递的依赖方式，如：
            // project(':moduleName')
            // 或
            // 'com.billy.demo:demoA:1.1.0'
            project.dependencies.add(dependencyMode, realDependency)
            println "CC >>>> add $realDependency to ${project.name}'s dependencies"
        } else if (componentProject) {
            //第二个参数未传，默认为按照module来进行依赖
            project.dependencies.add(dependencyMode, project(":$dependencyName"))
            println "CC >>>> add project(\":$dependencyName\") to ${project.name}'s dependencies"
        } else {
            throw new RuntimeException(
                    "CC >>>> add dependency by [ addComponent '$dependencyName' ] occurred an error:" +
                            "\n'$dependencyName' is not a module in current project" +
                            " and the 2nd param is not specified for realDependency" +
                            "\nPlease make sure the module name is '$dependencyName'" +
                            "\nelse" +
                            "\nyou can specify the real dependency via add the 2nd param, for example: " +
                            "addComponent '$dependencyName', 'com.billy.demo:demoB:1.1.0'")
        }
    }
}
//默认配置了AndroidManifest.xml在library模式和application模式下的文件路径
android {
    ndkVersion "22.0.7026061"
    //版本配置
    compileSdkVersion build_version.compileSdkVersion
    defaultConfig {
        minSdkVersion build_version.minSdkVersion
        targetSdkVersion build_version.targetSdkVersion
        versionCode build_version.versionCode
        versionName build_version.versionName
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
        //阿里路由框架配置
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }
        ndk {
            abiFilters 'armeabi', 'armeabi-v7a', 'arm64-v8a'
        }
    }

    //签名
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
        debug {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        debug {
            // port
            resValue("string", "PORT_NUMBER", "18082")
            //混淆
            minifyEnabled false
            //是否清理无用资源
            shrinkResources false
            //是否启用zipAlign压缩
            zipAlignEnabled false
            signingConfig signingConfigs.release
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            //混淆
            minifyEnabled false
            //是否清理无用资源
            shrinkResources false
            //是否启用zipAlign压缩
            zipAlignEnabled false
            signingConfig signingConfigs.release
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
            //默认的作为application运行时Manifest文件路径
            def debugManifest = 'src/main/debug/AndroidManifest.xml'
            if (runAsApp && project.file(debugManifest).exists()) {
                manifest.srcFile debugManifest
            } else {
                manifest.srcFile 'src/main/AndroidManifest.xml'
                //集成开发模式下自动排除debug文件夹中的所有Java文件
                // 可以将debug代码放在这个包内，例如：Application子类
                java {
                    exclude 'debug/**'
                }
            }
            // 注：2018-03-12推荐：将组件单独以app运行时的测试代码及资源放到src/main/debug/目录下
            if (runAsApp) {
                //debug模式下，如果存在src/main/debug/assets，则自动将其添加到assets源码目录
                if (project.file('src/main/debug/assets').exists()) {
                    assets.srcDirs = ['src/main/assets', 'src/main/debug/assets']
                }
                //debug模式下，如果存在src/main/debug/java，则自动将其添加到java源码目录
                if (project.file('src/main/debug/java').exists()) {
                    java.srcDirs = ['src/main/java', 'src/main/debug/java']
                }
                //debug模式下，如果存在src/main/debug/res，则自动将其添加到资源目录
                if (project.file('src/main/debug/res').exists()) {
                    res.srcDirs = ['src/main/res', 'src/main/debug/res']
                }
            }

        }
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    //防止编译的时候oom、GC
    dexOptions {
        preDexLibraries false
        maxProcessCount 8
        //分配给gradle的内存
        javaMaxHeapSize "4g"
    }

    lintOptions {
        // true--关闭lint报告的分析进度
//        quiet false
        //  true--所有正式版构建执行规则生成崩溃的lint检查，如果有崩溃问题将停止构建
        checkReleaseBuilds false
        // true--错误发生后停止gradle构建
        abortOnError false
    }

    aaptOptions {
        //停用 PNG 处理
        cruncherEnabled false
    }

    // 4.0
    buildFeatures {
        dataBinding true
        viewBinding true
    }

}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    if (runAsApp) {
        androidTestImplementation('com.android.support.test.espresso:espresso-core:3.0.2', {
            exclude group: 'com.android.support', module: 'support-annotations'
        })
        testImplementation libs.junit
        androidTestImplementation libs.test_runner
        androidTestImplementation libs.test_espresso
    }

    if (!isLibrary) {
        implementation project(':lib_base')
        //组件中依赖阿里路由编译框架
        annotationProcessor libs.arouter_compiler
        annotationProcessor libs.arch_comp_room_compiler
    }

    // Android调试数据库 https://github.com/amitshekhariitbhu/Android-Debug-Database
//    debugApi 'com.amitshekhar.android:debug-db:1.0.6'

    //2018-06-03新增：这里是为了示例添加全局拦截器
    if (project.ext.has('runAsApp') && project.ext.runAsApp) {
        //说明：需要兼容的情况有3种(单独组件作为app运行、打包在主app内、组件在多个app上复用但全局拦截器不同)
        //     为了兼容以上3种情况，建议将全局拦截器作为一个单独的module，在此处给不同app添加不同拦截器module
//        implementation project(':demo_interceptors')
    }

    configurations.all {
        resolutionStrategy.eachDependency { DependencyResolveDetails details ->
            def requested = details.requested
            if (requested.group == 'com.android.support') {
                if (!requested.name.startsWith("multidex")) {
                    details.useVersion versions.support
                }
            }
        }
    }
}