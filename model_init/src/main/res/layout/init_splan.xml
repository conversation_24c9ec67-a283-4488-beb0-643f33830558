<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <import type="android.view.View"/>
        <variable
            name="model"
            type="com.zizhiguanjia.model_init.viewmodel.InitViewModel" />
    </data>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tvTgTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="36dp"
            android:layout_marginRight="12.5dp"
            android:background="@drawable/init_splan_go_bg"
            android:onClick="@{model::onClick}"
            android:paddingLeft="12dp"
            android:paddingTop="5dp"
            android:paddingRight="12dp"
            android:paddingBottom="5dp"
            android:text="@{model.startTimeDes}"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:visibility="gone" />
    </RelativeLayout>
</layout>