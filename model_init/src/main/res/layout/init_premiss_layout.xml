<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">
        <RelativeLayout
            android:paddingBottom="25dp"
            android:background="@drawable/init_premiss_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
             <TextView
                 android:id="@+id/tvTopTitle"
                 android:gravity="center"
                 android:paddingBottom="16dp"
                 android:paddingTop="15dp"
                 android:text="温馨提示"
                 android:textColor="@color/white"
                 android:textSize="18sp"
                 android:background="@drawable/init_premiss_title_bg"
                 android:layout_width="match_parent"
                 android:layout_height="wrap_content"/>

                <ScrollView
                    android:scrollbars="none"
                    android:layout_below="@+id/tvTopTitle"
                    android:layout_above="@+id/llBottom"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">
                        <TextView
                            android:lineSpacingExtra="15dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginLeft="19dp"
                            android:layout_marginRight="19dp"
                            android:layout_marginBottom="24dp"
                            android:textSize="14sp"
                            android:id="@+id/tvDes"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"/>
                    </LinearLayout>
                </ScrollView>
                <LinearLayout
                    android:layout_marginTop="16dp"
                    android:id="@+id/llBottom"
                    android:layout_alignParentBottom="true"
                    android:gravity="center"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                        <TextView
                            android:id="@+id/tvcancel"
                            android:layout_marginRight="30dp"
                            android:textColor="#656565"
                            android:paddingTop="10dp"
                            android:paddingBottom="9dp"
                            android:paddingLeft="44dp"
                            android:paddingRight="44dp"
                            android:background="@drawable/init_premiss_cancel_bg"
                            android:textSize="16sp"
                            android:text="拒绝"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:id="@+id/tvConfim"
                            android:textColor="@color/white"
                            android:paddingTop="10dp"
                            android:paddingBottom="9dp"
                            android:paddingLeft="44dp"
                            android:paddingRight="44dp"
                            android:background="@drawable/init_premiss_confim_bg"
                            android:textSize="16sp"
                            android:text="同意"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                </LinearLayout>
        </RelativeLayout>
</LinearLayout>