package com.zizhiguanjia.model_init.repository;

import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;
import androidx.annotation.NonNull;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CarseHelper;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.lib_base.utils.SpannableStringUtils;
import com.zizhiguanjia.model_init.listener.ICommon;
import com.zizhiguanjia.model_init.listener.IInit;

import java.lang.ref.WeakReference;

public class InitRepository implements IInit {
    private WeakReference<ICommon> commonWeakReference;

    @Override
    public void init(ICommon iCommon) {
        if (iCommon != null) {
            commonWeakReference = new WeakReference<>(iCommon);
        }
        initConfig();
    }

    @Override
    public void initUserPremissDes() {
        String des1 = "亲爱的用户，感谢您信任并使用安全员考试宝典\n我们依据相关法律制定了";
        String yhxyDes = "《用户协议》";
        String ysxyDes = "《隐私协议》";
        String des2 = "，请您在点击同意之前仔细阅读并充分理解相关条款，其中的重点条款已为您标注，方便您了解自己的权利。";
        String des3 = "\n" +
                "1.为了您更好的享受刷题及安全员考试宝典的服务，我们会根据您的授权内容，收集和使用对应的必要信息（例如您的联系电话等）。\n" +
                "2.您可以对上述信息进行访问、更正、删除以及注销账户，我们也将提供专门的个人信息保护联系方式。\n" +
                "3.未经您的授权同意，我们不会将上述信息共享给第三方或用于您未授权的其他用途。";
        String des4 = "\n安全员考试宝典一如既往为您服务！";
        String des5 = "\n如您点击\"同意\"，即表示您已仔细阅读并同意本";
        SpannableStringBuilder textFrontColorSp = SpannableStringUtils.getBuilder(des1).setForegroundColor(Color.parseColor("#000000"))
                .append(yhxyDes).setForegroundColor(Color.parseColor("#0079FF"))
                .setClickSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        getListener().onClickYhxy();
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        ds.setUnderlineText(false);
                    }
                })
                .append("和").setForegroundColor(Color.parseColor("#000000")).setBold()
                .append(ysxyDes).setForegroundColor(Color.parseColor("#0079FF")).setClickSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        getListener().onClickYsxy();
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        ds.setUnderlineText(false);
                    }
                }).append(des2).setForegroundColor(Color.parseColor("#000000"))
                .append("\n\n我们将通过").setForegroundColor(Color.parseColor("#000000")).setBold()
                .append(ysxyDes).setForegroundColor(Color.parseColor("#0079FF")).setClickSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        getListener().onClickYsxy();
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        ds.setUnderlineText(false);
                    }
                })
                .append("向您说明：").setForegroundColor(Color.parseColor("#000000")).setBold()
                .append(des3).setForegroundColor(Color.parseColor("#000000")).setBold()
                .append(des4).setForegroundColor(Color.parseColor("#000000")).setBold()
                .append(des5).setForegroundColor(Color.parseColor("#000000")).setBold()
                .append(yhxyDes).setForegroundColor(Color.parseColor("#0079FF")).setClickSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        getListener().onClickYhxy();
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        ds.setUnderlineText(false);
                    }
                })
                .append("及").setForegroundColor(Color.parseColor("#000000")).setBold()
                .append(ysxyDes).setForegroundColor(Color.parseColor("#0079FF")).setClickSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        getListener().onClickYsxy();
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        ds.setUnderlineText(false);
                    }
                }).append("！").setForegroundColor(Color.parseColor("#000000")).setBold()
                .create();
        getListener().initUserPremissDes(textFrontColorSp);
    }

    @Override
    public ICommon getListener() {
        return commonWeakReference.get();
    }

    @Override
    public void initConfig() {
        if (DataHelper.checkUserPremiss()) {
            getListener().userConfim();
        } else {
            initUserPremissDes();
        }
    }

    @Override
    public void initAllSdk() {
        AccountHelper.initAccountInfo();
        CarseHelper.initCarse();
        DataHelper.setUserPremiss(true);
        SdkHelper.initJpushConfig(BaseConfig.deBug);
        SdkHelper.preOnekeyLogin();
        ConfigHelper.initConfig();
        getListener().initAllSdk();
    }

}
