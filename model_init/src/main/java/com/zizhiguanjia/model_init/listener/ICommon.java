package com.zizhiguanjia.model_init.listener;

import android.text.SpannableStringBuilder;
import android.view.View;

import com.zizhiguanjia.model_init.fragment.InitFragment;
import com.zizhiguanjia.model_init.navigator.InitNavigator;

public interface ICommon {
    void initParams(InitNavigator navigator, InitFragment initFragment);
    void initUserPremissDes(SpannableStringBuilder spannableStringBuilder);
    void userConfim();
    void onClick(View view);
    void onClickYhxy();
    void onClickYsxy();
    void initAllSdk();

}
