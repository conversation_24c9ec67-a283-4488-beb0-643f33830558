package com.zizhiguanjia.model_init.test;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_init.R;

public class TestFragment extends BaseFragment {
    TextView textView;
    @Override
    public int initLayoutResId() {
        return R.layout.init_test;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        LogUtils.e("重新进来了");
        textView=this.findViewById(R.id.testWebView);
        textView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                LogUtils.e("点击启动------");
//                String url="ykstbd://kst.com/index";
                String url="weixin://";
                Intent intent=new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                startActivity(intent);
            }
        });
    }

    @Override
    public void initViewData() {

    }

    @Override
    public void initObservable() {

    }
}
