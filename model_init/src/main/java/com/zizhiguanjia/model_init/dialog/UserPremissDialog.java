package com.zizhiguanjia.model_init.dialog;

import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.wb.lib_pop.code.CenterPopupView;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.model_init.R;
import com.zizhiguanjia.model_init.listener.UserPremissListener;

public class UserPremissDialog extends CenterPopupView implements View.OnClickListener {
    private SpannableStringBuilder spannableStringBuilder;
    private TextView desTv,confimTv,cancelTv;
    private UserPremissListener userPremissListener;
    public UserPremissDialog(@NonNull Context context,SpannableStringBuilder spannableStringBuilder,UserPremissListener userPremissListener) {
        super(context);
        this.spannableStringBuilder=spannableStringBuilder;
        this.userPremissListener=userPremissListener;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return R.layout.init_premiss_layout;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        desTv=this.findViewById(R.id.tvDes);
        cancelTv=this.findViewById(R.id.tvcancel);
        confimTv=this.findViewById(R.id.tvConfim);
        desTv.setText(spannableStringBuilder);
        desTv.setMovementMethod(LinkMovementMethod.getInstance());
        cancelTv.setOnClickListener(this);
        confimTv.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        this.dismiss();
        if(v.getId()==R.id.tvcancel){
            //取消
            SdkHelper.userConfimYsyxJg(getContext(),false);
            userPremissListener.userCancle();
        }else if(v.getId()==R.id.tvConfim){
            //确定
            SdkHelper.userConfimYsyxJg(getContext(),true);
            userPremissListener.userConfim();
        }
    }
}
