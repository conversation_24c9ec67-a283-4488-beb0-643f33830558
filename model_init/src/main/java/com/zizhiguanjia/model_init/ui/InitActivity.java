package com.zizhiguanjia.model_init.ui;

import android.content.Intent;
import android.net.Uri;

import androidx.fragment.app.Fragment;

import com.zizhiguanjia.lib_base.base.ContainerActivity;
import com.wb.lib_rxtools.RxJavaUtils;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.model_init.fragment.InitFragment;

import io.reactivex.functions.Consumer;

public class InitActivity extends ContainerActivity {
    @Override
    public Fragment initBaseFragment() {
        Uri data = getIntent().getData();
        if(data!=null){
            SdkHelper.getWakeUp(getIntent(),getActivity());
        }
        boolean hasGetInstallParams= KvUtils.get("key_Has_Get_InstallParams",false);
        if (!hasGetInstallParams) {
            RxJavaUtils.delay(1, new Consumer<Long>() {
                @Override
                public void accept(Long aLong) throws Exception {
                    SdkHelper.getInstall(getActivity());
                }
            });
        }
        if(data==null){
            return new InitFragment();
        }else{
            try {
                //url=http://www1.zizhiguanjia.cn/m/examguide?examid=22&areaName=广东省清远市&payType=-1&titleBg=3A73FF&msgId=18100443884273149&cityId=null&majId=null
                String params = null;
                params=data.getQueryParameter("url");
                if(data.getPath().contains("commonweb")){
                    String areaName=data.getQueryParameter("areaName");
                    String payType=data.getQueryParameter("payType");
                    String titleBg=data.getQueryParameter("titleBg");
                    String urls=params+"&areaName="+areaName+"&payType="+payType+"&titleBg="+titleBg;
                    params=urls;
                }
                return InitFragment.newInstance(data.getPath(),
                        params,
                        data.getQueryParameter("msgId"),
                        data.getQueryParameter("cityId"),
                        data.getQueryParameter("majId"));
            }catch (Exception e){
                e.printStackTrace();
                return new InitFragment();
            }
        }
    }
    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Uri data = intent.getData();
        if (data != null) {
            SdkHelper.getWakeUp(getIntent(),getActivity());
        }
    }
}
