package com.zizhiguanjia.model_init.fragment;

import android.os.Bundle;
import android.text.SpannableStringBuilder;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.constants.InitRouterPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.HomeHelper;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.model_init.R;
import com.zizhiguanjia.model_init.databinding.InitSplanBinding;
import com.zizhiguanjia.model_init.dialog.UserPremissDialog;
import com.zizhiguanjia.model_init.listener.UserPremissListener;
import com.zizhiguanjia.model_init.navigator.InitNavigator;
import com.zizhiguanjia.model_init.viewmodel.InitViewModel;

import java.util.HashMap;
import java.util.Map;

@BindRes( statusBarStyle= BarStyle.DARK_CONTENT)
@SwipeStyle()
@Route(path = InitRouterPath.MAIN_FRAGMENT)
public class InitFragment extends BaseFragment implements InitNavigator, UserPremissListener {
    public static InitFragment newInstance(String path,String params,String msgId,String cityId,String majId){
        Bundle args = new Bundle();
        if(path.contains("commonweb")){
            Map<String,String > maps=new HashMap<>();
            maps.put("url",params);
            maps.put("full","3");
            maps.put("cityId",cityId);
            maps.put("majId",majId);
            args.putString("params", GsonUtils.gsonString(maps));
        }else {
            args.putString("params", params);
        }
        args.putString("path", path);
        args.putString("msgId", msgId);
        InitFragment fragment = new InitFragment();
        fragment.setArguments(args);
        return fragment;
    }
    private  String params,path,msgId;
    @BindViewModel
    InitViewModel model;
    private InitSplanBinding initSplanBinding;
    @Override
    public int initLayoutResId() {
        return R.layout.init_splan;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        initSplanBinding=getBinding();
        initSplanBinding.setModel(model);
        model.initParams(this,this);
        try {
            AccountHelper.initAccountInfo();
        }catch (Exception ignored){

        }
        try {
            if (getArguments() != null) {
                path=getArguments().getString("path","");
                params=getArguments().getString("params","");
                msgId=getArguments().getString("msgId","");
            } else {
                path="";
                params="";
                msgId="";
            }
        }catch (Exception e){
            params="";
            path="";
            msgId="";
            e.printStackTrace();
        }
    }
    @Override
    public void commonMainPage() {
        finish();
        boolean gudie=KvUtils.get("newUserCertificateGuide",false);
        if(!gudie){
            HomeHelper.startHomeActivityByRoute("/indexGude");
        }else {
            if(path==null||path.isEmpty()){
                HomeHelper.start(getActivity());
//                startFragment(HomeHelper.gotoAvd());
            }else{
                reshAppPage();
            }

        }

    }
    @Override
    public void premissMainPage(SpannableStringBuilder spannableStringBuilder) {
            new PopupManager.Builder(getContext())
                    .dismissOnTouchOutside(false)
                    .dismissOnBackPressed(false)
                    .hasNavigationBar(false)
                    .maxWidth(DpUtils.dp2px(getContext(),310))
                    .maxHeight(DpUtils.dp2px(getContext(),445))
                    .asCustom(new UserPremissDialog(getContext(),spannableStringBuilder,this))
                    .show();
    }

    @Override
    public void reshAppPage() {
        //url=http://www1.zizhiguanjia.cn/m/examguide?examid=22&areaName=广东省清远市&payType=-1&titleBg=3A73FF&msgId=18100443884273149&cityId=null&majId=null

        LogUtils.e("看看路径---->>>"+params);
        if(path != null && path.contains("commonweb")){
            try {
                LogUtils.e("测试---->>>>1"+params);
//                MainHelper.start(getActivity(),params,msgId);
                HomeHelper.start(getActivity(),params,msgId);
            }catch (Exception e){
                e.printStackTrace();
                HomeHelper.start(getActivity());
//                startFragment(HomeHelper.gotoAvd());
            }
        }else {
            HomeHelper.start(getActivity());
//            startFragment(HomeHelper.gotoAvd());
        }
    }

    @Override
    public void userCancle() {
        getActivity().finish();
    }

    @Override
    public void userConfim() {
        SdkHelper.initJpushConfig(BaseConfig.deBug);
        model.userConfim();
    }
}
