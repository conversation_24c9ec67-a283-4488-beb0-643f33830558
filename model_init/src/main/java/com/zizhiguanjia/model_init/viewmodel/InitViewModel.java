package com.zizhiguanjia.model_init.viewmodel;

import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_network.BaseViewModel;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.exception.RxException;
import com.wb.lib_rxtools.subsciber.BaseSubscriber;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.lib_base.utils.SpannableStringUtils;
import com.zizhiguanjia.model_init.R;
import com.zizhiguanjia.model_init.fragment.InitFragment;
import com.zizhiguanjia.model_init.listener.ICommon;
import com.zizhiguanjia.model_init.navigator.InitNavigator;
import com.zizhiguanjia.model_init.repository.InitRepository;

import io.reactivex.disposables.Disposable;

public class InitViewModel extends CommonViewModel implements ICommon {
    private InitNavigator navigator;
    private InitFragment initFragment;
    public ObservableField<Boolean> startTime = new ObservableField<>();
    public ObservableField<String> startTimeDes = new ObservableField<>();
    private Disposable disposable;
    private InitRepository initRepository;
    public void initParams(InitNavigator navigator, InitFragment initFragment) {
        this.navigator = navigator;
        this.initFragment = initFragment;
        startTime.set(false);
        initRepository=new InitRepository();
        initRepository.init(this);
    }
    @Override
    public void initUserPremissDes(SpannableStringBuilder textFrontColorSp) {
        navigator.premissMainPage(textFrontColorSp);
    }
    @Override
    public void userConfim() {
        startTime.set(true);
        initRepository.initAllSdk();
//        startTimeDes.set("跳过4S");
        String downTime=ConfigHelper.getSplanTimeDown();
        LogUtils.e("看看倒计时---->>>"+downTime);
        if (disposable != null) disposable.dispose();
        disposable = RxJavaUtils.countDown(Integer.parseInt(ConfigHelper.getSplanTimeDown()), new BaseSubscriber<Long>() {
            @Override
            public void onError(RxException e) {
            }

            @Override
            public void onSuccess(Long aLong) {
                startTimeDes.set("跳过" + aLong + "S");
                if (aLong == 1) {
                    if (disposable != null) {
                        disposable.dispose();
                    }
                    navigator.commonMainPage();
                }
            }
        });

    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.tvTgTime) {
            if (disposable != null) {
                disposable.dispose();
            }
            navigator.commonMainPage();
        }
    }

    @Override
    public void onClickYhxy() {
        initFragment.initArguments().putString("url", BaseAPI.BASE_YHXY_URL);
        initFragment.startFragment(CommonHelper.showCommonWeb());
    }

    @Override
    public void onClickYsxy() {
        initFragment.initArguments().putString("url", BaseAPI.BASE_XSYT_URL);
        initFragment.startFragment(CommonHelper.showCommonWeb());
    }

    @Override
    public void initAllSdk() {
    }
}
