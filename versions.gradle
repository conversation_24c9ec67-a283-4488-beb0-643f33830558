ext {

    build_version = [
            compileSdkVersion: 30,
            buildToolsVersion: '30.0.3',
            minSdkVersion    : 21,
            applicationId    : "com.zizhiguanjia.flutter_exam",
            targetSdkVersion : 30,
            versionCode      : 20250701,
            versionName      : '1.5.1',
    ]

    versions = [
            android_plugin: '4.2.2',
            room          : '1.1.1',
            common_android_lib : 'v1.0.0',
            arch_comp_room      : '1.1.1',
            leakcanary          : '1.6.3',
            support             : '28.0.0',
            libaumsVersion         : "0.6.0",
            dbflowVersion :"4.2.4",
            androidasyncVersion    : "2.2.1",
            eventbusVersion        : "3.2.0",
            baseRvVersion          : "2.9.50",
            matisseVersion         : "0.5.3-beta3",
            ossversion         : "+",
            room          : '1.1.1',
            ossversion         : "+",
            LogReportVersion:"1.0.3",
            hutool        : '5.6.1',
            jverification_version        : '3.1.8',
            jpush_jcore       : '4.5.7',
            SharpView  : 'v2.4.4',
            shimmerRecyclerView  : 'v1.3',
            simplifyspan  : '1.0.4',
            topcontroldemo  : 'v1.0.1',
            cardview  : '1.0.1',
            shimmerlayout : '2.1.0',
            timePicker : '4.1.0',

    ]

    build_plugins = [
            android_gradle : [group: 'com.android.tools.build', name: 'gradle', version: versions.android_plugin],
            aspectjx_gradle: [group: 'com.hujiang.aspectjx', name: 'gradle-android-plugin-aspectjx', version: '2.0.8']
    ]
    libs = [
            support_v4               : [group: 'com.android.support', name: 'support-v4', version: versions.support],
            junit                    : [group: 'junit', name: 'junit', version: '4.12'],
            test_runner              : [group: 'com.android.support.test', name: 'runner', version: '1.0.2'],
            test_espresso            : [group: 'com.android.support.test.espresso', name: 'espresso-core', version: '3.0.2'],
            "arouter_api"            : "com.alibaba:arouter-api:1.5.0",
            "arouter_compiler"       : "com.alibaba:arouter-compiler:1.2.2",
            arch_comp_room_compiler  : [group: 'android.arch.persistence.room', name: 'compiler', version: versions.arch_comp_room],
            leak_canary_debug        : [group: 'com.squareup.leakcanary', name: 'leakcanary-android', version: versions.leakcanary],
            leak_canary_debug_support: [group: 'com.squareup.leakcanary', name: 'leakcanary-support-fragment', version: versions.leakcanary],
            leak_canary_release      : [group: 'com.squareup.leakcanary', name: 'leakcanary-android-no-op', version: versions.leakcanary],
            brvah                    : [group: 'com.github.CymChad', name: 'BaseRecyclerViewAdapterHelper', version: versions.baseRvVersion],
            matisse                  : [group: 'com.zhihu.android', name: 'matisse', version: versions.matisseVersion],
            "luban"                  : "top.zibin:Luban:1.1.8",
            XUpdate                  : "com.github.xuexiangjys:XUpdate:2.1.0",
            okhttputils              : "com.zhy:okhttputils:2.6.2",
            room                     : [group: 'android.arch.persistence.room', name: 'runtime', version: versions.room],
            room_compiler            : [group: 'android.arch.persistence.room', name: 'compiler', version: versions.room],
            // java工具类 https://hutool.cn/docs/#/
            hutool_all               : [group: 'cn.hutool', name: 'hutool-all', version: versions.hutool],
            hutool_core              : [group: 'cn.hutool', name: 'hutool-core', version: versions.hutool],
            hutool_cache             : [group: 'cn.hutool', name: 'hutool-cache', version: versions.hutool],
            hutool_crypto            : [group: 'cn.hutool', name: 'hutool-crypto', version: versions.hutool],
            hutool_http              : [group: 'cn.hutool', name: 'hutool-http', version: versions.hutool],
            SharpView                : [group: 'com.github.zengzhaoxing', name: 'SharpView', version: versions.SharpView],
            shimmerRecyclerView      : [group: 'com.github.sharish', name: 'ShimmerRecyclerView', version: versions.shimmerRecyclerView],
            simplifyspan             : [group: 'com.binaryfork', name: 'spanny', version: versions.simplifyspan],
            topcontroldemo           : [group: 'com.github.JadeKkang', name: 'topcontroldemo', version: versions.topcontroldemo],
            cardview                 : [group: 'com.zyp.cardview', name: 'cardview', version: versions.cardview],
            shimmerlayout            : [group: 'io.supercharge', name: 'shimmerlayout', version: versions.shimmerlayout],
            timePicer                : [group: 'com.contrarywind', name: 'wheelview', version: versions.timePicker],
    ]

}