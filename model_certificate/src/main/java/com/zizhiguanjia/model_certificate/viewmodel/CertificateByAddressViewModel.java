package com.zizhiguanjia.model_certificate.viewmodel;

import android.view.Gravity;
import android.view.View;

import com.wb.lib_network.BaseViewModel;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.listeners.CertificateJsonByAddressListener;
import com.zizhiguanjia.lib_base.utils.NetWorkUtils;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.api.CertificateServiceApi;
import com.zizhiguanjia.model_certificate.listener.ICertificateByAddress;
import com.zizhiguanjia.model_certificate.model.CertificateByAddressBean;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;
import com.zizhiguanjia.model_certificate.navigator.CertificateByAddressNavigator;

import java.util.HashMap;
import java.util.Map;

public class CertificateByAddressViewModel extends BaseViewModel implements ICertificateByAddress {
    private CertificateByAddressNavigator navigator;
    private CertificateServiceApi mApi=new Http().create(CertificateServiceApi.class);
    @Override
    public void initParams(CertificateByAddressNavigator certificateByAddressNavigator,String addressId){
        this.navigator=certificateByAddressNavigator;
        getCertificatetByAddressData(addressId);
    }

    @Override
    public void getCertificatetByAddressData(String addressId){
        if(!NetWorkUtils.getInstance().checkNetWork()) {
            navigator.showNetWork();
            return;
        }
        Map<String,String> params=new HashMap<>();
        params.put("areaId",addressId);
        launchOnlyResult(mApi.getSecondConstruction(params), new OnHandleException<BaseData<SecondConstructionBean>>() {
            @Override
            public void success(BaseData<SecondConstructionBean> data) {
                if(data.Data==null||data.Data.getRecords()==null||data.Data.getRecords().size()==0){
                    navigator.showEmpty();
                }else {
                        LogUtils.e("看看接受得---->>>"+data.getResult().getRecords().size());
//                    navigator.showCertificateAddressView(data.Data.getRecords());
                    navigator.showSecondContructions(data.Data.getParentMajors(),data.Data.getRecords());
                }
            }
            @Override
            public void error(String msg) {
                navigator.showEmpty();
                navigator.showErrorToast(msg);
            }
        });
    }

    @Override
    public void getCertificatetByAddressData(String addressId, SecondConstructionBean.GroupBean bean) {
        if (!NetWorkUtils.getInstance().checkNetWork()) {
            navigator.showNetWork();
            return;
        }
        Map<String, String> params = new HashMap<>();
        params.put("areaId", addressId);
        if (bean != null && bean.getId() != null) {
            params.put("majorType", String.valueOf(bean.getId()));
        }
        launchOnlyResult(mApi.getSecondConstruction(params), new OnHandleException<BaseData<SecondConstructionBean>>() {
            @Override
            public void success(BaseData<SecondConstructionBean> data) {
                if (data.Data == null || data.Data.getRecords() == null || data.Data.getRecords().size() == 0) {
                    navigator.showEmpty();
                } else {
                    LogUtils.e("看看接受得---->>>" + data.getResult().getRecords().size());
                    //                    navigator.showCertificateAddressView(data.Data.getRecords());
                    navigator.showSecondContructions(data.Data.getParentMajors(), data.Data.getRecords());
                }
            }

            @Override
            public void error(String msg) {
                navigator.showEmpty();
                navigator.showErrorToast(msg);
            }
        });
    }
    @Override
    public void getCertificatetByAddressData(String addressId, CertificateJsonByAddressListener certificateJsonByAddressListener) {
        if(!NetWorkUtils.getInstance().checkNetWork()) {
            ToastUtils.normal("检测当前网络！", Gravity.CENTER);
            return;
        }
        Map<String,String> params=new HashMap<>();
        params.put("areaId",addressId);
        launchOnlyResult(mApi.getCertificateByAddressDataList(params), new OnHandleException<BaseData<CertificateByAddressBean>>() {
            @Override
            public void success(BaseData<CertificateByAddressBean> data) {
                if(data.Data==null||data.Data.getRecords()==null||data.Data.getRecords().size()==0){
                    certificateJsonByAddressListener.onGetCetificateInfoByAddress(null);
                }else {
                     if(data.Data.getRecords()==null||data.Data.getRecords().size()==0){
                         certificateJsonByAddressListener.onGetCetificateInfoByAddress(null);
                     }
                    certificateJsonByAddressListener.onGetCetificateInfoByAddress(GsonUtils.newInstance().listToJson(data.Data.getRecords()));
                }
            }
            @Override
            public void error(String msg) {
                certificateJsonByAddressListener.onGetCetificateInfoByAddress(null);
            }
        });
    }

    @Override
    public void onclick(View view) {
           if(view.getId() == R.id.tvUpdateName){
               navigator.checkToPage();
           }
    }
}
