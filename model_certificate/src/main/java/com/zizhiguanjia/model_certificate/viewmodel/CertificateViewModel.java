package com.zizhiguanjia.model_certificate.viewmodel;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_certificate.api.CertificateServiceApi;
import com.zizhiguanjia.model_certificate.model.CertificateBean;
import com.zizhiguanjia.model_certificate.navigator.CertificateNavigator;

import java.util.HashMap;

public class CertificateViewModel extends CommonViewModel {
    private CertificateNavigator navigator;
    public void initParams(CertificateNavigator certificateNavigator){
        this.navigator=certificateNavigator;
    }
    private CertificateServiceApi mApi=new Http().create(CertificateServiceApi.class);
    public void getCertificateData(){
        launchOnlyResult(mApi.getCertificateDataList(new HashMap<>()), new OnHandleException<BaseData<CertificateBean>>() {
            @Override
            public void success(BaseData<CertificateBean> data) {
                if(data.Data==null||data.Data.getMajors()==null||data.Data.getMajors().size()==0){
                    navigator.emptyDataView();
                }else {
                    navigator.contentDataView();
                    navigator.showContentData(data.Data.getMajors());
                }
            }

            @Override
            public void error(String msg) {
                navigator.emptyDataView();
            }
        });
    }
}
