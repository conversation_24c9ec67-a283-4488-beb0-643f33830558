package com.zizhiguanjia.model_certificate.viewmodel;

import android.view.Gravity;
import android.view.View;

import androidx.databinding.ObservableField;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.LoginHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.tb.TableCertificate;
import com.zizhiguanjia.lib_base.utils.NetWorkUtils;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.api.CertificateServiceApi;
import com.zizhiguanjia.model_certificate.manager.CertificateManager;
import com.zizhiguanjia.model_certificate.model.CertificateByAddressBean;
import com.zizhiguanjia.model_certificate.model.UpdataCertificateBean;
import com.zizhiguanjia.model_certificate.model.ZzCertificateByAddressBean;
import com.zizhiguanjia.model_certificate.navigator.ZzCertificateByAddressNabigator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import cn.hutool.core.collection.ListUtil;
import io.reactivex.functions.Consumer;

public class ZzCertificateByAddressViewModel extends CommonViewModel {
    private CertificateServiceApi mApi=new Http().create(CertificateServiceApi.class);
    private ZzCertificateByAddressNabigator navigator;
    public ObservableField<Boolean> showAddressView=new ObservableField<>();
    private String areaId;
    public void initParams(ZzCertificateByAddressNabigator navigator,String enterType){
        this.navigator=navigator;
        getCertificateByAddress(enterType,"0");
        showAddressView.set(false);
    }
    public void getCertificateByAddress(String enterType,String areaId){
        if(!NetWorkUtils.getInstance().checkNetWork())return;
        Map<String,String> params=new HashMap<>();
        //BindOrderType 等于2的时候显示更改地区
        //  BindOrderType等于 3的时候不需要自动切换
        params.put("areaId",areaId);
        params.put("enterType",enterType);
        this.areaId=areaId;
        launchOnlyResult(mApi.getCertificateByAddressDataLists(params), new OnHandleException<BaseData<UpdataCertificateBean>>() {
            @Override
            public void success(BaseData<UpdataCertificateBean> data) {
                LogUtils.e("----->>>>看看"+data.getResult().getRecords().size());
                navigator.showContextView(data.getResult().getRecords());
            }
            @Override
            public void error(String msg) {
            }
        });
    }
    public void onclick(View view) {
        if(view.getId() == R.id.tvUpdateName){
            navigator.checkToPage();
        }
    }
    public void bindCertificate(Set<String> sets){
        List<String> list = new ArrayList(sets);
        String ejIds=StringUtils.ListToStr(list);
        LogUtils.e("包装的----->>>>"+ejIds);
        Map<String,String> params=new HashMap<>();
        List<String> listStr=getEjStrs(ejIds);
        if(listStr.size()==1){
            params.put("ejMajors",ejIds);
        }else {
            params.put("ejMajors",listStr.get(0));
            params.put("aqyMajorId",listStr.get(1));
        }
        if(StringUtils.isEmpty(areaId)){
        }else {
            params.put("areaId",areaId);
        }
        navigator.showOrHindLoading(true);
        launchOnlyResult(mApi.bindCertificate(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                navigator.showOrHindLoading(false);
//                BaseConfig.majId=reshCertificate(list);
//                if(BaseConfig.majId.startsWith("1")){
//                    BaseConfig.MAJOR_PID=1100;
//                }else {
//                    BaseConfig.MAJOR_PID=2000;
//                }
//                TableCertificate tableCertificate=CertificateHelper.getUserCertificate(AccountHelper.getCurrentLoginAccount());
//                tableCertificate.setCertificateId( BaseConfig.majId);
//                tableCertificate.setCertificateName(getCertificateName( BaseConfig.majId));
//                LogUtils.e("更新了数据"+tableCertificate.toString());
//                CertificateManager.getInstance().updataCertificate(tableCertificate);
//                LogUtils.e("看看工参---->>>"+ BaseConfig.MAJOR_PID+"****"+BaseConfig.majId);
                ToastUtils.normal(data.Message, Gravity.CENTER);
                RxJavaUtils.delay(1, new Consumer<Long>() {
                    @Override
                    public void accept(Long aLong) throws Exception {
//                        MainThreadUtils.post(new Runnable() {
//                            @Override
//                            public void run() {
//                                Bus.post(new MsgEvent(*********));
//                                navigator.closeView();
//                            }
//                        });
                        if(CertificateHelper.getCurrentCertificateDialog()){
                            LogUtils.e("开始关闭了---->>>无需回到首页");
                            navigator.closeView();
                        }else {
                            LogUtils.e("开始关闭了---->>>需要回到首页");
                            Bus.post(new MsgEvent(0x4545445));
                        }
                    }
                });
            }

            @Override
            public void error(String msg) {
                navigator.showOrHindLoading(false);
                ToastUtils.normal(msg, Gravity.CENTER);
            }
        });
    }
    private String getCertificateName(String ids){
        if(ids.contains("2035")){
            return "施工管理";
        }else if(ids.contains("2036")){
            return "工程法规";
        }else if(ids.contains("2037")){
        return "建筑工程";
        }else if(ids.contains("2038")){
        return "市政工程";
        }else if(ids.contains("2039")){
            return "机电工程";
        }
        return "施工管理";
    }
    private String reshCertificate(List<String> lists){
        try {
            if(lists==null||lists.size()==0)return "0";
            if(lists.size()==1){
                return lists.get(0);
            }
            Iterator<String> iter = lists.iterator();
            List<String> strs=new ArrayList<>();
            while(iter.hasNext()){
                String str = iter.next();
                if(str.startsWith("1")){
                    LogUtils.e("无效的----->>>"+str);
                }else {
                    if(!str.contains("2036")||!str.contains("2035"))
                    {
                        strs.add(str);
                    }
                }
            }
            if(strs==null||strs.size()==0)
                return "0";
            return strs.get(0);
        }catch (Exception e){
            return "0";
        }
    }
    private List<String> getEjStrs(String ejIds){
        List<String> listStr=new ArrayList<>();
        String[] lists=StringUtils.convertStrToArray2(ejIds);
        List<String>ejList=new ArrayList<>();
        List<String>aqyList=new ArrayList<>();
        for(String str:lists){
            if(str.startsWith("1")){
                aqyList.add(str);
            }else {
                ejList.add(str);
            }
        }
        String ejStr=StringUtils.ListToStr(ejList);
        String aqyStr=StringUtils.ListToStr(aqyList);
        listStr.add(ejStr);
        if(StringUtils.isEmpty(aqyStr)){

        }else {
            listStr.add(aqyStr);
        }
        return listStr;
    }
}
