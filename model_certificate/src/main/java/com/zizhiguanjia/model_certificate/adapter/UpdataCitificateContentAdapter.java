package com.zizhiguanjia.model_certificate.adapter;

import android.graphics.Color;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_adapter.ItemDelegate;
import com.wb.lib_adapter.MultiItemTypeAdapter;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.databinding.CerUpdatacertificateContentLayoutBinding;
import com.zizhiguanjia.model_certificate.databinding.CerUpdatacertificateLayoutBinding;
import com.zizhiguanjia.model_certificate.databinding.CerZzClassflyAqyBinding;
import com.zizhiguanjia.model_certificate.listener.CertificateUpdateListener;
import com.zizhiguanjia.model_certificate.listener.UpdataCertificateListenter;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;
import com.zizhiguanjia.model_certificate.model.UpdataCertificateBean;

public class UpdataCitificateContentAdapter extends MultiItemTypeAdapter<UpdataCertificateBean.RecordsBean> {

    public UpdataCitificateContentAdapter(UpdataCertificateListenter updataCertificateListenter) {
        addItemDelegate(new ItemDelegate<UpdataCertificateBean.RecordsBean>() {
            private CerUpdatacertificateContentLayoutBinding binding;
            @Override
            public int layoutId() {
                return R.layout.cer_updatacertificate_content_layout;
            }

            @Override
            public boolean isThisType(UpdataCertificateBean.RecordsBean item, int position) {
                return item.getId()==1100?true:false;
            }

            @Override
            public void convert(BaseViewHolder holder, UpdataCertificateBean.RecordsBean item, int position) {
                binding=holder.getBinding();
                binding.titleTv.setText(item.getName());
                binding.desTv.setText(item.getBindTip());
                if(binding.rightRecycle.getAdapter()==null){
                    binding.rightRecycle.setLayoutManager(new GridLayoutManager(holder.itemView.getContext(),3));
                    binding.rightRecycle.addItemDecoration(new GridSpaceItemDecoration(3, DpUtils.dp2px(holder.itemView.getContext(), 8), DpUtils.dp2px(holder.itemView.getContext(), 8)));
                    UpdataCertificateChildAdapter certificateAqyAdapter=new UpdataCertificateChildAdapter(item.getBindOrderType(), item.getSelectType(), updataCertificateListenter);
                    binding.rightRecycle.setAdapter(certificateAqyAdapter);
                }
                UpdataCertificateChildAdapter certificateAqyAdapter= (UpdataCertificateChildAdapter) binding.rightRecycle.getAdapter();
                certificateAqyAdapter.setDataItems(item.getChilds());
            }
        });
        addItemDelegate(new ItemDelegate<UpdataCertificateBean.RecordsBean>() {
            private CerUpdatacertificateContentLayoutBinding binding;
            @Override
            public int layoutId() {
                return R.layout.cer_updatacertificate_content_layout;
            }

            @Override
            public boolean isThisType(UpdataCertificateBean.RecordsBean item, int position) {
                return item.getId()==2000?true:false;
            }

            @Override
            public void convert(BaseViewHolder holder, UpdataCertificateBean.RecordsBean item, int position) {
                binding=holder.getBinding();
                binding.titleTv.setText(item.getName());
                binding.desTv.setText(item.getBindTip());
                if(binding.rightRecycle.getAdapter()==null){
                    binding.rightRecycle.setLayoutManager(new LinearLayoutManager(holder.itemView.getContext()));
                    UpdataCertificateAdapter certificateAqyAdapter=new UpdataCertificateAdapter(updataCertificateListenter);
                    binding.rightRecycle.setAdapter(certificateAqyAdapter);
                }
                UpdataCertificateAdapter certificateAqyAdapter= (UpdataCertificateAdapter) binding.rightRecycle.getAdapter();
                certificateAqyAdapter.setDataItems(item.getChilds());
            }
        });
    }
}
