package com.zizhiguanjia.model_certificate.adapter;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_image_loadcal.ImageManager;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.databinding.CerCertificateRightGroupLayoutBinding;
import com.zizhiguanjia.model_certificate.listener.CertificateUpdateListener;
import com.zizhiguanjia.model_certificate.model.CertificateConfigBean;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;

import androidx.recyclerview.widget.GridLayoutManager;

public class CertificateLeftAdapter extends BaseAdapter<SecondConstructionBean.RecordsBean.ChildsBean> {
    private CerCertificateRightGroupLayoutBinding binding;
    private String addressId;
    private String addressName;
    private boolean isUpdate;
    /**
     * 是否使用当前的父级id字段
     */
    private boolean useParentId = false;

    private CertificateUpdateListener certificateUpdateListener;

    public void setCertificateUpdateListener(CertificateUpdateListener certificateUpdateListener) {
        this.certificateUpdateListener = certificateUpdateListener;
    }

    public void setUseParentId(boolean useParentId) {
        this.useParentId = useParentId;
    }

    public CertificateLeftAdapter( String addressId, String addressName,boolean isUpdate) {
        super(R.layout.cer_certificate_right_group_layout);
        this.addressId = addressId;
        this.isUpdate=isUpdate;
        this.addressName = addressName;
    }

    @Override
    protected void bind(BaseViewHolder holder, SecondConstructionBean.RecordsBean.ChildsBean item, int position) {
        binding=holder.getBinding();
        binding.setData(item);
                ImageManager.getInstance().displayImage(item.getIcon(), binding.rightChildeImg);
                if (binding.citificateRightChildRcy.getAdapter() == null) {
                    CertificateAqyAdapter certificateAqyAdapter = new CertificateAqyAdapter(addressId, addressName,item.getId()+"",item.getName(),isUpdate);
                    certificateAqyAdapter.setCertificateUpdateListener(new CertificateUpdateListener() {
                        @Override
                        public void onUpdateCertificate(CertificateConfigBean certificateConfigBean) {
                            if (certificateUpdateListener == null) return;
                            certificateUpdateListener.onUpdateCertificate(certificateConfigBean);
                        }
                        /**
                         * 组改变
                         *
                         * @param bean 改变后的组
                         */
                        @Override
                        public void onChangeGroup(SecondConstructionBean.GroupBean bean) {
                            if (certificateUpdateListener == null) return;
                            certificateUpdateListener.onChangeGroup(bean);
                        }

                        /**
                         * @param groupId
                         * @param groupName
                         * @param majorChildBean
                         */
                        @Override
                        public void showMoreChild(String groupId, String groupName, SecondConstructionBean.RecordsBean.ChildsBean majorChildBean) {
                            if (certificateUpdateListener == null) return;
                            certificateUpdateListener.showMoreChild(groupId, groupName, majorChildBean);
                        }
                    });
                    binding.citificateRightChildRcy.setLayoutManager(new GridLayoutManager(holder.itemView.getContext(), 3));
                    binding.citificateRightChildRcy.addItemDecoration(new GridSpaceItemDecoration(3, DpUtils.dp2px(holder.itemView.getContext(), 8), DpUtils.dp2px(holder.itemView.getContext(), 8)));
                    binding.citificateRightChildRcy.setAdapter(certificateAqyAdapter);
                }
                CertificateAqyAdapter certificateAqyAdapter = (CertificateAqyAdapter) binding.citificateRightChildRcy.getAdapter();
                certificateAqyAdapter.setDataItems(item.getChilds());
    }
}
