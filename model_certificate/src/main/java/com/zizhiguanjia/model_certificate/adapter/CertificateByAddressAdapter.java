package com.zizhiguanjia.model_certificate.adapter;

import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.config.ClassFLyTypeConfig;
import com.zizhiguanjia.model_certificate.databinding.CerTificatebyaddressItemBinding;
import com.zizhiguanjia.model_certificate.listener.CertificateUpdateListener;
import com.zizhiguanjia.model_certificate.model.CertificateBean;
import com.zizhiguanjia.model_certificate.model.CertificateByAddressBean;
import com.zizhiguanjia.model_certificate.model.CertificateConfigBean;

public class CertificateByAddressAdapter extends BaseAdapter<CertificateByAddressBean.RecordsDTO> {
    private CerTificatebyaddressItemBinding binding;
    private String addressId,addressName;
    private CertificateUpdateListener certificateUpdateListener;

    public void setCertificateUpdateListener(CertificateUpdateListener certificateUpdateListener) {
        this.certificateUpdateListener = certificateUpdateListener;
    }

    public CertificateByAddressAdapter(String addressId,String addressName) {
        super(R.layout.cer_tificatebyaddress_item);
        this.addressId=addressId;
        this.addressName=addressName;
    }

    @Override
    protected void bind(BaseViewHolder holder, CertificateByAddressBean.RecordsDTO item, int position) {
        binding=holder.getBinding();
        binding.tvCertificateName.setText(item.getName());
        binding.tvCertificateName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CertificateByAddressAdapter.this.onClick(item);
            }
        });
    }
    public void onClick(CertificateByAddressBean.RecordsDTO majorChildBean) {
        CertificateConfigBean mainCertificateBean = new CertificateConfigBean();
        mainCertificateBean.setCityCode(addressId);
        mainCertificateBean.setCityName(addressName);
        mainCertificateBean.setSubjectId(majorChildBean.getId());
        mainCertificateBean.setSubjectName(majorChildBean.getName());
        mainCertificateBean.setDes(addressName+"·"+majorChildBean.getName());
        mainCertificateBean.setClassflyId(String.valueOf(majorChildBean.getId()+"").startsWith("2")?ClassFLyTypeConfig.CLASS_FLY_RJ : ClassFLyTypeConfig.CLASS_FLY_AQY);
        if (certificateUpdateListener == null) return;
        certificateUpdateListener.onUpdateCertificate(mainCertificateBean);
    }
}
