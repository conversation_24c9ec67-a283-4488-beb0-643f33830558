package com.zizhiguanjia.model_certificate.adapter;

import androidx.recyclerview.widget.GridLayoutManager;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_image_loadcal.ImageManager;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.databinding.CerUpdatacertificateLayoutBinding;
import com.zizhiguanjia.model_certificate.databinding.CerZzClassflyAqyBinding;
import com.zizhiguanjia.model_certificate.listener.UpdataCertificateListenter;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;
import com.zizhiguanjia.model_certificate.model.UpdataCertificateBean;

public class UpdataCertificateAdapter extends BaseAdapter<UpdataCertificateBean.RecordsBean.ChildsBean> {
    private CerUpdatacertificateLayoutBinding cerZzClassflyAqyBinding;
    private UpdataCertificateListenter updataCertificateListenter;
    public UpdataCertificateAdapter(UpdataCertificateListenter updataCertificateListenter) {
        super(R.layout.cer_updatacertificate_layout);
        this.updataCertificateListenter=updataCertificateListenter;
    }
    @Override
    protected void bind(BaseViewHolder holder, UpdataCertificateBean.RecordsBean.ChildsBean item, int position) {
        cerZzClassflyAqyBinding=holder.getBinding();
        cerZzClassflyAqyBinding.tvTitle.setText(item.getName());
        ImageManager.getInstance().displayImage(item.getIcon(), cerZzClassflyAqyBinding.rightChildeImg);
        if(cerZzClassflyAqyBinding.certificateFlyRcy.getAdapter()==null){
            cerZzClassflyAqyBinding.certificateFlyRcy.setLayoutManager(new GridLayoutManager(holder.itemView.getContext(),3));
            cerZzClassflyAqyBinding.certificateFlyRcy.addItemDecoration(new GridSpaceItemDecoration(3, DpUtils.dp2px(holder.itemView.getContext(), 8), DpUtils.dp2px(holder.itemView.getContext(), 8)));
            UpdataCertificateChildAdapter certificateAqyAdapter=new UpdataCertificateChildAdapter(item.getBindOrderType(),item.getSelectType(),updataCertificateListenter);
            cerZzClassflyAqyBinding.certificateFlyRcy.setAdapter(certificateAqyAdapter);
        }
        UpdataCertificateChildAdapter updataCertificateChildAdapter= (UpdataCertificateChildAdapter) cerZzClassflyAqyBinding.certificateFlyRcy.getAdapter();
        updataCertificateChildAdapter.setDataItems(item.getChilds());
    }
}
