package com.zizhiguanjia.model_certificate.adapter;

import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.config.ClassFLyTypeConfig;
import com.zizhiguanjia.model_certificate.databinding.CerTificatebyaddressItemBinding;
import com.zizhiguanjia.model_certificate.listener.CertificateUpdateListener;
import com.zizhiguanjia.model_certificate.manager.CertificateManager;
import com.zizhiguanjia.model_certificate.model.CertificateConfigBean;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;

import java.util.Objects;

public class CertificateAqyAdapter extends BaseAdapter<SecondConstructionBean.RecordsBean.ChildsBean> {
    private String addressId;
    private String addressName;
    private CertificateUpdateListener certificateUpdateListener;
    private CerTificatebyaddressItemBinding binding;
    private String groupId;
    private String groupName;
    private boolean isUpdate;
    /**
     * 是否使用当前的父级id字段
     */
    private boolean useParentId = false;

    public void setCertificateUpdateListener(CertificateUpdateListener certificateUpdateListener) {
        this.certificateUpdateListener = certificateUpdateListener;
    }

    public CertificateAqyAdapter(String addressId, String addressName, String groupId, String groupName, boolean isUpdate) {
        super(R.layout.cer_tificatebyaddress_item);
        this.addressId = addressId;
        this.addressName = addressName;
        this.groupId = groupId;
        this.groupName = groupName;
        this.isUpdate = isUpdate;
        LogUtils.e("看看--->>>isUpdate" + isUpdate);
    }

    @Override
    protected void bind(BaseViewHolder holder, SecondConstructionBean.RecordsBean.ChildsBean item, int position) {
        binding = holder.getBinding();
        binding.tvCertificateName.setText(item.getName());
        binding.setData(item);
        binding.tvCertificateName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClicks(item);
            }
        });
        String certificateId = CertificateManager.getInstance().getCurrentCitificateName();
        LogUtils.e("存储的Id" + certificateId);
        if (!isUpdate) {
            binding.tvCertificateName.setBackgroundResource(R.drawable.cer_major_child_text_bg);
        } else {
            if (StringUtils.isEmpty(certificateId)) {
                binding.tvCertificateName.setBackgroundResource(R.drawable.cer_major_child_text_bg);
            } else {
                if (certificateId.equals(String.valueOf(item.getId()))) {
                    binding.tvCertificateName.setBackgroundResource(R.drawable.certificate_mr_bg);
                } else {
                    if (item.getChilds() != null && !item.getChilds().isEmpty()) {
                        boolean have = false;
                        for (SecondConstructionBean.RecordsBean.ChildsBean child : item.getChilds()) {
                            if (Objects.equals(String.valueOf(child.getId()), certificateId)) {
                                have = true;
                                binding.tvCertificateName.setBackgroundResource(R.drawable.certificate_mr_bg);
                            }
                        }
                        if (!have) {
                            binding.tvCertificateName.setBackgroundResource(R.drawable.cer_major_child_text_bg);
                        }
                    } else {
                        binding.tvCertificateName.setBackgroundResource(R.drawable.cer_major_child_text_bg);
                    }
                }
            }
        }

    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public void setUseParentId(boolean useParentId) {
        this.useParentId = useParentId;
    }

    public void onClicks(SecondConstructionBean.RecordsBean.ChildsBean majorChildBean) {
        if (majorChildBean.getChilds() != null && !majorChildBean.getChilds().isEmpty()) {
            if (certificateUpdateListener == null) {
                return;
            }
            certificateUpdateListener.showMoreChild(groupId, groupName, majorChildBean);
            return;
        }
        CertificateConfigBean mainCertificateBean = new CertificateConfigBean();
        mainCertificateBean.setCityCode(addressId);
        mainCertificateBean.setCityName(addressName);
        mainCertificateBean.setSubjectId(majorChildBean.getId());
        mainCertificateBean.setSubjectName(majorChildBean.getName());
        mainCertificateBean.setDes(addressName + "·" + majorChildBean.getName());
        mainCertificateBean.setGroupId(useParentId ? majorChildBean.getParentId() + "" : groupId);
        mainCertificateBean.setClassflyId(
                String.valueOf(majorChildBean.getId() + "").startsWith("2") ? ClassFLyTypeConfig.CLASS_FLY_RJ : ClassFLyTypeConfig.CLASS_FLY_AQY);
        LogUtils.e("看看------>>>>" + GsonUtils.newInstance().GsonToString(mainCertificateBean));
        if (certificateUpdateListener == null) {
            return;
        }
        certificateUpdateListener.onUpdateCertificate(mainCertificateBean);
    }
}
