package com.zizhiguanjia.model_certificate.adapter;

import android.view.View;

import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_adapter.ItemDelegate;
import com.wb.lib_adapter.MultiItemTypeAdapter;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.databinding.CerZzClassflyAqyBinding;
import com.zizhiguanjia.model_certificate.listener.CertificateUpdateListener;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

public class CertificaterRightAdapter extends MultiItemTypeAdapter<SecondConstructionBean.RecordsBean>{
    private String addressId;
    private String addressName;
    private boolean isUpdate;
    /**
     * 是否使用当前的父级id字段
     */
    private boolean useParentId = false;

    private CertificateUpdateListener certificateUpdateListener;

    public void setCertificateUpdateListener(CertificateUpdateListener certificateUpdateListener) {
        this.certificateUpdateListener = certificateUpdateListener;
    }

    public void setUseParentId(boolean useParentId) {
        this.useParentId = useParentId;
    }

    public CertificaterRightAdapter(String addressId, String addressName,boolean isUpdate) {
        this.addressId = addressId;
        this.isUpdate=isUpdate;
        this.addressName = addressName;
        addItemDelegate(new ItemDelegate<SecondConstructionBean.RecordsBean>() {
            private CerZzClassflyAqyBinding cerZzClassflyAqyBinding;
            @Override
            public int layoutId() {
                return R.layout.cer_zz_classfly_aqy;
            }

            @Override
            public boolean isThisType(SecondConstructionBean.RecordsBean item, int position) {
//                return item.getId()!=2000?true:false;
                return true;
            }

            @Override
            public void convert(BaseViewHolder holder, SecondConstructionBean.RecordsBean item, int position) {
                cerZzClassflyAqyBinding=holder.getBinding();
                cerZzClassflyAqyBinding.setData(item);
                cerZzClassflyAqyBinding.lineV.setVisibility(View.VISIBLE);
                LogUtils.e("看看个数"+item.getChilds().size()+"-----"+(item.getChilds().size()>2?3:2));
//                cerZzClassflyAqyBinding.certificateFlyRcy.setLayoutManager(new GridLayoutManager(holder.itemView.getContext(),item.getChilds().size()>2?3:2));
//                cerZzClassflyAqyBinding.certificateFlyRcy.addItemDecoration(new GridSpaceItemDecoration(item.getChilds().size()>2?3:2, DpUtils.dp2px(holder.itemView.getContext(), 8), DpUtils.dp2px(holder.itemView.getContext(), 8)));
//                CertificateAqyAdapter certificateAqyAdapter=new CertificateAqyAdapter(addressId,addressName,item.getId()+"",isUpdate);
//                certificateAqyAdapter.setCertificateUpdateListener(certificateUpdateListener);
//                cerZzClassflyAqyBinding.certificateFlyRcy.setAdapter(certificateAqyAdapter);
//                certificateAqyAdapter.setDataItems(item.getChilds());
//                cerZzClassflyAqyBinding.postionV.setVisibility(position==0?View.VISIBLE:View.GONE);
                if(cerZzClassflyAqyBinding.certificateFlyRcy.getAdapter() != null){
                    ((CertificateAqyAdapter)cerZzClassflyAqyBinding.certificateFlyRcy.getAdapter()).setGroupId(item.getId()+"");
                    ((CertificateAqyAdapter)cerZzClassflyAqyBinding.certificateFlyRcy.getAdapter()).setUseParentId(useParentId);
                }else {
                    CertificateAqyAdapter certificateAqyAdapter = new CertificateAqyAdapter(addressId, addressName, item.getId() + "", item.getName(),
                            isUpdate);
                    certificateAqyAdapter.setCertificateUpdateListener(certificateUpdateListener);
                    cerZzClassflyAqyBinding.certificateFlyRcy.setAdapter(certificateAqyAdapter);
                }
                int count = cerZzClassflyAqyBinding.certificateFlyRcy.getItemDecorationCount();
                for (int i = 0; i < count; i++) {
                    cerZzClassflyAqyBinding.certificateFlyRcy.removeItemDecorationAt(0);
                }
                cerZzClassflyAqyBinding.certificateFlyRcy.setLayoutManager(new GridLayoutManager(holder.itemView.getContext(),item.getChilds().size()>2?3:2));
                cerZzClassflyAqyBinding.certificateFlyRcy.addItemDecoration(new GridSpaceItemDecoration(item.getChilds().size()>2?3:2, DpUtils.dp2px(holder.itemView.getContext(), 8), DpUtils.dp2px(holder.itemView.getContext(), 8)));
                ((CertificateAqyAdapter)cerZzClassflyAqyBinding.certificateFlyRcy.getAdapter()).setDataItems(item.getChilds());
                cerZzClassflyAqyBinding.postionV.setVisibility(position==0?View.VISIBLE:View.GONE);
            }
        });
        addItemDelegate(new ItemDelegate<SecondConstructionBean.RecordsBean>() {
            private CerZzClassflyAqyBinding cerZzClassflyAqyBinding;
            @Override
            public int layoutId() {
                return R.layout.cer_zz_classfly_aqy;
            }

            @Override
            public boolean isThisType(SecondConstructionBean.RecordsBean item, int position) {
//                return item.getId()==2000?true:false;
                return false;
            }

            @Override
            public void convert(BaseViewHolder holder, SecondConstructionBean.RecordsBean item, int position) {
                cerZzClassflyAqyBinding=holder.getBinding();
                cerZzClassflyAqyBinding.setData(item);
                cerZzClassflyAqyBinding.lineV.setVisibility(View.GONE);
//                cerZzClassflyAqyBinding.certificateFlyRcy.setLayoutManager(new LinearLayoutManager(holder.itemView.getContext()));
//                CertificateLeftAdapter certificateAqyAdapter=new CertificateLeftAdapter(addressId,addressName,isUpdate);
//                certificateAqyAdapter.setCertificateUpdateListener(certificateUpdateListener);
//                cerZzClassflyAqyBinding.certificateFlyRcy.setAdapter(certificateAqyAdapter);
//                certificateAqyAdapter.setDataItems(item.getChilds());
                if(cerZzClassflyAqyBinding.certificateFlyRcy.getAdapter() == null) {
                    CertificateLeftAdapter certificateAqyAdapter = new CertificateLeftAdapter(addressId, addressName, isUpdate);
                    certificateAqyAdapter.setCertificateUpdateListener(certificateUpdateListener);
                    cerZzClassflyAqyBinding.certificateFlyRcy.setAdapter(certificateAqyAdapter);
                    cerZzClassflyAqyBinding.certificateFlyRcy.setLayoutManager(new LinearLayoutManager(holder.itemView.getContext()));
                }
                ((CertificateLeftAdapter)cerZzClassflyAqyBinding.certificateFlyRcy.getAdapter()).setDataItems(item.getChilds());
            }
        });
    }

}
