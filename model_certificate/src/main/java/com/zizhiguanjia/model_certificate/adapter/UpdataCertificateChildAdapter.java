package com.zizhiguanjia.model_certificate.adapter;

import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.databinding.CerTificatebyaddressItemBinding;
import com.zizhiguanjia.model_certificate.listener.UpdataCertificateListenter;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;
import com.zizhiguanjia.model_certificate.model.UpdataCertificateBean;

public class UpdataCertificateChildAdapter extends BaseAdapter<UpdataCertificateBean.RecordsBean.ChildsBean> {
    private CerTificatebyaddressItemBinding binding;
    private int bindOrderType,selectType;
    private int selectOption=-1;
    private UpdataCertificateListenter updataCertificateListenter;
    public UpdataCertificateChildAdapter(int bindOrderType, int selectType, UpdataCertificateListenter updataCertificateListenter) {
        super(R.layout.cer_tificatebyaddress_item);
        this.bindOrderType = bindOrderType;
        this.updataCertificateListenter=updataCertificateListenter;
        this.selectType = selectType;
    }
    @Override
    protected void bind(BaseViewHolder holder, UpdataCertificateBean.RecordsBean.ChildsBean item, int position) {
        binding=holder.getBinding();
        binding.tvCertificateName.setText(item.getName());
        if(item.isSelect()){
            binding.tvCertificateName.setBackgroundResource(R.drawable.cer_major_child_text_yes_bg);
        }else{
            binding.tvCertificateName.setBackgroundResource(R.drawable.cer_major_child_text_bg);
        }
        binding.tvCertificateName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(updataCertificateListenter==null)return;
                updataCertificateListenter.updataUserBindItem(item.getId(),bindOrderType==1?true:false,selectType==2?false:true,String.valueOf(item.getId()).startsWith("2")?true:false);
            }
        });
    }
}
