package com.zizhiguanjia.model_certificate.adapter;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.view.ViewGroup;
import android.widget.TextView;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.listener.CertificateUpdateListener;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;

public class CertificateGroupAdapter extends BaseAdapter<SecondConstructionBean.GroupBean> {
    private CertificateUpdateListener certificateUpdateListener;
    /**
     * 当前选择的实体
     */
    private SecondConstructionBean.GroupBean currentSelectBean;

    public void setCertificateUpdateListener(CertificateUpdateListener certificateUpdateListener) {
        this.certificateUpdateListener = certificateUpdateListener;
    }

    public CertificateGroupAdapter() {
        super(R.layout.cer_item_list_certificate_group_select);
    }

    public void setCurrentSelectBean(SecondConstructionBean.GroupBean currentSelectBean) {
        if(this.currentSelectBean == null){
            this.currentSelectBean = currentSelectBean;
        }
    }

    public SecondConstructionBean.GroupBean getCurrentSelectBean() {
        return currentSelectBean;
    }

    @Override
    protected void bind(BaseViewHolder holder, SecondConstructionBean.GroupBean item, int position) {
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) holder.itemView.getLayoutParams();
        if(params == null ){
            params = new ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        params.leftMargin = DpUtils.dp2px(holder.itemView.getContext(),15);
        params.width = DpUtils.dp2px(holder.itemView.getContext(),70);
        params.rightMargin = position == getItemCount() - 1 ? DpUtils.dp2px(holder.itemView.getContext(),15) : 0;
        holder.itemView.setLayoutParams(params);
        TextView tvText = holder.itemView.findViewById(R.id.tv_text);
        tvText.setText(item.getName());
        if (currentSelectBean != null && currentSelectBean.getId() != null && currentSelectBean.getId().equals(item.getId())) {
            tvText.setTextColor(Color.WHITE);
            tvText.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor("#007AFF")));
        }else {
            tvText.setTextColor(Color.BLACK);
            tvText.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor("#F6F6F6")));
        }
        tvText.setOnClickListener(v -> {
            if(currentSelectBean == null || currentSelectBean.getId() != item.getId()){
                if (certificateUpdateListener != null) {
                    certificateUpdateListener.onChangeGroup(item);
                }
                currentSelectBean = item;
                notifyItemRangeChanged(0, getItemCount());
            }
        });
    }
}
