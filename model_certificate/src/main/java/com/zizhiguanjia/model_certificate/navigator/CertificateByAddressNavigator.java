package com.zizhiguanjia.model_certificate.navigator;

import com.zizhiguanjia.model_certificate.model.CertificateByAddressBean;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;

import java.util.List;

public interface CertificateByAddressNavigator {
    void showCertificateAddressView(List<CertificateByAddressBean.RecordsDTO> recordsDTOS);
    void initData();
    void initView();
    void initListener();
    void checkToPage();
    void showErrorToast(String msg);
    void showLoading();
    void showEmpty();
    void showNetWork();
    void showSecondContructions(List<SecondConstructionBean.GroupBean> parentMajors, List<SecondConstructionBean.RecordsBean> recordsBeans);
}
