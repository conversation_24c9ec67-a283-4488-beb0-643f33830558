package com.zizhiguanjia.model_certificate.navigator;

import com.zizhiguanjia.model_certificate.model.UpdataCertificateBean;

import java.util.List;

public interface ZzCertificateByAddressNabigator {
    void showContextView(List<UpdataCertificateBean.RecordsBean> Records);
    void initData();
    void initListener();
    void checkToPage();
    void showErrorView();
    void closeView();
    void showOrHindLoading(boolean b);
    void getCurrentAddressName(String name);
}
