package com.zizhiguanjia.model_certificate.api;

import io.reactivex.Observable;

import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.model_certificate.model.CertificateBean;
import com.zizhiguanjia.model_certificate.model.CertificateByAddressBean;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;
import com.zizhiguanjia.model_certificate.model.UpdataCertificateBean;
import com.zizhiguanjia.model_certificate.model.ZzCertificateByAddressBean;

import java.util.Map;

import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.QueryMap;

public interface CertificateServiceApi {
    @GET(BaseAPI.VERSION_DES+"/API/Examation/GetMajorList")
    Observable<BaseData<CertificateBean>> getCertificateDataList(@QueryMap Map<String,String> params);
//    Observable<BaseData<CertificateBean>> getCertificateDataList(@FieldMap Map<String,String> params);
    //SecondConstructionBean
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/common/GetMajorsByAreaId")
    Observable<BaseData<CertificateByAddressBean>> getCertificateByAddressDataList(@FieldMap Map<String,String> params);
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/common/GetUniMajorsByAreaId")
    Observable<BaseData<UpdataCertificateBean>> getCertificateByAddressDataLists(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/common/GetUniMajorsByAreaId")
    Observable<BaseData<SecondConstructionBean>> getSecondConstruction(@FieldMap Map<String,String> params);

    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/User/BindMajor")
    Observable<BaseData> bindCertificate(@FieldMap Map<String,String> params);

}
