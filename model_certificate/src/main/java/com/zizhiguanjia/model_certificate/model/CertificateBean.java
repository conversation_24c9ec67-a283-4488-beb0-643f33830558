package com.zizhiguanjia.model_certificate.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

public class CertificateBean implements Parcelable {

    private List<MajorsBean> Majors;
    public List<MajorsBean> getMajors() {
        return Majors;
    }
    public void setMajors(List<MajorsBean> Majors) {
        this.Majors = Majors;
    }
    public static class MajorsBean implements Parcelable {

        /**
         * MajorId : 1100
         * AreaId : 0
         * Name : 安全员
         * SubName : null
         * IsPublic : false
         * Childs : [{"MajorId":1101,"AreaId":0,"Name":"安全员A证","SubName":null,"IsPublic":false,"Childs":[{"MajorId":1101,"AreaId":-1,"Name":"安全员","SubName":"通用·A证","IsPublic":false,"Childs":[]},{"MajorId":1101,"AreaId":1,"Name":"安全员","SubName":"北京·A证","IsPublic":false,"Childs":[]}]},{"MajorId":1102,"AreaId":0,"Name":"安全员B证","SubName":null,"IsPublic":false,"Childs":[{"MajorId":1102,"AreaId":-1,"Name":"安全员","SubName":"通用·B证","IsPublic":false,"Childs":[]},{"MajorId":1102,"AreaId":1,"Name":"安全员","SubName":"北京·B证","IsPublic":false,"Childs":[]}]},{"MajorId":1106,"AreaId":0,"Name":"安全员C证","SubName":null,"IsPublic":false,"Childs":[{"MajorId":1106,"AreaId":-1,"Name":"安全员","SubName":"通用·C证","IsPublic":false,"Childs":[]},{"MajorId":1103,"AreaId":1,"Name":"安全员","SubName":"北京·C1证","IsPublic":false,"Childs":[]},{"MajorId":1104,"AreaId":1,"Name":"安全员","SubName":"北京·C2证","IsPublic":false,"Childs":[]},{"MajorId":1105,"AreaId":1,"Name":"安全员","SubName":"北京·C3证","IsPublic":false,"Childs":[]}]}]
         */

        private int MajorId;
        private int AreaId;
        private String Name;
        private String SubName;
        private boolean IsPublic;
        private List<ChildsBeanX> Childs;

        public int getMajorId() {
            return MajorId;
        }

        public void setMajorId(int MajorId) {
            this.MajorId = MajorId;
        }

        public int getAreaId() {
            return AreaId;
        }

        public void setAreaId(int AreaId) {
            this.AreaId = AreaId;
        }

        public String getName() {
            return Name;
        }

        public void setName(String Name) {
            this.Name = Name;
        }

        public String getSubName() {
            return SubName;
        }

        public void setSubName(String SubName) {
            this.SubName = SubName;
        }

        public boolean isIsPublic() {
            return IsPublic;
        }

        public void setIsPublic(boolean IsPublic) {
            this.IsPublic = IsPublic;
        }

        public List<ChildsBeanX> getChilds() {
            return Childs;
        }

        public void setChilds(List<ChildsBeanX> Childs) {
            this.Childs = Childs;
        }

        public static class ChildsBeanX {
            /**
             * MajorId : 1101
             * AreaId : 0
             * Name : 安全员A证
             * SubName : null
             * IsPublic : false
             * Childs : [{"MajorId":1101,"AreaId":-1,"Name":"安全员","SubName":"通用·A证","IsPublic":false,"Childs":[]},{"MajorId":1101,"AreaId":1,"Name":"安全员","SubName":"北京·A证","IsPublic":false,"Childs":[]}]
             */

            private int MajorId;
            private int AreaId;
            private String Name;
            private Object SubName;
            private boolean IsPublic;
            private List<ChildsBean> Childs;

            public int getMajorId() {
                return MajorId;
            }

            public void setMajorId(int MajorId) {
                this.MajorId = MajorId;
            }

            public int getAreaId() {
                return AreaId;
            }

            public void setAreaId(int AreaId) {
                this.AreaId = AreaId;
            }

            public String getName() {
                return Name;
            }

            public void setName(String Name) {
                this.Name = Name;
            }

            public Object getSubName() {
                return SubName;
            }

            public void setSubName(Object SubName) {
                this.SubName = SubName;
            }

            public boolean isIsPublic() {
                return IsPublic;
            }

            public void setIsPublic(boolean IsPublic) {
                this.IsPublic = IsPublic;
            }

            public List<ChildsBean> getChilds() {
                return Childs;
            }

            public void setChilds(List<ChildsBean> Childs) {
                this.Childs = Childs;
            }

            public static class ChildsBean implements Parcelable {

                /**
                 * MajorId : 1101
                 * AreaId : -1
                 * Name : 安全员
                 * SubName : 通用·A证
                 * IsPublic : false
                 * Childs : []
                 */

                private int MajorId;
                private int AreaId;
                private String Name;
                private String SubName;
                private boolean IsPublic;
                public int getMajorId() {
                    return MajorId;
                }

                public void setMajorId(int MajorId) {
                    this.MajorId = MajorId;
                }

                public int getAreaId() {
                    return AreaId;
                }

                public void setAreaId(int AreaId) {
                    this.AreaId = AreaId;
                }

                public String getName() {
                    return Name;
                }

                public void setName(String Name) {
                    this.Name = Name;
                }

                public String getSubName() {
                    return SubName;
                }

                public void setSubName(String SubName) {
                    this.SubName = SubName;
                }

                public boolean isIsPublic() {
                    return IsPublic;
                }

                public void setIsPublic(boolean IsPublic) {
                    this.IsPublic = IsPublic;
                }

                @Override
                public int describeContents() {
                    return 0;
                }

                @Override
                public void writeToParcel(Parcel dest, int flags) {
                    dest.writeInt(this.MajorId);
                    dest.writeInt(this.AreaId);
                    dest.writeString(this.Name);
                    dest.writeString(this.SubName);
                    dest.writeByte(this.IsPublic ? (byte) 1 : (byte) 0);
                }

                public ChildsBean() {
                }

                protected ChildsBean(Parcel in) {
                    this.MajorId = in.readInt();
                    this.AreaId = in.readInt();
                    this.Name = in.readString();
                    this.SubName = in.readString();
                    this.IsPublic = in.readByte() != 0;
                }

                public static final Creator<ChildsBean> CREATOR = new Creator<ChildsBean>() {
                    @Override
                    public ChildsBean createFromParcel(Parcel source) {
                        return new ChildsBean(source);
                    }

                    @Override
                    public ChildsBean[] newArray(int size) {
                        return new ChildsBean[size];
                    }
                };
            }
        }

        public MajorsBean() {
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(this.MajorId);
            dest.writeInt(this.AreaId);
            dest.writeString(this.Name);
            dest.writeString(this.SubName);
            dest.writeByte(this.IsPublic ? (byte) 1 : (byte) 0);
            dest.writeList(this.Childs);
        }

        protected MajorsBean(Parcel in) {
            this.MajorId = in.readInt();
            this.AreaId = in.readInt();
            this.Name = in.readString();
            this.SubName = in.readParcelable(Object.class.getClassLoader());
            this.IsPublic = in.readByte() != 0;
            this.Childs = new ArrayList<ChildsBeanX>();
            in.readList(this.Childs, ChildsBeanX.class.getClassLoader());
        }

        public static final Creator<MajorsBean> CREATOR = new Creator<MajorsBean>() {
            @Override
            public MajorsBean createFromParcel(Parcel source) {
                return new MajorsBean(source);
            }

            @Override
            public MajorsBean[] newArray(int size) {
                return new MajorsBean[size];
            }
        };
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeList(this.Majors);
    }

    public CertificateBean() {
    }

    protected CertificateBean(Parcel in) {
        this.Majors = new ArrayList<MajorsBean>();
        in.readList(this.Majors, MajorsBean.class.getClassLoader());
    }

    public static final Parcelable.Creator<CertificateBean> CREATOR = new Parcelable.Creator<CertificateBean>() {
        @Override
        public CertificateBean createFromParcel(Parcel source) {
            return new CertificateBean(source);
        }

        @Override
        public CertificateBean[] newArray(int size) {
            return new CertificateBean[size];
        }
    };
}
