package com.zizhiguanjia.model_certificate.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

public class SecondConstructionBean implements Parcelable {


    /**
     * DefaultArea : 北京、江苏、山东..
     * DefaultAreaId : 0
     * BindTip : 您可选择全部公共科目和任意1门实务开通vip
     * Records : [{"BindOrderType":2,"SelectType":1,"Id":1100,"Name":"安全员","Icon":null,"Childs":[{"BindOrderType":0,"SelectType":0,"Id":1101,"Name":"A证","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":1102,"Name":"B证","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":1106,"Name":"C证","Icon":null,"Childs":null}]},{"BindOrderType":2,"SelectType":0,"Id":2000,"Name":"二级建造师","Icon":null,"Childs":[{"BindOrderType":2,"SelectType":2,"Id":0,"Name":"公共科目","Icon":"http://apiapp.mayijianzhu.net/v5/images/xcx_anquanyuan/erjianggkmIcon.png","Childs":[{"BindOrderType":0,"SelectType":0,"Id":2035,"Name":"施工管理","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":2036,"Name":"工程法规","Icon":null,"Childs":null}]},{"BindOrderType":2,"SelectType":1,"Id":0,"Name":"专业科目","Icon":"http://apiapp.mayijianzhu.net/v5/images/xcx_anquanyuan/erjianzykmIcon.png","Childs":[{"BindOrderType":0,"SelectType":0,"Id":2037,"Name":"建筑工程","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":2038,"Name":"市政工程","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":2039,"Name":"机电工程","Icon":null,"Childs":null}]}]}]
     */

    private String DefaultArea;
    private int DefaultAreaId;
    private String BindTip;
    private List<RecordsBean> Records;
    private List<GroupBean> ParentMajors;
    private String DefaultMajor;
    private String DefaultMajorPid;

    public String getDefaultMajor() {
        return DefaultMajor;
    }

    public void setDefaultMajor(String defaultMajor) {
        DefaultMajor = defaultMajor;
    }

    public String getDefaultMajorPid() {
        return DefaultMajorPid;
    }

    public void setDefaultMajorPid(String defaultMajorPid) {
        DefaultMajorPid = defaultMajorPid;
    }

    public List<GroupBean> getParentMajors() {
        return ParentMajors;
    }

    public void setParentMajors(List<GroupBean> parentMajors) {
        ParentMajors = parentMajors;
    }

    public String getDefaultArea() {
        return DefaultArea;
    }

    public void setDefaultArea(String DefaultArea) {
        this.DefaultArea = DefaultArea;
    }

    public int getDefaultAreaId() {
        return DefaultAreaId;
    }

    public void setDefaultAreaId(int DefaultAreaId) {
        this.DefaultAreaId = DefaultAreaId;
    }

    public String getBindTip() {
        return BindTip;
    }

    public void setBindTip(String BindTip) {
        this.BindTip = BindTip;
    }

    public List<RecordsBean> getRecords() {
        return Records;
    }

    public void setRecords(List<RecordsBean> Records) {
        this.Records = Records;
    }

    public static class RecordsBean implements Parcelable {
        /**
         * BindOrderType : 2
         * SelectType : 1
         * Id : 1100
         * Name : 安全员
         * Icon : null
         * Childs : [{"BindOrderType":0,"SelectType":0,"Id":1101,"Name":"A证","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":1102,"Name":"B证","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":1106,"Name":"C证","Icon":null,"Childs":null}]
         */

        private int BindOrderType;
        private int SelectType;
        private int Id;
        private String Name;
        private String Icon;
        private String ParentId;
        private Object IsSelectMajor;
        private List<ChildsBean> Childs;

        public String getParentId() {
            return ParentId;
        }

        public void setParentId(String parentId) {
            ParentId = parentId;
        }

        public Object getIsSelectMajor() {
            return IsSelectMajor;
        }

        public void setIsSelectMajor(Object isSelectMajor) {
            IsSelectMajor = isSelectMajor;
        }
        public String getIcon() {
            return Icon;
        }

        public void setIcon(String icon) {
            Icon = icon;
        }

        public int getBindOrderType() {
            return BindOrderType;
        }

        public void setBindOrderType(int BindOrderType) {
            this.BindOrderType = BindOrderType;
        }

        public int getSelectType() {
            return SelectType;
        }

        public void setSelectType(int SelectType) {
            this.SelectType = SelectType;
        }

        public int getId() {
            return Id;
        }

        public void setId(int Id) {
            this.Id = Id;
        }

        public String getName() {
            return Name;
        }

        public void setName(String Name) {
            this.Name = Name;
        }



        public List<ChildsBean> getChilds() {
            return Childs;
        }

        public void setChilds(List<ChildsBean> Childs) {
            this.Childs = Childs;
        }

        public static class ChildsBean implements Parcelable {
            /**
             * BindOrderType : 0
             * SelectType : 0
             * Id : 1101
             * Name : A证
             * Icon : null
             * Childs : null
             */

            private int BindOrderType;
            private int SelectType;
            private int Id;
            private int ParentId;
            private String Name;
            private String Icon;
            private List<ChildsBean> Childs;

            public void setParentId(int parentId) {
                ParentId = parentId;
            }

            public int getParentId() {
                return ParentId;
            }

            public int getBindOrderType() {
                return BindOrderType;
            }

            public void setBindOrderType(int BindOrderType) {
                this.BindOrderType = BindOrderType;
            }

            public int getSelectType() {
                return SelectType;
            }

            public void setSelectType(int SelectType) {
                this.SelectType = SelectType;
            }

            public int getId() {
                return Id;
            }

            public void setId(int Id) {
                this.Id = Id;
            }

            public String getName() {
                return Name;
            }

            public void setName(String Name) {
                this.Name = Name;
            }

            public String getIcon() {
                return Icon;
            }

            public void setIcon(String icon) {
                Icon = icon;
            }

            public List<ChildsBean> getChilds() {
                return Childs;
            }

            public void setChilds(List<ChildsBean> childs) {
                Childs = childs;
            }

            @Override
            public int describeContents() {
                return 0;
            }

            @Override
            public void writeToParcel(Parcel dest, int flags) {
                dest.writeInt(this.BindOrderType);
                dest.writeInt(this.SelectType);
                dest.writeInt(this.Id);
                dest.writeString(this.Name);
                dest.writeString(this.Icon);
                dest.writeList(this.Childs);
            }

            public void readFromParcel(Parcel source) {
                this.BindOrderType = source.readInt();
                this.SelectType = source.readInt();
                this.Id = source.readInt();
                this.Name = source.readString();
                this.Icon = source.readString();
                this.Childs = new ArrayList<ChildsBean>();
                source.readList(this.Childs, ChildsBean.class.getClassLoader());
            }

            public ChildsBean() {
            }

            protected ChildsBean(Parcel in) {
                this.BindOrderType = in.readInt();
                this.SelectType = in.readInt();
                this.Id = in.readInt();
                this.Name = in.readString();
                this.Icon = in.readString();
                this.Childs = new ArrayList<ChildsBean>();
                in.readList(this.Childs, ChildsBean.class.getClassLoader());
            }

            public static final Creator<ChildsBean> CREATOR = new Creator<ChildsBean>() {
                @Override
                public ChildsBean createFromParcel(Parcel source) {
                    return new ChildsBean(source);
                }

                @Override
                public ChildsBean[] newArray(int size) {
                    return new ChildsBean[size];
                }
            };
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(this.BindOrderType);
            dest.writeInt(this.SelectType);
            dest.writeInt(this.Id);
            dest.writeString(this.Name);
            dest.writeString(this.Icon);
            dest.writeTypedList(this.Childs);
        }

        public void readFromParcel(Parcel source) {
            this.BindOrderType = source.readInt();
            this.SelectType = source.readInt();
            this.Id = source.readInt();
            this.Name = source.readString();
            this.Icon = source.readString();
            this.Childs = source.createTypedArrayList(ChildsBean.CREATOR);
        }

        public RecordsBean() {
        }

        protected RecordsBean(Parcel in) {
            this.BindOrderType = in.readInt();
            this.SelectType = in.readInt();
            this.Id = in.readInt();
            this.Name = in.readString();
            this.Icon = in.readString();
            this.Childs = in.createTypedArrayList(ChildsBean.CREATOR);
        }

        public static final Creator<RecordsBean> CREATOR = new Creator<RecordsBean>() {
            @Override
            public RecordsBean createFromParcel(Parcel source) {
                return new RecordsBean(source);
            }

            @Override
            public RecordsBean[] newArray(int size) {
                return new RecordsBean[size];
            }
        };
    }

    public static class GroupBean {
        private Integer ParentId;
        private Integer Id;
        private String Name;

        public Integer getParentId() {
            return ParentId;
        }

        public void setParentId(Integer parentId) {
            ParentId = parentId;
        }

        public Integer getId() {
            return Id;
        }

        public void setId(Integer id) {
            Id = id;
        }

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            Name = name;
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.DefaultArea);
        dest.writeInt(this.DefaultAreaId);
        dest.writeString(this.BindTip);
        dest.writeTypedList(this.Records);
    }

    public void readFromParcel(Parcel source) {
        this.DefaultArea = source.readString();
        this.DefaultAreaId = source.readInt();
        this.BindTip = source.readString();
        this.Records = source.createTypedArrayList(RecordsBean.CREATOR);
    }

    public SecondConstructionBean() {
    }

    protected SecondConstructionBean(Parcel in) {
        this.DefaultArea = in.readString();
        this.DefaultAreaId = in.readInt();
        this.BindTip = in.readString();
        this.Records = in.createTypedArrayList(RecordsBean.CREATOR);
    }

    public static final Creator<SecondConstructionBean> CREATOR = new Creator<SecondConstructionBean>() {
        @Override
        public SecondConstructionBean createFromParcel(Parcel source) {
            return new SecondConstructionBean(source);
        }

        @Override
        public SecondConstructionBean[] newArray(int size) {
            return new SecondConstructionBean[size];
        }
    };
}
