package com.zizhiguanjia.model_certificate.model;

import java.util.List;

public class ZzCertificateByAddressBean {

    private List<RecordsBean> Records;

    public List<RecordsBean> getRecords() {
        return Records;
    }

    public void setRecords(List<RecordsBean> Records) {
        this.Records = Records;
    }

    public static class RecordsBean {
        /**
         * Id : 1100
         * Name : 安全员
         * Icon : null
         * Childs : [{"Id":1101,"Name":"A证","Icon":null,"Childs":null},{"Id":1102,"Name":"B证","Icon":null,"Childs":null},{"Id":1106,"Name":"C证","Icon":null,"Childs":null}]
         */

        private int Id;
        private String Name;
        private String Icon;
        private List<ChildsBean> Childs;

        public int getId() {
            return Id;
        }

        public void setId(int Id) {
            this.Id = Id;
        }

        public String getName() {
            return Name;
        }

        public void setName(String Name) {
            this.Name = Name;
        }

        public String getIcon() {
            return Icon;
        }

        public void setIcon(String Icon) {
            this.Icon = Icon;
        }

        public List<ChildsBean> getChilds() {
            return Childs;
        }

        public void setChilds(List<ChildsBean> Childs) {
            this.Childs = Childs;
        }

        public static class ChildsBean {
            /**
             * Id : 1101
             * Name : A证
             * Icon : null
             * Childs : null
             */

            private int Id;
            private String Name;
            private String Icon;
            private List<ChildsBean> Childs;

            public int getId() {
                return Id;
            }

            public void setId(int Id) {
                this.Id = Id;
            }

            public String getName() {
                return Name;
            }

            public void setName(String Name) {
                this.Name = Name;
            }

            public String getIcon() {
                return Icon;
            }

            public void setIcon(String Icon) {
                this.Icon = Icon;
            }

            public List<ChildsBean> getChilds() {
                return Childs;
            }

            public void setChilds(List<ChildsBean> Childs) {
                this.Childs = Childs;
            }
        }
    }
}
