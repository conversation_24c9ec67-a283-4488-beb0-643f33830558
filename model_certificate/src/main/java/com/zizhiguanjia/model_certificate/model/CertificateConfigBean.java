package com.zizhiguanjia.model_certificate.model;

import android.os.Parcel;
import android.os.Parcelable;

public class CertificateConfigBean{
    private int classflyId;
    private int subjectId;
    private String subjectName;
    private String cityCode;
    private String cityName;
    private String des;
    private String groupId;

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    @Override
    public String toString() {
        return "CertificateConfigBean{" +
                "classflyId=" + classflyId +
                ", subjectId=" + subjectId +
                ", subjectName='" + subjectName + '\'' +
                ", cityCode='" + cityCode + '\'' +
                ", cityName='" + cityName + '\'' +
                ", des='" + des + '\'' +
                '}';
    }

    public int getClassflyId() {
        return classflyId;
    }

    public void setClassflyId(int classflyId) {
        this.classflyId = classflyId;
    }

    public int getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(int subjectId) {
        this.subjectId = subjectId;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
    //    private int classFlyId;
//    private int subjuectId;
//    private String name;
//    private String des;
//    private String cityCode;
//
//    public int getClassFlyId() {
//        return classFlyId;
//    }
//
//    public void setClassFlyId(int classFlyId) {
//        this.classFlyId = classFlyId;
//    }
//
//    public int getSubjuectId() {
//        return subjuectId;
//    }
//
//    public void setSubjuectId(int subjuectId) {
//        this.subjuectId = subjuectId;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public String getDes() {
//        return des;
//    }
//
//    public void setDes(String des) {
//        this.des = des;
//    }
//
//    public String getCityCode() {
//        return cityCode;
//    }
//
//    public void setCityCode(String cityCode) {
//        this.cityCode = cityCode;
//    }
//
//    @Override
//    public int describeContents() {
//        return 0;
//    }
//
//    @Override
//    public void writeToParcel(Parcel dest, int flags) {
//        dest.writeInt(this.classFlyId);
//        dest.writeInt(this.subjuectId);
//        dest.writeString(this.name);
//        dest.writeString(this.des);
//        dest.writeString(this.cityCode);
//    }
//
//    public CertificateConfigBean() {
//    }
//
//    protected CertificateConfigBean(Parcel in) {
//        this.classFlyId = in.readInt();
//        this.subjuectId = in.readInt();
//        this.name = in.readString();
//        this.des = in.readString();
//        this.cityCode = in.readString();
//    }
//
//    public static final Parcelable.Creator<CertificateConfigBean> CREATOR = new Parcelable.Creator<CertificateConfigBean>() {
//        @Override
//        public CertificateConfigBean createFromParcel(Parcel source) {
//            return new CertificateConfigBean(source);
//        }
//
//        @Override
//        public CertificateConfigBean[] newArray(int size) {
//            return new CertificateConfigBean[size];
//        }
//    };
}
