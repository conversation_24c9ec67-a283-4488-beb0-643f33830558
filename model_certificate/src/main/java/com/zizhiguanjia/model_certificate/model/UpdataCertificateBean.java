package com.zizhiguanjia.model_certificate.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

public class UpdataCertificateBean implements Parcelable {

    /**
     * DefaultArea : 北京、江苏、山东..
     * DefaultAreaId : 0
     * BindTip :
     * Records : [{"BindOrderType":0,"SelectType":1,"Id":1100,"Name":"安全员","Icon":null,"Childs":[{"BindOrderType":0,"SelectType":0,"Id":1101,"Name":"A证","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":1102,"Name":"B证","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":1106,"Name":"C证","Icon":null,"Childs":null}]},{"BindOrderType":0,"SelectType":0,"Id":2000,"Name":"二级建造师","Icon":null,"Childs":[{"BindOrderType":0,"SelectType":2,"Id":0,"Name":"公共科目","Icon":"http://apiapp.mayijianzhu.net/v5/images/xcx_anquanyuan/erjianggkmIcon.png","Childs":[{"BindOrderType":0,"SelectType":0,"Id":2035,"Name":"施工管理","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":2036,"Name":"工程法规","Icon":null,"Childs":null}]},{"BindOrderType":0,"SelectType":1,"Id":0,"Name":"专业科目","Icon":"http://apiapp.mayijianzhu.net/v5/images/xcx_anquanyuan/erjianzykmIcon.png","Childs":[{"BindOrderType":0,"SelectType":0,"Id":2037,"Name":"建筑工程","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":2038,"Name":"市政工程","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":2039,"Name":"机电工程","Icon":null,"Childs":null}]}]}]
     */

    private String DefaultArea;
    private int DefaultAreaId;
    private String BindTip;
    private List<RecordsBean> Records;

    public String getDefaultArea() {
        return DefaultArea;
    }

    public void setDefaultArea(String DefaultArea) {
        this.DefaultArea = DefaultArea;
    }

    public int getDefaultAreaId() {
        return DefaultAreaId;
    }

    public void setDefaultAreaId(int DefaultAreaId) {
        this.DefaultAreaId = DefaultAreaId;
    }

    public String getBindTip() {
        return BindTip;
    }

    public void setBindTip(String BindTip) {
        this.BindTip = BindTip;
    }

    public List<RecordsBean> getRecords() {
        return Records;
    }

    public void setRecords(List<RecordsBean> Records) {
        this.Records = Records;
    }

    public static class RecordsBean implements Parcelable {
        /**
         * BindOrderType : 0
         * SelectType : 1
         * Id : 1100
         * Name : 安全员
         * Icon : null
         * Childs : [{"BindOrderType":0,"SelectType":0,"Id":1101,"Name":"A证","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":1102,"Name":"B证","Icon":null,"Childs":null},{"BindOrderType":0,"SelectType":0,"Id":1106,"Name":"C证","Icon":null,"Childs":null}]
         */

        private int BindOrderType;
        private int SelectType;
        private int Id;
        private String Name;
        private String BindTip;
        private String Icon;

        public String getBindTip() {
            return BindTip;
        }

        public void setBindTip(String bindTip) {
            BindTip = bindTip;
        }

        private List<ChildsBean> Childs;

        public int getBindOrderType() {
            return BindOrderType;
        }

        public void setBindOrderType(int BindOrderType) {
            this.BindOrderType = BindOrderType;
        }

        public int getSelectType() {
            return SelectType;
        }

        public void setSelectType(int SelectType) {
            this.SelectType = SelectType;
        }

        public int getId() {
            return Id;
        }

        public void setId(int Id) {
            this.Id = Id;
        }

        public String getName() {
            return Name;
        }

        public void setName(String Name) {
            this.Name = Name;
        }

        public String getIcon() {
            return Icon;
        }

        public void setIcon(String Icon) {
            this.Icon = Icon;
        }

        public List<ChildsBean> getChilds() {
            return Childs;
        }

        public void setChilds(List<ChildsBean> Childs) {
            this.Childs = Childs;
        }

        public static class ChildsBean {
            /**
             * BindOrderType : 0
             * SelectType : 0
             * Id : 1101
             * Name : A证
             * Icon : null
             * Childs : null
             */

            private int BindOrderType;
            private int SelectType;
            private int Id;
            private String Name;
            private String Icon;
            private List<ChildsBean> Childs;
            private boolean isSelect=false;

            public boolean isSelect() {
                return isSelect;
            }

            public void setSelect(boolean select) {
                isSelect = select;
            }

            public int getBindOrderType() {
                return BindOrderType;
            }

            public void setBindOrderType(int BindOrderType) {
                this.BindOrderType = BindOrderType;
            }

            public int getSelectType() {
                return SelectType;
            }

            public void setSelectType(int SelectType) {
                this.SelectType = SelectType;
            }

            public int getId() {
                return Id;
            }

            public void setId(int Id) {
                this.Id = Id;
            }

            public String getName() {
                return Name;
            }

            public void setName(String Name) {
                this.Name = Name;
            }

            public String getIcon() {
                return Icon;
            }

            public void setIcon(String icon) {
                Icon = icon;
            }

            public List<ChildsBean> getChilds() {
                return Childs;
            }

            public void setChilds(List<ChildsBean> childs) {
                Childs = childs;
            }
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(this.BindOrderType);
            dest.writeInt(this.SelectType);
            dest.writeInt(this.Id);
            dest.writeString(this.Name);
            dest.writeString(this.Icon);
            dest.writeList(this.Childs);
        }

        public void readFromParcel(Parcel source) {
            this.BindOrderType = source.readInt();
            this.SelectType = source.readInt();
            this.BindTip=source.readString();
            this.Id = source.readInt();
            this.Name = source.readString();
            this.Icon = source.readString();
            this.Childs = new ArrayList<ChildsBean>();
            source.readList(this.Childs, ChildsBean.class.getClassLoader());
        }

        public RecordsBean() {
        }

        protected RecordsBean(Parcel in) {
            this.BindOrderType = in.readInt();
            this.SelectType = in.readInt();
            this.Id = in.readInt();
            this.Name = in.readString();
            this.Icon = in.readString();
            this.BindTip=in.readString();
            this.Childs = new ArrayList<ChildsBean>();
            in.readList(this.Childs, ChildsBean.class.getClassLoader());
        }

        public static final Parcelable.Creator<RecordsBean> CREATOR = new Parcelable.Creator<RecordsBean>() {
            @Override
            public RecordsBean createFromParcel(Parcel source) {
                return new RecordsBean(source);
            }

            @Override
            public RecordsBean[] newArray(int size) {
                return new RecordsBean[size];
            }
        };
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.DefaultArea);
        dest.writeInt(this.DefaultAreaId);
        dest.writeString(this.BindTip);
        dest.writeTypedList(this.Records);
    }

    public void readFromParcel(Parcel source) {
        this.DefaultArea = source.readString();
        this.DefaultAreaId = source.readInt();
        this.BindTip = source.readString();
        this.Records = source.createTypedArrayList(RecordsBean.CREATOR);
    }

    public UpdataCertificateBean() {
    }

    protected UpdataCertificateBean(Parcel in) {
        this.DefaultArea = in.readString();
        this.DefaultAreaId = in.readInt();
        this.BindTip = in.readString();
        this.Records = in.createTypedArrayList(RecordsBean.CREATOR);
    }

    public static final Parcelable.Creator<UpdataCertificateBean> CREATOR = new Parcelable.Creator<UpdataCertificateBean>() {
        @Override
        public UpdataCertificateBean createFromParcel(Parcel source) {
            return new UpdataCertificateBean(source);
        }

        @Override
        public UpdataCertificateBean[] newArray(int size) {
            return new UpdataCertificateBean[size];
        }
    };
}
