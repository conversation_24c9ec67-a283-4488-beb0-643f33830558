package com.zizhiguanjia.model_certificate.listener;

import android.view.View;

import com.zizhiguanjia.lib_base.listeners.CertificateJsonByAddressListener;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;
import com.zizhiguanjia.model_certificate.navigator.CertificateByAddressNavigator;

public interface ICertificateByAddress {
    void initParams(CertificateByAddressNavigator certificateByAddressNavigator, String addressId);
    void getCertificatetByAddressData(String addressId);
    void getCertificatetByAddressData(String addressId, SecondConstructionBean.GroupBean bean);
    void getCertificatetByAddressData(String addressId, CertificateJsonByAddressListener certificateJsonByAddressListener);
    void onclick(View view);

}
