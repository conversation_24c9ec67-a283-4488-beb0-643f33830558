package com.zizhiguanjia.model_certificate.listener;

import com.zizhiguanjia.model_certificate.model.CertificateConfigBean;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;

public interface CertificateUpdateListener {
    void onUpdateCertificate(CertificateConfigBean certificateConfigBean);
    /**
     * 组改变
     * @param bean 改变后的组
     */
    void onChangeGroup(SecondConstructionBean.GroupBean bean);

    void showMoreChild(String groupId, String groupName, SecondConstructionBean.RecordsBean.ChildsBean majorChildBean);
}
