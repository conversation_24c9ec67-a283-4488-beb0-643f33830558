package com.zizhiguanjia.model_certificate.manager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.db.AppDatabase;
import com.zizhiguanjia.lib_base.db.CertificateDao;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.listeners.CertificateDataListener;
import com.zizhiguanjia.lib_base.msgconfig.UserType;
import com.zizhiguanjia.lib_base.tb.TableCertificate;
import com.zizhiguanjia.lib_base.tb.TableSetConfigInfo;
import com.zizhiguanjia.model_certificate.config.CertificateConfig;
import com.zizhiguanjia.model_certificate.config.ClassFLyTypeConfig;
import com.zizhiguanjia.model_certificate.help.CertificateHelper;
import com.zizhiguanjia.model_certificate.model.CertificateBean;
import com.zizhiguanjia.model_certificate.model.CertificateConfigBean;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

public class CertificateManager {
    private static CertificateManager certificateManager;
    public static CertificateManager getInstance(){
        if(certificateManager==null){
            synchronized (CertificateManager.class){
                return certificateManager=new CertificateManager();
            }
        }
        return certificateManager;
    }
    public void getCertificateData(CertificateDataListener certificateDataListener){
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        RequestBody multiBody=multiBuilder.build();
        CertificateHelper.getInstance().getCertificateDatas(BaseAPI.VERSION_DES+"/API/Examation/GetMajors", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if(response.code()==200){
                    String jsonStr=response.body().string();
                    Type jsonType = new TypeToken<BaseData<CertificateBean>>() {}.getType();
                    BaseData<CertificateBean> commonExamBeanBaseData= new Gson().fromJson(jsonStr,jsonType);
                    if(commonExamBeanBaseData.Code.equals(BaseConfig.SUCCESS_CODE)){
                        if(commonExamBeanBaseData.Data==null||commonExamBeanBaseData.Data.getMajors()==null||commonExamBeanBaseData.Data.getMajors().size()==0)return;
                        String jsonStrs=GsonUtils.gsonString(commonExamBeanBaseData.Data.getMajors());
                        certificateDataListener.onPostCertificateData(jsonStrs);
                    }
                }
            }
        });
    }
    public TableCertificate getCertificateByAccount(String account){
        return AppDatabase.getInstance().certificateDao().getCertificateByAccount(account);
    }
    public TableCertificate getDefaultCertificata(){
        String account=AccountHelper.getCurrentLoginAccount();
        String jsonMaj=ConfigHelper.getDafalutCertificata();
        Map<String,String> params=GsonUtils.gsonToMaps(jsonMaj);
        TableCertificate tableCertificate=new TableCertificate();
        tableCertificate.setCityCode(params.get("cityCode"));
        tableCertificate.setCityName(params.get("cityName"));
        tableCertificate.setCertificateName(params.get("majName"));
        tableCertificate.setCertificateId(params.get("majId"));
        tableCertificate.setUserAccount(account);
        return  tableCertificate;
    }
    public boolean userChoiceCertificate(){
        String account=AccountHelper.getCurrentLoginAccount();
        TableCertificate tableCertificate=getCertificateByAccount(account);
        if(tableCertificate==null)return false;
        if(tableCertificate.getCityCode()==null||tableCertificate.getCityCode().equals("")||StringUtils.isEmpty(tableCertificate.getCityCode()))return false;
        if(tableCertificate.getCityCode().equals("0")||tableCertificate.getCertificateId().equals("0"))return false;
        return true;
    }
    public String initCertificate(boolean loginSuccess){
        String account=AccountHelper.getCurrentLoginAccount();
        if(loginSuccess){
            //查询游客模式
            TableCertificate tableCertificate1=AppDatabase.getInstance().certificateDao().getCertificateByAccount(String.valueOf(UserType.USER_TYPE_VISITOR));
            if(tableCertificate1==null){
            }else {
                boolean add=false;
                TableCertificate tableCertificate=AppDatabase.getInstance().certificateDao().getCertificateByAccount(account);
                if(tableCertificate==null){
                    add=true;
                    tableCertificate=new TableCertificate();
                    tableCertificate.setUserAccount(account);
                    tableCertificate.setCityName(tableCertificate1.getCityName());
                    tableCertificate.setCityCode(tableCertificate1.getCityCode());
                    tableCertificate.setCertificateName(tableCertificate1.getCertificateName());
                    tableCertificate.setCertificateId(tableCertificate1.getCertificateId());
                }
                if(add){
                    AppDatabase.getInstance().certificateDao().addCertificate(tableCertificate);
                }else {
                    AppDatabase.getInstance().certificateDao().updataCertificate(tableCertificate);
                }
            }
        }else {
            TableCertificate tableCertificate=AppDatabase.getInstance().certificateDao().getCertificateByAccount(account);
            if(tableCertificate==null){
                LogUtils.e("获取到的证书为空");
                AppDatabase.getInstance().certificateDao().addCertificate(getDefaultCertificata());
            }else {
                LogUtils.e("获取到的证书不为空");
            }
        }
        return updateDes();
    }
    public String exitAccountCertificate(){
        String account=AccountHelper.getCurrentLoginAccount();
        TableCertificate tableCertificate=AppDatabase.getInstance().certificateDao().getCertificateByAccount(account);
        if(tableCertificate==null){
            AppDatabase.getInstance().certificateDao().addCertificate(getDefaultCertificata());
        }else {
            tableCertificate.setCertificateId(getDefaultCertificata().getCertificateId());
            tableCertificate.setCertificateName(getDefaultCertificata().getCertificateName());
            tableCertificate.setCityCode(getDefaultCertificata().getCityCode());
            tableCertificate.setCityName(getDefaultCertificata().getCityName());
            AppDatabase.getInstance().certificateDao().updataCertificate(tableCertificate);
        }
        return updateDes();
    }
    public String updateDes(){
        String account=AccountHelper.getCurrentLoginAccount();
        TableCertificate tableCertificate2=AppDatabase.getInstance().certificateDao().getCertificateByAccount(account);
        // Add null check to prevent NullPointerException
//        if (tableCertificate2 == null) {
//            // If certificate is null, add default certificate and try again
//            tableCertificate2 = getDefaultCertificata();
//            AppDatabase.getInstance().certificateDao().addCertificate(tableCertificate2);
//        }
        BaseConfig.majId=tableCertificate2.getCertificateId();
        BaseConfig.cityCode=tableCertificate2.getCityCode();
        KvUtils.save("currentName", tableCertificate2.getCityName()+"-"+tableCertificate2.getCertificateName());
        String des=KvUtils.get("currentName","");//tableCertificate2.getDes();
        if(StringUtils.isEmpty(des)){
            return "正在加载中...";
        }else {
            return des;
        }
    }
    public String getCurrentCitificateAddressName(){
        String account=AccountHelper.getCurrentLoginAccount();
        TableCertificate tableCertificate2=AppDatabase.getInstance().certificateDao().getCertificateByAccount(account);
        return tableCertificate2.getCityName();
    }
    public String getCurrentCitificateName(){
        String account=AccountHelper.getCurrentLoginAccount();
        TableCertificate tableCertificate2=AppDatabase.getInstance().certificateDao().getCertificateByAccount(account);
        if(tableCertificate2==null){
            return "";
        }
        return tableCertificate2.getCertificateId();
    }
    public String addCertificateOrUpdataCertificate(TableCertificate tableCertificate,boolean add){
        LogUtils.e("即将更新---->>>"+tableCertificate.toString()+"****"+add);
        if(add){
            if(tableCertificate==null){
                tableCertificate=getDefaultCertificata();
                AppDatabase.getInstance().certificateDao().addCertificate(tableCertificate);
            }else {
                TableCertificate tableCertificate1=AppDatabase.getInstance().certificateDao().getCertificateByAccount(tableCertificate.getUserAccount());
                if(tableCertificate1==null){
                    AppDatabase.getInstance().certificateDao().addCertificate(tableCertificate);
                }else {
                    AppDatabase.getInstance().certificateDao().updataCertificate(tableCertificate);
                }

            }
        }else {
            AppDatabase.getInstance().certificateDao().updataCertificate(tableCertificate);
        }
        return updateDes();
    }
    public void updataCertificate(CertificateConfigBean certificateConfigBean){
        updataCertificate(certificateConfigBean.getCityCode(),certificateConfigBean.getCityName(),String.valueOf(certificateConfigBean.getSubjectId()),certificateConfigBean.getSubjectName(),false);
    }
    public void updataCertificate(TableCertificate tableCertificate){
        updataCertificate(tableCertificate.getCityCode(),tableCertificate.getCityName(),String.valueOf(tableCertificate.getCertificateId()),tableCertificate.getCertificateName(),tableCertificate.isVip());
    }
    public void updataCertificate(String cityCode,String cityName,String majId,String majName,boolean isVip){
        String account=AccountHelper.getCurrentLoginAccount();
        TableCertificate tableCertificate1=getCertificateByAccount(account);
        if(tableCertificate1==null){
            TableCertificate tableCertificate=new TableCertificate();
            tableCertificate.setUserAccount(account);
            tableCertificate.setCertificateId(majId);
            tableCertificate.setCertificateName(majName);
            tableCertificate.setCityCode(cityCode);
            tableCertificate.setCityName(cityName);
            tableCertificate.setVip(isVip);
            addCertificateOrUpdataCertificate(tableCertificate,true);
        }else {
            tableCertificate1.setCityName(cityName);
            tableCertificate1.setCityCode(cityCode);
            tableCertificate1.setCertificateName(majName);
            tableCertificate1.setCertificateId(majId);
            tableCertificate1.setVip(isVip);
            addCertificateOrUpdataCertificate(tableCertificate1,false);
        }
    }
    public boolean getCertificateState(String account){
        try {
            return AppDatabase.getInstance().certificateDao().getCertificateStateByAccount(account);
        }catch (Exception e){
            return false;
        }
    }
    public boolean isAqy(){
        String account=AccountHelper.getCurrentLoginAccount();
        TableCertificate tableCertificate=getCertificateByAccount(account);
        if(tableCertificate==null)return true;
        if(tableCertificate.getCertificateId().startsWith("1")){
            return true;
        }else {
            return false;
        }
    }
    public boolean getCurrentType(){
        String account=AccountHelper.getCurrentLoginAccount();
        TableCertificate tableCertificate=getCertificateByAccount(account);
        if(tableCertificate==null)return true;
        LogUtils.e("看看证书id"+tableCertificate.getCertificateId());
        if(tableCertificate.getCertificateId().startsWith("2")){
            return false;
        }else {
            return true;
        }
    }
    public Map<String,String> getCurrentCertificate(){
        Map<String,String> maps=new HashMap<>();
        try {
            String account=AccountHelper.getCurrentLoginAccount();
            TableCertificate tableCertificate=getCertificateByAccount(account);
            String addressId=tableCertificate.getCityCode();
            String citificateId=tableCertificate.getCertificateId();
            maps.put("addressId",addressId);
            maps.put("citificateId",citificateId);
        }catch (Exception e){
        }

        return maps;
    }
}
