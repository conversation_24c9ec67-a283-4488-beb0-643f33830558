package com.zizhiguanjia.model_certificate.ui;

import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.ContainerActivity;
import com.zizhiguanjia.lib_base.constants.CertificateRouterPath;
import com.zizhiguanjia.lib_base.constants.VideoRouterPath;
import com.zizhiguanjia.model_certificate.fragment.CertificateByAddressFragment;
import com.zizhiguanjia.model_certificate.fragment.ZzCertificateByAddressFragment;
@Route(path = CertificateRouterPath.MAIN_ACTIVITY)
public class CertificateActivity extends ContainerActivity {
    @Autowired(name = "addressId")
    public String addressId;
    @Autowired(name = "addressName")
    public String addressName;
    @Autowired(name = "tgAddress")
    public boolean tgAddress;
    @Autowired(name = "type")
    public int type;
    @Override
    public Fragment initBaseFragment() {
        ARouter.getInstance().inject(this);
        return CertificateByAddressFragment.getInstance(addressName,addressId,tgAddress);
//        return new ZzCertificateByAddressFragment();
    }
}
