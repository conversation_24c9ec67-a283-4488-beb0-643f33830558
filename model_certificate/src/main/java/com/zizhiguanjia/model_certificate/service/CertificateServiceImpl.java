package com.zizhiguanjia.model_certificate.service;

import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.constants.CertificateRouterPath;
import com.zizhiguanjia.lib_base.constants.DataRouterPath;
import com.zizhiguanjia.lib_base.constants.VideoRouterPath;
import com.zizhiguanjia.lib_base.listeners.CertificateDataListener;
import com.zizhiguanjia.lib_base.listeners.CertificateJsonByAddressListener;
import com.zizhiguanjia.lib_base.service.CertificateService;
import com.zizhiguanjia.lib_base.tb.TableCertificate;
import com.zizhiguanjia.model_certificate.manager.CertificateManager;
import com.zizhiguanjia.model_certificate.viewmodel.CertificateByAddressViewModel;
import com.zizhiguanjia.model_certificate.viewmodel.CertificateViewModel;

import java.util.Map;

@Route(path = CertificateRouterPath.SERVICE)
public class CertificateServiceImpl implements CertificateService {

    @Override
    public IFragment startChoseCertificateByAddress() {
        return ARouterUtils.navFragment(CertificateRouterPath.CHOSEADDRESSBYADDRESS_FRAGMENT);
    }

    @Override
    public IFragment startChoseCertificateZzByAddress() {
        return ARouterUtils.navFragment(CertificateRouterPath.CHOSEADDRESSBYADDRESSZZ_FRAGMENT);
    }

    @Override
    public String initCertificate(boolean loginSuccess) {
       return CertificateManager.getInstance().initCertificate(loginSuccess);
    }

    @Override
    public void updateCertificate(String cityCode,String cityName,String majId,String majName,String des) {
        CertificateManager.getInstance().updataCertificate(cityCode, cityName, majId, majName,false);
    }

    @Override
    public boolean userChoiceCertificate() {
        return CertificateManager.getInstance().userChoiceCertificate();
    }

    @Override
    public String exitAccountCertificate() {
        return CertificateManager.getInstance().exitAccountCertificate();
    }

    @Override
    public TableCertificate getUserCertificate(String account) {
        return CertificateManager.getInstance().getCertificateByAccount(account);
    }

    @Override
    public boolean getCertificateState(String account) {
        return CertificateManager.getInstance().getCertificateState(account);
    }

    @Override
    public void updataCertificate(TableCertificate certificateConfigBean) {
        CertificateManager.getInstance().updataCertificate(certificateConfigBean);
    }

    @Override
    public boolean isAqy() {
        return CertificateManager.getInstance().isAqy();
    }

    @Override
    public String getCertificateDes() {
        return CertificateManager.getInstance().updateDes();
    }

    @Override
    public void getCertificateJsonByAddress(String areaId, CertificateJsonByAddressListener certificateJsonByAddressListener) {
        CertificateByAddressViewModel certificateViewModel=new CertificateByAddressViewModel();
        certificateViewModel.getCertificatetByAddressData(areaId,certificateJsonByAddressListener);
    }

    @Override
    public Map<String, String> getCurrentCertificate() {
        return CertificateManager.getInstance().getCurrentCertificate();
    }

    @Override
    public void startCertificateActivity(String addressName, String addressId, boolean tgAddress) {
        LogUtils.e("传递来得数据------>>>"+addressName);
        ARouter.getInstance().build(CertificateRouterPath.MAIN_ACTIVITY)
                .withInt("type",1)
                .withString("addressName",addressName)
                .withString("addressId",addressId)
                .withBoolean("tgAddress",tgAddress)
                .navigation();
    }

    @Override
    public String getCurrentCertificateAddressName() {
        return CertificateManager.getInstance().getCurrentCitificateAddressName();
    }

    @Override
    public boolean payTypeDialog() {
        return  CertificateManager.getInstance().getCurrentType();
    }

    @Override
    public void start(Context context) {

    }

    @Override
    public IFragment mainPage(Context context) {
        return ARouterUtils.navFragment(CertificateRouterPath.MAIN_FRAGMENT);
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {

    }
}
