package com.zizhiguanjia.model_certificate.fragment;

import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.constants.CertificateRouterPath;
import com.zizhiguanjia.lib_base.helper.AddressHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.msgconfig.AddressMsgTypeConfig;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.adapter.UpdataCitificateContentAdapter;
import com.zizhiguanjia.model_certificate.databinding.CerZzcertificateLayoutBinding;
import com.zizhiguanjia.model_certificate.listener.UpdataCertificateListenter;
import com.zizhiguanjia.model_certificate.manager.CertificateManager;
import com.zizhiguanjia.model_certificate.model.UpdataCertificateBean;
import com.zizhiguanjia.model_certificate.navigator.ZzCertificateByAddressNabigator;
import com.zizhiguanjia.model_certificate.viewmodel.ZzCertificateByAddressViewModel;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;

import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import me.jessyan.autosize.AutoSizeConfig;
import me.jessyan.autosize.internal.CustomAdapt;
@Route(path = CertificateRouterPath.CHOSEADDRESSBYADDRESSZZ_FRAGMENT)
@BindRes(statusBarStyle = BarStyle.LIGHT_CONTENT, swipeBack = SwipeStyle.NONE)
public class ZzCertificateByAddressFragment extends BaseFragment implements TitleBar.OnLeftBarListener,ZzCertificateByAddressNabigator,CustomAdapt, UpdataCertificateListenter {
    @BindViewModel
    ZzCertificateByAddressViewModel zzCertificateByAddressViewModel;
    private CerZzcertificateLayoutBinding binding;
    private String addressName, addressId, majId;
    private boolean tgAddress, tgSave;
    private UpdataCitificateContentAdapter certificaterRightAdapter;
    private List<UpdataCertificateBean.RecordsBean> items;
    private Set<String> idsSet=new HashSet<>();
    private boolean singleType=false;
    private String enterType;
    private boolean checkAqy=false;
    private LoadingPopupView loadingPopupView;
    private String mRouth;
    public int initLayoutResId() {
        return R.layout.cer_zzcertificate_layout;
    }
    @Override
    public void initView(Bundle savedInstanceState) {
        binding=getBinding();
        binding.mlstvCommonListView.showLoading();
        mRouth=initArguments().getString("routh","");
        initData();
        initListener();
        enterType="2";
        zzCertificateByAddressViewModel.initParams(this,enterType);
        binding.setModel(zzCertificateByAddressViewModel);
        loadingPopupView=new PopupManager.Builder(getActivity()).asLoading("请稍等....");
        initListenterOnKey();
        binding.zzCertificateTtb.setBackListener(new TitleBar.OnLeftBarListener() {
            @Override
            public void onClicked(View v) {
                openCustBack();
            }
        });
    }
    @Override
    public void initViewData() {

    }
    private void initListenterOnKey() {
        getView().setFocusableInTouchMode(true);
        getView().requestFocus();
        getView().setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    openCustBack();
                    return true;
                }
                return false;
            }
        });
    }
    private void openCustBack(){
//        ToastUtils.normal("点击了关闭");
        MessageHelper.openGeneralCentDialog(getActivity(), "选定科目后才能享受VIP权益\n没选定前无法解锁全部刷题权限", "",
                "以后再说", "选择科目", false, false, new GeneralDialogListener() {
                    @Override
                    public void onCancel() {
                        LogUtils.e("开始关闭了---->>>"+mRouth);
                        if(StringUtils.isEmpty(mRouth)){
                            if(CertificateHelper.getCurrentCertificateDialog()){
                                LogUtils.e("开始关闭了---->>>无需回到首页");
                            }else {
                                LogUtils.e("开始关闭了---->>>需要回到首页");
                                Bus.post(new MsgEvent(0x4545445));
                            }
                        }
                        finish();
                    }
                    @Override
                    public void onConfim() {
                    }

                    /**
                     *
                     */
                    @Override
                    public void onDismiss() {

                    }
                });

    }
    @Override
    public void showContextView(List<UpdataCertificateBean.RecordsBean> Records) {
        checkAqy=Records.get(0).getBindOrderType()==2?true:false;
        binding.mlstvCommonListView.showContent();
        items.clear();
        items.addAll(Records);
        certificaterRightAdapter.setDataItems(items);
        if(Records==null||Records.size()==0){
            zzCertificateByAddressViewModel.showAddressView.set(false);
        }else {
            if(Records.get(0).getBindOrderType()==2){
                zzCertificateByAddressViewModel.showAddressView.set(true);
            }else {
                zzCertificateByAddressViewModel.showAddressView.set(false);
            }
        }
    }
    @Override
    public void initData() {
        items=new ArrayList<>();
        AutoSizeConfig.getInstance().setCustomFragment(true);
        addressName = initArguments().getString("addressName");
        addressId = initArguments().getString("addressId");
        tgAddress = initArguments().getBoolean("tgAddress", false);
        tgSave = initArguments().getBoolean("tgSave", true);
        certificaterRightAdapter=new UpdataCitificateContentAdapter(this);
        binding.rcyGroup.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.rcyGroup.setAdapter(certificaterRightAdapter);
        binding.rcyGroup.setBackgroundColor(Color.parseColor("#f6f6f6"));
        LogUtils.e("传递的地区"+addressName);
        String json=CertificateManager.getInstance().getCurrentCitificateAddressName();
        getCurrentAddressName(json);
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if(msgEvent.getCode()== AddressMsgTypeConfig.ADDRESS_USER_SELECT_ADDRESSIDS){
                    String json=msgEvent.getMsg();
                    Map<String,String> params=GsonUtils.gsonToMaps(json);
                    String name=params.get("addressName");
                    String ids=params.get("addressIds");
                    binding.tvCurrentAddressName.setText(name);
                    zzCertificateByAddressViewModel.getCertificateByAddress(enterType,ids);
                }
            }
        });
    }
    public  String[] convertStrToArray2(String str) {
        StringTokenizer st = new StringTokenizer(str, "·");
        String[] strArray = new String[st.countTokens()];
        int i = 0;
        while (st.hasMoreTokens()) {
            strArray[i++] = st.nextToken().trim();
        }
        return strArray;
    }
    @Override
    public void initListener() {
        binding.zzCertificateTtb.setBackListener(this);
        binding.certificatePostTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(singleType){
                    if(idsSet.size()==0){
                        ToastUtils.normal("请至少选择一个证书~", Gravity.CENTER);
                        return;
                    }

                }else {
                    if(idsSet.size()==0){
                        ToastUtils.normal("请至少选择一个证书~", Gravity.CENTER);
                        return;
                    }
                    if(!idsSet.contains("2036")){
                        ToastUtils.normal("工程法规必选科目,请选择~", Gravity.CENTER);
                        return;
                    }
                    if(!idsSet.contains("2035")){
                        ToastUtils.normal("施工管理必选科目,请选择~", Gravity.CENTER);
                        return;
                    }
                    if(enterType.equals("2")){
                        boolean isConAqy=false;
                        int isEj=0;
                        for (String str : idsSet) {
                            if(str.startsWith("1")){
                                isConAqy=true;
                            }
                        }
                        for (String str : idsSet) {
                            if(str.startsWith("2")){
                                isEj++;
                            }
                        }
                        if(checkAqy){
                            if(!isConAqy){
                                ToastUtils.normal("安全员证书未选择~", Gravity.CENTER);
                                return;
                            }
                        }
                        if(isEj<3){
                            ToastUtils.normal("专业科目未选择~", Gravity.CENTER);
                            return;
                        }

                    }else {
                        if(idsSet.size()<3){
                            ToastUtils.normal("专业科目未选择~", Gravity.CENTER);
                            return;
                        }
                    }

                }
                zzCertificateByAddressViewModel.bindCertificate(idsSet);
            }
        });
    }

    @Override
    public void checkToPage() {
        initArguments().putBoolean("addrtessId", true);
        startFragment(AddressHelper.mainPage(getContext()));
    }

    @Override
    public void showErrorView() {
        binding.mlstvCommonListView.showEmpty();
    }

    @Override
    public void closeView() {
        finish();
    }

    @Override
    public void showOrHindLoading(boolean b) {
        if(loadingPopupView==null)return;
        if(b){
            if(loadingPopupView.isShow()){
               return;
            }
            loadingPopupView.show();
        }else {
            if(loadingPopupView.isShow()){
                loadingPopupView.dismiss();
            }
        }

    }

    @Override
    public void getCurrentAddressName(String name) {
        binding.tvCurrentAddressName.setText(name);
    }

    @Override
    public boolean isBaseOnWidth() {
        return false;
    }
    @Override
    public float getSizeInDp() {
        return 812;
    }

    @Override
    public void onClicked(View v) {
        finish();
    }
    @Override
    public void updataUserBindItem(int ids,boolean single,boolean singleItem,boolean rj) {
        LogUtils.e("点击了----->>>"+ids+"*****"+single+"****"+singleItem);
        this.singleType=single;
        if(single){
            idsSet.clear();
        }
        for(UpdataCertificateBean.RecordsBean group:items){
            if(group.getChilds()==null||group.getChilds().size()==0){
            }else {
                for (UpdataCertificateBean.RecordsBean.ChildsBean childsBean: group.getChilds()){
                    LogUtils.e("分类-*---->>>"+group.getId()+"***"+group.getName());
                    if(group.getId()==2000){
                        if((childsBean.getChilds()==null||childsBean.getChilds().size()==0)){
                        }else {
                            fillLits(childsBean,ids,single,singleItem);
                        }
                    }else {
                        if(single||(singleItem&&!single)){
                            if(childsBean.getBindOrderType()==3&&childsBean.getSelectType()==2){
                            }else {
                                if(String.valueOf(ids).startsWith("1")){
                                    childsBean.setSelect(false);
                                }

                            }
                        }
                        if(String.valueOf(ids).startsWith("1")){
                            LogUtils.e("循环便利1---->>>"+ids+"*****"+childsBean.getId());
                        }
                        if(ids==childsBean.getId()){
                            if(String.valueOf(ids).startsWith("1")){
                                if(childsBean.isSelect()){
                                    idsSet.remove(String.valueOf(ids));
                                }else {
                                    idsSet.add(String.valueOf(ids));
                                }
                                childsBean.setSelect(childsBean.isSelect()?false:true);
                            }

                        }else {
                            if(String.valueOf(ids).startsWith("1")){
                                if(singleItem){
                                    if(childsBean.getBindOrderType()==3&&childsBean.getSelectType()==2){
                                    }else {
                                        if(ids==childsBean.getId()){
                                        }else {
                                            idsSet.remove(String.valueOf(childsBean.getId()));
                                        }
                                    }

                                }
                            }
                        }
                    }
                }
            }

        }
        idsSet.clear();
        for (UpdataCertificateBean.RecordsBean group:items){
            if(group.getChilds()==null||group.getChilds().size()==0){
            }else {
                for (UpdataCertificateBean.RecordsBean.ChildsBean childsBean: group.getChilds()){
                    if(group.getId()==2000){
                        for(UpdataCertificateBean.RecordsBean.ChildsBean item:childsBean.getChilds()){
                            if(item.isSelect()){
                                idsSet.add(item.getId()+"");
                            }
                            LogUtils.e("看看数据大小1---->>>"+item.getName()+"***"+item.isSelect());
                        }
                    }else {
                        if(childsBean.isSelect()){
                            idsSet.add(childsBean.getId()+"");
                        }
                        LogUtils.e("看看数据大小2---->>>"+childsBean.getName()+"***"+childsBean.isSelect());
                    }

                }
            }
        }
        certificaterRightAdapter.notifyDataSetChanged();
        if(idsSet.size()==0){
            upPostBtState(false);
        }else {
            upPostBtState(true);
        }
    }
    private void fillLits(UpdataCertificateBean.RecordsBean.ChildsBean childsBean,int ids,boolean single,boolean singleItem){
        if(String.valueOf(ids).startsWith("1"))return;
        for(UpdataCertificateBean.RecordsBean.ChildsBean item:childsBean.getChilds()){
            if(single||(singleItem&&!single)){
                //点击了按钮----->>>2039***2035****false****true***3***2***施工管理***公共科目
                LogUtils.e("点击了按钮----->>>"+ids+"***"+item.getId()+"****"+single+"****"+singleItem+"***"+childsBean.getBindOrderType()+"***"+childsBean.getSelectType()+"***"+item.getName()+"***"+childsBean.getName());
                //2039***2035***true施工管理
                LogUtils.e("看下数据1---->>>"+ids+"***"+item.getId()+"***"+item.isSelect()+item.getName());
                if(childsBean.getBindOrderType()==1&&childsBean.getSelectType()==1){
                    item.setSelect(false);
                }
                if(childsBean.getBindOrderType()==3&&childsBean.getSelectType()==2){
                }else {
                    if(ids==item.getId()){
                        if(item.isSelect()){
                            item.setSelect(false);
                        }else {
                            item.setSelect(true);
                        }
                    }else {
                       if(childsBean.getBindOrderType()==2&&childsBean.getSelectType()==1){
                           item.setSelect(false);
                       }
                        if(childsBean.getBindOrderType()==3&&childsBean.getSelectType()==1){
                            LogUtils.e("数据开始清除了------>>>>");
                            item.setSelect(false);
                        }
                        LogUtils.e("看下数据3---->>>"+ids+"***"+item.getId()+"***"+item.isSelect()+"****"+item.getName()+"***"+singleItem+"***"+single);
                    }
                    LogUtils.e("看下数据2---->>>"+ids+"***"+item.getId()+"***"+item.isSelect()+"****"+item.getName());
                }
            }else {
                LogUtils.e("看下数据4---->>>"+ids+"***"+item.getId()+"***"+item.isSelect()+"****"+item.getName());
                if(ids==item.getId()){
                    if(item.isSelect()){
                        item.setSelect(false);
                    }else {
                        item.setSelect(true);
                    }
                }
            }
        }
        LogUtils.e("数据原大小"+idsSet.toString()+"****"+ids);
    }
    private void upPostBtState(boolean isSelect){
        if(isSelect){
            binding.certificatePostTv.setBackgroundResource(R.drawable.cer_login_yes_bg);
        }else {
            binding.certificatePostTv.setBackgroundResource(R.drawable.cer_login_nor_bg);
        }
    }
}
