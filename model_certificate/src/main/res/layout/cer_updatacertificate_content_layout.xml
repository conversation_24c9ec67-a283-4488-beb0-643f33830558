<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_certificate.viewmodel.ZzCertificateByAddressViewModel" />
    </data>
    <RelativeLayout
        android:background="@drawable/cer_zzclasss_bg"
        android:layout_marginLeft="12dp"
        android:layout_marginBottom="12dp"
        android:layout_marginRight="12dp"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
       <LinearLayout
           android:orientation="vertical"
           android:layout_width="match_parent"
           android:layout_height="wrap_content">
           <TextView
               android:textStyle="bold"
               android:id="@+id/titleTv"
               android:layout_marginTop="16dp"
               android:layout_marginLeft="8dp"
               android:textColor="#141517"
               android:textSize="17sp"
               android:text="二级建造师"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"/>
           <TextView
               android:id="@+id/desTv"
               android:layout_marginLeft="8dp"
               android:layout_marginBottom="21dp"
               android:layout_marginTop="5dp"
               android:textColor="#A8A8A8"
               android:textSize="13.5sp"
               android:text="您可选择全部公共科目和任意1门实务开通VIP"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"/>
           <LinearLayout
               android:layout_width="match_parent"
               android:layout_height="match_parent"
               android:orientation="horizontal">
               <androidx.recyclerview.widget.RecyclerView
                   android:layout_marginBottom="13dp"
                   android:layout_marginRight="8dp"
                   android:id="@+id/rightRecycle"
                   android:layout_width="match_parent"
                   android:layout_height="match_parent" />
           </LinearLayout>
       </LinearLayout>

    </RelativeLayout>
</layout>