<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>

    </data>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <include
            android:id="@+id/icdeCommonContent"
            layout="@layout/cer_updatacertificate_content_layout"
             />
        <LinearLayout
            android:layout_alignParentBottom="true"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/certificatePostTv"
                android:paddingBottom="10dp"
                android:paddingTop="10dp"
                android:gravity="center"
                android:layout_marginRight="27dp"
                android:layout_marginLeft="27dp"
                android:text="确认开通"
                android:textSize="16sp"
                android:textColor="@color/white"
                android:background="@drawable/cer_login_nor_bg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <TextView
                android:layout_marginTop="49dp"
                android:layout_marginBottom="19dp"
                android:gravity="center"
                android:textSize="13sp"
                android:textColor="#999999"
                android:text="选定科目后暂不支持自助修改，需要联系客服，请谨慎选择。"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </RelativeLayout>
</layout>