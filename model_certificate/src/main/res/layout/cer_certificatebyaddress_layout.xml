<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_certificate.viewmodel.CertificateByAddressViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.wb.lib_weiget.titlebar.TitleBar
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/tlbCertificates"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tb_centerText="选择您的科目"
            app:tb_centerTextColor="#000000"
            app:tb_leftImageResource="@drawable/common_left_back_black_img"
            app:tb_leftType="imageButton"
            app:tb_statusBarColor="#fff"
            app:tb_titleBarColor="#fff" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:gravity="center"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="已选择地区："
                    android:textColor="#434D60"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tvCertificateName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:gravity="center"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="--"
                    android:textColor="#027AFF"
                    android:textSize="15sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvUpdateName"
                android:layout_width="35.5dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_marginTop="11dp"
                android:layout_marginRight="12dp"
                android:layout_marginBottom="11dp"
                android:background="@drawable/cer_updata_bg"
                android:gravity="center"
                android:onClick="@{model::onclick}"
                android:text="更改"
                android:textColor="#007AFF"
                android:textSize="12sp" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="#EBEBEB" />

<!--            <View-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="10dp"-->
<!--                android:background="#F4F7F9" />-->
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_group_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"/>

        <com.wb.lib_weiget.views.MultipleStatusView
            xmlns:app="http://schemas.android.com/apk/res-auto"
            app:emptyView="@layout/layout_common_empty"
            app:loadingView="@layout/custom_loading_view"
            app:noNetworkView="@layout/custom_no_network_view"
            android:id="@+id/msvExamChapter"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcyCertificate"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#f6f6f6"
                android:paddingBottom="17.5dp" />
        </com.wb.lib_weiget.views.MultipleStatusView>
    </LinearLayout>
</layout>