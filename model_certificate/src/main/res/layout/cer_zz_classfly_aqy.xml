<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="data"
            type="com.zizhiguanjia.model_certificate.model.SecondConstructionBean.RecordsBean" />
    </data>
    <LinearLayout
        android:orientation="vertical"
        android:background="#F6F6F6"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
                    <View
                        android:id="@+id/postionV"
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:background="#F4F7F9" />
        <LinearLayout
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/cer_zzclasss_bg"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textStyle="bold"
                android:text="@{data.name}"
                android:id="@+id/tvTitle"
                android:layout_marginTop="17dp"
                android:layout_marginLeft="11dp"
                android:textColor="#141517"
                android:textSize="17sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <View
                android:id="@+id/lineV"
                android:layout_width="match_parent"
                android:layout_height="16dp"/>
            <RelativeLayout
                android:paddingRight="9.5dp"
                android:paddingBottom="17dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/certificateFlyRcy"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
</layout>