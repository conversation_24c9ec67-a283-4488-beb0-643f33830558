<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="data"
            type="com.zizhiguanjia.model_certificate.model.ZzCertificateByAddressBean.RecordsBean" />
        <variable
            name="postion"
            type="java.lang.Integer" />
        <variable
            name="select"
            type="java.lang.Integer" />
        <variable
            name="model"
            type="com.zizhiguanjia.model_certificate.adapter.CertificateLeftAdapter" />
        <import type="android.view.View"/>
    </data>
    <LinearLayout
        android:gravity="center_vertical"
        android:background="@{select==postion?@color/cer_bg_yes:@color/cer_bg_no}"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="56dp">
        <View
            android:visibility="@{select==postion?View.VISIBLE:View.INVISIBLE}"
            android:background="#FF6A00"
            android:layout_width="2dp"
            android:layout_height="match_parent"/>
        <TextView
            android:layout_marginLeft="5dp"
            android:text="@{data.name}"
            android:textColor="@{select==postion?@color/cer_text_bg_yes:@color/cer_text_bg_no}"
            android:textSize="15sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</layout>