<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="view"
            type="android.view.View" />
        <variable
            name="model"
            type="com.zizhiguanjia.model_certificate.viewmodel.ZzCertificateByAddressViewModel" />
    </data>

    <LinearLayout
        android:background="#f6f6f6"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.wb.lib_weiget.titlebar.TitleBar xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/zzCertificateTtb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tb_centerText="选择需要升级的科目"
            app:tb_centerTextColor="#000000"
            app:tb_centerType="textView"
            app:tb_centerTextSize="18.5sp"
            app:tb_leftImageResource="@drawable/common_left_back_black_img"
            app:tb_leftType="imageButton"
            app:tb_showBottomLine="true"
            app:tb_bottomLineColor="#e8e8e8"
            app:tb_statusBarColor="#FFFFFF"
            app:tb_titleBarColor="#FFFFFF" />
        <com.wb.lib_weiget.views.MultipleStatusView xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/mlstvCommonListView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:emptyView="@layout/layout_common_empty"
            app:errorView="@layout/custom_error_view"
            app:loadingView="@layout/public_loading_list"
            app:noNetworkView="@layout/custom_no_network_view">
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <RelativeLayout
                    android:visibility="@{model.showAddressView?view.VISIBLE:view.GONE}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:gravity="center"
                            android:paddingTop="10dp"
                            android:paddingBottom="10dp"
                            android:text="已选择地区："
                            android:textColor="#434D60"
                            android:textSize="15sp" />

                        <TextView
                            android:id="@+id/tvCurrentAddressName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:gravity="center"
                            android:paddingTop="10dp"
                            android:paddingBottom="10dp"
                            android:text="--"
                            android:textColor="#027AFF"
                            android:textSize="15sp" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvUpdateName"
                        android:layout_width="35.5dp"
                        android:layout_height="20dp"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="11dp"
                        android:layout_marginRight="12dp"
                        android:layout_marginBottom="11dp"
                        android:background="@drawable/cer_updata_bg"
                        android:gravity="center"
                        android:onClick="@{model::onclick}"
                        android:text="更改"
                        android:textColor="#007AFF"
                        android:textSize="12sp" />
                </RelativeLayout>
                <ScrollView
                    android:fillViewport="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <LinearLayout
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rcyGroup"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_above="@+id/bottom_ll"
                            android:paddingTop="12dp" />
                        <LinearLayout
                            android:layout_marginTop="47dp"
                            android:id="@+id/bottom_ll"
                            android:layout_alignParentBottom="true"
                            android:orientation="vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/certificatePostTv"
                                android:paddingBottom="10dp"
                                android:paddingTop="10dp"
                                android:gravity="center"
                                android:layout_marginRight="27dp"
                                android:layout_marginLeft="27dp"
                                android:text="确认开通"
                                android:textSize="16sp"
                                android:textColor="@color/white"
                                android:background="@drawable/cer_login_nor_bg"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:layout_marginLeft="25dp"
                                android:layout_marginRight="25dp"
                                android:layout_marginTop="20dp"
                                android:layout_marginBottom="19dp"
                                android:gravity="center"
                                android:textSize="13sp"
                                android:textColor="#999999"
                                android:text="温馨提示：选定科目后暂不支持自助修改，需要联系客服，请谨慎选择。"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                    </LinearLayout>
                </ScrollView>
            </LinearLayout>
        </com.wb.lib_weiget.views.MultipleStatusView>

    </LinearLayout>
</layout>