<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="data"
            type="com.zizhiguanjia.model_certificate.model.SecondConstructionBean.RecordsBean" />
    </data>
    <LinearLayout
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_marginLeft="8dp"
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/rightChildeImg"
                    android:layout_width="16dp"
                    android:layout_height="17dp"/>
                <TextView
                    android:id="@+id/tvTitle"
                    android:textSize="15sp"
                    android:textColor="#141517"
                    android:layout_marginLeft="6dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <RelativeLayout
                android:layout_marginTop="16dp"
                android:paddingBottom="17dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/certificateFlyRcy"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
</layout>