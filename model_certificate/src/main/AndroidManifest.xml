<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.zizhiguanjia.model_certificate">
    <application>
        <activity android:name=".ui.CertificateActivity"/>
        <activity android:name=".ui.TestActivity">
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.VIEW" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--                <category android:name="android.intent.category.BROWSABLE" />-->
<!--                <data-->
<!--                    android:host="kst.com"-->
<!--                    android:path="/index"-->
<!--                    android:scheme="anquanyuan" />-->
<!--                <data-->
<!--                    android:host="kst.com"-->
<!--                    android:path="/commonweb"-->
<!--                    android:scheme="anquanyuan" />-->
<!--            </intent-filter>-->
        </activity>
    </application>
</manifest>