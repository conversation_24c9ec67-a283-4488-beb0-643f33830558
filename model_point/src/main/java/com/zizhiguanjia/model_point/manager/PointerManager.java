package com.zizhiguanjia.model_point.manager;

import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.helper.UmengHelper;
import com.zizhiguanjia.lib_base.msgconfig.PointCheckType;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;

import java.util.Map;
import java.util.WeakHashMap;

import cn.hutool.core.map.MapUtil;

public class PointerManager {
    private static PointerManager pointerUtils;
    private WeakHashMap weakHashMap = new WeakHashMap();
    private WeakHashMap routhHashMap = new WeakHashMap();

    public static PointerManager getInstance() {
        if (pointerUtils == null) {
            synchronized (PointerManager.class) {
                return pointerUtils = new PointerManager();
            }
        }
        return pointerUtils;
    }

    public void registRouterPointer(String key) {
        if (!isRegistRouterPointer(key)) {
            weakHashMap.put(key, key);
        }
    }

    public void unRegistRouterPonter(String key, boolean sendData) {
        if (isRegistRouterPointer(key)) {
            weakHashMap.remove(key);
            if (sendData) {
                UmengHelper.onEventObject(AppUtils.getApp(), key + "_true", null);
            }
        }
    }

    public void unRegistRouterPonter(String key, boolean sendData, Map<String, String> params) {
        if (isRegistRouterPointer(key)) {
            weakHashMap.remove(key);
            if (sendData) {
                UmengHelper.onEventObject(AppUtils.getApp(), key + "_true", params);
            }
        }
    }

    public boolean isRegistRouterPointer(String key) {
        if (weakHashMap == null || weakHashMap.size() == 0) return false;
        if (weakHashMap.containsKey(key)) return true;
        return false;
    }

    public void joinPointData(String key, Map<String, String> params, boolean saveCache) throws Exception {
        if (StringUtils.isEmpty(key)) {
            throw new Exception("String is not json");
        }
        if (MapUtil.isNotEmpty(params)) {
            if (saveCache) {
                registRouterPointer(key);
            }
            UmengHelper.onEventObject(AppUtils.getApp(), key, params);
        } else {
            joinPointData(key, saveCache);
        }
    }

    public void joinPointData(String key, boolean saveCache) throws Exception {
        if (StringUtils.isEmpty(key)) {
            throw new Exception("String is not json");
        }
        if (saveCache) {
            registRouterPointer(key);
        }
        UmengHelper.onEventObject(AppUtils.getApp(), key, null);
    }

    public void addRouthPoint(String key) throws Exception {
        if (StringUtils.isEmpty(key)) {
            throw new Exception("String is not json");
        }
        if (!routhHashMap.containsKey(key)) {
            routhHashMap.put(key, key);
        }
    }

    public boolean checkPoinRouth(int checkRouType) {
        if (checkRouType == PointCheckType.CHECK_MAIN_HOMEBT_CHAPER) {
            boolean p1 = PointHelper.isRegistPoinRouth(PointerMsgType.POINTER_HOME);
            boolean p2 = PointHelper.isRegistPoinRouth(PointerMsgType.POINTER_A_HOMEPAGE_STARTPRACTICE_CHAPTER);
            boolean p3 = PointHelper.isRegistPoinRouth(PointerMsgType.POINTER_HOME_START_CHAPTER);
            return p1 && p2 && p3 ? true : false;
        } else if (checkRouType == PointCheckType.CHECK_MAIN_HOMEBT_CHAPER_EXAM) {
            boolean p1 = PointHelper.isRegistPoinRouth(PointerMsgType.POINTER_HOME);
            boolean p2 = PointHelper.isRegistPoinRouth(PointerMsgType.POINTER_A_HOMEPAGE_STARTPRACTICE_CHAPTER);
            boolean p3 = PointHelper.isRegistPoinRouth(PointerMsgType.POINTER_HOME_START_CHAPTER);
            boolean p4 = PointHelper.isRegistPoinRouth(PointerMsgType.POINTER_HOME_START_CHAPTER_EXAM);
            return p1 && p2 && p3 && p4 ? true : false;
        }
        return false;
    }

    public void clernPointRouth(int checkRouthType) {
        if (checkRouthType == PointCheckType.CHECK_MAIN_CHAPER_TO_HOMEBT) {
            unRegistRouterPonter(PointerMsgType.POINTER_A_HOMEPAGE_STARTPRACTICE_CHAPTER, false);
            unRegistRouterPonter(PointerMsgType.POINTER_HOME_START_CHAPTER, false);
        } else if (checkRouthType == PointCheckType.CHECK_EXAM_CLEAL) {
            unRegistRouterPonter(PointerMsgType.POINTER_A_100_TRIGGERBOUNCED, false);
            unRegistRouterPonter(PointerMsgType.POINTER_A_PROBLEMPAGE_TRIGGERBOUNDCED, false);
            unRegistRouterPonter(PointerMsgType.POINTER_A_WRONGTOPIC_TRIGGERBOUNDCED, false);
        }
    }
    public void cleanPaySuccessPoint(Map<String, String> params) {
        unRegistRouterPonter(PointerMsgType.POINTER_A_100_TRIGGERBOUNCED, true, params);
        unRegistRouterPonter(PointerMsgType.POINTER_A_MONI_TRIGGERBOUNCED, true, params);
        unRegistRouterPonter(PointerMsgType.POINTER_A_PROBLEMPAGE_TRIGGERBOUNDCED, true, params);
        unRegistRouterPonter(PointerMsgType.POINTER_A_WRONGTOPIC_TRIGGERBOUNDCED, true, params);
        unRegistRouterPonter(PointerMsgType.POINTER_A_PAIDLANDINGPAGE_BUTTON1, true, params);
        unRegistRouterPonter(PointerMsgType.POINTER_A_PAIDLANDINGPAGE_BUTTON2, true, params);
    }
}
