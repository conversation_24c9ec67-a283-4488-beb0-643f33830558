package com.zizhiguanjia.model_point.service;

import android.content.Context;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.PointRouterPath;
import com.zizhiguanjia.lib_base.service.PointService;
import com.zizhiguanjia.model_point.manager.PointerManager;

import java.util.Map;

@Route(path = PointRouterPath.SERVICE)
public class PointServiceImpl implements PointService {
    @Override
    public void start(Context context) {
    }

    @Override
    public IFragment mainPage(Context context) {
        return null;
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {
    }

    @Override
    public void joinPointData(String key,boolean saveCache) {
        try {
            PointerManager.getInstance().joinPointData(key,saveCache);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void joinPointData(String key, Map<String, String> params, boolean saveCache) {
        try {
            PointerManager.getInstance().joinPointData(key,params,saveCache);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean isRegistPoinRouth(String routh) {
        return PointerManager.getInstance().isRegistRouterPointer(routh);
    }

    @Override
    public void addPoinRouth(String key) {
        try {
            PointerManager.getInstance().addRouthPoint(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean checkPoinRouth(int routhType) {
        return PointerManager.getInstance().checkPoinRouth(routhType);
    }

    @Override
    public void clernPointRouth(int routhType) {
        PointerManager.getInstance().clernPointRouth(routhType);
    }

    @Override
    public void unRegistPoinRouth(String key, Map<String, String> params, boolean sendPost) {
        PointerManager.getInstance().unRegistRouterPonter(key,sendPost,params);
    }

    @Override
    public void unRegistPoinRouth(String key, boolean sendPost) {
        PointerManager.getInstance().unRegistRouterPonter(key,sendPost);
    }

    @Override
    public void cleanPaySuccessPoin(Map<String, String> params) {
        PointerManager.getInstance().cleanPaySuccessPoint(params);
    }

}
