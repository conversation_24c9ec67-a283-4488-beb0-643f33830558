package com.zizhiguanjia.model_task.api;

import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import java.util.Map;
import io.reactivex.Observable;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface TaskApi {
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/ExamCoupon/CheckCouponText")
    Observable<BaseData> validationPassword(@FieldMap Map<String,String> params);
}
