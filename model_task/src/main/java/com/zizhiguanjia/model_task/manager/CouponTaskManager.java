package com.zizhiguanjia.model_task.manager;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.util.Patterns;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.ClipboardUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.bean.OrderDialogBean;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.listeners.CoupoinValidationListenter;
import com.zizhiguanjia.model_task.helper.TaskHelper;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.reactivex.functions.Consumer;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

public class CouponTaskManager {
    private static CouponTaskManager couponTask;

    public static CouponTaskManager getInstance() {
        if (couponTask == null) {
            synchronized (CouponTaskManager.class) {
                couponTask = new CouponTaskManager();
            }
        }
        return couponTask;
    }

    public void detectionCoupon(CoupoinValidationListenter coupoinValidationListenter) {
        String str = checkClpboardLegitimacy();
        if (StringUtils.isEmpty(str)) {
            //非法参数 任务停止

        } else {
            //交给服务处理
            LogUtils.e("看看剪贴板的----->>>" + str);
            validationPassword(str, coupoinValidationListenter);
        }
    }

    private void validationPassword(String txt, CoupoinValidationListenter coupoinValidationListenter) {
        LogUtils.e("剪贴板7===>>" + txt);
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("text", txt);
        RequestBody multiBody = multiBuilder.build();
        TaskHelper.getInstance().postData(BaseAPI.VERSION_DES + "/API/ExamCoupon/CheckCouponText", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                LogUtils.e("看看验证的2" + e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.code() == 200) {
                    try {
                        String jsonStr = response.body().string();
                        LogUtils.e("看看验证的----->>>>" + jsonStr);
                        Type jsonType = new TypeToken<BaseData<String>>() {
                        }.getType();
                        BaseData<String> commonExamBeanBaseData = new Gson().fromJson(jsonStr, jsonType);
                        if (commonExamBeanBaseData.Code.equals("200")) {
                            if (commonExamBeanBaseData.getResult().equals("0")) {
                                //非法
                                LogUtils.e("看看验证的----->>>>4444444444");
                            } else {
                                LogUtils.e("看看验证的----->>>>55555555");
                                if (commonExamBeanBaseData.Data == null || commonExamBeanBaseData.Data.isEmpty())
                                    return;
                                coupoinValidationListenter.onCoupoinShow();
                                RxJavaUtils.delay(1, new Consumer<Long>() {
                                    @Override
                                    public void accept(Long aLong) throws Exception {
                                        OrderDialogBean orderDialogBean = new OrderDialogBean();
                                        orderDialogBean.setIndex(3);
                                        orderDialogBean.setTips(commonExamBeanBaseData.Data);
                                        MessageHelper.orderOpenDialog(orderDialogBean);
                                        LogUtils.e("看看验证的----->>66666");
                                    }
                                });
                            }
                        } else {
                            LogUtils.e("看看验证的----->>33333");
                        }
                    } catch (Exception e) {
                        LogUtils.e("看看验证的2----->>>>" + e.getMessage());
                    }
                }
            }
        });
    }

    /**
     * 检测剪贴板的合法性  包含是否存在app 本身的合法性
     *
     * @return
     */
    private String checkClpboardLegitimacy() {
        String clipboardStr = getClipBoardTxt();
        LogUtils.e("剪贴板1===>>" + clipboardStr);
        if (!AccountHelper.isUserLogin()) return null;
        if (StringUtils.isEmpty(clipboardStr)) {
            if (BaseConfig.couponid == null || BaseConfig.couponid.isEmpty()) {
                return null;
            } else {
                MessageHelper.startCoupion(BaseConfig.couponid);
                return null;
            }

        } else {
            if (checkContainsChinese(clipboardStr)) {
                //判断剪贴板中包含中文
                LogUtils.e("剪贴板2====>>有中文");
                String matchStr = "【安全员考试宝典】";
                int index = clipboardStr.indexOf(matchStr);
                LogUtils.e("剪贴板3====>>" + index);
                if (index == -1) return null;
                String txt = clipboardStr.substring(0, index);
                LogUtils.e("剪贴板4====>>" + txt);
                try {
                    String stayStr = decode(txt);
                    LogUtils.e("剪贴板5====>>" + stayStr);
                    String[] strs = convertStrToArray2(stayStr);
                    LogUtils.e("剪贴板6====>>" + strs.toString());
                    if (strs == null || strs.length == 0) {
                        //非法参数
                        return null;
                    } else {
                        return clipboardStr;
                    }
                } catch (Exception e) {
                    return null;
                }
            } else {
                //不存在中文信息  此时说明存在Url
                LogUtils.e("剪贴板====>>无中文");
                String urlStr = getCopyUrl(clipboardStr);
                if (StringUtils.isEmpty(urlStr)) {
                    return null;
                } else {
                    //存在url
                    return clipboardStr;
                }
            }
        }
    }

    /**
     * 加密
     *
     * @param str
     * @return
     */
    private String encode(String str) {
        String htext = "";
        for (int i = 0; i < str.length(); i++) {
            char ss = str.charAt(i);
            htext = htext + (char) (ss + 8);
        }
        return htext;
    }

    /**
     * 解密
     *
     * @param str
     * @return
     */
    private String decode(String str) {
        String dtext = "";
        for (int i = 0; i < str.length(); i++) {
            char ss = str.charAt(i);
            dtext = dtext + (char) (ss - 8);
        }
        return dtext;
    }

    private boolean checkContainsChinese(String clipboardStr) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(clipboardStr);
        if (m.find()) {
            return true;
        }
        return false;
    }

    /**
     * 获取剪贴板数据
     *
     * @return
     */
    private String getClipBoardTxt() {
        try {
            String txt = ClipboardUtils.getText(AppUtils.getApp()).toString();
            ClipboardManager clipboardManager = (ClipboardManager) AppUtils.getApp().getSystemService(Context.CLIPBOARD_SERVICE);

            // 创建一个空的 ClipData 对象
            ClipData clip = ClipData.newPlainText("", "");
            // 将 ClipData 对象设置到剪贴板中，这会清空剪贴板的内容
            clipboardManager.setPrimaryClip(clip);
            return txt;
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 获取剪贴板的url;
     *
     * @param txt
     * @return
     */
    private String getCopyUrl(String txt) {
        Matcher matcher = Patterns.WEB_URL.matcher(txt);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    /**
     * 字符串赚数组
     */
    private String[] convertStrToArray2(String str) {
        StringTokenizer st = new StringTokenizer(str, "_");
        String[] strArray = new String[st.countTokens()];
        int i = 0;
        while (st.hasMoreTokens()) {
            strArray[i++] = st.nextToken().trim();
        }
        return strArray;
    }
}
