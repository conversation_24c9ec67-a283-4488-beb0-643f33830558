package com.zizhiguanjia.model_task.service;

import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.TaskRouterPath;
import com.zizhiguanjia.lib_base.helper.TaskHelper;
import com.zizhiguanjia.lib_base.listeners.CoupoinValidationListenter;
import com.zizhiguanjia.lib_base.service.TaskServie;
import com.zizhiguanjia.model_task.manager.CouponTaskManager;

@Route(path = TaskRouterPath.SERVICE)
public class TaskServiceImpl implements TaskServie {

    @Override
    public void start(Context context) {

    }

    @Override
    public IFragment mainPage(Context context) {
        return null;
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {

    }

    @Override
    public void detectionCoupon(CoupoinValidationListenter coupoinValidationListenter) {
        CouponTaskManager.getInstance().detectionCoupon(coupoinValidationListenter);
    }
}
