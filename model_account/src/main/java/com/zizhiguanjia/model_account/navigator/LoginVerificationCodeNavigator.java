package com.zizhiguanjia.model_account.navigator;

import com.wb.lib_pop.code.BasePopupView;

public interface LoginVerificationCodeNavigator {
    void userSelectYsxy(boolean select);
    void initLoadConfig();
    void showLoading(boolean visition,String msg);
    void toBindPhonePage(String name,String openId,String uninId);
    void successThridLogin();
    void showToastMsg(String msg);
    void loginSuccess();
    void initUserPremissDiaglog(BasePopupView basePopupView);
    void showUserPreMissDialog();
    void hindUserPreMissDialog();
    void successCode();
    void initSendCodeConfig();
    void setGlColor(boolean success);
    void deviceSd(String msg);
    void restTel(String tel);
}
