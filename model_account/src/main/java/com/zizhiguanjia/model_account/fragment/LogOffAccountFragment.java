package com.zizhiguanjia.model_account.fragment;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.constants.AccountRouterPath;
import com.zizhiguanjia.model_account.R;
import com.zizhiguanjia.model_account.databinding.AccountLogoffaccountLayuotBinding;
import com.zizhiguanjia.model_account.navigator.LogOffAccountNavigator;
import com.zizhiguanjia.model_account.viewmodel.LogOffAccountViewModel;

@BindRes( statusBarStyle= BarStyle.DARK_CONTENT)
@Route(path = AccountRouterPath.MAIN_LOGOFF_ACCOUNT)
public class LogOffAccountFragment extends BaseFragment implements LogOffAccountNavigator {
    @BindViewModel
    LogOffAccountViewModel model;
    private AccountLogoffaccountLayuotBinding binding;
    private BasePopupView loadingPopu;
    @Override
    public int initLayoutResId() {
        return R.layout.account_logoffaccount_layuot;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding=getBinding();
        binding.setModel(model);
        model.initParams(this,this);
        binding.tbAccountLock.setBackListener(new TitleBar.OnLeftBarListener() {
            @Override
            public void onClicked(View v) {
                finish();
            }
        });
    }
    @Override
    public void initLoadingPopu() {
        loadingPopu = new PopupManager.Builder(getContext()).asLoading("注销中...",R.layout.popup_center_impl_loading);
    }
    @Override
    public void initViewData() {
        super.initViewData();
    }

    @Override
    public void initObservable() {
        super.initObservable();
    }

    @Override
    public void showErrorMsg(String msg) {
        ToastUtils.normal(msg, Gravity.CENTER);
    }
    @Override
    public void showLoading(boolean visition) {
        if (visition) {
            if (loadingPopu.isShow()) return;
            loadingPopu.show();
        } else {
            if (loadingPopu.isShow()) {
                loadingPopu.dismiss();
            }
        }
    }
}
