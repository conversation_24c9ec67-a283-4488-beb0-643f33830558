package com.zizhiguanjia.model_account.fragment;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;

import androidx.lifecycle.Observer;

import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.model_account.R;
import com.zizhiguanjia.model_account.databinding.AccountBindphoneLayoutBinding;
import com.zizhiguanjia.model_account.navigator.BindPhoneNavigator;
import com.zizhiguanjia.model_account.viewmodel.BinPhoneViewModel;

public class BinPhoneFragment extends BaseFragment implements BindPhoneNavigator {
    private AccountBindphoneLayoutBinding bindphoneLayoutBinding;
    @BindViewModel
    BinPhoneViewModel model;
    private BasePopupView loadingPopu;

    @Override
    public int initLayoutResId() {
        return R.layout.account_bindphone_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        bindphoneLayoutBinding = getBinding();
        model.initParams(this, this);
        bindphoneLayoutBinding.tbSendCode.setOnClickListener(this);
        bindphoneLayoutBinding.setModel(model);
    }

    @Override
    public void initObservable() {
        super.initObservable();
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if (msgEvent.getCode() == AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS) {
                    //登录成功需要消除
                    finish();
                }
            }
        });
    }

    @Override
    public void initViewData() {
        String openId = initArguments().getString("thridOpenId", "");
        String name = initArguments().getString("thridName", "");
        String unionId=initArguments().getString("unionId","");
        model.setThridInfo(name, openId,unionId);
    }

    @Override
    public void initSendCodeConfig() {
        bindphoneLayoutBinding.tbSendCode.setTextAfter("s后获取");
        bindphoneLayoutBinding.tbSendCode.setLenght(60 * 1000);
        bindphoneLayoutBinding.tbSendCode.setTextBefore("获取验证码");
    }

    @Override
    public void initLoadingPopu() {
        loadingPopu = new PopupManager.Builder(getContext()).asLoading("发送中...",R.layout.popup_center_impl_loading);
    }

    @Override
    public void onClick(View v) {
        model.onclick(v);
    }

    @Override
    public void sendSysCode() {
        bindphoneLayoutBinding.tbSendCode.setIsStarTime();
    }

    @Override
    public void showLoading(boolean visition) {
        if (visition) {
            if (loadingPopu.isShow()) return;
            loadingPopu.show();
        } else {
            if (loadingPopu.isShow()) {
                loadingPopu.dismiss();
            }
        }
    }

    @Override
    public void successSysCode() {
        bindphoneLayoutBinding.tbSendCode.setIsStarTime();
    }

    @Override
    public void successBindhone() {
        Bus.post(new MsgEvent(AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS));
        finish();
    }

    @Override
    public void showErrorTost(String msg) {
        if(msg==null||msg.isEmpty())return;
        ToastUtils.normal(msg, Gravity.CENTER);
    }
}
