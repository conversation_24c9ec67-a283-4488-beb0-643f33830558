package com.zizhiguanjia.model_account.fragment;

import android.graphics.Color;
import android.os.Bundle;
import android.text.Html;
import android.view.Gravity;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.constants.AccountRouterPath;
import com.zizhiguanjia.model_account.R;
import com.zizhiguanjia.model_account.databinding.AccountUpdataPhoneLayoutBinding;
import com.zizhiguanjia.model_account.navigator.UpdataPhoneNavigator;
import com.zizhiguanjia.model_account.viewmodel.UpdataPhoneViewModel;
@Route(path = AccountRouterPath.MAIN_UPDATE_PHONE)
@BindRes( statusBarStyle= BarStyle.LIGHT_CONTENT,swipeBack = SwipeStyle.NONE)
public class UpdataPhoneFragment extends BaseFragment implements UpdataPhoneNavigator, TitleBar.OnTitleBarListener {
    private AccountUpdataPhoneLayoutBinding binding;
    @BindViewModel
    UpdataPhoneViewModel updataPhoneViewModel;
    private BasePopupView loadingPopu;
    @Override
    public int initLayoutResId() {
        return R.layout.account_updata_phone_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding=getBinding();
        binding.setModel(updataPhoneViewModel);
        updataPhoneViewModel.initParams(this,this);
        binding.tbSendCode.setOnClickListener(this);
        binding.tbAccountUpdata.setClickListener(this);
        String tes1="<b>提示：</b>更换手机号后，下次登录可使用新手机号登录；更换手机号不会删除账户里的数据；";
        binding.tvTsMsg.setText(Html.fromHtml(tes1));
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        binding.tbSendCode.onDestroy();
    }

    @Override
    public void initSendCodeConfig() {
        binding.tbSendCode.setTextAfter("s后获取");
        binding.tbSendCode.setLenght(60*1000);
        binding.tbSendCode.setTextBefore("获取验证码");
    }

    @Override
    public void initLoadingPopu() {
        loadingPopu=new PopupManager.Builder(getContext()).asLoading("发送中...",R.layout.popup_center_impl_loading);
    }

    @Override
    public void onClick(View v) {
        updataPhoneViewModel.onclick(v);
    }

    @Override
    public void sendSysCode() {
        binding.tbSendCode.setIsStarTime();
    }

    @Override
    public void showLoading(boolean visition) {
        if(visition){
            if(loadingPopu.isShow())return;
            loadingPopu.show();
        }else {
           if(loadingPopu.isShow()){
               loadingPopu.dismiss();
           }
        }
    }

    @Override
    public void successSysCode() {
        binding.tbSendCode.setIsStarTime();
    }

    @Override
    public void successUpdataPhone() {
        finish();
    }

    @Override
    public void setGlColor(boolean success) {
        binding.tbSendCode.setTextColor(success? Color.parseColor("#007AFF"):Color.parseColor("#9196A0"));
    }

    @Override
    public void showErrorMsg(String msg) {
        if(msg==null||msg.isEmpty())return;
        ToastUtils.normal(msg, Gravity.CENTER);
    }

    @Override
    public void onClicked(View v, int action) {
        if(action==TitleBar.ACTION_LEFT_BUTTON){
            finish();
        }
    }
}
