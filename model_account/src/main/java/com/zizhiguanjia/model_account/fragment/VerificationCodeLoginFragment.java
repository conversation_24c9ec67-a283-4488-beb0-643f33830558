package com.zizhiguanjia.model_account.fragment;

import android.graphics.Color;
import android.os.Bundle;
import android.text.method.LinkMovementMethod;
import android.view.Gravity;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.constants.AccountRouterPath;
import com.zizhiguanjia.lib_base.db.AppDatabase;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.lib_base.msgconfig.UserType;
import com.zizhiguanjia.lib_base.tb.TableAccount;
import com.zizhiguanjia.model_account.R;
import com.zizhiguanjia.model_account.databinding.AccountVerificationcedeLayoutBinding;
import com.zizhiguanjia.model_account.navigator.LoginVerificationCodeNavigator;
import com.zizhiguanjia.model_account.viewmodel.VerificationCodeViewModel;

import androidx.lifecycle.Observer;

@BindRes(statusBarStyle = BarStyle.DARK_CONTENT)
@Route(path = AccountRouterPath.MAIN_VERLOGIN_PHONE)
public class VerificationCodeLoginFragment extends BaseFragment implements LoginVerificationCodeNavigator, View.OnClickListener {
    private AccountVerificationcedeLayoutBinding binding;
    @BindViewModel
    private VerificationCodeViewModel model;
    private LoadingPopupView loading;
    private BasePopupView userPreMissDialog;
    private String tel;
    public static VerificationCodeLoginFragment getInstance(String tel){
        VerificationCodeLoginFragment verificationCodeLoginFragment=new VerificationCodeLoginFragment();
        Bundle bundle=new Bundle();
        bundle.putString("tel",tel);
        verificationCodeLoginFragment.setArguments(bundle);
        return verificationCodeLoginFragment;
    }
    @Override
    public int initLayoutResId() {
        return R.layout.account_verificationcede_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();
        model.initParams(this, this);
        model.initUserPremissDiaglog(binding.loginYsxySelectCb);
        binding.setMode(model);
        binding.tbLoginCode.onCreate(savedInstanceState);
        PointHelper.joinPointData(PointerMsgType.POINTER_LOGIN_ACCOUNT,false);
        tel=getBundle().getString("tel","");
        if(!StringUtils.isEmpty(tel)){
            model.accountPhone.set(tel);
            model.accountPhoneCode.set("");
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if(model.isOpenWx){
            showLoading(false, null);
            model.isOpenWx=false;
        }
        //如果登录状态则退出这个页面
        if(isLogin()){
            finish();
        }
    }

    /**
     * 判断是否登录
     */
    private boolean isLogin(){
        TableAccount tableAccount = AppDatabase.getInstance().accountDao().getAccountInfo();
        if(tableAccount==null){
            return false;
        }else {
            int userType = UserHelper.getUserTypeByAccount(tableAccount.getAccount());
            return userType != UserType.USER_TYPE_VISITOR;
        }
    }

    @Override
    public void initViewData() {
        binding.loginYsxyTv.setMovementMethod(LinkMovementMethod.getInstance());
        binding.tbLoginCode.setOnClickListener(this);
    }

    @Override
    public void initObservable() {
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if (msgEvent.getCode() == AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS) {
                    //登录成功需要消除
                    finish();
                }else if(msgEvent.getCode()==0x879999){
                    model.accountPhone.set(msgEvent.getMsg());
                    model.accountPhoneCode.set("");
                }
            }
        });
    }

    @Override
    public void userSelectYsxy(boolean select) {
        binding.loginYsxySelectCb.setSelected(select);
        if (select) showUserPreMissDialog();
        hindUserPreMissDialog();
    }

    @Override
    public void initLoadConfig() {
        loading = new PopupManager.Builder(getContext()).asLoading("登录中...",R.layout.popup_center_impl_loading);
    }
    @Override
    public void showLoading(boolean visition, String msg) {
        if (loading == null) return;
        if (visition) {
            if (loading.isShow()) return;
            loading.setTitle(msg);
            loading.show();
        } else {
            if (loading.isShow()) loading.dismiss();
        }
    }
    @Override
    public void toBindPhonePage(String name, String openId,String uniId) {
        initArguments().putString("thridName", name);
        initArguments().putString("thridOpenId", openId);
        initArguments().putString("unionId", uniId);
        startFragment(new BinPhoneFragment());
    }
    @Override
    public void successThridLogin() {
    }
    @Override
    public void showToastMsg(String msg) {
        if(msg==null||msg.isEmpty())return;
        ToastUtils.normal(msg, Gravity.CENTER);
    }

    @Override
    public void loginSuccess() {
        Bus.post(new MsgEvent(AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS));
    }
    @Override
    public void initUserPremissDiaglog(BasePopupView basePopupView) {
        this.userPreMissDialog=basePopupView;
    }

    @Override
    public void showUserPreMissDialog() {
        if (userPreMissDialog == null) return;
        userPreMissDialog.show();
    }
    @Override
    public void hindUserPreMissDialog() {
        if (userPreMissDialog == null) return;
        if (userPreMissDialog.isShow()) userPreMissDialog.dismiss();
    }

    @Override
    public void successCode() {
        binding.tbLoginCode.setIsStarTime();
    }

    @Override
    public void initSendCodeConfig() {
        binding.tbLoginCode.setTextAfter("s后获取");
        binding.tbLoginCode.setLenght(60 * 1000);
        binding.tbLoginCode.setTextBefore("获取验证码");
    }

    @Override
    public void setGlColor(boolean success) {
        binding.tbLoginCode.setTextColor(success? Color.parseColor("#007AFF"):Color.parseColor("#9196A0"));
    }

    @Override
    public void deviceSd(String msg) {
        openPermiss(msg);
    }

    @Override
    public void restTel(String tel) {
        model.accountPhone.set(tel);
        model.accountPhoneCode.set("");
    }

    private  void openPermiss(String msg){
        model.openPermiss(msg);
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        try {
            if(binding.tbLoginCode==null)return;
            binding.tbLoginCode.onDestroy();
        }catch (Exception e){
        }
    }

    @Override
    public void onClick(View v) {
        model.onclick(v);
    }
}
