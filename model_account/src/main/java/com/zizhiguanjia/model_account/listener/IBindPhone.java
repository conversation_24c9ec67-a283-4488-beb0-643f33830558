package com.zizhiguanjia.model_account.listener;

import android.text.Editable;
import android.view.View;

import com.zizhiguanjia.model_account.fragment.BinPhoneFragment;
import com.zizhiguanjia.model_account.navigator.BindPhoneNavigator;

public interface IBindPhone {
    void initParams(BinPhoneFragment updataPhoneFragment, BindPhoneNavigator navigator);
    void setThridInfo(String name,String ids,String unionId);
    void onclick(View view);
    void onPhoneNumEditTextChanged(Editable editable);
    void onPhoneCodeEditTextChanged(Editable editable);
    void sendCode();
    void postBindPhoneHttp();
    boolean checkData(int type);
}
