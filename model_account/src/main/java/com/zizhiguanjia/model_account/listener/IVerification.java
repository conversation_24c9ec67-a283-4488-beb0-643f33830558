package com.zizhiguanjia.model_account.listener;

import android.text.Editable;
import android.view.View;

import com.zizhiguanjia.model_account.fragment.VerificationCodeLoginFragment;
import com.zizhiguanjia.model_account.navigator.LoginVerificationCodeNavigator;

public interface IVerification {
    void initParams(LoginVerificationCodeNavigator navigator, VerificationCodeLoginFragment verificationCodeLoginFragment);
    void setNavigator(LoginVerificationCodeNavigator navigator);
    void getSpanLoginTxt();
    void onclick(View view);
    void thridLogin();
    void isBindThridLogin(String code);
    void onPhoneEditTextChanged(Editable editable);
    void onPhoneCodeEditTextChanged(Editable editable);
    boolean checkUserPreMiss();
    void postSysCodeHttp();
    void postLoginHttp();
    boolean checkData(int type);
    void openPermiss(String msg);
    void initUserPremissDiaglog(View mView);
    void permissionsPrompt();

}
