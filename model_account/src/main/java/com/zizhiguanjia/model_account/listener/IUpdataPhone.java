package com.zizhiguanjia.model_account.listener;

import android.text.Editable;
import android.view.View;

import com.zizhiguanjia.model_account.fragment.UpdataPhoneFragment;
import com.zizhiguanjia.model_account.navigator.UpdataPhoneNavigator;

public interface IUpdataPhone {
    void onclick(View view);
    void initParams(UpdataPhoneFragment updataPhoneFragment, UpdataPhoneNavigator navigator);
    void onOldEditTextChanged(Editable editText);
    void onNewEditTextChanged(Editable editText);
    void onCodeEditTextChanged(Editable editText);
    void sendCode(String phone);
    void postUpdataPhoneHttp();
    boolean checkData(int type);
}
