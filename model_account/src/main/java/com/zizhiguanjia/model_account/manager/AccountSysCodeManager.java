package com.zizhiguanjia.model_account.manager;

import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.listeners.SendSysyCodeListener;
import com.zizhiguanjia.model_account.help.AccountSysCodeHelp;

import java.io.IOException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

public class AccountSysCodeManager {
    private static  AccountSysCodeManager accountSysCodeManager;
    public static  AccountSysCodeManager getInstance(){
        if(accountSysCodeManager==null){
            synchronized (AccountSysCodeManager.class){
                return accountSysCodeManager=new AccountSysCodeManager();
            }
        }
        return accountSysCodeManager;
    }
    public void sendSysCodeByType(String type, String phone, SendSysyCodeListener sendSysyCodeListener){
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("type", type);
        multiBuilder.addFormDataPart("phone", phone);
        RequestBody multiBody=multiBuilder.build();
        AccountSysCodeHelp.getInstance().upload(BaseAPI.VERSION_DES+"/API/SmsCode/SendPhoneCodeApp", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        sendSysyCodeListener.afterGetSysCode(false,e.getLocalizedMessage());
                    }
                });
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if(response.code()==200){
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            BaseData baseData= null;
                            try {
                                baseData = GsonUtils.gsonToBean(response.body().string(), BaseData.class);
                                if(baseData==null){
                                    sendSysyCodeListener.afterGetSysCode(false,"获取验证码失败！");
                                }else {
                                    if(baseData.Code.equals("200")){
                                        sendSysyCodeListener.afterGetSysCode(true,baseData.Message);
                                    }else {
                                        sendSysyCodeListener.afterGetSysCode(false,baseData.Message);
                                    }
                                }
                            } catch (IOException e) {
                                sendSysyCodeListener.afterGetSysCode(false,e.getLocalizedMessage());
                            }

                        }
                    });
                }

            }
        });
    }
}
