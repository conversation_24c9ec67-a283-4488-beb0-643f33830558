package com.zizhiguanjia.model_account.manager;

import android.view.Gravity;

import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_arch.utils.AppManager;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.db.AppDatabase;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.listeners.AutoOneKeyLoginListener;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.UserType;
import com.zizhiguanjia.lib_base.tb.TableAccount;
import com.zizhiguanjia.lib_base.tb.TableUserInfo;
import com.zizhiguanjia.lib_base.utils.NoDoubleClickUtils;
import com.zizhiguanjia.model_account.help.AccountSysCodeHelp;

import java.io.IOException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

public class AccountManager implements AutoOneKeyLoginListener {
    private static AccountManager accountManager;

    public static AccountManager getInstance() {
        if (accountManager == null) {
            synchronized (AccountManager.class) {
                return accountManager = new AccountManager();
            }
        }
        return accountManager;
    }

    public void exitAccount() {
        try {
            String uid=AccountHelper.getUserId();
            SdkHelper.deleteAlias(uid);
        }catch (Exception e){
        }
        try {
            accountExitPostData();
        }catch (Exception e){
        }

    }

    public void updataUserType(int userType) {
        TableAccount tableAccount = getCurrentAccountInfo();
        tableAccount.setAccount(String.valueOf(userType));
        tableAccount.setLastTime(System.currentTimeMillis());
        addAccountOrUpdataAccount(tableAccount, false);
    }

    public TableAccount getCurrentAccountInfo() {
        return AppDatabase.getInstance().accountDao().getAccountInfo();
    }

    public TableAccount defaultAccount() {
        TableAccount tableAccount = new TableAccount();
        tableAccount.setAccount(String.valueOf(UserType.USER_TYPE_VISITOR));
        tableAccount.setLastTime(System.currentTimeMillis());
        return tableAccount;
    }

    public void initAccountInfo() {
        TableAccount tableAccount = AppDatabase.getInstance().accountDao().getAccountInfo();
        if (tableAccount == null) {
            TableAccount tableAccount1 = defaultAccount();
            addAccountOrUpdataAccount(tableAccount1, true);
        }

    }

    public void successLoginAccount(String json) throws Exception {
        TableUserInfo tableUserInfo = GsonUtils.gsonToBean(json, TableUserInfo.class);
        if (tableUserInfo == null) {
            throw new Exception("string is not json");
        }
        if (StringUtils.isEmpty(tableUserInfo.getAccount())) {
            throw new Exception("account is not empty");
        }
        TableAccount tableAccount = getCurrentAccountInfo();
        if (tableAccount == null) {
            tableAccount = new TableAccount();
            tableAccount.setAccount(tableUserInfo.getAccount());
            tableAccount.setLastTime(System.currentTimeMillis());
            addAccountOrUpdataAccount(tableAccount, true);
        } else {
            tableAccount.setAccount(tableUserInfo.getAccount());
            tableAccount.setLastTime(System.currentTimeMillis());
            addAccountOrUpdataAccount(tableAccount, false);
        }
        SdkHelper.registUserId(tableUserInfo.getUserId());
        UserHelper.addUserInfo(json);
        if(tableUserInfo.isVip()){
            UserHelper.passVip();
        }
    }

    public void addAccountOrUpdataAccount(TableAccount tableAccount, boolean add) {
        if (add) {
            AppDatabase.getInstance().accountDao().addAccount(tableAccount);
        } else {
            AppDatabase.getInstance().accountDao().updataAccount(tableAccount);
        }
    }

    public String getCurrentLoginAccount() {
        TableAccount tableAccount = getCurrentAccountInfo();
        if (tableAccount == null) {
            initAccountInfo();
            return String.valueOf(UserType.USER_TYPE_VISITOR);
        } else {
            tableAccount.setLastTime(System.currentTimeMillis());
            addAccountOrUpdataAccount(tableAccount, false);
        }
        return tableAccount.getAccount();
    }

    public boolean checkAccountState() {
        boolean mLogin = false;
        TableAccount tableAccount = AppDatabase.getInstance().accountDao().getAccountInfo();
        if (tableAccount == null) mLogin = false;
        int userType = UserHelper.getUserTypeByAccount(tableAccount.getAccount());
        if (userType == UserType.USER_TYPE_VISITOR) {
            mLogin = false;
        } else {
            mLogin = true;
        }
//        boolean mLogin=UserHelper.checkUserLogin();
        if (mLogin) return true;
        if(!NoDoubleClickUtils.isDoubleClick()){
            SdkHelper.startOneKeylogin(this);
        }

        return false;
    }

    @Override
    public void afterGetOnkeyToken(int successCode, String msg) {
        if (successCode == 0x1) {
            Bus.post(new MsgEvent(AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_SUCCESS));
            if(msg==null||msg.isEmpty())return;
            ToastUtils.normal(msg, Gravity.CENTER);
        } else if (successCode == 0x2) {
            if(AppManager.getInstance().currentFragment().getClass().getName().contains("VerificationCodeLoginFragment"))return;
            AccountHelper.start(AppUtils.getApp());
        } else if (successCode == 0x3) {
            if(msg==null||msg.isEmpty())return;
            ToastUtils.normal(msg, Gravity.CENTER);

        }
    }
    public String getUserId(){
        TableUserInfo tableUserInfo=UserHelper.getUserInfo();
        if(tableUserInfo==null){
            return String.valueOf(UserType.USER_TYPE_VISITOR);
        }
        return tableUserInfo.getUserId();
    }

    public int getAccountLoginType() {
        TableAccount tableAccount = AppDatabase.getInstance().accountDao().getAccountInfo();
        if (tableAccount == null) return -1;
        int userType = UserHelper.getUserTypeByAccount(tableAccount.getAccount());
        return userType;
    }
    public boolean loginSuperintendent(){
        return loginSuperintendent(true);
    }
    /**
     * 登录验证
     * @param oneKeyLoginCheckDoubleClick 一键登录是否验证二次点击，防止其他地方的点击操作加了二次验证导致这里无法触发弹窗
     * @return true-已登录
     */
    public boolean loginSuperintendent(boolean oneKeyLoginCheckDoubleClick){
        boolean mLogin=false;
        TableAccount tableAccount = AppDatabase.getInstance().accountDao().getAccountInfo();
        if(tableAccount==null){
            mLogin = false;
        }else {
            int userType = UserHelper.getUserTypeByAccount(tableAccount.getAccount());
            if (userType == UserType.USER_TYPE_VISITOR) {
                mLogin = false;
            }else {
                mLogin = true;
            }
        }
        if (mLogin) return true;
        if(!oneKeyLoginCheckDoubleClick){
            SdkHelper.startOneKeylogin(this);
        }else if(!NoDoubleClickUtils.isDoubleClick()){
            SdkHelper.startOneKeylogin(this);
        }
        return mLogin;
    }
    public void accountExitPostData(){
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("","");
        RequestBody multiBody=multiBuilder.build();
        AccountSysCodeHelp.getInstance().upload(BaseAPI.VERSION_DES+"/API/user/logout", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if(response.code()==200){
                        BaseData baseData = GsonUtils.gsonToBean(response.body().string(), BaseData.class);
                    }
                }catch (Exception e){
                }
            }
        });
    }
}
