package com.zizhiguanjia.model_account.viewmodel;

import android.graphics.Color;
import android.text.Editable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.Gravity;
import android.view.View;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.exception.RxException;
import com.wb.lib_rxtools.subsciber.BaseSubscriber;
import com.wb.lib_utils.utils.DataUtils;
import com.wb.lib_utils.utils.DeviceUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.ClientShaperPlamTypeConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.lib_base.helper.ConfigHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.helper.SdkHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.listeners.ILoginVipTs;
import com.zizhiguanjia.lib_base.listeners.SendSysyCodeListener;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.lib_base.utils.NetWorkUtils;
import com.zizhiguanjia.lib_base.utils.SpannableStringUtils;
import com.zizhiguanjia.lib_base.utils.ThirdPlatformOptionsUtils;
import com.zizhiguanjia.model_account.R;
import com.zizhiguanjia.model_account.api.AccountService;
import com.zizhiguanjia.model_account.config.AccountLoginStateType;
import com.zizhiguanjia.model_account.config.AccountType;
import com.zizhiguanjia.model_account.fragment.VerificationCodeLoginFragment;
import com.zizhiguanjia.model_account.listener.IVerification;
import com.zizhiguanjia.model_account.manager.AccountSysCodeManager;
import com.zizhiguanjia.model_account.model.LoginBean;
import com.zizhiguanjia.model_account.navigator.LoginVerificationCodeNavigator;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;
import cn.hutool.core.util.PhoneUtil;
import io.reactivex.disposables.Disposable;

public class VerificationCodeViewModel extends CommonViewModel implements IVerification {
    private LoginVerificationCodeNavigator navigator;
    public ObservableField<SpannableStringBuilder> ysxyObservableField = new ObservableField<>();
    private AccountService mApi = Http.getInstance().create(AccountService.class);
    public ObservableField<String> accountPhone = new ObservableField<>();
    public ObservableField<String> accountPhoneCode = new ObservableField<>();
    public ObservableField<Boolean> accountYsxy = new ObservableField<>();
    public ObservableField<Boolean> accountLoginVer = new ObservableField<>();
    private VerificationCodeLoginFragment verificationCodeLoginFragment;
    private Disposable timeDisposable;
    public boolean isOpenWx = false;

    @Override
    public void initParams(LoginVerificationCodeNavigator navigator, VerificationCodeLoginFragment verificationCodeLoginFragment) {
        this.verificationCodeLoginFragment = verificationCodeLoginFragment;
        setNavigator(navigator);
        getSpanLoginTxt();
        navigator.initLoadConfig();
        navigator.initSendCodeConfig();
        accountYsxy.set(false);
    }

    @Override
    public void setNavigator(LoginVerificationCodeNavigator navigator) {
        this.navigator = navigator;
    }

    @Override
    public void getSpanLoginTxt() {
        SpannableStringBuilder textFrontColorSp = SpannableStringUtils.getBuilder("我已经阅读并同意")
                .setForegroundColor(Color.parseColor("#A8A8A8"))
                .append("《用户服务协议》").setForegroundColor(Color.parseColor("#689DF6"))
                .setClickSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        verificationCodeLoginFragment.initArguments().putString("url", BaseAPI.BASE_YHXY_URL);
                        verificationCodeLoginFragment.startFragment(CommonHelper.showCommonWeb());
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        ds.setUnderlineText(false);
                    }
                })
                .append("与").setForegroundColor(Color.parseColor("#A8A8A8"))
                .append("《安全员考试宝典隐私政策》").setForegroundColor(Color.parseColor("#689DF6")).setClickSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        verificationCodeLoginFragment.initArguments().putString("url", BaseAPI.BASE_XSYT_URL);
                        verificationCodeLoginFragment.startFragment(CommonHelper.showCommonWeb());
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        ds.setUnderlineText(false);
                    }
                })
                .create();
        ysxyObservableField.set(textFrontColorSp);
    }

    @Override
    public void onclick(View view) {
        if (view.getId() == R.id.login_ysxy_select_cb) {
            if (view.isSelected()) {
                accountYsxy.set(false);
                navigator.userSelectYsxy(false);
            } else {
                accountYsxy.set(true);
                navigator.userSelectYsxy(true);
            }
            checkUserPreMiss();
        } else if (view.getId() == R.id.imgThridWxLogin) {
            boolean sendCode = checkUserPreMiss();
            if (sendCode) thridLogin();
        } else if (view.getId() == R.id.tbLoginCode) {
            boolean sendCode = checkUserPreMiss();
            if (sendCode) postSysCodeHttp();
        } else if (view.getId() == R.id.tvLogin) {
            if (accountYsxy.get()) {
                PointHelper.joinPointData(PointerMsgType.POINTER_A_VERIFY_CODE_LOGIN, false);
                postLoginHttp();
            } else {
                checkUserPreMiss();
            }
        } else if (view.getId() == R.id.imgClose) {
            verificationCodeLoginFragment.finish();
        }
    }

    @Override
    public void thridLogin() {
        PointHelper.joinPointData(PointerMsgType.POINTER_W_WECHAT_LOGIN, false);
        if (!checkData(AccountType.ACCOUTN_THRIDER_LOGIN)) return;
        navigator.showLoading(true, "授权中...");
        isOpenWx = true;
        SdkHelper.startThridLogin(ClientShaperPlamTypeConfig.Wx, new ThirdPlatformOptionsUtils.ThirdPlatformOptionsCallback() {
            @Override
            public void onWechatLoginRepCode(String code) {
                super.onWechatLoginRepCode(code);
                isOpenWx = false;
                navigator.showLoading(false, null);
                if (code != null && !code.isEmpty()) {
                    //授权成功
                    isBindThridLogin(code);
                }else {
                    navigator.showToastMsg("获取授权信息错误，请重新获取!");
                }
            }
        });
    }

    @Override
    public void isBindThridLogin(String code) {
        if (!checkData(AccountType.ACCOUNT_BINDTHRIRDE)) return;
        Map<String, String> params = new HashMap<>();
        params.put("code", code);
        params.put("channelUID", BaseConfig.channelUID);
        if(BaseConfig.couponid==null||BaseConfig.couponid.isEmpty()){
        }else{
            params.put("couponId", BaseConfig.couponid);
        }
        navigator.showLoading(true, "请稍后...");
        launchOnlyResult(mApi.isBindThridLogin(params), new OnHandleException<BaseData<LoginBean>>() {
            @Override
            public void success(BaseData<LoginBean> data) {
                navigator.showLoading(false, null);
                if (data.Code.equals(BaseConfig.UNBOUND_CODE)) {
                    //未绑定
                    navigator.toBindPhonePage("", data.getResult().getOpenId(),data.getResult().getUnionId());
                } else {
                    //已绑定
                    try {
                        if(data.getResult().getLoginState()== AccountLoginStateType.LOGIN_TYPE_NORMAL){
                            navigator.showToastMsg(data.Message);
                            AccountHelper.successLoginAccount(com.wb.lib_network.utils.GsonUtils.gsonString(data.Data));
                            navigator.loginSuccess();
                        }else if(data.getResult().getLoginState()== AccountLoginStateType.LOGIN_TYPE_UNSEAL){
                            if(StringUtils.isEmpty(data.getResult().getToKen())){
                                navigator.showToastMsg("参数异常，请重试~");
                                return;
                            }
                            MessageHelper.openAccountLogOffSwithTs(verificationCodeLoginFragment.getActivity(),data.getResult().getToKen());
                        }else if(data.getResult().getLoginState()== AccountLoginStateType.LOGIN_TYPE_VIP_TS){
                            MessageHelper.openAccountVipTs(verificationCodeLoginFragment.getActivity(),data.getResult().getSubjectName() , data.getResult().getSubjectName(), data.getResult().getVipPhone(), data.getResult().getToKen(), new ILoginVipTs() {
                                @Override
                                public void onGoTel(String tel) {
                                    navigator.restTel(tel);
                                }
                            });
                        }else {
                            MessageHelper.openAccountLock(verificationCodeLoginFragment.getActivity());
                        }
                    } catch (Exception e) {
                        navigator.showToastMsg(e.getLocalizedMessage());
                    }
                }

            }

            @Override
            public void error(String msg) {
                navigator.showLoading(false, null);
                navigator.showToastMsg(msg);
            }
        });
    }

    @Override
    public void onPhoneEditTextChanged(Editable editable) {
        accountPhone.set(editable.toString());
        accountLoginVer.set(StringUtils.isEmpty(accountPhone.get()) || StringUtils.isEmpty(accountPhoneCode.get()) ? false : true);
        navigator.setGlColor(PhoneUtil.isPhone(editable.toString()) ? true : false);
    }

    @Override
    public void onPhoneCodeEditTextChanged(Editable editable) {
        accountPhoneCode.set(editable.toString());
        accountLoginVer.set(StringUtils.isEmpty(accountPhone.get()) || StringUtils.isEmpty(accountPhoneCode.get()) ? false : true);
    }

    @Override
    public boolean checkUserPreMiss() {
        if (accountYsxy.get()) {
            //权限正常
            navigator.hindUserPreMissDialog();
            return true;
        } else {
            navigator.showUserPreMissDialog();
            if (timeDisposable != null) timeDisposable.dispose();
            timeDisposable = RxJavaUtils.countDown(5, new BaseSubscriber<Long>() {
                @Override
                public void onError(RxException e) {
                    timeDisposable.dispose();
                    navigator.hindUserPreMissDialog();
                }

                @Override
                public void onSuccess(Long aLong) {
                    if (aLong == 0 || accountYsxy.get()) {
                        timeDisposable.dispose();
                        navigator.hindUserPreMissDialog();
                    }
                }
            });
            return false;
        }
    }

    @Override
    public void postSysCodeHttp() {
        if (!checkData(AccountType.ACCOUNT_SYSYCODE)) return;
        navigator.showLoading(true, "发送中...");
        AccountSysCodeManager.getInstance().sendSysCodeByType("2", accountPhone.get(), new SendSysyCodeListener() {
            @Override
            public void afterGetSysCode(boolean state, String msg) {
                navigator.showLoading(false, null);
                if (state) {
                    //发送成功
                    navigator.successCode();
                    navigator.showToastMsg(msg);
                } else {
                    ToastUtils.normal(msg, Gravity.CENTER);
                }
            }
        });
    }

    @Override
    public void postLoginHttp() {
        if (!checkData(AccountType.ACCOUNT_LOGIN)) return;
        navigator.showLoading(true, "登录中...");
        Map<String, String> params = new HashMap<>();
        params.put("phone", accountPhone.get());
        params.put("phoneCode", accountPhoneCode.get());
        params.put("channelUID", BaseConfig.channelUID);
        if(BaseConfig.couponid==null||BaseConfig.couponid.isEmpty()){
        }else{
            params.put("couponId", BaseConfig.couponid);
        }
        launchOnlyResult(mApi.postLogin(params), new OnHandleException<BaseData<LoginBean>>() {
            @Override
            public void success(BaseData<LoginBean> data) {
                navigator.showLoading(false, null);
                try {
                    if(data.getResult().getLoginState()== AccountLoginStateType.LOGIN_TYPE_NORMAL){
                        navigator.showToastMsg(data.Message);
                        AccountHelper.successLoginAccount(com.wb.lib_network.utils.GsonUtils.gsonString(data.Data));
                        navigator.loginSuccess();
                    }else if(data.getResult().getLoginState()== AccountLoginStateType.LOGIN_TYPE_UNSEAL){
                        if(StringUtils.isEmpty(data.getResult().getToKen())){
                            navigator.showToastMsg("参数异常，请重试~");
                            return;
                        }
                        MessageHelper.openAccountLogOffSwithTs(verificationCodeLoginFragment.getActivity(),data.getResult().getToKen());
                    }else if(data.getResult().getLoginState()== AccountLoginStateType.LOGIN_TYPE_VIP_TS){
                        MessageHelper.openAccountVipTs(verificationCodeLoginFragment.getActivity(),data.getResult().getSubjectName() , data.getResult().getSubjectName(), data.getResult().getVipPhone(), data.getResult().getToKen(), new ILoginVipTs() {
                            @Override
                            public void onGoTel(String tel) {
                                navigator.restTel(tel);
                            }
                        });
                    }else {
                        MessageHelper.openAccountLock(verificationCodeLoginFragment.getActivity());
                    }
                } catch (Exception e) {
                    navigator.showToastMsg(e.getLocalizedMessage());
                }
            }

            @Override
            public void error(String msg) {
                navigator.showLoading(false, null);
                if (msg.contains("锁定")) {
                    navigator.deviceSd(msg);
                } else {
                    navigator.showToastMsg(msg);
                }
            }
        });
    }

    @Override
    public boolean checkData(int type) {
        if (type == AccountType.ACCOUNT_LOGIN) {
            String phoneNum = accountPhone.get();
            String phoneCode = accountPhoneCode.get();
            if (StringUtils.isEmpty(phoneNum)) {
                navigator.showToastMsg("手机号码不能为空！");
                return false;
            }
            if (StringUtils.isEmpty(phoneCode)) {
                navigator.showToastMsg("验证码不能为空！");
                return false;
            }
            if (!PhoneUtil.isMobile(phoneNum)) {
                navigator.showToastMsg("手机号码不合法！");
                return false;

            }
            if (!DataUtils.isNumber(phoneCode)) {
                navigator.showToastMsg("验证码不合法！");
                return false;
            }
            if (!NetWorkUtils.getInstance().checkNetWork()) {
                return false;
            }
            return true;
        } else if (type == AccountType.ACCOUTN_THRIDER_LOGIN) {
            if (!NetWorkUtils.getInstance().checkNetWork()) {
                return false;
            }
            return true;
        } else if (type == AccountType.ACCOUNT_SYSYCODE) {
            if (StringUtils.isEmpty(accountPhone.get())) {
                navigator.showToastMsg("手机号码不能为空！");
                return false;
            }
            if (!PhoneUtil.isMobile(accountPhone.get())) {
                navigator.showToastMsg("手机号码不合法！");
                return false;
            }
            if (!NetWorkUtils.getInstance().checkNetWork()) {
                return false;
            }
            return true;
        } else if (type == AccountType.ACCOUNT_BINDTHRIRDE) {
            if (!NetWorkUtils.getInstance().checkNetWork()) {
                return false;
            }
            return true;
        } else {

        }
        return false;
    }

    @Override
    public void openPermiss(String msg) {
        MessageHelper.openGeneralCentDialog(verificationCodeLoginFragment.getActivity(),
                "温馨提示",
                msg, "",
                "联系我们",
                true, false, new GeneralDialogListener() {
            @Override
            public void onCancel() {
            }
            @Override
            public void onConfim() {
                permissionsPrompt();
            }

                    /**
                     *
                     */
                    @Override
                    public void onDismiss() {

                    }
                });
    }

    @Override
    public void initUserPremissDiaglog(View mView) {
        BasePopupView userPreMissDialog=MessageHelper.openAccountPrivacyTipic(verificationCodeLoginFragment.getActivity(),mView);
        navigator.initUserPremissDiaglog(userPreMissDialog);
    }

    @Override
    public void permissionsPrompt() {
//        PermissionUtils.with(verificationCodeLoginFragment.getActivity()).addPermissions(Manifest.permission.CALL_PHONE)
//                .setPermissionsCheckListener(new PermissionListener() {
//                    @Override
//                    public void permissionRequestSuccess() {
//                        DeviceUtils.callPhone(verificationCodeLoginFragment.getContext(), ConfigHelper.getKfTel());
//                    }
//                    @Override
//                    public void permissionRequestFail(String[] grantedPermissions, String[] deniedPermissions, String[] forceDeniedPermissions) {
//                        MessageHelper.permissionsTips(verificationCodeLoginFragment.getActivity(),"温馨提示",
//                                "请前往设置->应用->【" + PermissionUtils.getAppName(verificationCodeLoginFragment.getActivity()) + "】->权限中打开打电话权限，否则功能无法正常运行！","确定");
//                    }
//                })
//                .createConfig()
//                .setForceAllPermissionsGranted(false)
//                .setForceDeniedPermissionTips("请前往设置->应用->【" + PermissionUtils.getAppName(verificationCodeLoginFragment.getContext()) + "】->权限中打开相关权限，否则功能无法正常运行！")
//                .buildConfig()
//                .startCheckPermission();
        DeviceUtils.dial(verificationCodeLoginFragment.getContext(), ConfigHelper.getKfTel());
    }


}
