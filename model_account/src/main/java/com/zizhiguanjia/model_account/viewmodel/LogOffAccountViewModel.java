package com.zizhiguanjia.model_account.viewmodel;

import android.view.View;

import androidx.databinding.ObservableField;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.model_account.R;
import com.zizhiguanjia.model_account.api.AccountService;
import com.zizhiguanjia.model_account.fragment.LogOffAccountFragment;
import com.zizhiguanjia.model_account.navigator.LogOffAccountNavigator;

import java.util.HashMap;

import io.reactivex.functions.Consumer;

public class LogOffAccountViewModel extends CommonViewModel {
    private LogOffAccountNavigator navigator;
    private LogOffAccountFragment logOffAccountFragment;
    private AccountService api= Http.getInstance().create(AccountService.class);
    public void initParams(LogOffAccountNavigator logOffAccountNavigator,LogOffAccountFragment logOffAccountFragment){
        this.navigator=logOffAccountNavigator;
        this.logOffAccountFragment=logOffAccountFragment;
        navigator.initLoadingPopu();
        ysxy.set(false);
    }
    public ObservableField<Boolean> ysxy=new ObservableField<>();
    public void onClick(View view){
        if(view.getId()== R.id.imgYsxy){
            if(ysxy.get()){
                ysxy.set(false);
            }else {
                ysxy.set(true);
            }
        }else if(view.getId()==R.id.tvlogOffPhone){
            if(ysxy.get()){
                postLogOffAccount();
            }else {
                navigator.showErrorMsg("请先确认以上协议~");
            }
        }
    }
    private void postLogOffAccount(){
        navigator.showLoading(true);
        launchOnlyResult(api.logOffAccount(new HashMap<>()), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                navigator.showLoading(false);
                navigator.showErrorMsg(data.getMsg());
                RxJavaUtils.delay(1, new Consumer<Long>() {
                    @Override
                    public void accept(Long aLong) throws Exception {
                        MainThreadUtils.post(new Runnable() {
                            @Override
                            public void run() {
                                Bus.post(new MsgEvent(AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT));
                            }
                        });
                    }
                });
            }

            @Override
            public void error(String msg) {
                navigator.showLoading(false);
                navigator.showErrorMsg(msg);
            }
        });
    }
}
