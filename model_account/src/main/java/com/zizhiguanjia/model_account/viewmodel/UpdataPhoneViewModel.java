package com.zizhiguanjia.model_account.viewmodel;

import android.accounts.AccountManager;
import android.text.Editable;
import android.view.Gravity;
import android.view.View;
import android.widget.EditText;

import androidx.databinding.ObservableField;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.listeners.SendSysyCodeListener;
import com.zizhiguanjia.lib_base.utils.NetWorkUtils;
import com.zizhiguanjia.model_account.R;
import com.zizhiguanjia.model_account.api.AccountService;
import com.zizhiguanjia.model_account.config.AccountType;
import com.zizhiguanjia.model_account.fragment.UpdataPhoneFragment;
import com.zizhiguanjia.model_account.listener.IUpdataPhone;
import com.zizhiguanjia.model_account.manager.AccountSysCodeManager;
import com.zizhiguanjia.model_account.navigator.UpdataPhoneNavigator;

import java.util.HashMap;
import java.util.Map;

import cn.hutool.core.util.PhoneUtil;

public class UpdataPhoneViewModel extends CommonViewModel implements IUpdataPhone {
    private UpdataPhoneFragment updataPhoneFragment;
    private UpdataPhoneNavigator navigator;
//    public ObservableField<String> oldPhoneObs = new ObservableField<>();
    public ObservableField<String> newPhoneObs = new ObservableField<>();
    public ObservableField<String> phoneCodeObs = new ObservableField<>();
    private AccountService mApi = Http.getInstance().create(AccountService.class);
    public ObservableField<Boolean>accountLoginVer=new ObservableField<>();
    public ObservableField<String> tsMsg=new ObservableField<>();
    @Override
    public void onclick(View view) {
        if (view.getId() == R.id.tbSendCode) {
            //发送验证码
            sendCode(newPhoneObs.get());
        } else if (view.getId() == R.id.tvPostUpdataPhone) {
            postUpdataPhoneHttp();
        }
    }
    @Override
    public void initParams(UpdataPhoneFragment updataPhoneFragment, UpdataPhoneNavigator navigator) {
        this.updataPhoneFragment = updataPhoneFragment;
        this.navigator = navigator;
        navigator.initSendCodeConfig();
        navigator.initLoadingPopu();
    }
    @Override
    public void onOldEditTextChanged(Editable editText) {
//        oldPhoneObs.set(editText.toString());
//        if(StringUtils.isEmpty(oldPhoneObs.get())||StringUtils.isEmpty(newPhoneObs.get())||StringUtils.isEmpty(phoneCodeObs.get())){
//            accountLoginVer.set(false);
//        }else {
//            accountLoginVer.set(true);
//        }
    }
    @Override
    public void onNewEditTextChanged(Editable editText) {
        newPhoneObs.set(editText.toString());
        if(StringUtils.isEmpty(newPhoneObs.get())||StringUtils.isEmpty(phoneCodeObs.get())){
            accountLoginVer.set(false);
        }else {
            accountLoginVer.set(true);
        }
        navigator.setGlColor(PhoneUtil.isPhone(editText.toString())?true:false);

    }
    @Override
    public void onCodeEditTextChanged(Editable editText) {
        phoneCodeObs.set(editText.toString());
        if(StringUtils.isEmpty(newPhoneObs.get())||StringUtils.isEmpty(phoneCodeObs.get())){
            accountLoginVer.set(false);
        }else {
            accountLoginVer.set(true);
        }
    }
    @Override
    public void sendCode(String phone) {
        if(!checkData(AccountType.ACCOUNT_UPDATAPHONE_SENDCODE))return;
        navigator.showLoading(true);
        AccountSysCodeManager.getInstance().sendSysCodeByType("2", phone, new SendSysyCodeListener() {
            @Override
            public void afterGetSysCode(boolean state, String msg) {
                navigator.showLoading(false);
                ToastUtils.normal(msg, Gravity.CENTER);
                if (state) {
                    navigator.successSysCode();
                }
            }
        });

    }
    @Override
    public void postUpdataPhoneHttp() {
        if(!checkData(AccountType.ACCOUNT_UPDATAPHONE))return;
        Map<String, String> params = new HashMap<>();
        params.put("phone", newPhoneObs.get());
        params.put("phoneCode", phoneCodeObs.get());
        navigator.showLoading(true);
        launchOnlyResult(mApi.postUpdataPhone(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                navigator.showLoading(false);
                ToastUtils.normal("修改成功！", Gravity.CENTER);
                navigator.successUpdataPhone();
            }
            @Override
            public void error(String msg) {
                navigator.showLoading(false);
                ToastUtils.normal(msg, Gravity.CENTER);
            }
        });
    }

    @Override
    public boolean checkData(int type) {
        if(type== AccountType.ACCOUNT_UPDATAPHONE){
//            if (StringUtils.isEmpty(oldPhoneObs.get())) {
//                navigator.showErrorMsg("旧手机号码不能为空！");
//                return false;
//            }
            if (StringUtils.isEmpty(newPhoneObs.get())) {
                navigator.showErrorMsg("新手机号码不能为空！");
                return false;
            }
            if (StringUtils.isEmpty(phoneCodeObs.get())) {
                navigator.showErrorMsg("手机验证码不能为空！");
                return false;
            }
//            if (!PhoneUtil.isMobile(oldPhoneObs.get())) {
//                navigator.showErrorMsg("旧手机号码不合法！");
//                return false;
//            }
            if (!PhoneUtil.isMobile(newPhoneObs.get())) {
                navigator.showErrorMsg("新手机号码不合法！");
                return false;
            }
            if(!NetWorkUtils.getInstance().checkNetWork()){
                return false;
            }
            return true;
        }else if(type== AccountType.ACCOUNT_UPDATAPHONE_SENDCODE){
//            if (StringUtils.isEmpty(oldPhoneObs.get())) {
//                ToastUtils.normal("旧手机号码不能为空！", Gravity.CENTER);
//                return false;
//            }
            if (StringUtils.isEmpty(newPhoneObs.get())) {
                ToastUtils.normal("新手机号码不能为空！", Gravity.CENTER);
                return false;
            }
//            if (!PhoneUtil.isMobile(oldPhoneObs.get())) {
//                ToastUtils.normal("旧手机号码不合法！", Gravity.CENTER);
//                return false;
//            }
            if (!PhoneUtil.isMobile(newPhoneObs.get())) {
                ToastUtils.normal("新手机号码不合法！", Gravity.CENTER);
                return false;
            }
            if(!NetWorkUtils.getInstance().checkNetWork()){
                return false;
            }
            return true;
        }
        return true;
    }
}
