package com.zizhiguanjia.model_account.viewmodel;

import android.text.Editable;
import android.view.Gravity;
import android.view.View;

import androidx.databinding.ObservableField;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.NetworkUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.listeners.ILoginVipTs;
import com.zizhiguanjia.lib_base.listeners.SendSysyCodeListener;
import com.zizhiguanjia.lib_base.utils.NetWorkUtils;
import com.zizhiguanjia.model_account.R;
import com.zizhiguanjia.model_account.api.AccountService;
import com.zizhiguanjia.model_account.config.AccountLoginStateType;
import com.zizhiguanjia.model_account.config.AccountType;
import com.zizhiguanjia.model_account.fragment.BinPhoneFragment;
import com.zizhiguanjia.model_account.fragment.UpdataPhoneFragment;
import com.zizhiguanjia.model_account.fragment.VerificationCodeLoginFragment;
import com.zizhiguanjia.model_account.listener.IBindPhone;
import com.zizhiguanjia.model_account.manager.AccountSysCodeManager;
import com.zizhiguanjia.model_account.model.LoginBean;
import com.zizhiguanjia.model_account.navigator.BindPhoneNavigator;
import com.zizhiguanjia.model_account.navigator.UpdataPhoneNavigator;

import java.util.HashMap;
import java.util.Map;

import cn.hutool.core.util.PhoneUtil;

public class BinPhoneViewModel extends CommonViewModel implements IBindPhone {
    public ObservableField<String> thridNameObs = new ObservableField<>();
    public ObservableField<String> thridOpenIdObs = new ObservableField<>();
    public ObservableField<String> phoneNumObs = new ObservableField<>();
    public ObservableField<String> phoneCodeObs = new ObservableField<>();
    public ObservableField<String> unionIdObs=new ObservableField<>();
    private BinPhoneFragment binPhoneFragment;
    private BindPhoneNavigator navigator;
    private AccountService mApi = Http.getInstance().create(AccountService.class);

    @Override
    public void initParams(BinPhoneFragment updataPhoneFragment, BindPhoneNavigator navigator) {
        this.binPhoneFragment = updataPhoneFragment;
        this.navigator = navigator;
        navigator.initSendCodeConfig();
        navigator.initLoadingPopu();
    }

    @Override
    public void setThridInfo(String name, String ids,String unionId) {
        thridNameObs.set("嗨 " + name);
        thridOpenIdObs.set(ids);
        unionIdObs.set(unionId);
    }

    @Override
    public void onclick(View view) {
        if (view.getId() == R.id.tbSendCode) {
            //发送验证码
            sendCode();
        } else if (view.getId() == R.id.tvBindPhone) {
            postBindPhoneHttp();
        } else if (view.getId() == R.id.imgClose) {
            binPhoneFragment.finish();
        }
    }

    @Override
    public void onPhoneNumEditTextChanged(Editable editable) {
        phoneNumObs.set(editable.toString());
    }

    @Override
    public void onPhoneCodeEditTextChanged(Editable editable) {
        phoneCodeObs.set(editable.toString());
    }

    @Override
    public void sendCode() {
        if (!checkData(AccountType.ACCOUNT_BINDPHONE_SYSCODE)) return;
        navigator.showLoading(true);
        AccountSysCodeManager.getInstance().sendSysCodeByType("2", phoneNumObs.get(), new SendSysyCodeListener() {
            @Override
            public void afterGetSysCode(boolean state, String msg) {
                navigator.showLoading(false);
                navigator.showErrorTost(msg);
                if (state) {
                    navigator.successSysCode();
                }
            }
        });
    }

    @Override
    public void postBindPhoneHttp() {
        if (!checkData(AccountType.ACCOUNT_BINDPHONE)) return;
        Map<String, String> params = new HashMap<>();
        params.put("phone", phoneNumObs.get());
        params.put("phoneCode", phoneCodeObs.get());
        params.put("openId", thridOpenIdObs.get());
        params.put("unionId",unionIdObs.get());
        params.put("channelUID", BaseConfig.channelUID);
        if(BaseConfig.couponid==null||BaseConfig.couponid.isEmpty()){
        }else {
            params.put("couponId", BaseConfig.couponid);
        }

        navigator.showLoading(true);
        launchOnlyResult(mApi.postBindPhone(params), new OnHandleException<BaseData<LoginBean>>() {
            @Override
            public void success(BaseData<LoginBean> data) {
                navigator.showLoading(false);
                try {
                    if(data.getResult().getLoginState()== AccountLoginStateType.LOGIN_TYPE_NORMAL){
                        navigator.showErrorTost(data.Message);
                        AccountHelper.successLoginAccount(com.wb.lib_network.utils.GsonUtils.gsonString(data.Data));
                        navigator.successBindhone();
                    }else if(data.getResult().getLoginState()== AccountLoginStateType.LOGIN_TYPE_UNSEAL){
                        if(StringUtils.isEmpty(data.getResult().getToKen())){
                            navigator.showErrorTost("参数异常，请重试~");
                            return;
                        }
                        MessageHelper.openAccountLogOffSwithTs(binPhoneFragment.getActivity(),data.getResult().getToKen());
                    }else if(data.getResult().getLoginState()== AccountLoginStateType.LOGIN_TYPE_VIP_TS){
                        MessageHelper.openAccountVipTs(binPhoneFragment.getActivity(),data.getResult().getSubjectName() , data.getResult().getSubjectName(), data.getResult().getVipPhone(), data.getResult().getToKen(), new ILoginVipTs() {
                            @Override
                            public void onGoTel(String tel) {
//                                navigator.restTel(tel);
                                binPhoneFragment.finish();
                                Bus.post(new MsgEvent(0x879999,tel));
//                                binPhoneFragment.startFragment(VerificationCodeLoginFragment.getInstance(tel));
                            }
                        });
                    }else {
                        MessageHelper.openAccountLock(binPhoneFragment.getActivity());
                    }
                } catch (Exception e) {
                    navigator.showErrorTost(e.getLocalizedMessage());
                }




//                try {
//                    ToastUtils.normal(data.Message, Gravity.CENTER);
//                    AccountHelper.successLoginAccount(com.wb.lib_network.utils.GsonUtils.gsonString(data.Data));
//                    navigator.successBindhone();
//                } catch (Exception e) {
//                    navigator.showErrorTost(e.getMessage());
//                }
            }

            @Override
            public void error(String msg) {
                navigator.showLoading(false);
                navigator.showErrorTost(msg);
            }
        });
    }

    @Override
    public boolean checkData(int type) {
        if (type == AccountType.ACCOUNT_BINDPHONE) {
            if (StringUtils.isEmpty(phoneNumObs.get())) {
                navigator.showErrorTost("手机号码不能为空！");
                return false;
            }
            if (StringUtils.isEmpty(phoneNumObs.get())) {
                navigator.showErrorTost("手机号码不合法！");
                return false;
            }
            if (StringUtils.isEmpty(phoneCodeObs.get())) {
                navigator.showErrorTost("验证码不能为空！");
                return false;
            }
            if (StringUtils.length(phoneCodeObs.get()) != 6) {
                navigator.showErrorTost("验证码长度不合法！");
                return false;
            }
            if (!NetWorkUtils.getInstance().checkNetWork()) {
                return false;
            }
            return true;
        } else if (type == AccountType.ACCOUNT_BINDPHONE_SYSCODE) {
            if (StringUtils.isEmpty(phoneNumObs.get())) {
                ToastUtils.normal("新手机号码不能为空！", Gravity.CENTER);
                return false;
            }
            if (!PhoneUtil.isMobile(phoneNumObs.get())) {
                ToastUtils.normal("旧手机号码不合法！", Gravity.CENTER);
                return false;
            }
            if (!NetWorkUtils.getInstance().checkNetWork()) {
                return false;
            }
            return true;
        }
        return false;
    }
}
