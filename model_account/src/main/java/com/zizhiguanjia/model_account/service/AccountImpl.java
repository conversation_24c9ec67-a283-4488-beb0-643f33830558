package com.zizhiguanjia.model_account.service;

import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.constants.AccountRouterPath;
import com.zizhiguanjia.lib_base.constants.CommonRouterPath;
import com.zizhiguanjia.lib_base.constants.LoginRouterPath;
import com.zizhiguanjia.lib_base.constants.MessageRouterPath;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.lib_base.helper.CertificateHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.msgconfig.UserMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.UserType;
import com.zizhiguanjia.lib_base.service.AccountServie;
import com.zizhiguanjia.model_account.R;
import com.zizhiguanjia.model_account.manager.AccountManager;

@Route(path = AccountRouterPath.SERVICE)
public class AccountImpl implements AccountServie {
    @Override
    public void start(Context context) {
        ARouter.getInstance().build(AccountRouterPath.MAIN_ACTIVITY).withTransition(R.anim.slide_in_right,R.anim.slide_out_right).navigation(AppUtils.getApp());
    }

    @Override
    public IFragment mainPage(Context context) {
        return null;
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {

    }

    @Override
    public IFragment showUpdataPhone() {
        return ARouterUtils.navFragment(AccountRouterPath.MAIN_UPDATE_PHONE);
    }

    @Override
    public IFragment showAccountLogin() {
        return ARouterUtils.navFragment(AccountRouterPath.MAIN_VERLOGIN_PHONE);
    }

    @Override
    public boolean checkLoginState() {
        return AccountManager.getInstance().checkAccountState();
    }

    @Override
    public void exitAccount() {
        AccountManager.getInstance().exitAccount();
        exitSaveAccount();
    }

    @Override
    public void initAccountInfo() {
        AccountManager.getInstance().initAccountInfo();
        UserHelper.iniAppToken();
    }

    @Override
    public void successLoginAccount(String json) {
        try {
            AccountManager.getInstance().successLoginAccount(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getCurrentLoginAccount() {
        return AccountManager.getInstance().getCurrentLoginAccount();
    }

    @Override
    public boolean isUserLogin() {
        if(AccountManager.getInstance().getAccountLoginType()== UserType.USER_TYPE_VISITOR)return false;
        return true;
    }

    @Override
    public String getUserId() {
        return AccountManager.getInstance().getUserId();
    }

    @Override
    public void exitSaveAccount() {
        UserHelper.exitUser();
        AccountManager.getInstance().updataUserType(UserType.USER_TYPE_VISITOR);
        CertificateHelper.exitAccountCertificate();
        Bus.post(new MsgEvent(UserMsgTypeConfig.USER_MSG_EXITACCOUNT));
    }

    @Override
    public void exitAccountPostData() {
        AccountManager.getInstance().accountExitPostData();
    }

    @Override
    public boolean loginSuperintendent() {
        return AccountManager.getInstance().loginSuperintendent();
    }
    /**
     * 登录验证
     * @param oneKeyLoginCheckDoubleClick 一键登录是否验证二次点击，防止其他地方的点击操作加了二次验证导致这里无法触发弹窗
     * @return true-已登录
     */
    @Override
    public boolean loginSuperintendent(boolean oneKeyLoginCheckDoubleClick) {
        return AccountManager.getInstance().loginSuperintendent(oneKeyLoginCheckDoubleClick);
    }
    @Override
    public IFragment logOffAccountFragment() {
        return ARouterUtils.navFragment(AccountRouterPath.MAIN_LOGOFF_ACCOUNT);
    }

    @Override
    public void openStaticAccountLock(int type, String token) {
        if(type==1){
            ARouter.getInstance().build(AccountRouterPath.MAIN_LOCK_ACTIVITY)
                    .withInt("type",type)
                    .withString("token",token)
                    .navigation();
        }else if(type==3){
            ARouter.getInstance().build(AccountRouterPath.MAIN_LOCK_ACTIVITY)
                    .withInt("type",type)
                    .withString("token",token)
                    .navigation();
        }else {
            ARouter.getInstance().build(AccountRouterPath.MAIN_LOCK_ACTIVITY)
                    .withInt("type",type)
                    .navigation();
        }
    }

}
