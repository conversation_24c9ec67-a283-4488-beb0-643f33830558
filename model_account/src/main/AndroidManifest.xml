<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.zizhiguanjia.model_account">
    <application>
        <activity
            android:theme="@style/BaseAppTheme"
            android:screenOrientation="portrait"
            android:name=".ui.LoginActivity"/>
        <activity
            android:theme="@style/account_myTransparent"
            android:name=".ui.AccountLockActivity"/>
<!--        <activity-->
<!--            android:screenOrientation="portrait"-->
<!--            android:name=".ui.LoginActivity"-->
<!--            android:launchMode="singleTop">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
<!--        </activity>-->
    </application>
</manifest>