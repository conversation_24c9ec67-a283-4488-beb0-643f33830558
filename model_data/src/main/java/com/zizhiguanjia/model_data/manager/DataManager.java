package com.zizhiguanjia.model_data.manager;

import android.view.Gravity;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.listeners.ExamDataUploadListener;
import com.zizhiguanjia.lib_base.msgconfig.DataMsgTypeConfig;
import com.zizhiguanjia.model_data.config.DataKvConfig;
import com.zizhiguanjia.model_data.helper.DataHelper;
import com.zizhiguanjia.model_data.model.CommonExamBean;
import com.zizhiguanjia.model_data.model.PostExamData;
import com.zizhiguanjia.model_data.model.RequestUserAnswerBean;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import cn.hutool.core.collection.ListUtil;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

import static com.zizhiguanjia.lib_base.config.BaseConfig.MAX_POST_EXAM;

public class DataManager {
    private static DataManager dataManager;
    private String paperType;
    private String currentTime;
    private String paperValue;
    private String isSubmit="false";
    private List<RequestUserAnswerBean> requestUserAnswerBeans=new ArrayList<>();
    private boolean isTask=false;
    public static DataManager getInstance(){
        if(dataManager==null){
            synchronized (DataManager.class){
                return dataManager=new DataManager();
            }
        }
        return dataManager;
    }
    public void initExamConfig(String paperType,String paperValue,String currentTime){
        this.paperType=paperType;
        this.paperValue=paperValue;
        userSubmit(false);
        this.currentTime=currentTime;
    }
    public void userSubmit(boolean isSubmit){
        this.isSubmit=isSubmit?"true":"false";
    }
    /**
     * 获取用户是否拥有权限
     * @return
     */
    public boolean checkUserPremiss(){
        return KvUtils.get(DataKvConfig.DATA_KV_USERPREMISS,false);
    }

    /**
     * 设置权限
     * @param yes
     * @return
     */
    public boolean setkUserPremiss(boolean yes){
        KvUtils.save(DataKvConfig.DATA_KV_USERPREMISS,yes);
        return KvUtils.get(DataKvConfig.DATA_KV_USERPREMISS,false);
    }

    /**
     * 封装答题卡数据json
     * @param json
     * @return
     */
    public String getExamSheetDataList(String json){
        LogUtils.e("正在处理答题卡数据*-----");
        try {
            Type jsonType = new TypeToken<BaseData<CommonExamBean>>() {}.getType();
            BaseData<CommonExamBean> commonExamBeanBaseData= new Gson().fromJson(json,jsonType);
            if(commonExamBeanBaseData.Data==null){
                BaseData baseData=new BaseData();
                baseData.Code="1003";
                baseData.Message="Json解析错误";
                return GsonUtils.gsonString(baseData);
            }else {
                return DataHelper.getInstance().getSheetlistData(commonExamBeanBaseData.Data.getRecords(),
                        commonExamBeanBaseData.Data.getLocation(),
                        commonExamBeanBaseData.Data.getDuration(),
                        commonExamBeanBaseData.Data.getExamType(),commonExamBeanBaseData.Data.getRemainingQuestionsCount());
            }
        }catch (Exception e){
            BaseData baseData=new BaseData();
            baseData.Code="1003";
            baseData.Message="Json解析错误";
            return GsonUtils.gsonString(baseData);
        }
    }
    public void uploadPostRecodel(String json){
        RequestUserAnswerBean requestUserAnswerBean=GsonUtils.gsonToBean(json, RequestUserAnswerBean.class);
        if(requestUserAnswerBean==null)return;
        cleanSaveData(requestUserAnswerBean);
        requestUserAnswerBeans.add(requestUserAnswerBean);
        againPostRecodel();
    }
    private void cleanSaveData(RequestUserAnswerBean requestUserAnswerBean){
        if(requestUserAnswerBeans==null||requestUserAnswerBeans.size()==0)return;
        Iterator<RequestUserAnswerBean> it_b=requestUserAnswerBeans.iterator();
        while(it_b.hasNext()){
            RequestUserAnswerBean a=it_b.next();
            if (requestUserAnswerBean.getId().equals(a.getId())) {
                it_b.remove();
            }
        }
    }
    public void againPostRecodel(){
        LogUtils.e("看看上传的分段数据---->>>"+"****"+requestUserAnswerBeans.size()+"****"+MAX_POST_EXAM+"*****"+isTask);
        if(isTask)return;
        isTask=true;
        List<RequestUserAnswerBean> subLists=ListUtil.sub(requestUserAnswerBeans,0, MAX_POST_EXAM);
//        LogUtils.e("看看上传的分段数据---->>>"+subLists.size()+"****"+requestUserAnswerBeans.size()+"****"+MAX_POST_EXAM);
        if(subLists.size()<MAX_POST_EXAM){
            isTask=false;
            return;
        }
        postExamData(subLists,false,false);
    }
    public void uploadVideoCountData(String json){
        if(StringUtils.isEmpty(json)){
            return;
        }
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("views", json);
        RequestBody multiBody=multiBuilder.build();
        DataHelper.getInstance().getExamSheetData(BaseAPI.VERSION_DES+"/Api/Video/SubmitVideoViewCount", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //暂时不处理上传失败的数据
            }
        });
    }

    public void uploadOrderData(String json){
        if(StringUtils.isEmpty(json)){
            return;
        }
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("billCode", json);
        RequestBody multiBody=multiBuilder.build();
        DataHelper.getInstance().getExamSheetData(BaseAPI.VERSION_DES+"/Api/Pay/Cancel", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //暂时不处理上传失败的数据
            }
        });
    }
    public void postExamData(List<RequestUserAnswerBean> subs,boolean isBusCall,boolean isClear){
        String jsons;
        if(isClear){
            if(subs==null||subs.size()==0){
                jsons="";
            }else {
                jsons= com.wb.lib_utils.utils.GsonUtils.newInstance().listToJson(subs);
            }
        }else {
            if(subs==null||subs.size()==0){
                return;
            }else {
                jsons= com.wb.lib_utils.utils.GsonUtils.newInstance().listToJson(subs);
            }
        }
        if(!isClear){
            if(StringUtils.isEmpty(jsons)){
                isTask=false;
                return;
            }
        }
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("paperType", paperType);
        if(!StringUtils.isEmpty(paperValue)){
            multiBuilder.addFormDataPart("paperValue", paperValue);
        }
        multiBuilder.addFormDataPart("dataList", jsons);
        multiBuilder.addFormDataPart("isSubmit", isSubmit);
        long lastTime=0;
        if(StringUtils.isEmpty(currentTime)){
            lastTime=System.currentTimeMillis();
        }else {
            lastTime=Long.parseLong(currentTime);
        }
        multiBuilder.addFormDataPart("remainingTime", String.valueOf((System.currentTimeMillis()-lastTime)/1000));
        RequestBody multiBody=multiBuilder.build();
        LogUtils.e("上传数据------>>>>>>"+jsons);
        DataHelper.getInstance().getExamSheetData(BaseAPI.VERSION_DES+"/API/Exam/SubmitAnswers", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                LogUtils.e("上传数据2------>>>>>>"+e.getMessage());
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        isTask=false;
                        againPostRecodel();
                    }
                });
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if(response.code()==200){
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                String json=response.body().string();
                                LogUtils.e("上传数据1------>>>>>>"+json);
                                Type jsonType = new TypeToken<BaseData<PostExamData>>() {}.getType();
                                BaseData<PostExamData> baseData=new Gson().fromJson(json,jsonType);
                                if(baseData.Code.equals("200")){
                                    LogUtils.e("------>>>>看看解析的传递2"+baseData.Data.isPopupScoreFrame());
                                    currentTime=String.valueOf(System.currentTimeMillis());
                                    if(isBusCall){
                                        Bus.post(new MsgEvent(DataMsgTypeConfig.DATA_UPLOADDATA_SUCCESS));
                                    }
                                    if(baseData.Data.isPopupScoreFrame()){
                                        String timeDes=baseData.Data.getStudyMinutes();
                                        LogUtils.e("上传数据3------>>>>>>"+timeDes);
                                        Bus.post(new MsgEvent(DataMsgTypeConfig.DATA_UPLOADDATA_POP_SUCCESS,timeDes));
                                        Bus.post(new MsgEvent(DataMsgTypeConfig.DATA_UPLOADDATA_POP_SUCCESS1,timeDes));
                                        Bus.post(new MsgEvent(DataMsgTypeConfig.DATA_UPLOADDATA_POP_SUCCESS2,timeDes));
                                    }
                                    if(requestUserAnswerBeans==null||requestUserAnswerBeans.isEmpty())return;
                                    requestUserAnswerBeans.clear();
                                }else {
                                    if(baseData.Message != null && !baseData.Message.isEmpty()) {
                                        ToastUtils.normal(baseData.Message, Gravity.CENTER);
                                    }
                                }
                                isTask=false;
                                againPostRecodel();
                            }catch (Exception e){
                                ToastUtils.normal("服务器错误", Gravity.CENTER);
                            }

                        }
                    });
                }

            }
        });
    }

    /**
     * 用户主动提交答案
      */
    public void userStartHandPaper(ExamDataUploadListener examDataUploadListener){
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("paperType", paperType);
        if(!StringUtils.isEmpty(paperValue)){
            multiBuilder.addFormDataPart("paperValue", paperValue);
        }
        multiBuilder.addFormDataPart("isSubmit", "true");
        if(requestUserAnswerBeans==null||requestUserAnswerBeans.size()==0){
        }else {
            String jsons= com.wb.lib_utils.utils.GsonUtils.newInstance().listToJson(requestUserAnswerBeans);
            multiBuilder.addFormDataPart("dataList", jsons);
        }
        long lastTime=0;
        if(StringUtils.isEmpty(currentTime)){
            lastTime=System.currentTimeMillis();
        }else {
            lastTime=Long.parseLong(currentTime);
        }
        multiBuilder.addFormDataPart("remainingTime", String.valueOf((System.currentTimeMillis()-lastTime)/1000));
        RequestBody multiBody=multiBuilder.build();
        DataHelper.getInstance().getExamSheetData(BaseAPI.VERSION_DES+"/API/Exam/SubmitAnswers", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        examDataUploadListener.onExamHandPaper(false);
                    }
                });
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if(response.code()==200){
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                String json=response.body().string();
                                BaseData baseData=GsonUtils.gsonToBean(json,BaseData.class);
                                if(baseData.Code.equals("200")){
                                    MainThreadUtils.post(new Runnable() {
                                        @Override
                                        public void run() {
                                            examDataUploadListener.onExamHandPaper(true);
                                            if(requestUserAnswerBeans==null||requestUserAnswerBeans.isEmpty())return;
                                            requestUserAnswerBeans.clear();
                                        }
                                    });
                                }else {
                                    if(baseData.Message != null && !baseData.Message.isEmpty()) {
                                        ToastUtils.normal(baseData.Message, Gravity.CENTER);
                                    }
                                    MainThreadUtils.post(new Runnable() {
                                        @Override
                                        public void run() {
                                            examDataUploadListener.onExamHandPaper(false);
                                        }
                                    });
                                }
                            }catch (Exception e){
                                    ToastUtils.normal("服务器错误", Gravity.CENTER);
                            }
                        }
                    });
                }
            }
        });
    }
    public void postAllQuestion(){
//        if(requestUserAnswerBeans.isEmpty())return;
//        if(isTask)return;
//        isTask=true;
        postExamData(requestUserAnswerBeans,true,true);
    }
}
