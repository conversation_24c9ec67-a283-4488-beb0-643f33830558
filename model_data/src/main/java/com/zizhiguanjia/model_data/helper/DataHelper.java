package com.zizhiguanjia.model_data.helper;

import android.text.format.DateUtils;

import com.wb.lib_network.AbstractHttp;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseUrlInterceptor;
import com.zizhiguanjia.lib_base.config.ResponInterceptor;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.utils.TimeUtils;
import com.zizhiguanjia.model_data.model.CommonExamBean;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Editor;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;

public class DataHelper  extends AbstractHttp {
    private static DataHelper dataHelper;
    public static DataHelper getInstance(){
        if(dataHelper==null){
            synchronized (DataHelper.class){
                return dataHelper=new DataHelper();
            }
        }
        return dataHelper;
    }
    public String getSheetlistData(List<CommonExamBean.RecordsBean> records,int Location,int Duration,int PaperType,int RemainingQuestionsCount){
        long last=System.currentTimeMillis();


        boolean userVip=UserHelper.isBecomeVip();
        CommonExamBean commonExamBean=new CommonExamBean();
        commonExamBean.setExamType(PaperType);
        commonExamBean.setDuration(Duration);
        commonExamBean.setLocation(Location);
        commonExamBean.setRemainingQuestionsCount(RemainingQuestionsCount);
        List<CommonExamBean.RecordsBean> recordsBeanLists=new ArrayList<>();
        int countFlags=0;
        int maxNum=0;
        int doNum=0;
        List<CommonExamBean.RecordsBean.ItemsBean>lists=new ArrayList<>();
        for(CommonExamBean.RecordsBean recordsBean1:records){
            if(recordsBean1.getItems()!=null||recordsBean1.getItems().size()!=0){
                lists.addAll(recordsBean1.getItems());
            }
        }
        for(int j=0;j<records.size();j++){
            CommonExamBean.RecordsBean recordsBean=records.get(j);
            int questionCount=recordsBean.getQuestionCount();
            List<CommonExamBean.RecordsBean.ItemsBean> itemsBeans=new ArrayList<>();
            for(int i=0;i<questionCount;i++){
                int questionNum=(j==0?0:countFlags)+i+1;
                CommonExamBean.RecordsBean.ItemsBean itemsBean=getSheetListDataByQuestionNum(lists,questionNum,PaperType,userVip);
                if(itemsBean==null){
                    //没有
                    itemsBean=new CommonExamBean.RecordsBean.ItemsBean();
                    itemsBean.setQuestionNum(questionNum);
                    itemsBean.setCreate(true);
                    itemsBean.setSee(false);
                    itemsBeans.add(itemsBean);
                }else {
                    if(itemsBean.isDone()){
                        ++doNum;
                    }
                    ++maxNum;
                    itemsBeans.add(itemsBean);
                }
            }
            if(j==0){
                countFlags=questionCount;
            }else {
                countFlags=countFlags+questionCount;
            }
            recordsBean.setItems(itemsBeans);
            recordsBeanLists.add(recordsBean);
        }
        commonExamBean.setDoNum(doNum);
        commonExamBean.setMaxNum(maxNum);
        LogUtils.e("正在处理答题卡数据*-----处理完毕"+(System.currentTimeMillis()-last));
        if(recordsBeanLists==null||recordsBeanLists.size()==0){
            BaseData baseData=new BaseData();
            baseData.Code="1004";
            baseData.Message="暂无数据";
            return GsonUtils.gsonString(baseData);
        }else {
            commonExamBean.setRecords(recordsBeanLists);
            BaseData<CommonExamBean> baseData=new BaseData<CommonExamBean>();
            baseData.Code="200";
            baseData.Data=commonExamBean;
            return GsonUtils.gsonString(baseData);
        }
    }

    private CommonExamBean.RecordsBean.ItemsBean getSheetListDataByQuestionNum(List<CommonExamBean.RecordsBean.ItemsBean> itemsBeans,int questionNum,int PaperType,boolean isVip){
        CommonExamBean.RecordsBean.ItemsBean itemsBeanss=getItem(itemsBeans,questionNum);
        if(itemsBeanss==null){
            //未查询到数据
            if(PaperType==5||PaperType==6){
                CommonExamBean.RecordsBean.ItemsBean itemsBean=new CommonExamBean.RecordsBean.ItemsBean();
                itemsBean.setCreate(true);
                itemsBean.setSee(true);
                itemsBean.setQuestionNum(questionNum);
                itemsBean.setDone(false);
                return itemsBean;
            }else {
                if(isVip){
                    CommonExamBean.RecordsBean.ItemsBean itemsBean=new CommonExamBean.RecordsBean.ItemsBean();
                    itemsBean.setCreate(true);
                    itemsBean.setSee(true);
                    itemsBean.setQuestionNum(questionNum);
                    itemsBean.setDone(false);
                    return itemsBean;
                }else {
                    return null;
                }
            }

        }else {
            //查询到数据
            return itemsBeanss;
        }
    }
    public CommonExamBean.RecordsBean.ItemsBean getItem(List<CommonExamBean.RecordsBean.ItemsBean> itemsBeans,int questionNum){
        for(CommonExamBean.RecordsBean.ItemsBean itemsBean:itemsBeans){
            if(questionNum==itemsBean.getQuestionNum()){
                itemsBean.setSee(true);
                return itemsBean;
            }
        }
        return null;
    }
    @Override
    protected String baseUrl() {
        return BaseAPI.Base_Url_Api;
    }
    @Override
    protected Iterable<Interceptor> interceptors() {
        final List<Interceptor> interceptorList = new ArrayList<>();
        interceptorList.add(new BaseUrlInterceptor());
        interceptorList.add(new ResponInterceptor());
        return interceptorList;

    }
    public void getExamSheetData(String url, RequestBody requestBody, Callback callback){
        Request request = new Request.Builder()
                .url(BaseAPI.Base_Url_Api+url)
                .post(requestBody)
                .build();
        doAsync(request, callback);
    }
    private void doAsync(Request request, Callback callback) {
        //创建请求会话
        Call call = okHttpClient().newCall(request);
        //异步执行会话请求
        call.enqueue(callback);
    }

}
