package com.zizhiguanjia.model_data.service;

import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.base.IFragment;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.constants.DataRouterPath;
import com.zizhiguanjia.lib_base.listeners.ExamDataUploadListener;
import com.zizhiguanjia.lib_base.service.DataService;
import com.zizhiguanjia.model_data.helper.DataHelper;
import com.zizhiguanjia.model_data.manager.DataManager;

@Route(path = DataRouterPath.SERVICE)
public class DataServiceImpl implements DataService {
    @Override
    public boolean checkUserPremiss() {
        return DataManager.getInstance().checkUserPremiss();
    }

    @Override
    public boolean setUserPremiss(boolean userPremiss) {
        return DataManager.getInstance().setkUserPremiss(userPremiss);
    }

    @Override
    public String getExamSheetDataList(String json) {
        String json1=DataManager.getInstance().getExamSheetDataList(json);
        LogUtils.e("看看解析的字段名c"+json1);
        return json1;
    }

    @Override
    public boolean uploadRecolder(String json) {

         DataManager.getInstance().uploadPostRecodel(json);
         return true;
    }

    @Override
    public void initExamConfig(String paperType, String paperValue, String currentTime) {
        DataManager.getInstance().initExamConfig(paperType,paperValue,currentTime);
    }

    @Override
    public void userSubmit(boolean isSubmit) {
        DataManager.getInstance().userSubmit(isSubmit);
    }

    @Override
    public void userStartHandPaper(ExamDataUploadListener examDataUploadListener) {
        userSubmit(true);
        DataManager.getInstance().userStartHandPaper(examDataUploadListener);
    }

    @Override
    public void postAllQuestion() {
        DataManager.getInstance().postAllQuestion();
    }

    @Override
    public void uploadVideoCountData(String json) {
        DataManager.getInstance().uploadVideoCountData(json);
    }

    @Override
    public void uploadOrderState(String num) {
        DataManager.getInstance().uploadOrderData(num);
    }

    @Override
    public void start(Context context) {

    }

    @Override
    public IFragment mainPage(Context context) {
        return null;
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {

    }
}
