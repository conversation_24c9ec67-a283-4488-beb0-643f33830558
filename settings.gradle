include ':model_home'
include ':model_message'
include ':model_carse'
include ':model_list'
include ':model_core'
include ':model_fixbug'
include ':lib_jpush'
include ':model_config'
include ':model_point'
include ':model_address'
include ':flutter_module'
include ':model_umeng'
include ':model_task'
setBinding(new Binding([gradle: this]))
evaluate(new File(
  settingsDir,
  '../app.baodian.flutter.native/.android/include_flutter.groovy'
))
include ':model_video'
include ':model_certificate'
include ':model_sdk'
include ':model_user'
include ':model_account'
include ':model_common'
include ':model_data'
include ':model_init'
include ':model_pay'
include ':lib_base'
include ':model_address'
include ':app'
rootProject.name = "AndroidExam"
project(':flutter_module').projectDir = new File('../app.baodian.flutter.native')
include ':app.baodian.flutter.native'
project(':app.baodian.flutter.native').projectDir = new File('../app.baodian.flutter.native')
include ':model_utils'
include ':flutapp.baodian.flutter.nativeter'
project(':app.baodian.flutter.native').projectDir = new File(settingsDir.parentFile, '../app.baodian.flutter.native/.android')